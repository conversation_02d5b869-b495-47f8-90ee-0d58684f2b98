package main

import (
	json "github.com/goccy/go-json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// AnalysisOutput 完整的分析输出结构
type AnalysisOutput struct {
	ImageInfo       ImageInfo                  `json:"image_info"`
	ColorStandards  map[string]ColorStandard   `json:"color_standards"`
	AnalysisResults []AnalysisResult           `json:"analysis_results"`
	ColorSummary    map[string][]string        `json:"color_summary"`
	ColorStatistics map[string]ColorStatistics `json:"color_statistics"`
	TargetOrgan     string                     `json:"target_organ"`
}

// ImageInfo 图像信息
type ImageInfo struct {
	Path         string `json:"path"`
	Size         [2]int `json:"size"`
	TotalNumbers int    `json:"total_numbers"`
}

// WebOutput 网页输出格式
type WebOutput struct {
	AnalysisTimestamp string                     `json:"analysis_timestamp"`
	SourceFiles       SourceFiles                `json:"source_files"`
	TargetOrgan       string                     `json:"target_organ"`
	ColorStatistics   map[string]ColorStatistics `json:"color_statistics"`
	TotalCount        int                        `json:"total_count"`
	DetailedResults   []DetailedResult           `json:"detailed_results"`
}

// SourceFiles 源文件信息
type SourceFiles struct {
	OCRFile   string `json:"ocr_file"`
	ImageFile string `json:"image_file"`
}

// DetailedResult 详细结果（网页格式）
type DetailedResult struct {
	NumericValue       string      `json:"numeric_value"`
	FloatValue         float64     `json:"float_value"`
	TextElements       []string    `json:"text_elements"`
	NumericCoordinates [2]int      `json:"numeric_coordinates"`
	NumericRGB         [3]int      `json:"numeric_rgb"`
	FinalColor         string      `json:"final_color"`
	TextColors         []TextColor `json:"text_colors"`
}

// AnalyzeColorsWithPairs 根据精细化策略分析数字-文字键值对的颜色
func (oca *OptimizedColorAnalyzer) AnalyzeColorsWithPairs() (*AnalysisOutput, error) {
	fmt.Println("[分析] 开始颜色分析")

	// 提取数字-文字键值对
	pairs, targetOrgan, err := oca.ExtractNumericTextPairs()
	if err != nil {
		return nil, fmt.Errorf("提取数字-文字键值对失败: %v", err)
	}

	if len(pairs) == 0 {
		return nil, fmt.Errorf("未找到数字-文字键值对")
	}

	var analysisResults []AnalysisResult
	colorSummary := make(map[string][]string)

	for _, pair := range pairs {
		// 分析数字的颜色（使用多边形区域采样）
		numX, numY := pair.NumericCoordinates[0], pair.NumericCoordinates[1]

		// 根据文字大小调整采样范围
		sampleSize := 5
		if len(pair.NumericPolygon) >= 4 {
			// 计算多边形的宽度和高度
			var minX, maxX, minY, maxY float64
			for i, point := range pair.NumericPolygon {
				if len(point) >= 2 {
					if i == 0 {
						minX, maxX = float64(point[0]), float64(point[0])
						minY, maxY = float64(point[1]), float64(point[1])
					} else {
						if float64(point[0]) < minX {
							minX = float64(point[0])
						}
						if float64(point[0]) > maxX {
							maxX = float64(point[0])
						}
						if float64(point[1]) < minY {
							minY = float64(point[1])
						}
						if float64(point[1]) > maxY {
							maxY = float64(point[1])
						}
					}
				}
			}

			width := maxX - minX
			height := maxY - minY

			// 根据文字大小调整采样范围
			minSize := width
			if height < width {
				minSize = height
			}
			sampleSize = int(minSize / 3)
			if sampleSize < 3 {
				sampleSize = 3
			}
			if sampleSize > 7 {
				sampleSize = 7
			}
		}

		numRGB := oca.GetPixelColor(numX, numY, sampleSize)
		numColor := oca.ClassifyColorByStandards(numRGB)

		// 分析文字的颜色
		var textColors []TextColor
		for i, coords := range pair.TextCoordinates {
			if coords[0] > 0 && coords[1] > 0 { // 有效坐标
				textRGB := oca.GetPixelColor(coords[0], coords[1], 5)
				textColor := oca.ClassifyColorByStandards(textRGB)
				textColors = append(textColors, TextColor{
					Text:      pair.TextElements[i],
					RGB:       RGB{R: textRGB.R, G: textRGB.G, B: textRGB.B},
					ColorName: textColor,
					HexColor:  fmt.Sprintf("#%02X%02X%02X", textRGB.R, textRGB.G, textRGB.B),
				})
			}
		}

		// 应用颜色优先级规则
		finalColor := oca.ApplyColorPriorityRules(numColor, textColors)

		// 调试输出：显示分析结果（仅在调试模式下）
		if os.Getenv("COLOR_DEBUG") == "1" {
			// 构建文本颜色信息字符串
			textColorInfo := ""
			for i, tc := range textColors {
				if i > 0 {
					textColorInfo += ", "
				}
				textColorInfo += fmt.Sprintf("%s:%s", tc.ColorName, tc.HexColor)
			}
			if textColorInfo == "" {
				textColorInfo = "无文本"
			}

			fmt.Printf("[智能分析] %s -> %v | 数字颜色: %s, 文本颜色: %s, 最终颜色: %s\n",
				pair.NumericValue, pair.TextElements, numColor, textColorInfo, finalColor)
		}

		// 记录分析结果
		result := AnalysisResult{
			NumericValue:         pair.NumericValue,
			FloatValue:           pair.FloatValue,
			NumericCoordinates:   pair.NumericCoordinates,
			NumericRGB:           [3]int{numRGB.R, numRGB.G, numRGB.B},
			NumericColorOriginal: numColor,
			TextElements:         pair.TextElements,
			TextColors:           textColors,
			FinalColor:           finalColor,
			Polygon:              pair.NumericPolygon,
			PairIndex:            len(analysisResults),
		}

		analysisResults = append(analysisResults, result)
		colorSummary[finalColor] = append(colorSummary[finalColor], pair.NumericValue)

		// 详细调试输出（可选）
		// fmt.Printf("[详细] %s -> %v | 数字RGB: (%d,%d,%d) 颜色: %s -> 最终颜色: %s\n",
		//	pair.NumericValue, pair.TextElements, numRGB.R, numRGB.G, numRGB.B, numColor, finalColor)
	}

	// 生成统计信息
	totalCount := len(analysisResults)
	colorStats := make(map[string]ColorStatistics)
	for colorName, values := range colorSummary {
		count := len(values)
		percentage := 0.0
		if totalCount > 0 {
			percentage = (float64(count) / float64(totalCount)) * 100
		}
		colorStats[colorName] = ColorStatistics{
			Count:      count,
			Percentage: percentage,
			Values:     values,
		}
	}

	fmt.Printf("[颜色分析] 分析完成，总计 %d 个数字，颜色分布: ", totalCount)
	for colorName, stats := range colorStats {
		fmt.Printf("%s(%d个,%.1f%%) ", colorName, stats.Count, stats.Percentage)
	}
	fmt.Println()

	if targetOrgan != "" {
		fmt.Printf("[器官] 目标器官: %s\n", targetOrgan)
	}

	bounds := oca.image.Bounds()
	return &AnalysisOutput{
		ImageInfo: ImageInfo{
			Path:         oca.imagePath,
			Size:         [2]int{bounds.Dx(), bounds.Dy()},
			TotalNumbers: totalCount,
		},
		ColorStandards:  oca.colorStandards,
		AnalysisResults: analysisResults,
		ColorSummary:    colorSummary,
		ColorStatistics: colorStats,
		TargetOrgan:     targetOrgan,
	}, nil
}

// SaveResults 保存分析结果
func (oca *OptimizedColorAnalyzer) SaveResults(results *AnalysisOutput) (string, error) {
	outputPath := filepath.Join(oca.outputDir, "optimized_color_analysis_results.json")

	file, err := os.Create(outputPath)
	if err != nil {
		return "", fmt.Errorf("创建结果文件失败: %v", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	err = encoder.Encode(results)
	if err != nil {
		return "", fmt.Errorf("保存结果失败: %v", err)
	}

	fmt.Printf("[保存] 结果已保存: %s\n", outputPath)
	return outputPath, nil
}

// SaveResultsForWeb 保存分析结果到JSON文件供网页使用
func (oca *OptimizedColorAnalyzer) SaveResultsForWeb(results *AnalysisOutput) (string, error) {
	// 准备保存的数据
	webData := WebOutput{
		AnalysisTimestamp: time.Now().Format("2006-01-02T15:04:05"),
		SourceFiles: SourceFiles{
			OCRFile:   filepath.Base(oca.ocrDataPath),
			ImageFile: filepath.Base(oca.imagePath),
		},
		TargetOrgan:     results.TargetOrgan,
		ColorStatistics: results.ColorStatistics,
		TotalCount:      results.ImageInfo.TotalNumbers,
	}

	// 添加详细结果（数字-文字键值对格式）
	for _, result := range results.AnalysisResults {
		var coords [2]int
		if len(result.NumericCoordinates) >= 2 {
			coords[0] = result.NumericCoordinates[0]
			coords[1] = result.NumericCoordinates[1]
		}
		detail := DetailedResult{
			NumericValue:       result.NumericValue,
			FloatValue:         result.FloatValue,
			TextElements:       result.TextElements,
			NumericCoordinates: coords,
			NumericRGB:         result.NumericRGB,
			FinalColor:         result.FinalColor,
			TextColors:         result.TextColors,
		}
		webData.DetailedResults = append(webData.DetailedResults, detail)
	}

	// 保存到文件（网页专用）
	outputFile := filepath.Join(oca.outputDir, "text_with_color.json")
	file, err := os.Create(outputFile)
	if err != nil {
		return "", fmt.Errorf("创建网页数据文件失败: %v", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	err = encoder.Encode(webData)
	if err != nil {
		return "", fmt.Errorf("保存网页数据失败: %v", err)
	}

	fmt.Printf("[保存] 网页数据已保存到: %s\n", outputFile)
	return outputFile, nil
}

// GenerateDetailedReport 生成详细的分析报告
func (oca *OptimizedColorAnalyzer) GenerateDetailedReport(results *AnalysisOutput) (string, error) {
	reportPath := filepath.Join(oca.outputDir, "optimized_analysis_report.txt")

	file, err := os.Create(reportPath)
	if err != nil {
		return "", fmt.Errorf("创建报告文件失败: %v", err)
	}
	defer file.Close()

	// 写入报告内容
	fmt.Fprintln(file, "基于标准颜色范围的优化颜色分析报告（数字-文字键值对）- Go版本")
	fmt.Fprintln(file, strings.Repeat("=", 70))
	fmt.Fprintln(file)

	// 基本信息
	fmt.Fprintln(file, "基本信息:")
	fmt.Fprintln(file, strings.Repeat("-", 30))
	fmt.Fprintf(file, "图像路径: %s\n", results.ImageInfo.Path)
	fmt.Fprintf(file, "图像尺寸: %dx%d\n", results.ImageInfo.Size[0], results.ImageInfo.Size[1])
	fmt.Fprintf(file, "分析键值对总数: %d\n", results.ImageInfo.TotalNumbers)

	// 目标器官信息
	if results.TargetOrgan != "" {
		fmt.Fprintf(file, "目标器官: %s\n", results.TargetOrgan)
	}
	fmt.Fprintln(file)

	// 颜色标准
	fmt.Fprintln(file, "颜色识别标准:")
	fmt.Fprintln(file, strings.Repeat("-", 30))
	for colorName, standard := range results.ColorStandards {
		fmt.Fprintf(file, "%s:\n", colorName)
		fmt.Fprintf(file, "  标准RGB: (%d,%d,%d)\n", standard.StandardRGB[0], standard.StandardRGB[1], standard.StandardRGB[2])
		fmt.Fprintf(file, "  范围: (%d,%d,%d) 到 (%d,%d,%d)\n",
			standard.RangeMin[0], standard.RangeMin[1], standard.RangeMin[2],
			standard.RangeMax[0], standard.RangeMax[1], standard.RangeMax[2])
		fmt.Fprintf(file, "  说明: %s\n\n", standard.Description)
	}

	// 颜色分布统计
	fmt.Fprintln(file, "颜色分布统计:")
	fmt.Fprintln(file, strings.Repeat("-", 30))
	for colorName, stats := range results.ColorStatistics {
		fmt.Fprintf(file, "%s: %d 个 (%.1f%%)\n", colorName, stats.Count, stats.Percentage)
	}
	fmt.Fprintln(file)

	// 异常值检测
	fmt.Fprintln(file, "异常值检测结果:")
	fmt.Fprintln(file, strings.Repeat("-", 30))

	redStats, hasRed := results.ColorStatistics["红色"]
	orangeStats, hasOrange := results.ColorStatistics["橘色"]

	if hasRed && redStats.Count > 0 {
		fmt.Fprintf(file, "检测到 %d 个红色异常值:\n", redStats.Count)
		for _, value := range redStats.Values {
			fmt.Fprintf(file, "  - %s\n", value)
		}
	} else {
		fmt.Fprintln(file, "未检测到红色异常值")
	}

	if hasOrange && orangeStats.Count > 0 {
		fmt.Fprintf(file, "检测到 %d 个橘色警告值:\n", orangeStats.Count)
		for _, value := range orangeStats.Values {
			fmt.Fprintf(file, "  - %s\n", value)
		}
	} else {
		fmt.Fprintln(file, "未检测到橘色警告值")
	}
	fmt.Fprintln(file)

	// 详细分析数据
	fmt.Fprintln(file, "详细分析数据（数字-文字键值对）:")
	fmt.Fprintln(file, strings.Repeat("-", 30))
	for _, result := range results.AnalysisResults {
		fmt.Fprintf(file, "数字值: %s\n", result.NumericValue)
		fmt.Fprintf(file, "  文字元素: %v\n", result.TextElements)
		fmt.Fprintf(file, "  数字颜色: %s\n", result.NumericColorOriginal)
		fmt.Fprintf(file, "  最终颜色: %s\n", result.FinalColor)
		fmt.Fprintf(file, "  数字RGB: (%d,%d,%d)\n", result.NumericRGB[0], result.NumericRGB[1], result.NumericRGB[2])
		fmt.Fprintf(file, "  数字坐标: (%d,%d)\n", result.NumericCoordinates[0], result.NumericCoordinates[1])

		// 文字颜色信息
		if len(result.TextColors) > 0 {
			fmt.Fprintln(file, "  文字颜色:")
			for _, textColor := range result.TextColors {
				fmt.Fprintf(file, "    - %s: %s RGB(%d,%d,%d)\n",
					textColor.Text, textColor.ColorName,
					textColor.RGB.R, textColor.RGB.G, textColor.RGB.B)
			}
		}
		fmt.Fprintln(file)
	}

	// 分析结论
	fmt.Fprintln(file, "分析结论:")
	fmt.Fprintln(file, strings.Repeat("-", 30))

	totalAbnormal := 0
	if hasRed {
		totalAbnormal += redStats.Count
	}
	if hasOrange {
		totalAbnormal += orangeStats.Count
	}

	if totalAbnormal > 0 {
		abnormalRate := (float64(totalAbnormal) / float64(results.ImageInfo.TotalNumbers)) * 100
		fmt.Fprintf(file, "检测到 %d 个异常/警告值，占总数的 %.1f%%\n", totalAbnormal, abnormalRate)
		fmt.Fprintln(file, "建议重点关注这些数值，可能需要进一步医学评估。")
	} else {
		fmt.Fprintln(file, "未检测到明显的异常值，所有数值颜色均在正常范围内。")
	}

	fmt.Fprintln(file)
	fmt.Fprintln(file, "优化效果:")
	fmt.Fprintln(file, "1. 使用数字-文字键值对进行精确分析")
	fmt.Fprintln(file, "2. 应用颜色优先级规则提高准确性")
	fmt.Fprintln(file, "3. 支持目标器官识别")
	fmt.Fprintln(file, "4. 符合医学报告的颜色标准规范")
	fmt.Fprintln(file, "5. Go语言实现，性能更优")

	fmt.Printf("[报告] 详细报告已保存: %s\n", reportPath)
	return reportPath, nil
}

// RunAnalysis 运行完整的颜色分析
func (oca *OptimizedColorAnalyzer) RunAnalysis() error {
	fmt.Println("[开始] 基于标准颜色范围的优化颜色分析 (Go版本)")
	fmt.Println(strings.Repeat("=", 70))

	// 加载数据
	err := oca.LoadData()
	if err != nil {
		return fmt.Errorf("加载数据失败: %v", err)
	}

	// 分析颜色
	results, err := oca.AnalyzeColorsWithPairs()
	if err != nil {
		return fmt.Errorf("颜色分析失败: %v", err)
	}

	// 保存结果
	resultsPath, err := oca.SaveResults(results)
	if err != nil {
		return fmt.Errorf("保存结果失败: %v", err)
	}

	// 保存网页数据
	webDataPath, err := oca.SaveResultsForWeb(results)
	if err != nil {
		return fmt.Errorf("保存网页数据失败: %v", err)
	}

	// 生成报告
	reportPath, err := oca.GenerateDetailedReport(results)
	if err != nil {
		return fmt.Errorf("生成报告失败: %v", err)
	}

	fmt.Println()
	fmt.Println(strings.Repeat("=", 70))
	fmt.Println("优化颜色分析完成！(Go版本)")
	fmt.Printf("结果文件: %s\n", resultsPath)
	fmt.Printf("网页数据: %s\n", webDataPath)
	fmt.Printf("详细报告: %s\n", reportPath)
	fmt.Println(strings.Repeat("=", 70))

	// 打印简要统计
	fmt.Println()
	fmt.Println("颜色分布统计:")
	for colorName, stats := range results.ColorStatistics {
		fmt.Printf("- %s: %d 个 (%.1f%%)\n", colorName, stats.Count, stats.Percentage)
	}

	// 突出显示异常值
	redCount := 0
	orangeCount := 0
	if redStats, exists := results.ColorStatistics["红色"]; exists {
		redCount = redStats.Count
	}
	if orangeStats, exists := results.ColorStatistics["橘色"]; exists {
		orangeCount = orangeStats.Count
	}

	if redCount > 0 || orangeCount > 0 {
		fmt.Printf("\n⚠️  检测到异常值: %d 个红色 + %d 个橘色\n", redCount, orangeCount)
	} else {
		fmt.Println("\n✅ 未检测到异常值")
	}

	return nil
}
