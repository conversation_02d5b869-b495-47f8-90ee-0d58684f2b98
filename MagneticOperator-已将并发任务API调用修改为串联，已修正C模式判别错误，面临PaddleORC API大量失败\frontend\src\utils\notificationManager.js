/**
 * 增强的通知管理器
 * 统一管理Toast通知、进度通知和系统状态通知
 */

import eventManager from './eventManager.js'

class NotificationManager {
  constructor() {
    this.toastComponent = null
    this.notifications = new Map()
    this.progressNotifications = new Map()
    this.config = {
      maxToasts: 5,
      defaultDuration: 5000,
      progressUpdateInterval: 100,
      debugMode: true
    }
    this.stats = {
      totalNotifications: 0,
      activeNotifications: 0,
      completedNotifications: 0,
      failedNotifications: 0
    }
  }

  /**
   * 初始化通知管理器
   */
  init(toastComponent) {
    this.toastComponent = toastComponent
    this.setupEventListeners()
    this.log('NotificationManager 初始化完成')
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听并发进度事件
    eventManager.on('concurrent-progress', (data) => {
      this.handleConcurrentProgress(data)
    })

    // 监听OCR进度事件
    eventManager.on('ocr-progress', (data) => {
      this.handleOCRProgress(data)
    })

    // 监听任务完成事件
    eventManager.on('task-completed', (data) => {
      this.handleTaskCompleted(data)
    })

    // 监听任务错误事件
    eventManager.on('task-error', (data) => {
      this.handleTaskError(data)
    })

    // 监听系统状态更新
    eventManager.on('system-status', (data) => {
      this.handleSystemStatus(data)
    })
  }

  /**
   * 处理并发进度通知
   */
  handleConcurrentProgress(data) {
    const notificationId = `concurrent_${data.mode}`
    
    const config = {
      id: notificationId,
      title: '并发截图处理',
      message: `${data.mode === 'B' ? 'B02生化分析' : 'C03病理分析'} (${data.progress}%)`,
      type: 'info',
      duration: 0, // 不自动消失
      showProgress: true,
      progress: data.progress,
      persistent: true
    }

    this.showProgressNotification(config)
    this.updateOperationStatus(data.message)
  }

  /**
   * 处理OCR进度通知
   */
  handleOCRProgress(data) {
    const notificationId = `ocr_${data.organName.replace(/\s+/g, '_')}`
    
    const config = {
      id: notificationId,
      title: 'OCR识别处理',
      message: `${data.organName} - 置信度: ${data.confidence}%`,
      type: data.confidence > 80 ? 'success' : data.confidence > 60 ? 'warning' : 'error',
      duration: 3000,
      showProgress: true,
      progress: data.progress
    }

    this.showNotification(config)
  }

  /**
   * 处理任务完成通知
   */
  handleTaskCompleted(data) {
    const config = {
      title: '任务完成',
      message: `${data.taskType} 已完成 (耗时: ${this.formatDuration(data.duration)})`,
      type: 'success',
      duration: 5000,
      showProgress: false
    }

    this.showNotification(config)
    this.stats.completedNotifications++
    
    // 清理相关的进度通知
    this.clearProgressNotifications(data.taskType)
  }

  /**
   * 处理任务错误通知
   */
  handleTaskError(data) {
    const config = {
      title: '任务失败',
      message: `${data.taskType}: ${data.error}`,
      type: 'error',
      duration: 8000,
      showProgress: false,
      actions: [
        {
          text: '重试',
          action: () => this.retryTask(data.taskType)
        },
        {
          text: '查看详情',
          action: () => this.showErrorDetails(data)
        }
      ]
    }

    this.showNotification(config)
    this.stats.failedNotifications++
  }

  /**
   * 处理系统状态通知
   */
  handleSystemStatus(data) {
    const config = {
      title: '系统状态',
      message: data.message,
      type: this.getStatusType(data.status),
      duration: 4000,
      showProgress: false
    }

    this.showNotification(config)
  }

  /**
   * 显示通知
   */
  showNotification(config) {
    if (!this.toastComponent) {
      this.error('Toast组件未初始化')
      return null
    }

    // 生成唯一ID
    if (!config.id) {
      config.id = this.generateNotificationId()
    }

    // 应用默认配置
    const finalConfig = {
      duration: this.config.defaultDuration,
      showProgress: false,
      ...config
    }

    try {
      const result = this.toastComponent.showToast(finalConfig)
      
      if (result) {
        this.notifications.set(config.id, {
          config: finalConfig,
          timestamp: Date.now(),
          status: 'active'
        })
        
        this.stats.totalNotifications++
        this.stats.activeNotifications++
        
        this.log(`显示通知: ${finalConfig.title} (ID: ${config.id})`)
      }
      
      return result
    } catch (error) {
      this.error('显示通知失败:', error)
      return null
    }
  }

  /**
   * 显示进度通知
   */
  showProgressNotification(config) {
    const notification = this.showNotification(config)
    
    if (notification && config.id) {
      this.progressNotifications.set(config.id, {
        notification,
        config,
        lastUpdate: Date.now()
      })
    }
    
    return notification
  }

  /**
   * 更新进度通知
   */
  updateProgressNotification(id, progress, message) {
    const progressNotification = this.progressNotifications.get(id)
    
    if (progressNotification && this.toastComponent) {
      try {
        this.toastComponent.updateProgress(id, progress, message)
        progressNotification.lastUpdate = Date.now()
        this.log(`更新进度通知: ${id} (${progress}%)`)
      } catch (error) {
        this.error(`更新进度通知失败 (${id}):`, error)
      }
    }
  }

  /**
   * 清理进度通知
   */
  clearProgressNotifications(taskType) {
    const toRemove = []
    
    for (const [id, notification] of this.progressNotifications) {
      if (id.includes(taskType)) {
        toRemove.push(id)
      }
    }
    
    toRemove.forEach(id => {
      this.removeNotification(id)
      this.progressNotifications.delete(id)
    })
  }

  /**
   * 移除通知
   */
  removeNotification(id) {
    if (this.toastComponent) {
      try {
        this.toastComponent.removeToast(id)
        
        const notification = this.notifications.get(id)
        if (notification) {
          notification.status = 'removed'
          this.stats.activeNotifications--
        }
        
        this.log(`移除通知: ${id}`)
      } catch (error) {
        this.error(`移除通知失败 (${id}):`, error)
      }
    }
  }

  /**
   * 清除所有通知
   */
  clearAllNotifications() {
    if (this.toastComponent) {
      try {
        this.toastComponent.clearAllToasts()
        
        // 更新状态
        for (const notification of this.notifications.values()) {
          if (notification.status === 'active') {
            notification.status = 'cleared'
            this.stats.activeNotifications--
          }
        }
        
        this.progressNotifications.clear()
        this.log('清除所有通知')
      } catch (error) {
        this.error('清除所有通知失败:', error)
      }
    }
  }

  /**
   * 更新操作状态
   */
  updateOperationStatus(message) {
    // 触发操作状态更新事件
    if (window.app && window.app.updateOperationStatus) {
      window.app.updateOperationStatus(message)
    }
    
    // 发送自定义事件
    window.dispatchEvent(new CustomEvent('operation-status-update', {
      detail: { message, timestamp: Date.now() }
    }))
  }

  /**
   * 重试任务
   */
  retryTask(taskType) {
    this.log(`重试任务: ${taskType}`)
    
    // 发送重试事件
    window.dispatchEvent(new CustomEvent('task-retry', {
      detail: { taskType, timestamp: Date.now() }
    }))
  }

  /**
   * 显示错误详情
   */
  showErrorDetails(data) {
    this.log('显示错误详情:', data)
    
    // 发送显示错误详情事件
    window.dispatchEvent(new CustomEvent('show-error-details', {
      detail: data
    }))
  }

  /**
   * 获取状态类型
   */
  getStatusType(status) {
    const statusMap = {
      'success': 'success',
      'error': 'error',
      'warning': 'warning',
      'info': 'info',
      'processing': 'info',
      'completed': 'success',
      'failed': 'error'
    }
    
    return statusMap[status] || 'info'
  }

  /**
   * 格式化持续时间
   */
  formatDuration(ms) {
    if (ms < 1000) {
      return `${ms}ms`
    } else if (ms < 60000) {
      return `${(ms / 1000).toFixed(1)}s`
    } else {
      const minutes = Math.floor(ms / 60000)
      const seconds = Math.floor((ms % 60000) / 1000)
      return `${minutes}m ${seconds}s`
    }
  }

  /**
   * 生成通知ID
   */
  generateNotificationId() {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 日志输出
   */
  log(...args) {
    if (this.config.debugMode) {
      console.log('[NotificationManager]', ...args)
    }
  }

  /**
   * 错误输出
   */
  error(...args) {
    console.error('[NotificationManager]', ...args)
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.stats,
      activeNotifications: this.stats.activeNotifications,
      progressNotifications: this.progressNotifications.size,
      totalNotifications: this.notifications.size
    }
  }

  /**
   * 获取活跃通知列表
   */
  getActiveNotifications() {
    const active = []
    
    for (const [id, notification] of this.notifications) {
      if (notification.status === 'active') {
        active.push({
          id,
          ...notification.config,
          timestamp: notification.timestamp
        })
      }
    }
    
    return active
  }
}

// 创建全局实例
const notificationManager = new NotificationManager()

export default notificationManager
export { NotificationManager }