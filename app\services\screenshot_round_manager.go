package services

import (
	"fmt"
	"path/filepath"
	"sync"
	"time"
)

// RoundStatus 轮次状态
type RoundStatus int

const (
	RoundPending    RoundStatus = iota // 等待中
	RoundProcessing                    // 处理中
	RoundCompleted                     // 已完成
	RoundFailed                        // 失败
)

// ScreenshotRound 截图轮次信息
type ScreenshotRound struct {
	RoundNumber   int               `json:"round_number"`   // 轮次编号 (1-10)
	UserName      string            `json:"user_name"`      // 用户名
	Status        RoundStatus       `json:"status"`         // 轮次状态
	Screenshots   []*ScreenshotTask `json:"screenshots"`    // 截图任务列表（每轮2个）
	Results       []*OCRTaskResult  `json:"results"`        // OCR结果列表
	StartTime     time.Time         `json:"start_time"`     // 开始时间
	CompletedTime *time.Time        `json:"completed_time"` // 完成时间
	Errors        []string          `json:"errors"`         // 错误信息
	mu            sync.RWMutex      `json:"-"`              // 读写锁
}

// Lock 锁定轮次以进行写操作
func (sr *ScreenshotRound) Lock() {
	sr.mu.Lock()
}

// Unlock 解锁写操作
func (sr *ScreenshotRound) Unlock() {
	sr.mu.Unlock()
}

// RLock 锁定轮次以进行读操作
func (sr *ScreenshotRound) RLock() {
	sr.mu.RLock()
}

// RUnlock 解锁读操作
func (sr *ScreenshotRound) RUnlock() {
	sr.mu.RUnlock()
}

// RoundProgressCallback 轮次进度回调函数类型
type RoundProgressCallback func(round *ScreenshotRound, progress float64)

// RoundCompletedCallback 轮次完成回调函数类型
type RoundCompletedCallback func(round *ScreenshotRound)

// ScreenshotRoundManager 截图轮次管理器
type ScreenshotRoundManager struct {
	processor          *ConcurrentOCRProcessor  // 并发OCR处理器
	screenshotService  *ScreenshotService       // 截图服务
	configService      *ConfigService           // 配置服务
	app                AppInterface             // App接口引用，用于通知
	rounds             map[int]*ScreenshotRound // 轮次映射 (轮次编号 -> 轮次信息)
	currentRound       int                      // 当前轮次
	maxRounds          int                      // 最大轮次数 (默认10)
	progressCallbacks  []RoundProgressCallback  // 进度回调函数列表
	completedCallbacks []RoundCompletedCallback // 完成回调函数列表
	mu                 sync.RWMutex             // 读写锁
	running            bool                     // 运行状态
	startTime          time.Time                // 开始时间
}

// NewScreenshotRoundManager 创建截图轮次管理器
func NewScreenshotRoundManager(
	processor *ConcurrentOCRProcessor,
	screenshotService *ScreenshotService,
	configService *ConfigService,
	app AppInterface,
) *ScreenshotRoundManager {
	manager := &ScreenshotRoundManager{
		processor:          processor,
		screenshotService:  screenshotService,
		configService:      configService,
		app:                app,
		rounds:             make(map[int]*ScreenshotRound),
		currentRound:       0,
		maxRounds:          10, // 默认10轮
		progressCallbacks:  make([]RoundProgressCallback, 0),
		completedCallbacks: make([]RoundCompletedCallback, 0),
		running:            false,
	}

	// 注册OCR结果回调
	processor.AddResultCallback(manager.handleOCRResult)

	return manager
}

// Start 启动轮次管理器
func (srm *ScreenshotRoundManager) Start() error {
	srm.mu.Lock()
	defer srm.mu.Unlock()

	if srm.running {
		return fmt.Errorf("轮次管理器已在运行中")
	}

	// 启动OCR处理器
	if !srm.processor.IsRunning() {
		if err := srm.processor.Start(); err != nil {
			return fmt.Errorf("启动OCR处理器失败: %v", err)
		}
	}

	srm.running = true
	srm.startTime = time.Now()
	srm.currentRound = 0

	// 清空之前的轮次数据
	srm.rounds = make(map[int]*ScreenshotRound)

	fmt.Printf("[轮次管理] 轮次管理器已启动，最大轮次数: %d\n", srm.maxRounds)
	return nil
}

// Stop 停止轮次管理器
func (srm *ScreenshotRoundManager) Stop() error {
	srm.mu.Lock()
	defer srm.mu.Unlock()

	if !srm.running {
		return fmt.Errorf("轮次管理器未运行")
	}

	srm.running = false

	fmt.Printf("[轮次管理] 轮次管理器已停止\n")
	return nil
}

// StartNewRound 开始新的截图轮次（非阻塞）
func (srm *ScreenshotRoundManager) StartNewRound(userName string) (*ScreenshotRound, error) {
	srm.mu.Lock()
	defer srm.mu.Unlock()

	if !srm.running {
		return nil, fmt.Errorf("轮次管理器未运行")
	}

	// 检查是否已达到最大轮次
	if srm.currentRound >= srm.maxRounds {
		return nil, fmt.Errorf("已达到最大轮次数 %d", srm.maxRounds)
	}

	srm.currentRound++
	roundNumber := srm.currentRound

	// 创建新轮次
	round := &ScreenshotRound{
		RoundNumber: roundNumber,
		UserName:    userName,
		Status:      RoundPending,
		Screenshots: make([]*ScreenshotTask, 0, 2), // 每轮2个截图
		Results:     make([]*OCRTaskResult, 0, 2),
		StartTime:   time.Now(),
		Errors:      make([]string, 0),
	}

	srm.rounds[roundNumber] = round

	fmt.Printf("[轮次管理] 开始第%d轮截图，用户: %s\n", roundNumber, userName)
	return round, nil
}

// TakeScreenshot 执行截图并提交OCR处理（非阻塞）
func (srm *ScreenshotRoundManager) TakeScreenshot(roundNumber int, mode string) error {
	srm.mu.RLock()
	round, exists := srm.rounds[roundNumber]
	running := srm.running
	srm.mu.RUnlock()

	if !running {
		return fmt.Errorf("轮次管理器未运行")
	}

	if !exists {
		return fmt.Errorf("轮次 %d 不存在", roundNumber)
	}

	round.Lock()
	defer round.Unlock()

	// 检查轮次状态
	if round.Status == RoundCompleted || round.Status == RoundFailed {
		return fmt.Errorf("轮次 %d 已结束，状态: %v", roundNumber, round.Status)
	}

	// 检查截图数量（每轮最多2个）
	if len(round.Screenshots) >= 2 {
		return fmt.Errorf("轮次 %d 已达到最大截图数量 2", roundNumber)
	}

	// 更新轮次状态
	if round.Status == RoundPending {
		round.Status = RoundProcessing
	}

	// 执行截图
	screenNumber := len(round.Screenshots) + 1
	imagePath, err := srm.screenshotService.TakeScreenshot(mode, round.UserName)
	if err != nil {
		errorMsg := fmt.Sprintf("截图失败: %v", err)
		round.Errors = append(round.Errors, errorMsg)
		fmt.Printf("[轮次管理] 轮次%d截图%d失败: %v\n", roundNumber, screenNumber, err)
		return err
	}

	// 创建截图任务
	taskID := fmt.Sprintf("R%02d_S%d_%s_%d", roundNumber, screenNumber, mode, time.Now().Unix())
	task := &ScreenshotTask{
		ID:           taskID,
		ImagePath:    imagePath,
		UserName:     round.UserName,
		RoundNumber:  roundNumber,
		ScreenNumber: screenNumber,
		Timestamp:    time.Now(),
		Mode:         mode,
	}

	// 添加到轮次截图列表
	round.Screenshots = append(round.Screenshots, task)

	fmt.Printf("[轮次管理] 轮次%d截图%d已完成，开始OCR处理: %s\n", roundNumber, screenNumber, imagePath)

	// 异步提交OCR处理任务
	go func() {
		if err := srm.processor.SubmitTask(task); err != nil {
			errorMsg := fmt.Sprintf("提交OCR任务失败: %v", err)
			round.Lock()
			round.Errors = append(round.Errors, errorMsg)
			round.Unlock()
			fmt.Printf("[轮次管理] 轮次%d提交OCR任务失败: %v\n", roundNumber, err)
		}
	}()

	return nil
}

// handleOCRResult 处理OCR结果回调
func (srm *ScreenshotRoundManager) handleOCRResult(result *OCRTaskResult) {
	roundNumber := result.Task.RoundNumber

	srm.mu.RLock()
	round, exists := srm.rounds[roundNumber]
	srm.mu.RUnlock()

	if !exists {
		fmt.Printf("[轮次管理] 收到未知轮次的OCR结果: %d\n", roundNumber)
		return
	}

	round.mu.Lock()
	defer round.mu.Unlock()

	// 添加结果到轮次
	round.Results = append(round.Results, result)

	if result.Error != nil {
		errorMsg := fmt.Sprintf("OCR处理失败: %v", result.Error)
		round.Errors = append(round.Errors, errorMsg)
		fmt.Printf("[轮次管理] 轮次%d OCR处理失败: %v\n", roundNumber, result.Error)
	} else {
		fmt.Printf("[轮次管理] 轮次%d OCR处理成功: %s -> %s\n",
			roundNumber, filepath.Base(result.Task.ImagePath), result.Result.OrganName)

		// 显示详细的toast通知
		if srm.app != nil {
			// 构建模式描述
			modeDesc := ""
			switch result.Task.Mode {
			case "B":
				modeDesc = "B02生化分析"
			case "C":
				modeDesc = "C03病理分析"
			default:
				modeDesc = result.Task.Mode + "模式"
			}

			// 构建通知消息
			title := "OCR识别完成"
			message := fmt.Sprintf("进度: %d/10；%s；器官/部位：%s",
				roundNumber, modeDesc, result.Result.OrganName)

			// 显示toast通知
			srm.app.ShowSuccessNotification(title, message, 5000)
			fmt.Printf("[Toast] %s: %s\n", title, message)
		}
	}

	// 检查轮次是否完成
	if len(round.Results) == len(round.Screenshots) {
		// 所有截图都已处理完成
		now := time.Now()
		round.CompletedTime = &now

		// 检查是否有错误
		hasErrors := len(round.Errors) > 0
		for _, result := range round.Results {
			if result.Error != nil {
				hasErrors = true
				break
			}
		}

		if hasErrors {
			round.Status = RoundFailed
			fmt.Printf("[轮次管理] 轮次%d处理完成但有错误\n", roundNumber)
		} else {
			round.Status = RoundCompleted
			fmt.Printf("[轮次管理] 轮次%d处理完成，耗时: %v\n",
				roundNumber, round.CompletedTime.Sub(round.StartTime))
		}

		// 调用完成回调
		srm.notifyRoundCompleted(round)
	} else {
		// 计算进度并调用进度回调
		progress := float64(len(round.Results)) / float64(len(round.Screenshots))
		srm.notifyRoundProgress(round, progress)
	}
}

// notifyRoundProgress 通知轮次进度
func (srm *ScreenshotRoundManager) notifyRoundProgress(round *ScreenshotRound, progress float64) {
	srm.mu.RLock()
	callbacks := make([]RoundProgressCallback, len(srm.progressCallbacks))
	copy(callbacks, srm.progressCallbacks)
	srm.mu.RUnlock()

	for _, callback := range callbacks {
		go func(cb RoundProgressCallback) {
			defer func() {
				if r := recover(); r != nil {
					fmt.Printf("[轮次管理] 进度回调异常: %v\n", r)
				}
			}()
			cb(round, progress)
		}(callback)
	}
}

// notifyRoundCompleted 通知轮次完成
func (srm *ScreenshotRoundManager) notifyRoundCompleted(round *ScreenshotRound) {
	srm.mu.RLock()
	callbacks := make([]RoundCompletedCallback, len(srm.completedCallbacks))
	copy(callbacks, srm.completedCallbacks)
	srm.mu.RUnlock()

	for _, callback := range callbacks {
		go func(cb RoundCompletedCallback) {
			defer func() {
				if r := recover(); r != nil {
					fmt.Printf("[轮次管理] 完成回调异常: %v\n", r)
				}
			}()
			cb(round)
		}(callback)
	}
}

// AddProgressCallback 添加进度回调
func (srm *ScreenshotRoundManager) AddProgressCallback(callback RoundProgressCallback) {
	srm.mu.Lock()
	defer srm.mu.Unlock()
	srm.progressCallbacks = append(srm.progressCallbacks, callback)
}

// AddCompletedCallback 添加完成回调
func (srm *ScreenshotRoundManager) AddCompletedCallback(callback RoundCompletedCallback) {
	srm.mu.Lock()
	defer srm.mu.Unlock()
	srm.completedCallbacks = append(srm.completedCallbacks, callback)
}

// GetRound 获取指定轮次信息
func (srm *ScreenshotRoundManager) GetRound(roundNumber int) (*ScreenshotRound, bool) {
	srm.mu.RLock()
	defer srm.mu.RUnlock()
	round, exists := srm.rounds[roundNumber]
	return round, exists
}

// GetCurrentRound 获取当前轮次编号
func (srm *ScreenshotRoundManager) GetCurrentRound() int {
	srm.mu.RLock()
	defer srm.mu.RUnlock()
	return srm.currentRound
}

// GetAllRounds 获取所有轮次信息
func (srm *ScreenshotRoundManager) GetAllRounds() map[int]*ScreenshotRound {
	srm.mu.RLock()
	defer srm.mu.RUnlock()

	// 创建副本避免并发问题
	rounds := make(map[int]*ScreenshotRound)
	for k, v := range srm.rounds {
		rounds[k] = v
	}
	return rounds
}

// GetOverallProgress 获取整体进度
func (srm *ScreenshotRoundManager) GetOverallProgress() map[string]interface{} {
	srm.mu.RLock()
	defer srm.mu.RUnlock()

	totalRounds := srm.maxRounds
	completedRounds := 0
	totalScreenshots := 0
	completedScreenshots := 0
	totalErrors := 0

	for _, round := range srm.rounds {
		round.RLock()
		if round.Status == RoundCompleted {
			completedRounds++
		}
		totalScreenshots += len(round.Screenshots)
		completedScreenshots += len(round.Results)
		totalErrors += len(round.Errors)
		round.RUnlock()
	}

	return map[string]interface{}{
		"total_rounds":          totalRounds,
		"current_round":         srm.currentRound,
		"completed_rounds":      completedRounds,
		"total_screenshots":     totalScreenshots,
		"completed_screenshots": completedScreenshots,
		"total_errors":          totalErrors,
		"round_progress":        float64(completedRounds) / float64(totalRounds),
		"screenshot_progress": func() float64 {
			if totalScreenshots == 0 {
				return 0
			}
			return float64(completedScreenshots) / float64(totalScreenshots)
		}(),
		"start_time": srm.startTime,
		"running":    srm.running,
	}
}

// IsRunning 检查管理器是否运行中
func (srm *ScreenshotRoundManager) IsRunning() bool {
	srm.mu.RLock()
	defer srm.mu.RUnlock()
	return srm.running
}

// SetMaxRounds 设置最大轮次数
func (srm *ScreenshotRoundManager) SetMaxRounds(maxRounds int) {
	srm.mu.Lock()
	defer srm.mu.Unlock()
	if maxRounds > 0 {
		srm.maxRounds = maxRounds
	}
}

// GetCurrentRoundNumber 获取当前轮次编号（兼容性方法）
func (srm *ScreenshotRoundManager) GetCurrentRoundNumber() int {
	return srm.GetCurrentRound()
}

// GetTotalRounds 获取总轮次数
func (srm *ScreenshotRoundManager) GetTotalRounds() int {
	srm.mu.RLock()
	defer srm.mu.RUnlock()
	return len(srm.rounds)
}

// GetCompletedRoundsCount 获取已完成的轮次数
func (srm *ScreenshotRoundManager) GetCompletedRoundsCount() int {
	srm.mu.RLock()
	defer srm.mu.RUnlock()

	completedCount := 0
	for _, round := range srm.rounds {
		round.RLock()
		if round.Status == RoundCompleted {
			completedCount++
		}
		round.RUnlock()
	}
	return completedCount
}

// WaitForRoundCompletion 等待指定轮次完成
func (srm *ScreenshotRoundManager) WaitForRoundCompletion(roundNumber int, timeout time.Duration) (*ScreenshotRound, error) {
	// 检查轮次是否存在
	srm.mu.RLock()
	round, exists := srm.rounds[roundNumber]
	srm.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("轮次 %d 不存在", roundNumber)
	}

	// 如果已经完成，直接返回
	round.RLock()
	status := round.Status
	round.RUnlock()

	if status == RoundCompleted || status == RoundFailed {
		return round, nil
	}

	// 等待完成
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	timeoutChan := time.After(timeout)

	for {
		select {
		case <-timeoutChan:
			return nil, fmt.Errorf("等待轮次 %d 完成超时", roundNumber)
		case <-ticker.C:
			round.RLock()
			status := round.Status
			round.RUnlock()

			if status == RoundCompleted || status == RoundFailed {
				return round, nil
			}
		}
	}
}
