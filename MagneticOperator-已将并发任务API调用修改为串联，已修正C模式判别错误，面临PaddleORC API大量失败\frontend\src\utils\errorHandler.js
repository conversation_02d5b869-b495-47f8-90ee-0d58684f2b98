/**
 * 统一错误处理服务
 * 提供错误解析、记录、显示和重试功能
 */

class ErrorHandler {
  constructor() {
    this.errorTypes = {
      NETWORK: 'network',
      VALIDATION: 'validation',
      BUSINESS: 'business',
      SYSTEM: 'system',
      PERMISSION: 'permission'
    }
    
    this.errorCodes = {
      // 网络错误
      NETWORK_TIMEOUT: 4000,
      NETWORK_CONNECTION: 4001,
      NETWORK_API: 4002,
      
      // 业务错误
      BUSINESS_VALIDATION: 2000,
      BUSINESS_NOT_FOUND: 2001,
      BUSINESS_CONFLICT: 2002,
      
      // 系统错误
      SYSTEM_INTERNAL: 1000,
      SYSTEM_TIMEOUT: 1001,
      SYSTEM_OVERLOAD: 1002,
      
      // 截图相关错误
      SCREENSHOT_CAPTURE: 3000,
      SCREENSHOT_SAVE: 3001,
      SCREENSHOT_PROCESS: 3002,
      
      // OCR相关错误
      OCR_PROCESS: 3100,
      OCR_TIMEOUT: 3101,
      OCR_INVALID_IMG: 3102
    }
    
    this.retryableErrors = [
      this.errorCodes.NETWORK_TIMEOUT,
      this.errorCodes.NETWORK_CONNECTION,
      this.errorCodes.SYSTEM_TIMEOUT,
      this.errorCodes.OCR_TIMEOUT
    ]
  }

  /**
   * 处理错误
   * @param {Error|Object} error 错误对象
   * @param {string} context 错误上下文
   * @param {Object} options 处理选项
   */
  handleError(error, context = '', options = {}) {
    const errorInfo = this.parseError(error, context)
    
    // 记录错误
    this.logError(errorInfo)
    
    // 显示用户友好的错误信息
    if (options.showToUser !== false) {
      this.showUserError(errorInfo, options)
    }
    
    // 发送错误事件
    this.emitErrorEvent(errorInfo)
    
    return errorInfo
  }

  /**
   * 解析错误
   */
  parseError(error, context) {
    const errorInfo = {
      id: this.generateErrorId(),
      timestamp: Date.now(),
      context,
      type: this.errorTypes.SYSTEM,
      code: this.errorCodes.SYSTEM_INTERNAL,
      message: '未知错误',
      details: '',
      originalError: error,
      isRetryable: false,
      userMessage: '操作失败，请稍后重试'
    }

    if (error) {
      // 处理后端返回的结构化错误
      if (error.code && error.message) {
        errorInfo.code = error.code
        errorInfo.message = error.message
        errorInfo.details = error.details || ''
        errorInfo.type = this.determineErrorType(error.code)
        errorInfo.isRetryable = this.retryableErrors.includes(error.code)
        errorInfo.userMessage = this.getUserMessage(error.code, error.message)
      }
      // 处理网络错误
      else if (error.name === 'NetworkError' || error.message?.includes('fetch')) {
        errorInfo.type = this.errorTypes.NETWORK
        errorInfo.code = this.errorCodes.NETWORK_CONNECTION
        errorInfo.message = '网络连接失败'
        errorInfo.isRetryable = true
        errorInfo.userMessage = '网络连接失败，请检查网络设置后重试'
      }
      // 处理超时错误
      else if (error.name === 'TimeoutError' || error.message?.includes('timeout')) {
        errorInfo.type = this.errorTypes.NETWORK
        errorInfo.code = this.errorCodes.NETWORK_TIMEOUT
        errorInfo.message = '请求超时'
        errorInfo.isRetryable = true
        errorInfo.userMessage = '请求超时，请稍后重试'
      }
      // 处理验证错误
      else if (error.name === 'ValidationError') {
        errorInfo.type = this.errorTypes.VALIDATION
        errorInfo.code = this.errorCodes.BUSINESS_VALIDATION
        errorInfo.message = error.message || '数据验证失败'
        errorInfo.userMessage = `输入数据有误：${error.message}`
      }
      // 处理普通错误
      else {
        errorInfo.message = error.message || error.toString()
        errorInfo.details = error.stack || ''
      }
    }

    return errorInfo
  }

  /**
   * 确定错误类型
   */
  determineErrorType(code) {
    if (code >= 4000 && code < 5000) return this.errorTypes.NETWORK
    if (code >= 2000 && code < 3000) return this.errorTypes.BUSINESS
    if (code >= 1000 && code < 2000) return this.errorTypes.SYSTEM
    return this.errorTypes.SYSTEM
  }

  /**
   * 获取用户友好的错误信息
   */
  getUserMessage(code, message) {
    const userMessages = {
      [this.errorCodes.NETWORK_TIMEOUT]: '网络请求超时，请稍后重试',
      [this.errorCodes.NETWORK_CONNECTION]: '网络连接失败，请检查网络设置',
      [this.errorCodes.BUSINESS_VALIDATION]: '输入的数据格式不正确',
      [this.errorCodes.BUSINESS_NOT_FOUND]: '请求的资源不存在',
      [this.errorCodes.BUSINESS_CONFLICT]: '操作冲突，请刷新后重试',
      [this.errorCodes.SYSTEM_INTERNAL]: '系统内部错误，请联系技术支持',
      [this.errorCodes.SYSTEM_OVERLOAD]: '系统繁忙，请稍后重试',
      [this.errorCodes.SCREENSHOT_CAPTURE]: '截图失败，请重试',
      [this.errorCodes.SCREENSHOT_SAVE]: '截图保存失败',
      [this.errorCodes.SCREENSHOT_PROCESS]: '截图处理失败',
      [this.errorCodes.OCR_PROCESS]: 'OCR识别失败',
      [this.errorCodes.OCR_TIMEOUT]: 'OCR识别超时',
      [this.errorCodes.OCR_INVALID_IMG]: '图片格式不支持'
    }
    
    return userMessages[code] || message || '操作失败，请稍后重试'
  }

  /**
   * 记录错误日志
   */
  logError(errorInfo) {
    const logData = {
      id: errorInfo.id,
      timestamp: errorInfo.timestamp,
      context: errorInfo.context,
      type: errorInfo.type,
      code: errorInfo.code,
      message: errorInfo.message,
      details: errorInfo.details,
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    // 开发环境详细日志
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 Error [${errorInfo.type}:${errorInfo.code}]`)
      console.error('Error Info:', logData)
      if (errorInfo.originalError) {
        console.error('Original Error:', errorInfo.originalError)
      }
      console.groupEnd()
    }
    // 生产环境简化日志
    else {
      console.error(`[${errorInfo.id}] ${errorInfo.type}:${errorInfo.code} - ${errorInfo.message}`)
    }

    // 发送到日志服务（如果需要）
    this.sendToLogService(logData)
  }

  /**
   * 显示用户错误信息
   */
  showUserError(errorInfo, options = {}) {
    const { notificationManager } = window.app || {}
    
    if (notificationManager) {
      const config = {
        title: this.getErrorTitle(errorInfo.type),
        message: errorInfo.userMessage,
        type: 'error',
        duration: options.duration || 5000,
        actions: []
      }

      // 添加重试按钮
      if (errorInfo.isRetryable && options.onRetry) {
        config.actions.push({
          text: '重试',
          action: options.onRetry
        })
      }

      // 添加详情按钮
      if (process.env.NODE_ENV === 'development') {
        config.actions.push({
          text: '详情',
          action: () => this.showErrorDetails(errorInfo)
        })
      }

      notificationManager.showNotification(config)
    } else {
      // 降级到简单的alert
      console.warn('NotificationManager not available, using alert')
      alert(errorInfo.userMessage)
    }
  }

  /**
   * 获取错误标题
   */
  getErrorTitle(type) {
    const titles = {
      [this.errorTypes.NETWORK]: '网络错误',
      [this.errorTypes.VALIDATION]: '输入错误',
      [this.errorTypes.BUSINESS]: '业务错误',
      [this.errorTypes.SYSTEM]: '系统错误',
      [this.errorTypes.PERMISSION]: '权限错误'
    }
    
    return titles[type] || '操作失败'
  }

  /**
   * 显示错误详情
   */
  showErrorDetails(errorInfo) {
    const details = {
      错误ID: errorInfo.id,
      时间: new Date(errorInfo.timestamp).toLocaleString(),
      类型: errorInfo.type,
      代码: errorInfo.code,
      消息: errorInfo.message,
      上下文: errorInfo.context,
      详情: errorInfo.details
    }

    console.table(details)
    
    // 可以显示模态框或其他UI组件
    window.dispatchEvent(new CustomEvent('show-error-details', {
      detail: errorInfo
    }))
  }

  /**
   * 发送错误事件
   */
  emitErrorEvent(errorInfo) {
    window.dispatchEvent(new CustomEvent('app-error', {
      detail: errorInfo
    }))
  }

  /**
   * 发送到日志服务
   */
  async sendToLogService(logData) {
    try {
      // 这里可以发送到远程日志服务
      // await fetch('/api/logs', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(logData)
      // })
    } catch (error) {
      console.warn('发送日志失败:', error)
    }
  }

  /**
   * 生成错误ID
   */
  generateErrorId() {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 重试机制
   */
  async retry(fn, options = {}) {
    const {
      maxAttempts = 3,
      delay = 1000,
      backoff = 1.5,
      onRetry = null
    } = options

    let lastError
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        
        if (attempt === maxAttempts) {
          break
        }

        const errorInfo = this.parseError(error, 'retry')
        if (!errorInfo.isRetryable) {
          break
        }

        if (onRetry) {
          onRetry(attempt, error)
        }

        const waitTime = delay * Math.pow(backoff, attempt - 1)
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }
    
    throw lastError
  }

  /**
   * 安全执行异步函数
   */
  async safeAsync(fn, context = '', options = {}) {
    try {
      return await fn()
    } catch (error) {
      const errorInfo = this.handleError(error, context, options)
      
      if (options.retry && errorInfo.isRetryable) {
        return await this.retry(fn, options.retry)
      }
      
      if (options.throwError !== false) {
        throw errorInfo
      }
      
      return options.defaultValue
    }
  }
}

// 创建全局实例
const errorHandler = new ErrorHandler()

// 全局错误处理
window.addEventListener('error', (event) => {
  errorHandler.handleError(event.error, 'global-error')
})

window.addEventListener('unhandledrejection', (event) => {
  errorHandler.handleError(event.reason, 'unhandled-promise')
})

export default errorHandler