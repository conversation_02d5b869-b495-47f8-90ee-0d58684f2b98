package main

import (
	"bufio"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

// IntegrationScript 统一轮次管理器集成脚本
type IntegrationScript struct {
	projectRoot string
	appGoPath   string
	backupDir   string
}

// NewIntegrationScript 创建新的集成脚本
func NewIntegrationScript(projectRoot string) *IntegrationScript {
	return &IntegrationScript{
		projectRoot: projectRoot,
		appGoPath:   filepath.Join(projectRoot, "app.go"),
		backupDir:   filepath.Join(projectRoot, "backup", fmt.Sprintf("integration_%s", time.Now().Format("20060102_150405"))),
	}
}

// Run 执行集成脚本
func (is *IntegrationScript) Run() error {
	fmt.Println("=== 统一轮次管理器集成脚本 ===")
	fmt.Println("项目根目录:", is.projectRoot)
	fmt.Println("app.go路径:", is.appGoPath)
	fmt.Println("备份目录:", is.backupDir)
	fmt.Println()

	// 步骤1: 创建备份
	if err := is.createBackup(); err != nil {
		return fmt.Errorf("创建备份失败: %v", err)
	}

	// 步骤2: 检查现有代码
	if err := is.analyzeExistingCode(); err != nil {
		return fmt.Errorf("分析现有代码失败: %v", err)
	}

	// 步骤3: 修改App结构体
	if err := is.modifyAppStruct(); err != nil {
		return fmt.Errorf("修改App结构体失败: %v", err)
	}

	// 步骤4: 修改NewApp函数
	if err := is.modifyNewAppFunction(); err != nil {
		return fmt.Errorf("修改NewApp函数失败: %v", err)
	}

	// 步骤5: 修改startup函数
	if err := is.modifyStartupFunction(); err != nil {
		return fmt.Errorf("修改startup函数失败: %v", err)
	}

	// 步骤6: 添加兼容性方法
	if err := is.addCompatibilityMethods(); err != nil {
		return fmt.Errorf("添加兼容性方法失败: %v", err)
	}

	// 步骤7: 验证修改
	if err := is.validateChanges(); err != nil {
		return fmt.Errorf("验证修改失败: %v", err)
	}

	fmt.Println("\n=== 集成完成 ===")
	fmt.Println("请运行以下命令验证集成结果:")
	fmt.Println("go build -o MagneticOperator.exe")
	fmt.Println("如果遇到问题，可以从备份目录恢复:", is.backupDir)

	return nil
}

// createBackup 创建备份
func (is *IntegrationScript) createBackup() error {
	fmt.Println("步骤1: 创建备份...")

	// 创建备份目录
	if err := os.MkdirAll(is.backupDir, 0755); err != nil {
		return err
	}

	// 备份app.go
	appGoContent, err := ioutil.ReadFile(is.appGoPath)
	if err != nil {
		return err
	}

	backupPath := filepath.Join(is.backupDir, "app.go")
	if err := ioutil.WriteFile(backupPath, appGoContent, 0644); err != nil {
		return err
	}

	fmt.Printf("✓ 已备份 app.go 到 %s\n", backupPath)
	return nil
}

// analyzeExistingCode 分析现有代码
func (is *IntegrationScript) analyzeExistingCode() error {
	fmt.Println("\n步骤2: 分析现有代码...")

	content, err := ioutil.ReadFile(is.appGoPath)
	if err != nil {
		return err
	}

	codeStr := string(content)

	// 检查App结构体
	appStructRegex := regexp.MustCompile(`type App struct \{[\s\S]*?\}`)
	if !appStructRegex.MatchString(codeStr) {
		return fmt.Errorf("未找到App结构体定义")
	}
	fmt.Println("✓ 找到App结构体定义")

	// 检查NewApp函数
	newAppRegex := regexp.MustCompile(`func NewApp\(\) \*App`)
	if !newAppRegex.MatchString(codeStr) {
		return fmt.Errorf("未找到NewApp函数定义")
	}
	fmt.Println("✓ 找到NewApp函数定义")

	// 检查startup函数
	startupRegex := regexp.MustCompile(`func \(a \*App\) startup\(ctx context\.Context\)`)
	if !startupRegex.MatchString(codeStr) {
		return fmt.Errorf("未找到startup函数定义")
	}
	fmt.Println("✓ 找到startup函数定义")

	// 检查是否已经集成
	if strings.Contains(codeStr, "unifiedRoundIntegration") {
		fmt.Println("⚠ 检测到已存在统一轮次管理器相关代码，将跳过部分修改")
	}

	return nil
}

// modifyAppStruct 修改App结构体
func (is *IntegrationScript) modifyAppStruct() error {
	fmt.Println("\n步骤3: 修改App结构体...")

	content, err := ioutil.ReadFile(is.appGoPath)
	if err != nil {
		return err
	}

	codeStr := string(content)

	// 如果已经包含unifiedRoundIntegration，跳过
	if strings.Contains(codeStr, "unifiedRoundIntegration") {
		fmt.Println("✓ App结构体已包含统一轮次管理器字段，跳过修改")
		return nil
	}

	// 查找cloudService字段后的位置
	cloudServiceRegex := regexp.MustCompile(`cloudService \*services\.CloudService`)
	if !cloudServiceRegex.MatchString(codeStr) {
		return fmt.Errorf("未找到cloudService字段，无法确定插入位置")
	}

	// 在cloudService后添加统一轮次管理器字段
	newField := `

	// 统一轮次管理器 - 新的统一轮次管理系统
	unifiedRoundIntegration *services.UnifiedRoundIntegration`

	codeStr = cloudServiceRegex.ReplaceAllString(codeStr, `cloudService *services.CloudService`+newField)

	// 注释旧字段
	oldFields := []struct {
		pattern string
		replace string
	}{
		{
			pattern: `(\s+)roundManager map\[string\]\*RoundStatus`,
			replace: `$1// roundManager map[string]*RoundStatus // 已弃用，保留用于兼容性`,
		},
		{
			pattern: `(\s+)currentUserCheckingInfo map\[string\]\*models\.CurrentUserCheckingInfo`,
			replace: `$1// currentUserCheckingInfo map[string]*models.CurrentUserCheckingInfo // 已弃用，保留用于兼容性`,
		},
		{
			pattern: `(\s+)checkingInfoMutex\s+sync\.RWMutex.*`,
			replace: `$1// checkingInfoMutex sync.RWMutex // 已弃用，保留用于兼容性`,
		},
	}

	for _, field := range oldFields {
		regex := regexp.MustCompile(field.pattern)
		if regex.MatchString(codeStr) {
			codeStr = regex.ReplaceAllString(codeStr, field.replace)
		}
	}

	// 写回文件
	if err := ioutil.WriteFile(is.appGoPath, []byte(codeStr), 0644); err != nil {
		return err
	}

	fmt.Println("✓ App结构体修改完成")
	return nil
}

// modifyNewAppFunction 修改NewApp函数
func (is *IntegrationScript) modifyNewAppFunction() error {
	fmt.Println("\n步骤4: 修改NewApp函数...")

	content, err := ioutil.ReadFile(is.appGoPath)
	if err != nil {
		return err
	}

	codeStr := string(content)

	// 如果已经包含统一轮次管理器初始化，跳过
	if strings.Contains(codeStr, "NewUnifiedRoundIntegration") {
		fmt.Println("✓ NewApp函数已包含统一轮次管理器初始化，跳过修改")
		return nil
	}

	// 查找return app语句
	returnRegex := regexp.MustCompile(`(\s+)(return app)`)
	if !returnRegex.MatchString(codeStr) {
		return fmt.Errorf("未找到NewApp函数的return语句")
	}

	// 在return前添加统一轮次管理器初始化
	initCode := `$1// 初始化统一轮次管理器
$1app.unifiedRoundIntegration = services.NewUnifiedRoundIntegration(app)
$1
$2`

	codeStr = returnRegex.ReplaceAllString(codeStr, initCode)

	// 写回文件
	if err := ioutil.WriteFile(is.appGoPath, []byte(codeStr), 0644); err != nil {
		return err
	}

	fmt.Println("✓ NewApp函数修改完成")
	return nil
}

// modifyStartupFunction 修改startup函数
func (is *IntegrationScript) modifyStartupFunction() error {
	fmt.Println("\n步骤5: 修改startup函数...")

	content, err := ioutil.ReadFile(is.appGoPath)
	if err != nil {
		return err
	}

	codeStr := string(content)

	// 如果已经包含统一轮次管理器启动代码，跳过
	if strings.Contains(codeStr, "unifiedRoundIntegration.Start()") {
		fmt.Println("✓ startup函数已包含统一轮次管理器启动代码，跳过修改")
		return nil
	}

	// 查找startup函数的结束位置（最后一个}前）
	startupRegex := regexp.MustCompile(`func \(a \*App\) startup\(ctx context\.Context\) \{[\s\S]*?\n(\s*)(\})(?:\s*\n\s*func|\s*$)`)
	matches := startupRegex.FindStringSubmatch(codeStr)
	if len(matches) < 3 {
		return fmt.Errorf("无法找到startup函数的结束位置")
	}

	// 添加统一轮次管理器启动代码
	startupCode := `
	// 启动统一轮次管理器
	if a.unifiedRoundIntegration != nil {
		if err := a.unifiedRoundIntegration.Start(); err != nil {
			utils.LogError("启动统一轮次管理器失败", zap.Error(err))
		} else {
			utils.LogInfo("统一轮次管理器启动成功")
			
			// 从旧系统迁移数据
			if err := a.unifiedRoundIntegration.MigrateFromLegacyRoundManager(); err != nil {
				utils.LogWarn("迁移轮次数据失败", zap.Error(err))
			} else {
				utils.LogInfo("轮次数据迁移成功")
			}
		}
	}`

	replacement := startupCode + "\n" + matches[1] + matches[2]
	codeStr = startupRegex.ReplaceAllString(codeStr, matches[0][:len(matches[0])-len(matches[1]+matches[2])]+replacement)

	// 写回文件
	if err := ioutil.WriteFile(is.appGoPath, []byte(codeStr), 0644); err != nil {
		return err
	}

	fmt.Println("✓ startup函数修改完成")
	return nil
}

// addCompatibilityMethods 添加兼容性方法
func (is *IntegrationScript) addCompatibilityMethods() error {
	fmt.Println("\n步骤6: 添加兼容性方法...")

	content, err := ioutil.ReadFile(is.appGoPath)
	if err != nil {
		return err
	}

	codeStr := string(content)

	// 如果已经包含兼容性方法，跳过
	if strings.Contains(codeStr, "markModeCompletedUnified") {
		fmt.Println("✓ 兼容性方法已存在，跳过添加")
		return nil
	}

	// 在文件末尾添加兼容性方法
	compatibilityMethods := `

// === 统一轮次管理器兼容性方法 ===

// markModeCompletedUnified 兼容性方法 - 使用统一轮次管理器
func (a *App) markModeCompletedUnified(userName, mode string) (int, bool, int) {
	if a.unifiedRoundIntegration != nil {
		return a.unifiedRoundIntegration.GetLegacyCompatibleMethods().MarkModeCompleted(userName, mode)
	}
	// 回退到旧方法
	return a.markModeCompleted(userName, mode)
}

// getCurrentRoundUnified 兼容性方法 - 使用统一轮次管理器
func (a *App) getCurrentRoundUnified(userName string) int {
	if a.unifiedRoundIntegration != nil {
		return a.unifiedRoundIntegration.GetLegacyCompatibleMethods().GetCurrentRound(userName)
	}
	// 回退到旧方法
	return a.getCurrentRound(userName)
}

// GetCurrentRoundNumberUnified 兼容性方法 - 使用统一轮次管理器
func (a *App) GetCurrentRoundNumberUnified(userName string) int {
	if a.unifiedRoundIntegration != nil {
		return a.unifiedRoundIntegration.GetLegacyCompatibleMethods().GetCurrentRoundNumber(userName)
	}
	// 回退到旧方法
	return a.GetCurrentRoundNumber(userName)
}

// generateUserKey 生成用户键
func (a *App) generateUserKey(userName string) string {
	return fmt.Sprintf("%s_%s", userName, time.Now().Format("2006-01-02"))
}

// GetUnifiedRoundManager 获取统一轮次管理器
func (a *App) GetUnifiedRoundManager() *services.UnifiedRoundManager {
	if a.unifiedRoundIntegration != nil {
		return a.unifiedRoundIntegration.GetManager()
	}
	return nil
}

// GetUnifiedRoundStatus 获取统一轮次状态
func (a *App) GetUnifiedRoundStatus(userName string) *services.UnifiedRoundStatus {
	if manager := a.GetUnifiedRoundManager(); manager != nil {
		return manager.GetRoundStatus(userName)
	}
	return nil
}

// IsUnifiedRoundManagerEnabled 检查统一轮次管理器是否启用
func (a *App) IsUnifiedRoundManagerEnabled() bool {
	return a.unifiedRoundIntegration != nil && a.unifiedRoundIntegration.GetManager().IsRunning()
}
`

	codeStr += compatibilityMethods

	// 写回文件
	if err := ioutil.WriteFile(is.appGoPath, []byte(codeStr), 0644); err != nil {
		return err
	}

	fmt.Println("✓ 兼容性方法添加完成")
	return nil
}

// validateChanges 验证修改
func (is *IntegrationScript) validateChanges() error {
	fmt.Println("\n步骤7: 验证修改...")

	content, err := ioutil.ReadFile(is.appGoPath)
	if err != nil {
		return err
	}

	codeStr := string(content)

	// 验证关键修改
	checks := []struct {
		name    string
		pattern string
	}{
		{"统一轮次管理器字段", `unifiedRoundIntegration \*services\.UnifiedRoundIntegration`},
		{"统一轮次管理器初始化", `NewUnifiedRoundIntegration`},
		{"统一轮次管理器启动", `unifiedRoundIntegration\.Start\(\)`},
		{"兼容性方法", `markModeCompletedUnified`},
	}

	for _, check := range checks {
		matched, _ := regexp.MatchString(check.pattern, codeStr)
		if matched {
			fmt.Printf("✓ %s: 已添加\n", check.name)
		} else {
			fmt.Printf("✗ %s: 未找到\n", check.name)
		}
	}

	fmt.Println("\n验证完成")
	return nil
}

// promptUser 提示用户确认
func promptUser(message string) bool {
	fmt.Printf("%s (y/N): ", message)
	reader := bufio.NewReader(os.Stdin)
	response, _ := reader.ReadString('\n')
	response = strings.TrimSpace(strings.ToLower(response))
	return response == "y" || response == "yes"
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run integrate_unified_round_manager.go <项目根目录>")
		fmt.Println("示例: go run integrate_unified_round_manager.go f:\\myHbuilderAPP\\MagneticOperator")
		os.Exit(1)
	}

	projectRoot := os.Args[1]

	// 检查项目根目录
	if _, err := os.Stat(filepath.Join(projectRoot, "app.go")); os.IsNotExist(err) {
		fmt.Printf("错误: 在指定目录 %s 中未找到 app.go 文件\n", projectRoot)
		os.Exit(1)
	}

	// 提示用户确认
	if !promptUser("即将开始统一轮次管理器集成，是否继续?") {
		fmt.Println("操作已取消")
		os.Exit(0)
	}

	// 创建并运行集成脚本
	script := NewIntegrationScript(projectRoot)
	if err := script.Run(); err != nil {
		fmt.Printf("集成失败: %v\n", err)
		os.Exit(1)
	}
}