package services

import (
	"fmt"
	"log"
	"math"
	"sort"
	"strings"
	"time"

	json "github.com/goccy/go-json"

	"MagneticOperator/app/models"
	"MagneticOperator/app/utils"

	"github.com/valyala/fasthttp"
)

// 网络重试配置常量（API服务）
const (
	APIMaxRetries    = 3                // 最大重试次数
	APIBaseDelay     = 1 * time.Second  // 基础延迟时间
	APIMaxDelay      = 30 * time.Second // 最大延迟时间
	APIBackoffFactor = 2.0              // 指数退避因子
)

// APIRetryableHTTPClient 带重试机制的HTTP客户端（API服务专用）
type APIRetryableHTTPClient struct {
	client *utils.FastHTTPClient
}

// NewAPIRetryableHTTPClient 创建带重试机制的HTTP客户端（API服务专用）
func NewAPIRetryableHTTPClient(timeout time.Duration) *APIRetryableHTTPClient {
	return &APIRetryableHTTPClient{
		client: utils.NewFastHTTPClient(timeout),
	}
}

// DoWithRetry 执行HTTP请求并在失败时重试（API服务专用）
func (r *APIRetryableHTTPClient) DoWithRetry(method, url string, body []byte, headers map[string]string) (*fasthttp.Response, error) {
	var lastErr error

	for attempt := 0; attempt <= APIMaxRetries; attempt++ {
		// 如果不是第一次尝试，等待一段时间
		if attempt > 0 {
			delay := time.Duration(math.Pow(APIBackoffFactor, float64(attempt-1))) * APIBaseDelay
			if delay > APIMaxDelay {
				delay = APIMaxDelay
			}
			log.Printf("[API网络重试] 第%d次重试，等待%v...\n", attempt, delay)
			time.Sleep(delay)
		}

		resp, err := r.client.DoWithRetry(method, url, body, headers)
		if err == nil {
			// 检查HTTP状态码
			if resp.StatusCode() >= 200 && resp.StatusCode() < 300 {
				return resp, nil
			}
			// 如果是客户端错误（4xx），不重试
			if resp.StatusCode() >= 400 && resp.StatusCode() < 500 {
				return resp, fmt.Errorf("客户端错误，状态码: %d", resp.StatusCode())
			}
			// 服务器错误（5xx）可以重试
			fasthttp.ReleaseResponse(resp)
			lastErr = fmt.Errorf("服务器错误，状态码: %d", resp.StatusCode())
		} else {
			lastErr = err
		}

		log.Printf("[API网络重试] 第%d次尝试失败: %v\n", attempt+1, lastErr)
	}

	return nil, fmt.Errorf("API网络请求失败，已重试%d次: %w", APIMaxRetries, lastErr)
}

// CozeUserInput holds the user-specific data required for the Coze workflow.
type CozeUserInput struct {
	UserID         string
	Gender         int
	BirthDate      string // Expected format: "YYYY-MM-DD"
	RegistrationID string
}

// calculateAge calculates age from a birth date string in "YYYY-MM-DD" format.
func calculateAge(birthDateStr string) (int, error) {
	if birthDateStr == "" {
		return 0, fmt.Errorf("birth date is empty")
	}
	birthDate, err := time.Parse("2006-01-02", birthDateStr)
	if err != nil {
		return 0, fmt.Errorf("invalid birth date format '%s': %w", birthDateStr, err)
	}
	now := time.Now()
	age := now.Year() - birthDate.Year()
	if now.Month() < birthDate.Month() || (now.Month() == birthDate.Month() && now.Day() < birthDate.Day()) {
		age--
	}
	if age < 0 {
		log.Printf("Warning: Calculated negative age for birthdate %s. Setting age to 0.", birthDateStr)
		age = 0
	}
	return age, nil
}

// APIService API服务
type APIService struct {
	configService *ConfigService
}

// NewAPIService 创建新的API服务
func NewAPIService(configService *ConfigService) *APIService {
	return &APIService{
		configService: configService,
	}
}

// UploadImageToDCloud 方法已移除，不再支持云存储上传功能

// CallCozeWorkflow 调用扣子工作流
func (as *APIService) CallCozeWorkflow(picURL, picName, reportID string, userInput CozeUserInput) error {
	fmt.Printf("[DEBUG] 开始调用扣子工作流 - 图片URL: %s, 图片名称: %s, 报告ID: %s, UserID: %s, Gender: %d, BirthDate: %s, RegistrationID: %s\n", picURL, picName, reportID, userInput.UserID, userInput.Gender, userInput.BirthDate, userInput.RegistrationID)

	config := as.configService.GetConfig()
	if config == nil {
		fmt.Printf("[ERROR] 配置未加载\n")
		return fmt.Errorf("配置未加载")
	}
	fmt.Printf("[DEBUG] 配置加载成功 - SpaceID: %s, WorkflowID: %s, AppID: %s\n", config.APIKeys.Coze.SpaceID, config.APIKeys.Coze.WorkflowIDPostPic, config.APIKeys.Coze.AppID)

	userAge, ageErr := calculateAge(userInput.BirthDate)
	if ageErr != nil {
		log.Printf("警告: 无法计算用户 %s (生日: %s) 的年龄: %v. 年龄将设置为0.", userInput.UserID, userInput.BirthDate, ageErr)
		userAge = 0 // Default age to 0 or handle as per business logic if error occurs
	}

	requestBody := map[string]interface{}{
		"space_id":          config.APIKeys.Coze.SpaceID,
		"workflow_id":       config.APIKeys.Coze.WorkflowIDPostPic,
		"app_id":            config.APIKeys.Coze.AppID,
		"is_async":          false,
		"stream":            false,
		"auto_save_history": true,
		"parameters": map[string]interface{}{
			"input_pic_url": picURL,
			// "pic_name":        filepath.Base(picName),
			"report_id":       reportID,
			"check_time":      time.Now().Format("2006-01-02 15:04:05"),
			"site_id":         config.SiteInfo.SiteID,
			"site_name":       config.SiteInfo.SiteName,
			"user_id":         userInput.UserID,
			"user_gender":     userInput.Gender,
			"user_age":        userAge,
			"registration_id": userInput.RegistrationID,
		},
	}

	log.Printf("请求体 parameters: %v", requestBody["parameters"])
	fmt.Printf("[DEBUG] 构建请求体完成，准备调用API\n")

	response, err := as.callCozeAPIWithResponse(requestBody, config.APIKeys.Coze.Token)
	if err != nil {
		fmt.Printf("[ERROR] 扣子API调用失败: %v\n", err)
		return err
	}

	fmt.Printf("[DEBUG] 扣子API调用成功，响应: %+v\n", response)
	return nil
}

// CallCozeRegistrationWorkflow 调用扣子挂号工作流
// func (as *APIService) CallCozeRegistrationWorkflow(fullNumber, shortNumber string) error {
// 	config := as.configService.GetConfig()
// 	if config == nil {
// 		return fmt.Errorf("配置未加载")
// 	}

// 	requestBody := map[string]interface{}{
// 		"space_id":          config.APIKeys.Coze.SpaceID,
// 		"workflow_id":       config.APIKeys.Coze.WorkflowIDPostRegistration,
// 		"app_id":            config.APIKeys.Coze.AppID,
// 		"is_async":          false,
// 		"stream":            false,
// 		"auto_save_history": true,
// 		"parameters": map[string]interface{}{
// 			"full_registration_number":  fullNumber,
// 			"short_registration_number": shortNumber,
// 			"site_id":                   config.SiteInfo.SiteID,
// 			"site_name":                 config.SiteInfo.SiteName,
// 			"register_time":             time.Now().Format("2006-01-02 15:04:05"),
// 		},
// 	}

// 	_, err := as.callCozeAPIWithResponse(requestBody, config.APIKeys.Coze.Token)
// 	return err
// }

// callCozeAPIWithResponse 调用扣子API并返回响应
func (as *APIService) callCozeAPIWithResponse(requestBody map[string]interface{}, token string) (map[string]interface{}, error) {
	fmt.Printf("[DEBUG] 开始调用扣子API\n")

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Printf("[ERROR] JSON编码失败: %v\n", err)
		return nil, fmt.Errorf("JSON编码失败: %v", err)
	}
	fmt.Printf("[DEBUG] JSON编码成功，请求体大小: %d bytes\n", len(jsonBody))

	// 准备请求头
	headers := map[string]string{
		"Authorization": "Bearer " + token,
		"Content-Type":  "application/json",
	}
	fmt.Printf("[DEBUG] 请求头设置完成，开始发送请求到: https://api.coze.cn/v1/workflow/run\n")

	// 发送请求（使用重试机制）
	client := NewAPIRetryableHTTPClient(30 * time.Second)
	resp, err := client.DoWithRetry("POST", "https://api.coze.cn/v1/workflow/run", jsonBody, headers)
	if err != nil {
		fmt.Printf("[ERROR] HTTP请求失败: %v\n", err)
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer fasthttp.ReleaseResponse(resp)
	fmt.Printf("[DEBUG] HTTP请求发送成功，状态码: %d\n", resp.StatusCode())

	// 处理响应
	respBody := resp.Body()
	fmt.Printf("[DEBUG] 响应体读取成功，大小: %d bytes\n", len(respBody))
	fmt.Printf("[DEBUG] 响应内容: %s\n", string(respBody))

	// 尝试解析为JSON对象
	var jsonObj map[string]interface{}
	if err := json.Unmarshal(respBody, &jsonObj); err != nil {
		// 如果解析失败，尝试作为字符串处理
		fmt.Printf("[ERROR] JSON解析失败: %v\n", err)
		return nil, fmt.Errorf("API响应格式错误: %s", string(respBody))
	}
	fmt.Printf("[DEBUG] JSON解析成功\n")

	// 检查响应状态码
	if resp.StatusCode() >= 400 {
		if msg, ok := jsonObj["msg"].(string); ok {
			fmt.Printf("[ERROR] API返回错误: %s\n", msg)
			return nil, fmt.Errorf("API调用失败: %s", msg)
		}
		fmt.Printf("[ERROR] API调用失败，状态码: %d\n", resp.StatusCode())
		return nil, fmt.Errorf("API调用失败: 状态码 %d", resp.StatusCode())
	}

	fmt.Printf("[DEBUG] API调用成功完成\n")
	return jsonObj, nil
}

// GetRegistrations 获取候检者列表
func (as *APIService) GetRegistrations(date string) ([]models.Registration, error) {
	config := as.configService.GetConfig()
	if config == nil {
		return nil, fmt.Errorf("配置未加载")
	}

	// 使用传入的日期参数
	today := date
	log.Printf("Today's date: %s", today)

	// 云函数 getRegistrationsBySiteAndDevic 接口文档：
	// * 获取指定站点和设备的所有报到记录
	// * @param {Object} params - 参数对象
	// * @param {string} params.site_id - 站点ID
	// * @param {string} params.device_no - 设备MAC地址
	// * @param {string} params.date - 日期，格式为 YYYY-MM-DD字符串
	// * @param {number} params.page - 页码，默认为1
	// * @param {number} params.pageSize - 每页数量，默认为20
	// * @returns {Promise<Object>} 包含报到号和操作结果的对象
	// */
	//  async getRegistrationsBySiteAndDevice(params = {}) {
	// 	 const { site_id, device_no, page = 1, pageSize = 20, date } = params;
	// 	 const offset = (page - 1) * pageSize;

	// 构建请求参数
	// 使用原始MAC地址格式（保留冒号），因为云数据库存储的是带冒号的格式
	deviceNo := config.DeviceInfo.MACAddress
	requestBody := map[string]interface{}{
		"site_id":   config.SiteInfo.SiteID,
		"device_no": deviceNo,
		"page":      1,
		"pageSize":  20,
		"date":      today,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 调试打印：输出请求JSON数据
	log.Printf("Request JSON data: %s", string(jsonData))
	log.Printf("-------------------------")

	// 准备请求头
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	// 注意：如果需要token验证，需要添加以下头部
	// headers["uni-id-token"] = "your_token_here"

	// 发送请求（使用重试机制）
	client := NewAPIRetryableHTTPClient(30 * time.Second)
	resp, err := client.DoWithRetry("POST", config.APIKeys.CloudFunction.RegistrationsURL, jsonData, headers)

	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer fasthttp.ReleaseResponse(resp)
	// 读取响应
	respBody := resp.Body()
	log.Printf("-------------------------")
	log.Printf("读取响应respBody: %s", respBody)

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API调用失败，状态码: %d, 响应: %s", resp.StatusCode(), string(respBody))
	}

	// 解析响应 - 根据新的API格式
	var result struct {
		ErrCode string                `json:"errCode"`
		ErrMsg  string                `json:"errMsg"`
		Data    []models.Registration `json:"data"`
		Total   int                   `json:"total"`
	}

	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if result.ErrCode != "0" {
		return nil, fmt.Errorf("云函数返回错误: %s", result.ErrMsg)
	}

	// 处理数据，为前端显示做准备
	for i := range result.Data {
		// 提取用户姓名
		if len(result.Data[i].UserInfo) > 0 {
			result.Data[i].Name = result.Data[i].UserInfo[0].Name
		}
		// 设置报到号别名
		result.Data[i].Number = result.Data[i].RegistrationNumber
		// 格式化时间戳为可读时间
		if result.Data[i].RegistrationTime > 0 {
			// 将毫秒时间戳转换为秒时间戳
			t := time.Unix(result.Data[i].RegistrationTime/1000, 0)
			result.Data[i].RegisterTime = t.Format("2006-01-02 15:04:05")
		}
	}

	// 对相同user_id的记录进行去重，只保留最新的一次报到记录
	userLatestMap := make(map[string]models.Registration)
	for _, registration := range result.Data {
		userID := registration.UserID
		if existing, exists := userLatestMap[userID]; !exists || registration.RegistrationTime > existing.RegistrationTime {
			userLatestMap[userID] = registration
		}
	}

	// 将去重后的数据转换为切片
	var uniqueRegistrations []models.Registration
	for _, registration := range userLatestMap {
		uniqueRegistrations = append(uniqueRegistrations, registration)
	}

	// 按报到时间降序排序，最新的在前面
	sort.Slice(uniqueRegistrations, func(i, j int) bool {
		return uniqueRegistrations[i].RegistrationTime > uniqueRegistrations[j].RegistrationTime
	})

	return uniqueRegistrations, nil
}

// CreateOrUpdateScreenshotRecord 创建或更新截图记录到云数据库
func (as *APIService) CreateOrUpdateScreenshotRecord(params models.ScreenshotRecordParams) error {
	config := as.configService.GetConfig()
	if config == nil {
		return fmt.Errorf("配置未加载")
	}

	// 构建请求参数
	requestBody := map[string]interface{}{
		"user_name":       params.UserName,
		"user_id":         params.UserID,
		"site_id":         config.SiteInfo.SiteID,
		"device_no":       strings.ReplaceAll(config.DeviceInfo.MACAddress, ":", ""),
		"registration_id": params.RegistrationID,
		"analysis_type":   params.AnalysisType,
		"detected_organ":  params.DetectedOrgan,
		"filename":        params.Filename,
		"cloud_url":       params.CloudURL,
		"operator_name":   params.OperatorName,
		"ocr_success":     params.OCRSuccess,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %v", err)
	}

	log.Printf("Screenshot Record Request JSON: %s", string(jsonData))

	// 创建HTTP请求 - 使用配置文件中的URL
	screenshotRecordsURL := config.APIKeys.CloudFunction.ScreenshotRecordsURL
	if screenshotRecordsURL == "" {
		return fmt.Errorf("截图记录URL未配置")
	}

	// 设置请求头
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	// DCloud认证已移除，现在使用云函数直接调用

	// 发送请求（使用重试机制）
	client := NewAPIRetryableHTTPClient(30 * time.Second)
	resp, err := client.DoWithRetry("POST", screenshotRecordsURL, jsonData, headers)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer fasthttp.ReleaseResponse(resp)

	// 读取响应
	respBody := resp.Body()

	log.Printf("Screenshot Record Response Status: %d", resp.StatusCode())
	log.Printf("Screenshot Record Response Body: %s", string(respBody))

	// 解析响应
	var result struct {
		ErrCode string      `json:"errCode"`
		ErrMsg  string      `json:"errMsg"`
		Data    interface{} `json:"data"`
	}

	if err := json.Unmarshal(respBody, &result); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	if result.ErrCode != "0" {
		return fmt.Errorf("云函数返回错误: %s", result.ErrMsg)
	}

	log.Printf("截图记录更新成功: %s", params.UserName)
	return nil
}

// GetSiteInfoByDeviceMAC 根据设备MAC地址获取站点信息
func (as *APIService) GetSiteInfoByDeviceMAC(deviceMAC string) (map[string]interface{}, error) {
	config := as.configService.GetConfig()
	if config == nil {
		return nil, fmt.Errorf("配置未加载")
	}

	log.Printf("Getting site info for device MAC: %s", deviceMAC)

	// 云函数 getSiteInfoByDeviceMAC 接口文档：
	// * 根据设备MAC地址获取站点信息
	// * @param {Object} params - 参数对象
	// * @param {string} params.device_mac - 设备MAC地址
	// * @returns {Promise<Object>} 包含站点信息的对象

	// 构建请求参数
	requestBody := map[string]interface{}{
		"mac_address": deviceMAC,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 调试打印：输出请求JSON数据
	log.Printf("[getSiteInfoByDeviceMAC] HTTP请求参数: %s", string(jsonData))
	log.Printf("-------------------------")

	// 创建HTTP请求
	// 准备请求头
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	// 注意：如果需要token验证，需要添加以下头部
	// headers["uni-id-token"] = "your_token_here"

	// 发送请求（使用重试机制）
	client := NewAPIRetryableHTTPClient(30 * time.Second)
	resp, err := client.DoWithRetry("POST", config.APIKeys.CloudFunction.SiteInfoByDeviceMACURL, jsonData, headers)

	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer fasthttp.ReleaseResponse(resp)

	// 读取响应
	respBody := resp.Body()
	log.Printf("从[getSiteInfoByDeviceMAC] 响应respBody: %s", respBody)
	log.Printf("-------------------------")

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("[getSiteInfoByDeviceMAC] API调用失败，状态码: %d, 响应: %s", resp.StatusCode(), string(respBody))
	}

	// 解析响应 - 根据云函数API格式
	var result struct {
		ErrMsg   string                 `json:"errMsg"`
		ErrCode  string                 `json:"errCode"`
		SiteInfo map[string]interface{} `json:"site_info"`
	}

	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查错误码
	if result.ErrCode != "0" {
		return nil, fmt.Errorf("云函数返回错误: %s (errCode: %s)", result.ErrMsg, result.ErrCode)
	}

	// 返回站点信息
	if result.SiteInfo != nil {
		return result.SiteInfo, nil
	}

	return nil, fmt.Errorf("云函数未返回有效的站点信息")
}

// MarkPatientCompleted 标记患者完成10轮检测
func (as *APIService) MarkPatientCompleted(userID string, registrationNumber string) error {
	config := as.configService.GetConfig()
	if config == nil {
		return fmt.Errorf("配置未加载")
	}

	// 构建请求参数
	requestBody := map[string]interface{}{
		"user_id":             userID,
		"registration_number": registrationNumber,
		"site_id":             config.SiteInfo.SiteID,
		"device_no":           strings.ReplaceAll(config.DeviceInfo.MACAddress, ":", ""),
		"is_completed":        true,
		"completion_time":     time.Now().Format("2006-01-02 15:04:05"),
	}

	// 转换为JSON
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("序列化请求参数失败: %v", err)
	}

	// 准备请求头
	headers := map[string]string{
		"Content-Type": "application/json",
	}

	// 发送HTTP请求
	client := utils.NewFastHTTPClient(30 * time.Second)
	resp, err := client.Post(config.APIKeys.CloudFunction.MarkPatientCompletedURL, jsonData, headers)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer fasthttp.ReleaseResponse(resp)

	// 读取响应
	body := resp.Body()

	// 解析响应
	var result struct {
		ErrCode string `json:"errCode"`
		ErrMsg  string `json:"errMsg"`
	}

	if err := json.Unmarshal(body, &result); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查错误码
	if result.ErrCode != "0" {
		return fmt.Errorf("标记患者完成状态失败: %s (errCode: %s)", result.ErrMsg, result.ErrCode)
	}

	return nil
}

// 用GetRegistrations 的 云函数getRegistrationsBySiteAndDevice替代
// GetCompletedPatients 获取指定日期（今日）已完成检测的患者列表
func (as *APIService) GetCompletedPatients() ([]models.Registration, error) {
	// 获取今天的日期
	today := time.Now().Format("2006-01-02")
	return as.GetCompletedPatientsByDate(today)
}

// GetCompletedPatientsByDate 获取指定日期已完成检测的患者列表
func (as *APIService) GetCompletedPatientsByDate(date string) ([]models.Registration, error) {
	// 获取所有报到记录
	allRegistrations, err := as.GetRegistrations(date)
	if err != nil {
		return nil, err
	}

	// 筛选已完成检测的患者（health_check_completed = true）
	var completedPatients []models.Registration
	for _, reg := range allRegistrations {
		if reg.HealthCheckCompleted {
			completedPatients = append(completedPatients, reg)
		}
	}

	return completedPatients, nil
}

// GetPendingPatients 获取指定日期的候检者列表（已报到未检测）
func (as *APIService) GetPendingPatients(date string) ([]models.Registration, error) {
	// 获取所有报到记录
	allRegistrations, err := as.GetRegistrations(date)
	if err != nil {
		return nil, err
	}

	// 筛选候检者（user_confirmed = true 且 health_check_completed = false）
	var pendingPatients []models.Registration
	for _, reg := range allRegistrations {
		if reg.UserConfirmed && !reg.HealthCheckCompleted {
			pendingPatients = append(pendingPatients, reg)
		}
	}

	return pendingPatients, nil
}

// GetUnanalyzedPatients 获取指定日期已完成检测但未分析结果的患者列表
func (as *APIService) GetUnanalyzedPatients(date string) ([]models.Registration, error) {
	// 获取所有报到记录
	allRegistrations, err := as.GetRegistrations(date)
	if err != nil {
		return nil, err
	}

	// 筛选已检测但未分析的患者（health_check_completed = true 且 health_check_result_analyzed = false）
	var unanalyzedPatients []models.Registration
	for _, reg := range allRegistrations {
		if reg.HealthCheckCompleted && !reg.HealthCheckResultAnalyzed {
			unanalyzedPatients = append(unanalyzedPatients, reg)
		}
	}

	return unanalyzedPatients, nil
}
