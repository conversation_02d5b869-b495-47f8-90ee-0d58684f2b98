<template>
  <div class="site-info-panel">
    <div class="panel-header">
      <h3>网点信息</h3>
      <button @click="toggleEdit" class="edit-btn">
        {{ isEditing ? '保存' : '编辑' }}
      </button>
    </div>
    
    <div class="panel-content">
      <div class="form-group">
        <label>网点名称:</label>
        <input 
          v-if="isEditing" 
          v-model="editForm.SiteInfo.SiteName" 
          type="text" 
          placeholder="请输入网点名称"
        />
        <span v-else>{{ config?.SiteInfo?.SiteName || '未设置' }}</span>
      </div>
      
      <div class="form-group">
        <label>网点ID:</label>
        <input 
          v-if="isEditing" 
          v-model="editForm.SiteInfo.SiteID" 
          type="text" 
          placeholder="请输入网点ID"
        />
        <span v-else>{{ config?.SiteInfo?.SiteID || '未设置' }}</span>
      </div>
      
      <div class="form-group">
        <label>联系电话:</label>
        <input 
          v-if="isEditing" 
          v-model="editForm.SiteInfo.Phone" 
          type="text" 
          placeholder="请输入联系电话"
        />
        <span v-else>{{ config?.SiteInfo?.Phone || '未设置' }}</span>
      </div>
      
      <div class="form-group">
        <label>地址:</label>
        <textarea 
          v-if="isEditing" 
          v-model="editForm.SiteInfo.Address" 
          placeholder="请输入地址"
          rows="2"
        ></textarea>
        <span v-else class="address">{{ config?.SiteInfo?.Address || '未设置' }}</span>
      </div>
      
      <div class="form-group">
        <label>MAC地址:</label>
        <span class="mac-address">{{ config?.DeviceInfo?.MacAddress || '获取中...' }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SiteInfoPanel',
  props: {
    config: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      isEditing: false,
      editForm: {
        SiteInfo: {
          SiteName: '',
          SiteID: '',
          Phone: '',
          Address: ''
        }
      }
    }
  },
  watch: {
    config: {
      handler(newConfig) {
        if (newConfig && newConfig.SiteInfo) {
          this.editForm.SiteInfo = { ...newConfig.SiteInfo }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    toggleEdit() {
      if (this.isEditing) {
        // 保存
        this.saveChanges()
      } else {
        // 开始编辑
        if (this.config && this.config.SiteInfo) {
          this.editForm.SiteInfo = { ...this.config.SiteInfo }
        }
      }
      this.isEditing = !this.isEditing
    },
    
    saveChanges() {
      // 验证必填字段
      if (!this.editForm.SiteInfo.SiteName.trim()) {
        this.showNotification('输入错误', '请输入网点名称')
        return
      }
      
      if (!this.editForm.SiteInfo.SiteID.trim()) {
        this.showNotification('输入错误', '请输入网点ID')
        return
      }
      
      // 发送更新事件
      this.$emit('update-site', this.editForm)
    },
    
    showNotification(title, message) {
      // 调用后端的置顶信息窗口通知
      if (window.go && window.go.main && window.go.main.App) {
        window.go.main.App.ShowWailsNotification('error', title, message, 5000)
      } else {
        // 备用方案：控制台输出
        console.error(`${title}: ${message}`)
      }
    }
  }
}
</script>

<style scoped>
.site-info-panel {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  width: 100%;
  box-sizing: border-box;
}

.panel-header {
  background: #f8f9fa;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.edit-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.edit-btn:hover {
  background: #2980b9;
}

.panel-content {
  padding: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
  max-width: 100%;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group span {
  display: block;
  padding: 8px 0;
  color: #333;
  font-size: 14px;
  min-height: 20px;
}

.address {
  line-height: 1.4;
  word-break: break-all;
}

.mac-address {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  color: #666;
}

.form-group textarea {
  resize: vertical;
  min-height: 60px;
}
</style>