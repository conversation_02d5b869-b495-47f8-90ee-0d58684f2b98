package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"MagneticOperator/app/utils"

	"go.uber.org/zap"
)

// SimpleScreenshotTask 简化的截图任务
type SimpleScreenshotTask struct {
	ID        string    `json:"id"`         // 任务唯一标识
	ImagePath string    `json:"image_path"` // 图片路径
	UserName  string    `json:"user_name"`  // 用户名
	Mode      string    `json:"mode"`       // 截图模式（B或C）
	Timestamp time.Time `json:"timestamp"`  // 创建时间
	Status    string    `json:"status"`     // 任务状态：pending/processing/completed/failed
	OrganName string    `json:"organ_name"` // OCR识别的器官名称
	Error     error     `json:"error"`      // 错误信息
}

// OCRProcessorInterface 通用OCR处理器接口
type OCRProcessorInterface interface {
	Start() error
	Stop() error
	SubmitTask(ctx context.Context, task *ScreenshotTask) error
	AddResultCallback(callback ResultCallback)
	IsRunning() bool
	GetStats() *ProcessorStats
	// 新增方法用于优雅关闭和任务状态查询
	HasPendingTasks() bool
	GetRemainingTasksInfo() map[string]interface{}
	GracefulStop(timeout time.Duration) error
}

// SimpleScreenshotManager 简化的截图管理器
type SimpleScreenshotManager struct {
	processor         OCRProcessorInterface   // 通用OCR处理器接口
	screenshotService *ScreenshotService      // 截图服务
	app               AppInterface            // App接口引用
	tasks             []*SimpleScreenshotTask // 20个任务列表
	completedTasks    int                     // 已完成任务数
	totalTasks        int                     // 总任务数（固定20）
	mu                sync.RWMutex            // 读写锁
	running           bool                    // 运行状态
	startTime         time.Time               // 开始时间
	ctx               context.Context         // 上下文
	cancel            context.CancelFunc      // 取消函数
}

// NewSimpleScreenshotManager 创建简化的截图管理器
func NewSimpleScreenshotManager(
	processor OCRProcessorInterface,
	screenshotService *ScreenshotService,
	app AppInterface,
) *SimpleScreenshotManager {
	ctx, cancel := context.WithCancel(context.Background())

	manager := &SimpleScreenshotManager{
		processor:         processor,
		screenshotService: screenshotService,
		app:               app,
		tasks:             make([]*SimpleScreenshotTask, 0, 20),
		completedTasks:    0,
		totalTasks:        20, // 固定20个任务：10个器官 × 2种模式
		running:           false,
		ctx:               ctx,
		cancel:            cancel,
	}

	// 注册OCR结果回调
	processor.AddResultCallback(manager.handleOCRResult)

	return manager
}

// Start 启动管理器
func (ssm *SimpleScreenshotManager) Start(userName string) error {
	ssm.mu.Lock()
	defer ssm.mu.Unlock()

	if ssm.running {
		return fmt.Errorf("管理器已在运行中")
	}

	// 启动OCR处理器
	if !ssm.processor.IsRunning() {
		if err := ssm.processor.Start(); err != nil {
			return fmt.Errorf("启动OCR处理器失败: %v", err)
		}
	}

	ssm.running = true
	ssm.startTime = time.Now()
	ssm.completedTasks = 0
	ssm.tasks = make([]*SimpleScreenshotTask, 0, 20)

	utils.LogInfo("简化截图管理器已启动", zap.String("user", userName))
	return nil
}

// Stop 停止管理器
func (ssm *SimpleScreenshotManager) Stop() error {
	ssm.mu.Lock()
	defer ssm.mu.Unlock()

	if !ssm.running {
		return fmt.Errorf("管理器未运行")
	}

	ssm.running = false
	ssm.cancel()

	utils.LogInfo("简化截图管理器已停止")
	return nil
}

// TakeScreenshot 执行截图并提交OCR处理（非阻塞）
func (ssm *SimpleScreenshotManager) TakeScreenshot(userName, mode string) error {
	ssm.mu.Lock()
	defer ssm.mu.Unlock()

	if !ssm.running {
		return fmt.Errorf("管理器未运行")
	}

	// 检查任务数量限制
	if len(ssm.tasks) >= ssm.totalTasks {
		return fmt.Errorf("已达到最大任务数量 %d", ssm.totalTasks)
	}

	// 执行截图
	imagePath, err := ssm.screenshotService.TakeScreenshot(mode, userName)
	if err != nil {
		return fmt.Errorf("截图失败: %v", err)
	}

	// 创建任务
	taskID := fmt.Sprintf("%s_%s_%d", userName, mode, time.Now().Unix())
	task := &SimpleScreenshotTask{
		ID:        taskID,
		ImagePath: imagePath,
		UserName:  userName,
		Mode:      mode,
		Timestamp: time.Now(),
		Status:    "pending",
	}

	// 添加到任务列表
	ssm.tasks = append(ssm.tasks, task)

	utils.LogInfo("截图任务已创建", zap.String("task_id", taskID), zap.String("mode", mode), zap.String("path", imagePath))

	// 异步提交OCR处理
	go ssm.submitOCRTask(task)

	return nil
}

// submitOCRTask 提交OCR任务
func (ssm *SimpleScreenshotManager) submitOCRTask(task *SimpleScreenshotTask) {
	// 更新任务状态
	ssm.updateTaskStatus(task.ID, "processing")

	// 创建OCR任务
	ocrTask := &ScreenshotTask{
		ID:        task.ID,
		ImagePath: task.ImagePath,
		UserName:  task.UserName,
		Timestamp: task.Timestamp,
		Mode:      task.Mode,
	}

	// 提交到OCR处理器
	if err := ssm.processor.SubmitTask(context.Background(), ocrTask); err != nil {
		utils.LogError("提交OCR任务失败", task.ID, err)
		ssm.updateTaskStatus(task.ID, "failed")
		ssm.updateTaskError(task.ID, err)
	}
}

// handleOCRResult 处理OCR结果回调
func (ssm *SimpleScreenshotManager) handleOCRResult(result *OCRTaskResult) {
	taskID := result.Task.ID

	if result.Error != nil {
		utils.LogError("OCR处理失败", taskID, result.Error)
		ssm.updateTaskStatus(taskID, "failed")
		ssm.updateTaskError(taskID, result.Error)
	} else {
		utils.LogInfo("OCR处理成功", zap.String("task_id", taskID), zap.String("organ", result.Result.OrganName))
		ssm.updateTaskStatus(taskID, "completed")
		ssm.updateTaskOrgan(taskID, result.Result.OrganName)

		// 显示成功通知
		if ssm.app != nil {
			modeDesc := "B02生化分析"
			if result.Task.Mode == "C" {
				modeDesc = "C03病理分析"
			}

			progress := ssm.getProgress()
			title := "OCR识别完成"
			message := fmt.Sprintf("进度: %d/%d；%s；器官/部位：%s",
				progress.Completed, progress.Total, modeDesc, result.Result.OrganName)

			ssm.app.ShowSuccessNotification(title, message, 5000)
		}
	}

	// 增加完成计数
	ssm.mu.Lock()
	ssm.completedTasks++
	completed := ssm.completedTasks
	total := ssm.totalTasks
	ssm.mu.Unlock()

	// 检查是否全部完成
	if completed >= total {
		utils.LogInfo("所有截图任务已完成", zap.Int("total", total), zap.Duration("duration", time.Since(ssm.startTime)))
		if ssm.app != nil {
			ssm.app.ShowSuccessNotification("全部完成", fmt.Sprintf("20次截图OCR处理全部完成，耗时: %v", time.Since(ssm.startTime)), 8000)
		}
	}
}

// updateTaskStatus 更新任务状态
func (ssm *SimpleScreenshotManager) updateTaskStatus(taskID, status string) {
	ssm.mu.Lock()
	defer ssm.mu.Unlock()

	for _, task := range ssm.tasks {
		if task.ID == taskID {
			task.Status = status
			break
		}
	}
}

// updateTaskError 更新任务错误
func (ssm *SimpleScreenshotManager) updateTaskError(taskID string, err error) {
	ssm.mu.Lock()
	defer ssm.mu.Unlock()

	for _, task := range ssm.tasks {
		if task.ID == taskID {
			task.Error = err
			break
		}
	}
}

// updateTaskOrgan 更新任务器官名称
func (ssm *SimpleScreenshotManager) updateTaskOrgan(taskID, organName string) {
	ssm.mu.Lock()
	defer ssm.mu.Unlock()

	for _, task := range ssm.tasks {
		if task.ID == taskID {
			task.OrganName = organName
			break
		}
	}
}

// ProgressInfo 进度信息
type ProgressInfo struct {
	Total     int `json:"total"`
	Completed int `json:"completed"`
	Pending   int `json:"pending"`
	Failed    int `json:"failed"`
}

// GetProgress 获取进度信息
func (ssm *SimpleScreenshotManager) getProgress() ProgressInfo {
	ssm.mu.RLock()
	defer ssm.mu.RUnlock()

	progress := ProgressInfo{
		Total: len(ssm.tasks),
	}

	for _, task := range ssm.tasks {
		switch task.Status {
		case "completed":
			progress.Completed++
		case "failed":
			progress.Failed++
		default:
			progress.Pending++
		}
	}

	return progress
}

// GetProgress 获取进度信息（公开方法）
func (ssm *SimpleScreenshotManager) GetProgress() ProgressInfo {
	return ssm.getProgress()
}

// GetTasks 获取所有任务
func (ssm *SimpleScreenshotManager) GetTasks() []*SimpleScreenshotTask {
	ssm.mu.RLock()
	defer ssm.mu.RUnlock()

	// 返回副本
	tasks := make([]*SimpleScreenshotTask, len(ssm.tasks))
	copy(tasks, ssm.tasks)
	return tasks
}

// GetTasksByOrgan 按器官分组获取任务
func (ssm *SimpleScreenshotManager) GetTasksByOrgan() map[string][]*SimpleScreenshotTask {
	ssm.mu.RLock()
	defer ssm.mu.RUnlock()

	organMap := make(map[string][]*SimpleScreenshotTask)

	for _, task := range ssm.tasks {
		if task.Status == "completed" && task.OrganName != "" {
			organMap[task.OrganName] = append(organMap[task.OrganName], task)
		}
	}

	return organMap
}

// IsRunning 检查是否运行中
func (ssm *SimpleScreenshotManager) IsRunning() bool {
	ssm.mu.RLock()
	defer ssm.mu.RUnlock()
	return ssm.running
}

// IsCompleted 检查是否全部完成
func (ssm *SimpleScreenshotManager) IsCompleted() bool {
	ssm.mu.RLock()
	defer ssm.mu.RUnlock()
	return ssm.completedTasks >= ssm.totalTasks
}

// GetProcessor 获取OCR处理器（用于访问处理器方法）
func (ssm *SimpleScreenshotManager) GetProcessor() OCRProcessorInterface {
	return ssm.processor
}
