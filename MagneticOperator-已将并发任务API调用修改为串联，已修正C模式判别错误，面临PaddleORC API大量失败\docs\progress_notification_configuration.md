# 进度通知系统配置文档

## 概述

根据用户需求，第二窗口现在专门用于显示进度信息，不再显示调试信息、日志信息或错误信息。这样可以为检测操作者提供清晰的进度反馈，而不会被开发调试信息干扰。

## 通知类型分类

### 1. 进度类通知（发送到第二窗口）
- **ShowProgressNotification**: OCR处理进度、文件处理进度等
- **ShowOCRProcessNotification**: OCR专用进度通知
- **UpdateProgressNotification**: 进度更新事件

### 2. 非进度类通知（仅控制台输出 + 置顶窗口）
- **ShowSuccessNotification**: 操作成功提示
- **ShowErrorNotification**: 错误信息
- **ShowWarningNotification**: 警告信息
- **ShowInfoNotification**: 一般信息提示
- **ShowWailsNotification**: 通用通知接口

## 修改内容

### 后端修改 (app.go)

#### 1. ShowWailsNotification 函数
```go
// 修改前：同时发送到置顶窗口和第二窗口
// 修改后：只发送到置顶窗口，控制台输出，不发送到第二窗口
func (a *App) ShowWailsNotification(notificationType, title, message string, duration int) string {
    // 使用置顶信息窗口显示通知
    if a.topInfoWindowService != nil {
        a.topInfoWindowService.ShowMessage(title, message)
    }

    // 非进度类通知不发送到第二窗口，只在控制台输出
    fmt.Printf("[%s] %s: %s\n", notificationType, title, message)
    return "console-notification-sent"
}
```

#### 2. ShowProgressNotification 函数
```go
// 修改前：同时发送到置顶窗口和第二窗口
// 修改后：只发送到第二窗口，不使用置顶窗口
func (a *App) ShowProgressNotification(title, message string, progress int, duration int) string {
    progressMessage := fmt.Sprintf("%s (%d%%)", message, progress)
    
    // 发送进度通知事件到前端主窗口
    runtime.EventsEmit(a.ctx, "showProcessingNotification", map[string]interface{}{
        "organName":   title,
        "currentStep": progress,
        "totalSteps":  100,
        "message":     progressMessage,
    })

    // 备用方案：控制台输出
    fmt.Printf("[进度] %s: %s (%d%%)\n", title, message, progress)
    return "console-progress-sent"
}
```

### 前端修改

#### 撤销的修改
为了确保第二窗口只显示进度信息，撤销了以下前端组件中向第二窗口发送消息的修改：

1. **App.vue** - `showNotification` 方法
2. **PatientPanel.vue** - `showMessage` 方法
3. **LogPanel.vue** - `showMessage` 方法

这些方法现在只在本地显示或输出到控制台，不会发送到第二窗口。

#### 保留的集成
以下前端组件的通知方法仍然使用 `ShowWailsNotification`，但由于后端修改，这些通知不会发送到第二窗口：

- **PatientPanel.vue**: `showNotification`, `showConfirm`
- **ScreenshotPanel.vue**: `showNotification`, `showConfirm`
- **LogPanel.vue**: `showConfirm`
- **QRCodePanel.vue**: `showNotification`
- **SiteInfoPanel.vue**: 各种通知方法
- **CropSettingsPanel.vue**: 各种通知方法

## 测试验证

### 测试程序
创建了两个测试程序来验证配置：

1. **test_progress_only_notification.go**: 简化测试，验证通知逻辑
2. **test_progress_notification_integration.go**: 集成测试，使用实际服务

### 测试结果
```
=== 测试通知功能 ===

1. 测试非进度类通知（应该只在控制台输出，同时显示在置顶窗口）:
[置顶窗口] 操作成功: 文件保存成功
[success] 操作成功: 文件保存成功
[置顶窗口] 操作失败: 文件读取失败
[error] 操作失败: 文件读取失败
[置顶窗口] 警告: 磁盘空间不足
[warning] 警告: 磁盘空间不足
[置顶窗口] 信息: 系统更新可用
[info] 信息: 系统更新可用

2. 测试进度通知（应该只发送到第二窗口）:
[第二窗口] OCR处理进度: 处理第 1 轮次 (10%)
[第二窗口] OCR处理进度: 处理第 2 轮次 (20%)
[第二窗口] OCR处理进度: 处理第 3 轮次 (30%)
[第二窗口] OCR处理进度: 处理第 4 轮次 (40%)
[第二窗口] OCR处理进度: 处理第 5 轮次 (50%)
[第二窗口] OCR处理进度: 处理第 6 轮次 (60%)
[第二窗口] OCR处理进度: 处理第 7 轮次 (70%)
[第二窗口] OCR处理进度: 处理第 8 轮次 (80%)
[第二窗口] OCR处理进度: 处理第 9 轮次 (90%)
[第二窗口] OCR处理进度: 处理第 10 轮次 (100%)

=== 测试完成 ===
```

## 使用指南

### 对于开发者
- 调试信息、错误日志继续使用控制台输出
- 用户操作反馈使用置顶窗口
- 只有进度相关的信息才发送到第二窗口

### 对于操作者
- 第二窗口专门显示操作进度，界面简洁清晰
- 不会被调试信息干扰
- 可以专注于当前操作的进度状态

### 进度通知最佳实践
1. 使用 `ShowProgressNotification` 显示处理进度
2. 进度消息应该简洁明了，包含当前步骤和百分比
3. 对于长时间运行的任务，建议分成10个或更多步骤显示
4. 进度完成后可以发送一个100%的最终通知

## 配置优势

1. **职责分离**: 第二窗口专注于进度显示，置顶窗口处理用户交互反馈
2. **用户体验**: 操作者不会被技术调试信息干扰
3. **开发友好**: 开发者仍然可以在控制台看到所有调试信息
4. **维护性**: 通知类型明确分类，易于维护和扩展

## 注意事项

- 确保进度通知的消息格式一致
- 避免在进度通知中包含错误或警告信息
- 如需在第二窗口显示其他类型信息，需要重新评估设计
- 测试时注意验证通知是否发送到正确的目标