package main

import (
	"fmt"
	"log"

	"MagneticOperator/app/services"
)

// 示例：如何使用 Viper 配置管理
func exampleViperUsage() {
	// 创建 Viper 配置服务
	configService := services.NewViperConfigService("./config", "development")

	// 加载配置
	config, err := configService.LoadConfig()
	if err != nil {
		log.Printf("加载配置失败: %v", err)
		return
	}

	// 使用配置
	fmt.Printf("站点ID: %s\n", config.SiteInfo.SiteID)
	fmt.Printf("站点名称: %s\n", config.SiteInfo.SiteName)

	// 获取API密钥
	ocrToken := configService.GetAPIKey("ocr", "token")
	fmt.Printf("OCR Token: %s\n", ocrToken)

	// 检查调试模式
	if configService.IsDebugMode() {
		fmt.Println("当前处于调试模式")
	}

	// 获取裁剪设置
	cropSettings := configService.GetCropSettings()
	fmt.Printf("裁剪设置 - 顶部: %.3f, 底部: %.3f\n",
		cropSettings.TopPercent, cropSettings.BottomPercent)

	// 修改配置并保存
	config.SiteInfo.SiteName = "更新后的站点名称"
	if err := configService.SaveConfig(config); err != nil {
		log.Printf("保存配置失败: %v", err)
	}
}
