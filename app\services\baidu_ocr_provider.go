// Package services 百度OCR提供者实现
package services

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"sync"
	"time"

	json "github.com/goccy/go-json"
	"github.com/valyala/fasthttp"

	"MagneticOperator/app/models"
)

// BaiduOCRProvider 百度OCR提供者
type BaiduOCRProvider struct {
	config        *models.OCRConfig
	organDB       *OrganDatabase
	configService ConfigServiceInterface
	app           AppInterface
	client        *RetryableHTTPClient

	// 器官名称缓存
	organNameCache map[string]string
	cacheMutex     sync.RWMutex
}

// NewBaiduOCRProvider 创建百度OCR提供者
func NewBaiduOCRProvider(config *models.OCRConfig, organDB *OrganDatabase, configService ConfigServiceInterface, app AppInterface) *BaiduOCRProvider {
	return &BaiduOCRProvider{
		config:         config,
		organDB:        organDB,
		configService:  configService,
		app:            app,
		client:         NewRetryableHTTPClient(60 * time.Second),
		organNameCache: make(map[string]string),
	}
}

// ProcessImage 处理图片并返回OCR结果
func (p *BaiduOCRProvider) ProcessImage(ctx context.Context, imagePath string) (*OCRResult, error) {
	fmt.Printf("[百度OCR] 开始处理图片: %s\n", imagePath)

	// 健康检查
	if err := p.checkOCRAPIHealthWithRetry(p.config.APIURL); err != nil {
		return nil, fmt.Errorf("百度OCR API健康检查失败: %w", err)
	}

	// 读取图片文件
	imageBytes, err := os.ReadFile(imagePath)
	if err != nil {
		return nil, fmt.Errorf("读取图片文件失败: %w", err)
	}

	// Base64编码图片数据
	base64Image := base64.StdEncoding.EncodeToString(imageBytes)

	// 创建请求负载
	requestPayload := map[string]interface{}{
		"file":     base64Image,
		"fileType": 1, // 图像类型

		// 模型功能相关参数
		"useDocOrientationClassify": false,
		"useDocUnwarping":           false,
		"useTextlineOrientation":    false,
		"useGeneralOcr":             true,
		"useSealRecognition":        false,
		"useTableRecognition":       true, // 用于医疗报告表格数据提取
		"useFormulaRecognition":     false,

		// 其他优化参数
		"layoutThreshold": 0.5,
		"layoutNms":       false,
	}

	jsonData, err := json.Marshal(requestPayload)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 准备请求头
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	if p.config.Token != "" {
		headers["Authorization"] = "token " + p.config.Token
	}

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	// 发送HTTP请求
	resp, err := p.client.DoWithRetry("POST", p.config.APIURL, jsonData, headers)
	if err != nil {
		return nil, fmt.Errorf("OCR API请求失败: %w", err)
	}
	defer func() {
		// fasthttp的Response需要手动释放
		fasthttp.ReleaseResponse(resp)
	}()

	// 读取响应
	responseBody := resp.Body()
	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("OCR API返回错误状态码: %d, 响应: %s", resp.StatusCode(), string(responseBody))
	}

	// 解析OCR数据
	extractionResult := p.extractOCRDataOptimized(responseBody)
	if extractionResult.Error != nil {
		return nil, fmt.Errorf("OCR数据提取失败: %w", extractionResult.Error)
	}

	// 校准器官名称
	calibratedOrganName := p.calibrateOrganName(extractionResult.OrganName)

	// 构建最终结果
	result := &OCRResult{
		OrganName:     calibratedOrganName,
		Confidence:    extractionResult.Confidence,
		ImagePath:     imagePath,
		KeyValuePairs: extractionResult.KeyValuePairs,
		RawResponse:   json.RawMessage(responseBody),
	}

	fmt.Printf("[百度OCR] 处理完成，置信度: %.2f，器官: %s\n", result.Confidence, result.OrganName)
	return result, nil
}

// GetProviderName 获取提供者名称
func (p *BaiduOCRProvider) GetProviderName() string {
	return "Baidu OCR"
}

// ValidateConfig 验证配置是否有效
func (p *BaiduOCRProvider) ValidateConfig() error {
	if p.config == nil {
		return fmt.Errorf("百度OCR配置为空")
	}

	if p.config.APIURL == "" {
		return fmt.Errorf("百度OCR API URL未配置")
	}

	if p.config.Token == "" {
		return fmt.Errorf("百度OCR Token未配置")
	}

	fmt.Printf("[百度OCR] 配置验证通过\n")
	return nil
}

// Close 关闭连接和清理资源
func (p *BaiduOCRProvider) Close() {
	fmt.Printf("[百度OCR] 清理资源\n")
	p.cacheMutex.Lock()
	p.organNameCache = make(map[string]string)
	p.cacheMutex.Unlock()
}

// checkOCRAPIHealthWithRetry 健康检查（带重试）
func (p *BaiduOCRProvider) checkOCRAPIHealthWithRetry(apiURL string) error {
	for i := 0; i < HealthCheckMaxRetries; i++ {
		if p.checkOCRAPIAvailability(apiURL) {
			return nil
		}
		if i < HealthCheckMaxRetries-1 {
			time.Sleep(HealthCheckInterval)
		}
	}
	return fmt.Errorf("OCR API健康检查失败，已重试 %d 次", HealthCheckMaxRetries)
}

// checkOCRAPIAvailability 检查OCR API可用性
func (p *BaiduOCRProvider) checkOCRAPIAvailability(apiURL string) bool {
	headers := map[string]string{
		"Content-Type": "application/json",
	}

	resp, err := p.client.DoWithRetry("GET", apiURL, nil, headers)
	if err != nil {
		return false
	}
	defer func() {
		// fasthttp的Response需要手动释放
		fasthttp.ReleaseResponse(resp)
	}()

	return resp.StatusCode() == 200 || resp.StatusCode() == 405 // 405表示方法不允许，但服务可用
}

// extractOCRDataOptimized 优化的OCR数据提取
func (p *BaiduOCRProvider) extractOCRDataOptimized(responseBody []byte) *OCRExtractionResult {
	// 这里复用现有的extractOCRDataOptimized逻辑
	// 为了简化，我们创建一个临时的OCRService实例来调用现有方法
	tempService := &OCRService{
		configService:  p.configService,
		organDB:        p.organDB,
		app:            p.app,
		organNameCache: make(map[string]string),
	}

	return tempService.extractOCRDataOptimized(responseBody)
}

// calibrateOrganName 校准器官名称
func (p *BaiduOCRProvider) calibrateOrganName(rawOrganName string) string {
	// 检查缓存
	p.cacheMutex.RLock()
	if cached, exists := p.organNameCache[rawOrganName]; exists {
		p.cacheMutex.RUnlock()
		return cached
	}
	p.cacheMutex.RUnlock()

	// 使用现有的校准逻辑
	tempService := &OCRService{
		configService:  p.configService,
		organDB:        p.organDB,
		app:            p.app,
		organNameCache: make(map[string]string),
	}

	calibratedName := tempService.calibrateOrganName(rawOrganName)

	// 更新缓存
	p.cacheMutex.Lock()
	p.organNameCache[rawOrganName] = calibratedName
	p.cacheMutex.Unlock()

	return calibratedName
}
