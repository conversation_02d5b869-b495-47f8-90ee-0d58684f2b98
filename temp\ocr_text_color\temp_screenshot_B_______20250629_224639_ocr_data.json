{"logId": "dfa4457c-056b-4e0c-8d35-fd49a24131df", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 1, "label": "image", "score": 0.5830339193344116, "coordinate": [201.80441284179688, 9.670825004577637, 767.5709838867188, 1463.86865234375]}, {"cls_id": 22, "label": "aside_text", "score": 0.5355043411254883, "coordinate": [10.168191909790039, 13.216926574707031, 50.30656433105469, 1411.7816162109375]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[87, 11], [330, 11], [330, 34], [87, 34]], [[349, 11], [390, 11], [390, 32], [349, 32]], [[417, 9], [489, 9], [489, 34], [417, 34]], [[530, 13], [594, 13], [594, 32], [530, 32]], [[615, 13], [654, 13], [654, 32], [615, 32]], [[17, 25], [41, 25], [41, 48], [17, 48]], [[13, 136], [46, 136], [46, 166], [13, 166]], [[506, 145], [558, 145], [558, 172], [506, 172]], [[393, 181], [668, 181], [668, 204], [393, 204]], [[20, 197], [39, 197], [39, 214], [20, 214]], [[20, 250], [37, 250], [37, 268], [20, 268]], [[487, 277], [576, 277], [576, 304], [487, 304]], [[262, 290], [273, 290], [273, 299], [262, 299]], [[12, 308], [32, 298], [43, 320], [23, 329]], [[393, 311], [670, 311], [670, 334], [393, 334]], [[13, 368], [30, 352], [46, 368], [30, 384]], [[11, 415], [36, 405], [46, 428], [21, 439]], [[13, 459], [44, 459], [44, 492], [13, 492]], [[13, 513], [44, 513], [44, 545], [13, 545]], [[15, 570], [42, 570], [42, 597], [15, 597]], [[258, 585], [480, 585], [480, 608], [258, 608]], [[530, 585], [617, 585], [617, 611], [530, 611]], [[670, 583], [766, 583], [766, 608], [670, 608]], [[345, 608], [393, 608], [393, 635], [345, 635]], [[546, 610], [602, 610], [602, 635], [546, 635]], [[711, 611], [738, 611], [738, 636], [711, 636]], [[20, 629], [37, 629], [37, 647], [20, 647]], [[17, 679], [42, 679], [42, 704], [17, 704]], [[240, 699], [334, 699], [334, 724], [240, 724]], [[15, 733], [42, 733], [42, 760], [15, 760]], [[458, 758], [583, 758], [583, 788], [458, 788]], [[15, 786], [42, 786], [42, 813], [15, 813]], [[13, 837], [46, 837], [46, 871], [13, 871]], [[13, 890], [44, 890], [44, 922], [13, 922]], [[308, 883], [382, 883], [382, 915], [308, 915]], [[482, 883], [556, 883], [556, 915], [482, 915]], [[655, 883], [729, 883], [729, 915], [655, 915]], [[275, 901], [290, 901], [290, 919], [275, 919]], [[443, 899], [469, 899], [469, 926], [443, 926]], [[615, 899], [642, 899], [642, 926], [615, 926]], [[329, 913], [360, 913], [360, 938], [329, 938]], [[500, 912], [535, 912], [535, 942], [500, 942]], [[674, 910], [711, 910], [711, 942], [674, 942]], [[11, 942], [46, 942], [46, 976], [11, 976]], [[13, 1003], [44, 1003], [44, 1028], [13, 1028]], [[490, 1064], [555, 1068], [549, 1147], [484, 1143]], [[414, 1176], [630, 1176], [630, 1201], [414, 1201]], [[449, 1283], [593, 1283], [593, 1308], [449, 1308]], [[395, 1319], [646, 1319], [646, 1342], [395, 1342]], [[425, 1351], [617, 1351], [617, 1375], [425, 1375]], [[449, 1378], [593, 1380], [592, 1405], [448, 1403]], [[447, 1410], [592, 1408], [593, 1433], [447, 1435]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["尺寸：Samsung Galaxy …", "412", "× 914", "10.▼", "无...", "51", "Q", "提示", "开始处理生化平衡分析模式截图", "Q", "Q", "操作成功", "V", "🌸", "生化平衡分析模式截图处理完成", "🚀", "🌸", "51", "51", "51", "生化平衡分析模式截图处理", "测试前端", "测试后端通", "完成", "Toast", "知", "Q", ",", "当前受检者", "133", "暂无受检者", "13", "ü", "凶", "待检测", "已完成", "待分析", "P", "☑", "ı", "(0)", "(0)", "(0)", "ă", "🚀", "自", "暂无待检测的候检者", "Toast调试信息", "Wails Runtime状态: available", "事件监听器状态：active", "当前Toast数量:2", "最大Toast数量：3"], "rec_scores": [0.9322580099105835, 0.9997636675834656, 0.9592230916023254, 0.7135069966316223, 0.8010431528091431, 0.9996583461761475, 0.39179012179374695, 0.9995248317718506, 0.9990440011024475, 0.9809019565582275, 0.9948177933692932, 0.9994993209838867, 0.1412743777036667, 0.1798575073480606, 0.9958654642105103, 0.2479335367679596, 0.3056175708770752, 0.9914751052856445, 0.9920990467071533, 0.9977153539657593, 0.9983819127082825, 0.9990039467811584, 0.994647204875946, 0.9996567964553833, 0.9990389943122864, 0.9999148845672607, 0.9927694797515869, 0.16812235116958618, 0.9997937083244324, 0.5843570232391357, 0.9998455047607422, 0.6414237022399902, 0.45863527059555054, 0.12770292162895203, 0.9990738034248352, 0.9997664093971252, 0.9996368885040283, 0.19173789024353027, 0.34314998984336853, 0.719290018081665, 0.9693934917449951, 0.9762107729911804, 0.9849011301994324, 0.1375788003206253, 0.31843170523643494, 0.755418062210083, 0.9993289113044739, 0.9994940161705017, 0.9729243516921997, 0.9869338870048523, 0.9547473788261414, 0.9685705304145813], "rec_polys": [[[87, 11], [330, 11], [330, 34], [87, 34]], [[349, 11], [390, 11], [390, 32], [349, 32]], [[417, 9], [489, 9], [489, 34], [417, 34]], [[530, 13], [594, 13], [594, 32], [530, 32]], [[615, 13], [654, 13], [654, 32], [615, 32]], [[17, 25], [41, 25], [41, 48], [17, 48]], [[13, 136], [46, 136], [46, 166], [13, 166]], [[506, 145], [558, 145], [558, 172], [506, 172]], [[393, 181], [668, 181], [668, 204], [393, 204]], [[20, 197], [39, 197], [39, 214], [20, 214]], [[20, 250], [37, 250], [37, 268], [20, 268]], [[487, 277], [576, 277], [576, 304], [487, 304]], [[262, 290], [273, 290], [273, 299], [262, 299]], [[12, 308], [32, 298], [43, 320], [23, 329]], [[393, 311], [670, 311], [670, 334], [393, 334]], [[13, 368], [30, 352], [46, 368], [30, 384]], [[11, 415], [36, 405], [46, 428], [21, 439]], [[13, 459], [44, 459], [44, 492], [13, 492]], [[13, 513], [44, 513], [44, 545], [13, 545]], [[15, 570], [42, 570], [42, 597], [15, 597]], [[258, 585], [480, 585], [480, 608], [258, 608]], [[530, 585], [617, 585], [617, 611], [530, 611]], [[670, 583], [766, 583], [766, 608], [670, 608]], [[345, 608], [393, 608], [393, 635], [345, 635]], [[546, 610], [602, 610], [602, 635], [546, 635]], [[711, 611], [738, 611], [738, 636], [711, 636]], [[20, 629], [37, 629], [37, 647], [20, 647]], [[17, 679], [42, 679], [42, 704], [17, 704]], [[240, 699], [334, 699], [334, 724], [240, 724]], [[15, 733], [42, 733], [42, 760], [15, 760]], [[458, 758], [583, 758], [583, 788], [458, 788]], [[15, 786], [42, 786], [42, 813], [15, 813]], [[13, 837], [46, 837], [46, 871], [13, 871]], [[13, 890], [44, 890], [44, 922], [13, 922]], [[308, 883], [382, 883], [382, 915], [308, 915]], [[482, 883], [556, 883], [556, 915], [482, 915]], [[655, 883], [729, 883], [729, 915], [655, 915]], [[275, 901], [290, 901], [290, 919], [275, 919]], [[443, 899], [469, 899], [469, 926], [443, 926]], [[615, 899], [642, 899], [642, 926], [615, 926]], [[329, 913], [360, 913], [360, 938], [329, 938]], [[500, 912], [535, 912], [535, 942], [500, 942]], [[674, 910], [711, 910], [711, 942], [674, 942]], [[11, 942], [46, 942], [46, 976], [11, 976]], [[13, 1003], [44, 1003], [44, 1028], [13, 1028]], [[490, 1064], [555, 1068], [549, 1147], [484, 1143]], [[414, 1176], [630, 1176], [630, 1201], [414, 1201]], [[449, 1283], [593, 1283], [593, 1308], [449, 1308]], [[395, 1319], [646, 1319], [646, 1342], [395, 1342]], [[425, 1351], [617, 1351], [617, 1375], [425, 1375]], [[449, 1378], [593, 1380], [592, 1405], [448, 1403]], [[447, 1410], [592, 1408], [593, 1433], [447, 1435]]], "rec_boxes": [[87, 11, 330, 34], [349, 11, 390, 32], [417, 9, 489, 34], [530, 13, 594, 32], [615, 13, 654, 32], [17, 25, 41, 48], [13, 136, 46, 166], [506, 145, 558, 172], [393, 181, 668, 204], [20, 197, 39, 214], [20, 250, 37, 268], [487, 277, 576, 304], [262, 290, 273, 299], [12, 298, 43, 329], [393, 311, 670, 334], [13, 352, 46, 384], [11, 405, 46, 439], [13, 459, 44, 492], [13, 513, 44, 545], [15, 570, 42, 597], [258, 585, 480, 608], [530, 585, 617, 611], [670, 583, 766, 608], [345, 608, 393, 635], [546, 610, 602, 635], [711, 611, 738, 636], [20, 629, 37, 647], [17, 679, 42, 704], [240, 699, 334, 724], [15, 733, 42, 760], [458, 758, 583, 788], [15, 786, 42, 813], [13, 837, 46, 871], [13, 890, 44, 922], [308, 883, 382, 915], [482, 883, 556, 915], [655, 883, 729, 915], [275, 901, 290, 919], [443, 899, 469, 926], [615, 899, 642, 926], [329, 913, 360, 938], [500, 912, 535, 942], [674, 910, 711, 942], [11, 942, 46, 976], [13, 1003, 44, 1028], [484, 1064, 555, 1147], [414, 1176, 630, 1201], [449, 1283, 593, 1308], [395, 1319, 646, 1342], [425, 1351, 617, 1375], [448, 1378, 593, 1405], [447, 1408, 593, 1435]]}, "table_res_list": []}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//dfa4457c-056b-4e0c-8d35-fd49a24131df/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T14%3A46%3A51Z%2F-1%2F%2F7659d693b0371412487db9e21a926deebdf22f05493cf338adba6e5cb6c6a888", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//dfa4457c-056b-4e0c-8d35-fd49a24131df/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T14%3A46%3A52Z%2F-1%2F%2Faf417fce6a98c2d65922426d18bd8246b8a039578bae4df23c9e293fe83f29b0"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//dfa4457c-056b-4e0c-8d35-fd49a24131df/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T14%3A46%3A51Z%2F-1%2F%2Fd4870b972f080b783b4abc7a9aec68f74846846344029ae9e8901ae90011a395"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}