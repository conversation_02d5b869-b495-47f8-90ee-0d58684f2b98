{"logId": "7257b620-1784-4e96-a4bc-01c11b3c4757", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 8, "label": "table", "score": 0.9848606586456299, "coordinate": [13.827919006347656, 73.02131652832031, 767.7449951171875, 1713.9228515625]}, {"cls_id": 9, "label": "table_title", "score": 0.6251488327980042, "coordinate": [19.465713500976562, 27.288297653198242, 520.7885131835938, 63.18240737915039]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.5345309972763062, "coordinate": [19.465713500976562, 27.288297653198242, 520.7885131835938, 63.18240737915039]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[17, 27], [521, 25], [521, 61], [17, 63]], [[100, 77], [157, 77], [157, 102], [100, 102]], [[192, 79], [384, 79], [384, 102], [192, 102]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[196, 157], [491, 157], [491, 175], [196, 175]], [[98, 179], [161, 179], [161, 204], [98, 204]], [[192, 181], [423, 181], [423, 204], [192, 204]], [[98, 204], [161, 204], [161, 229], [98, 229]], [[192, 204], [266, 204], [266, 231], [192, 231]], [[98, 229], [162, 229], [162, 256], [98, 256]], [[192, 231], [319, 227], [320, 254], [193, 258]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [556, 257], [556, 281], [192, 281]], [[98, 281], [161, 281], [161, 306], [98, 306]], [[196, 284], [310, 284], [310, 304], [196, 304]], [[98, 306], [161, 306], [161, 332], [98, 332]], [[194, 307], [727, 307], [727, 331], [194, 331]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [428, 336], [428, 354], [196, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[194, 359], [412, 359], [412, 383], [194, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[194, 384], [393, 384], [393, 408], [194, 408]], [[98, 403], [156, 408], [153, 435], [95, 430]], [[194, 409], [443, 409], [443, 433], [194, 433]], [[98, 434], [155, 434], [155, 459], [98, 459]], [[192, 434], [474, 434], [474, 458], [192, 458]], [[98, 458], [155, 458], [155, 484], [98, 484]], [[194, 463], [399, 463], [399, 481], [194, 481]], [[98, 484], [153, 484], [153, 509], [98, 509]], [[194, 486], [404, 486], [404, 509], [194, 509]], [[98, 511], [155, 511], [155, 536], [98, 536]], [[194, 511], [567, 511], [567, 534], [194, 534]], [[98, 536], [153, 536], [153, 561], [98, 561]], [[192, 536], [310, 536], [310, 561], [192, 561]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[192, 561], [310, 561], [310, 586], [192, 586]], [[98, 586], [155, 586], [155, 611], [98, 611]], [[194, 588], [438, 588], [438, 611], [194, 611]], [[98, 611], [155, 611], [155, 638], [98, 638]], [[192, 613], [377, 613], [377, 636], [192, 636]], [[98, 638], [155, 638], [155, 663], [98, 663]], [[192, 640], [408, 640], [408, 663], [192, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[192, 663], [301, 663], [301, 688], [192, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[188, 686], [227, 686], [227, 717], [188, 717]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[190, 713], [262, 713], [262, 740], [190, 740]], [[98, 740], [157, 740], [157, 765], [98, 765]], [[192, 740], [689, 740], [689, 763], [192, 763]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[192, 765], [325, 765], [325, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [294, 792], [294, 817], [192, 817]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[190, 815], [244, 815], [244, 844], [190, 844]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[190, 842], [364, 842], [364, 867], [190, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [447, 869], [447, 892], [192, 892]], [[98, 892], [155, 892], [155, 919], [98, 919]], [[192, 894], [449, 894], [449, 917], [192, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[194, 919], [441, 919], [441, 944], [194, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [349, 946], [349, 969], [194, 969]], [[98, 969], [155, 969], [155, 996], [98, 996]], [[192, 971], [524, 971], [524, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [332, 996], [332, 1021], [192, 1021]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[194, 1022], [508, 1022], [508, 1046], [194, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[194, 1047], [606, 1047], [606, 1071], [194, 1071]], [[98, 1071], [157, 1071], [157, 1096], [98, 1096]], [[192, 1072], [731, 1072], [731, 1096], [192, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [347, 1098], [347, 1121], [192, 1121]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[190, 1123], [367, 1121], [367, 1146], [190, 1148]], [[98, 1148], [155, 1148], [155, 1174], [98, 1174]], [[190, 1149], [504, 1148], [504, 1173], [190, 1174]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[194, 1174], [412, 1174], [412, 1198], [194, 1198]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [456, 1198], [456, 1223], [190, 1225]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[190, 1224], [260, 1224], [260, 1251], [190, 1251]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1249], [430, 1249], [430, 1273], [192, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[194, 1276], [482, 1276], [482, 1300], [194, 1300]], [[98, 1301], [157, 1301], [157, 1326], [98, 1326]], [[196, 1305], [639, 1305], [639, 1323], [196, 1323]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[194, 1328], [460, 1328], [460, 1351], [194, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [476, 1353], [476, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1378], [500, 1378], [500, 1401], [192, 1401]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [428, 1403], [428, 1426], [192, 1426]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[192, 1428], [327, 1428], [327, 1453], [192, 1453]], [[98, 1453], [155, 1453], [155, 1478], [98, 1478]], [[192, 1455], [356, 1455], [356, 1478], [192, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[192, 1480], [314, 1480], [314, 1505], [192, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[190, 1503], [242, 1503], [242, 1532], [190, 1532]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[192, 1530], [332, 1530], [332, 1555], [192, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[188, 1553], [264, 1553], [264, 1586], [188, 1586]], [[98, 1582], [157, 1582], [157, 1607], [98, 1607]], [[191, 1578], [370, 1584], [369, 1611], [190, 1605]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1607], [401, 1607], [401, 1632], [192, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[192, 1634], [375, 1634], [375, 1657], [192, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1659], [478, 1659], [478, 1682], [192, 1682]], [[100, 1682], [155, 1682], [155, 1709], [100, 1709]], [[192, 1684], [286, 1684], [286, 1712], [192, 1712]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["按照标准图谱相似度递减列表：", "0.000", "厦部第1腰椎水平截面", "4.479", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.054", "C反应蛋白C-REACTIVEPROTEIN", "0.072", "血尿酸SERUMURICACID", "0.074", "脂肪酶*", "0.075", "血管紧张素Ⅱ*", "0.087", "胆固醇COMMONPLASMA CHOLESTERIN", "0.099", "血管紧张素I", "0.101", "血浆丰配化脂肪酸NONETHERIZED FATTYACIDSOF PLASMA", "0.161", "血钾PLASMAPOTASSIUM", "0.095", "血清蛋白SERUM ALBUMEN", "0.097", "血红蛋白HAEMOGLOBIN", "0.103", "尿中蛋白质PROTEININURINE", "0.105", "PERIPHERIC BLOOD LEUCOCYTES", "0.107", "嗜碱性粒细胞BASOPHILS", "0.107", "血红血球ERYTHROCYTES", "0.114", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.131", "免疫球蛋白G*", "0.132", "免疫球蛋白M*", "0.133", "尿白血球URINELEUCOCYTES", "0.135", "单核细胞MONOCYTES", "0.143", "血清蛋白SERUMPROTEIN", "0.147", "BETA球蛋白", "0.201", "锂*", "0.092", "胆汁酸*", "0.093", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.099", "ALPHA2球蛋白*", "0.102", "胰高血糖素", "0.104", "糖苷*", "0.105", "红细胞沉降率(ESR)", "0.106", "血清补体SERUMCOMPLEMENT", "0.107", "肿瘤标志物MELANOGENE在尿*", "0.107", "血清溶菌酵SERUMLYSOZYME", "0.109", "血细胞比容，全血", "0.111", "游离胆固醇FREEPLASMACHOLESTERIN", "0.113", "糖基化血红蛋白*", "0.114", "血清铜蓝蛋白SERUMCERULOPLASMIN", "0.115", "肌酸磷酸酵素COMMON CREATINPHOSPHOKINASE", "0.115", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.116", "RHEUMOFACTOR*", "0.116", "肿瘤标志物胸苷激酶", "0.117", "血清淀粉酵素SERUMALPHAAMYLASE", "0.117", "尿肌酥URINECREATININE", "0.117", "伽马球蛋白GAMMA-GLOBULINS", "0.117", "铁蛋白*", "0.118", "嗜酸性粒细胞EOSINOPHILES", "0.118", "促肾上腺皮质激素CORTICOTROPIN", "0.118", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.118", "尿中肾上腺素URINEADRENALIN", "0.118", "嗜中性粒细胞STABNEUTROPHILS", "0.119", "生长激素SOMATOTROPICHORMONE", "0.119", "血组织胺BLOODHISTAMINE", "0.119", "ALPHA1球蛋白*", "0.120", "血糖BLOOD SUGAR", "0.120", "甲状腺球蛋白*", "0.120", "肾素*", "0.120", "抗链球菌溶血素*", "0.121", "催乳素*", "0.121", "ALPHA1-抗胰蛋白酶*", "0.123", "维生素B1（THIAMINE）", "0.126", "DELTA氨基乙酰丙酸*", "0.126", "血浆磷脂PLASMA PHOSPHOTIDES", "0.127", "维生素B6*"], "rec_scores": [0.9956884980201721, 0.9991452097892761, 0.9235565066337585, 0.9989500045776367, 0.9983981251716614, 0.9667975902557373, 0.9999244809150696, 0.9659162163734436, 0.9999293088912964, 0.9965614080429077, 0.9998855590820312, 0.9802283644676208, 0.9999462366104126, 0.9428358674049377, 0.999926745891571, 0.9795368313789368, 0.9999297857284546, 0.9789831638336182, 0.9998544454574585, 0.9332330822944641, 0.9999052286148071, 0.9812120795249939, 0.999720573425293, 0.9809539914131165, 0.9997221231460571, 0.9985331296920776, 0.9998520016670227, 0.9969345927238464, 0.9995471239089966, 0.9688208699226379, 0.999531626701355, 0.9848082661628723, 0.9992643594741821, 0.9961680173873901, 0.9993475675582886, 0.9929898977279663, 0.9995128512382507, 0.9875523447990417, 0.999610424041748, 0.9918586611747742, 0.9995530843734741, 0.9923917055130005, 0.9996044039726257, 0.9973767995834351, 0.9995597004890442, 0.9954931735992432, 0.9992172122001648, 0.992581307888031, 0.9994300603866577, 0.9606835842132568, 0.9998161196708679, 0.969925045967102, 0.9997072219848633, 0.9949657917022705, 0.9996426701545715, 0.9461562037467957, 0.9996402859687805, 0.9972966909408569, 0.9994826316833496, 0.973040759563446, 0.9995371103286743, 0.9220654964447021, 0.9993749856948853, 0.9972162842750549, 0.9995378255844116, 0.993736982345581, 0.9995242357254028, 0.9920037388801575, 0.9994146227836609, 0.9945255517959595, 0.9985436201095581, 0.9800680875778198, 0.9995222091674805, 0.9608697891235352, 0.9990524053573608, 0.9947469830513, 0.9994711875915527, 0.9837502241134644, 0.9992499351501465, 0.9963960647583008, 0.9994398951530457, 0.9896174669265747, 0.9991324543952942, 0.9871059060096741, 0.9992870092391968, 0.9952670931816101, 0.999285101890564, 0.9578018188476562, 0.9991861581802368, 0.9962862133979797, 0.9991371035575867, 0.908932089805603, 0.9994661211967468, 0.9942595362663269, 0.999498188495636, 0.9984483122825623, 0.9994878768920898, 0.9974063634872437, 0.9993160963058472, 0.9933616518974304, 0.9994661211967468, 0.9978678822517395, 0.9994169473648071, 0.9967248439788818, 0.9988118410110474, 0.9917929172515869, 0.9987851977348328, 0.9760702848434448, 0.9992280006408691, 0.9667790532112122, 0.99952632188797, 0.9408621191978455, 0.9994872212409973, 0.8973817825317383, 0.9993170499801636, 0.9738208055496216, 0.9995825886726379, 0.9844926595687866, 0.9994697570800781, 0.9883496165275574, 0.9995576739311218, 0.9556136727333069, 0.9991583824157715, 0.9539235234260559, 0.9992308616638184, 0.9760136008262634, 0.9992958903312683, 0.9817430377006531], "rec_polys": [[[17, 27], [521, 25], [521, 61], [17, 63]], [[100, 77], [157, 77], [157, 102], [100, 102]], [[192, 79], [384, 79], [384, 102], [192, 102]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[196, 157], [491, 157], [491, 175], [196, 175]], [[98, 179], [161, 179], [161, 204], [98, 204]], [[192, 181], [423, 181], [423, 204], [192, 204]], [[98, 204], [161, 204], [161, 229], [98, 229]], [[192, 204], [266, 204], [266, 231], [192, 231]], [[98, 229], [162, 229], [162, 256], [98, 256]], [[192, 231], [319, 227], [320, 254], [193, 258]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [556, 257], [556, 281], [192, 281]], [[98, 281], [161, 281], [161, 306], [98, 306]], [[196, 284], [310, 284], [310, 304], [196, 304]], [[98, 306], [161, 306], [161, 332], [98, 332]], [[194, 307], [727, 307], [727, 331], [194, 331]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [428, 336], [428, 354], [196, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[194, 359], [412, 359], [412, 383], [194, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[194, 384], [393, 384], [393, 408], [194, 408]], [[98, 403], [156, 408], [153, 435], [95, 430]], [[194, 409], [443, 409], [443, 433], [194, 433]], [[98, 434], [155, 434], [155, 459], [98, 459]], [[192, 434], [474, 434], [474, 458], [192, 458]], [[98, 458], [155, 458], [155, 484], [98, 484]], [[194, 463], [399, 463], [399, 481], [194, 481]], [[98, 484], [153, 484], [153, 509], [98, 509]], [[194, 486], [404, 486], [404, 509], [194, 509]], [[98, 511], [155, 511], [155, 536], [98, 536]], [[194, 511], [567, 511], [567, 534], [194, 534]], [[98, 536], [153, 536], [153, 561], [98, 561]], [[192, 536], [310, 536], [310, 561], [192, 561]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[192, 561], [310, 561], [310, 586], [192, 586]], [[98, 586], [155, 586], [155, 611], [98, 611]], [[194, 588], [438, 588], [438, 611], [194, 611]], [[98, 611], [155, 611], [155, 638], [98, 638]], [[192, 613], [377, 613], [377, 636], [192, 636]], [[98, 638], [155, 638], [155, 663], [98, 663]], [[192, 640], [408, 640], [408, 663], [192, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[192, 663], [301, 663], [301, 688], [192, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[188, 686], [227, 686], [227, 717], [188, 717]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[190, 713], [262, 713], [262, 740], [190, 740]], [[98, 740], [157, 740], [157, 765], [98, 765]], [[192, 740], [689, 740], [689, 763], [192, 763]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[192, 765], [325, 765], [325, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [294, 792], [294, 817], [192, 817]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[190, 815], [244, 815], [244, 844], [190, 844]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[190, 842], [364, 842], [364, 867], [190, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [447, 869], [447, 892], [192, 892]], [[98, 892], [155, 892], [155, 919], [98, 919]], [[192, 894], [449, 894], [449, 917], [192, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[194, 919], [441, 919], [441, 944], [194, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [349, 946], [349, 969], [194, 969]], [[98, 969], [155, 969], [155, 996], [98, 996]], [[192, 971], [524, 971], [524, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [332, 996], [332, 1021], [192, 1021]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[194, 1022], [508, 1022], [508, 1046], [194, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[194, 1047], [606, 1047], [606, 1071], [194, 1071]], [[98, 1071], [157, 1071], [157, 1096], [98, 1096]], [[192, 1072], [731, 1072], [731, 1096], [192, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [347, 1098], [347, 1121], [192, 1121]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[190, 1123], [367, 1121], [367, 1146], [190, 1148]], [[98, 1148], [155, 1148], [155, 1174], [98, 1174]], [[190, 1149], [504, 1148], [504, 1173], [190, 1174]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[194, 1174], [412, 1174], [412, 1198], [194, 1198]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [456, 1198], [456, 1223], [190, 1225]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[190, 1224], [260, 1224], [260, 1251], [190, 1251]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1249], [430, 1249], [430, 1273], [192, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[194, 1276], [482, 1276], [482, 1300], [194, 1300]], [[98, 1301], [157, 1301], [157, 1326], [98, 1326]], [[196, 1305], [639, 1305], [639, 1323], [196, 1323]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[194, 1328], [460, 1328], [460, 1351], [194, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [476, 1353], [476, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1378], [500, 1378], [500, 1401], [192, 1401]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [428, 1403], [428, 1426], [192, 1426]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[192, 1428], [327, 1428], [327, 1453], [192, 1453]], [[98, 1453], [155, 1453], [155, 1478], [98, 1478]], [[192, 1455], [356, 1455], [356, 1478], [192, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[192, 1480], [314, 1480], [314, 1505], [192, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[190, 1503], [242, 1503], [242, 1532], [190, 1532]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[192, 1530], [332, 1530], [332, 1555], [192, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[188, 1553], [264, 1553], [264, 1586], [188, 1586]], [[98, 1582], [157, 1582], [157, 1607], [98, 1607]], [[191, 1578], [370, 1584], [369, 1611], [190, 1605]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1607], [401, 1607], [401, 1632], [192, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[192, 1634], [375, 1634], [375, 1657], [192, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1659], [478, 1659], [478, 1682], [192, 1682]], [[100, 1682], [155, 1682], [155, 1709], [100, 1709]], [[192, 1684], [286, 1684], [286, 1712], [192, 1712]]], "rec_boxes": [[17, 25, 521, 63], [100, 77, 157, 102], [192, 79, 384, 102], [98, 102, 157, 127], [192, 102, 270, 129], [194, 129, 462, 152], [98, 154, 161, 179], [196, 157, 491, 175], [98, 179, 161, 204], [192, 181, 423, 204], [98, 204, 161, 229], [192, 204, 266, 231], [98, 229, 162, 256], [192, 227, 320, 258], [98, 256, 161, 281], [192, 257, 556, 281], [98, 281, 161, 306], [196, 284, 310, 304], [98, 306, 161, 332], [194, 307, 727, 331], [98, 332, 161, 358], [196, 336, 428, 354], [98, 358, 157, 383], [194, 359, 412, 383], [98, 383, 155, 409], [194, 384, 393, 408], [95, 403, 156, 435], [194, 409, 443, 433], [98, 434, 155, 459], [192, 434, 474, 458], [98, 458, 155, 484], [194, 463, 399, 481], [98, 484, 153, 509], [194, 486, 404, 509], [98, 511, 155, 536], [194, 511, 567, 534], [98, 536, 153, 561], [192, 536, 310, 561], [98, 561, 155, 588], [192, 561, 310, 586], [98, 586, 155, 611], [194, 588, 438, 611], [98, 611, 155, 638], [192, 613, 377, 636], [98, 638, 155, 663], [192, 640, 408, 663], [98, 663, 155, 688], [192, 663, 301, 688], [98, 688, 155, 715], [188, 686, 227, 717], [98, 713, 157, 740], [190, 713, 262, 740], [98, 740, 157, 765], [192, 740, 689, 763], [98, 765, 157, 790], [192, 765, 325, 790], [98, 790, 157, 817], [192, 792, 294, 817], [98, 815, 157, 842], [190, 815, 244, 844], [98, 840, 157, 867], [190, 842, 364, 867], [98, 867, 157, 892], [192, 869, 447, 892], [98, 892, 155, 919], [192, 894, 449, 917], [98, 917, 155, 944], [194, 919, 441, 944], [98, 944, 157, 969], [194, 946, 349, 969], [98, 969, 155, 996], [192, 971, 524, 994], [98, 994, 157, 1021], [192, 996, 332, 1021], [98, 1021, 157, 1046], [194, 1022, 508, 1046], [98, 1046, 157, 1071], [194, 1047, 606, 1071], [98, 1071, 157, 1096], [192, 1072, 731, 1096], [98, 1096, 157, 1123], [192, 1098, 347, 1121], [98, 1123, 157, 1148], [190, 1121, 367, 1148], [98, 1148, 155, 1174], [190, 1148, 504, 1174], [98, 1173, 157, 1199], [194, 1174, 412, 1198], [98, 1198, 157, 1224], [190, 1198, 456, 1225], [98, 1224, 157, 1249], [190, 1224, 260, 1251], [98, 1249, 157, 1274], [192, 1249, 430, 1273], [98, 1274, 157, 1301], [194, 1276, 482, 1300], [98, 1301, 157, 1326], [196, 1305, 639, 1323], [98, 1326, 157, 1351], [194, 1328, 460, 1351], [98, 1351, 157, 1376], [192, 1353, 476, 1376], [98, 1376, 157, 1403], [192, 1378, 500, 1401], [100, 1403, 157, 1428], [192, 1403, 428, 1426], [100, 1428, 157, 1453], [192, 1428, 327, 1453], [98, 1453, 155, 1478], [192, 1455, 356, 1478], [98, 1478, 157, 1505], [192, 1480, 314, 1505], [98, 1505, 157, 1530], [190, 1503, 242, 1532], [98, 1530, 157, 1555], [192, 1530, 332, 1555], [98, 1555, 155, 1582], [188, 1553, 264, 1586], [98, 1582, 157, 1607], [190, 1578, 370, 1611], [98, 1607, 157, 1632], [192, 1607, 401, 1632], [100, 1632, 157, 1657], [192, 1634, 375, 1657], [100, 1657, 157, 1684], [192, 1659, 478, 1682], [100, 1682, 155, 1709], [192, 1684, 286, 1712]]}, "table_res_list": [{"cell_box_list": [[98.4310302734375, 75.90805315971375, 170.47595977783203, 127.01651763916016], [194.07169342041016, 74.90940117835999, 767.2018203735352, 103.01248168945312], [42.870567321777344, 101.72949981689453, 70.04915618896484, 126.97485733032227], [70.03086471557617, 101.76371383666992, 98.52132415771484, 127.04427337646484], [192.0, 102.0, 270.0, 129.0], [69.98562240600586, 127.08510208129883, 98.47930908203125, 152.6848602294922], [98.37953186035156, 127.0469741821289, 170.52050018310547, 152.59251403808594], [193.41458892822266, 126.38352584838867, 767.4178848266602, 153.4791030883789], [42.80075645446777, 152.50193786621094, 69.97784423828125, 178.3448944091797], [69.9581298828125, 152.55747985839844, 98.41415405273438, 178.40530395507812], [98.27442932128906, 152.45970916748047, 170.61621856689453, 178.56790161132812], [192.39220428466797, 152.9906768798828, 767.4393081665039, 178.91513061523438], [42.770307540893555, 178.1659164428711, 69.93019485473633, 203.92674255371094], [69.92691802978516, 178.18570709228516, 98.41565704345703, 229.35873413085938], [98.34410858154297, 178.2771759033203, 170.71720123291016, 204.2721405029297], [192.0, 181.0, 423.0, 204.0], [42.770986557006836, 203.75323486328125, 69.89491271972656, 229.3004150390625], [98.27365112304688, 204.0913543701172, 170.96651458740234, 255.17425537109375], [191.0897445678711, 204.29376220703125, 767.1688003540039, 229.5338592529297], [69.92436599731445, 229.42080688476562, 98.43118286132812, 255.09437561035156], [191.1103744506836, 229.6138916015625, 767.2634048461914, 255.67398071289062], [42.715415954589844, 255.00765991210938, 69.93074417114258, 280.83665466308594], [69.90034484863281, 254.94891357421875, 98.39651489257812, 280.8057403564453], [98.25553894042969, 254.98780822753906, 171.2022476196289, 280.8301544189453], [192.0, 257.0, 556.0, 281.0], [42.71273612976074, 280.7057647705078, 69.89121627807617, 306.41078186035156], [69.88666152954102, 280.64892578125, 98.37635803222656, 306.41160583496094], [98.30741119384766, 280.60662841796875, 171.41710662841797, 306.57713317871094], [191.7315902709961, 280.8648681640625, 767.3862075805664, 307.2486572265625], [42.738792419433594, 306.30650329589844, 69.87725830078125, 331.86729431152344], [69.91334533691406, 306.2610321044922, 98.35426330566406, 331.9943389892578], [98.26307678222656, 306.45587158203125, 171.42760467529297, 332.15065002441406], [194.0, 307.0, 727.0, 331.0], [69.91270446777344, 332.00328063964844, 98.42774963378906, 357.6165008544922], [98.30323791503906, 332.07252502441406, 171.6146011352539, 357.62718200683594], [191.7487564086914, 332.6374969482422, 767.4211196899414, 358.1809539794922], [42.696231842041016, 357.52415466308594, 69.92042922973633, 383.3902130126953], [69.89514541625977, 357.4933319091797, 98.41000366210938, 383.36436462402344], [98.26358795166016, 357.5307159423828, 171.69300079345703, 383.39952087402344], [194.0, 359.0, 412.0, 383.0], [42.72314643859863, 383.28077697753906, 69.91228866577148, 408.9939422607422], [69.91235733032227, 383.2157745361328, 98.41128540039062, 408.98484802246094], [98.27864837646484, 383.1979522705078, 171.7468032836914, 409.0606231689453], [194.0, 384.0, 393.0, 408.0], [42.76701545715332, 408.9104461669922, 69.92093658447266, 434.4168243408203], [69.9465446472168, 408.85279846191406, 98.39488220214844, 434.45716857910156], [98.2599868774414, 409.0095977783203, 171.82843780517578, 485.2851104736328], [192.5956802368164, 409.11216735839844, 767.4573135375977, 434.1499786376953], [69.92310333251953, 434.45433044433594, 98.4504623413086, 485.3128204345703], [192.64705657958984, 434.5476531982422, 767.5318374633789, 460.23374938964844], [42.73402214050293, 459.78990173339844, 69.9548110961914, 485.3300018310547], [192.65229034423828, 460.41310119628906, 767.4840469360352, 485.45509338378906], [69.92640686035156, 485.1145782470703, 98.45148468017578, 535.4405670166016], [98.29806518554688, 485.1388702392578, 171.85796356201172, 561.1099090576172], [192.90929412841797, 485.6833038330078, 767.3848648071289, 511.26988220214844], [42.81427192687988, 510.34446716308594, 69.95616912841797, 535.2854766845703], [194.0, 511.0, 567.0, 534.0], [69.94761276245117, 535.3607940673828, 98.50751495361328, 586.1374664306641], [192.75273895263672, 535.8635406494141, 767.5697402954102, 561.2567596435547], [42.84080505371094, 560.5346221923828, 70.02185821533203, 586.2183990478516], [98.3281478881836, 560.8393707275391, 171.77535247802734, 611.8391876220703], [192.53775787353516, 562.1179046630859, 767.4816665649414, 587.2083892822266], [69.92068099975586, 586.1452178955078, 98.43600463867188, 637.1394195556641], [194.0, 588.0, 438.0, 611.0], [42.87009811401367, 611.5390167236328, 69.9735336303711, 637.0821075439453], [98.278564453125, 611.7980499267578, 171.8311996459961, 637.4201202392578], [192.70389556884766, 612.2905426025391, 767.4694595336914, 636.9558258056641], [70.01461029052734, 637.1358795166016, 98.49747467041016, 662.7212066650391], [98.35596466064453, 637.2832794189453, 171.8036117553711, 662.9483795166016], [192.71549224853516, 637.5128936767578, 767.5063247680664, 663.5102691650391], [42.88932228088379, 662.4733428955078, 70.03268814086914, 688.4456329345703], [69.94707489013672, 662.4626007080078, 98.40375518798828, 688.4281768798828], [98.31195068359375, 662.6985626220703, 171.70868682861328, 688.5700836181641], [192.0, 663.0, 301.0, 688.0], [69.89476776123047, 688.4233551025391, 98.41449737548828, 713.9883575439453], [98.3047866821289, 688.4604644775391, 171.72945404052734, 714.1103668212891], [192.5254898071289, 688.5877227783203, 767.6079483032227, 714.4683380126953], [42.9005069732666, 713.8457183837891, 69.9544677734375, 739.5657501220703], [69.91051864624023, 713.8381500244141, 98.3938217163086, 739.5666656494141], [98.26073455810547, 714.0333404541016, 171.71509552001953, 764.9138336181641], [190.0, 713.0, 262.0, 740.0], [42.9449462890625, 739.6858673095703, 70.05307388305664, 765.1606597900391], [70.00070190429688, 739.5576934814453, 98.46212768554688, 765.0675201416016], [192.78076934814453, 740.1401519775391, 767.8279190063477, 765.6398468017578], [42.93932342529297, 764.9111480712891, 70.04168319702148, 790.8968048095703], [69.95280456542969, 764.8480987548828, 98.38739013671875, 790.8377838134766], [98.29207611083984, 764.8158111572266, 171.5014419555664, 790.8798370361328], [192.7323989868164, 765.9848785400391, 767.6185073852539, 790.9627838134766], [69.94058609008789, 790.8329010009766, 98.38655090332031, 816.6164703369141], [98.35057830810547, 790.8778228759766, 171.65164947509766, 816.4997711181641], [192.0, 792.0, 294.0, 817.0], [42.92459297180176, 816.3055572509766, 69.96742248535156, 842.0423736572266], [69.93196105957031, 816.4140777587891, 98.40950775146484, 842.0990753173828], [98.30732727050781, 816.4801177978516, 171.60486602783203, 842.0094757080078], [192.59259796142578, 816.8908843994141, 767.5174331665039, 841.5421905517578], [70.00570297241211, 841.8901519775391, 98.46232604980469, 867.5795440673828], [98.33909606933594, 842.1097564697266, 171.5524673461914, 893.3173980712891], [192.62950897216797, 842.2133331298828, 767.6175308227539, 868.0755157470703], [42.908023834228516, 867.3742218017578, 70.0491828918457, 893.3589630126953], [69.95627212524414, 867.4290924072266, 98.41926574707031, 893.2967071533203], [192.0, 869.0, 447.0, 892.0], [69.9013786315918, 893.3025665283203, 98.45030975341797, 918.9178009033203], [98.37799835205078, 893.2956085205078, 171.64745330810547, 919.0121002197266], [192.7135238647461, 893.4859161376953, 767.3201675415039, 919.5281524658203], [42.853403091430664, 918.8206329345703, 69.97705078125, 944.5169830322266], [69.9272232055664, 918.7724151611328, 98.44506072998047, 944.5532379150391], [98.33834075927734, 918.9265899658203, 171.56331634521484, 944.6491241455078], [194.0, 919.0, 441.0, 944.0], [69.91023254394531, 944.5306549072266, 98.5160903930664, 995.6332550048828], [98.39537811279297, 944.6831207275391, 171.60663604736328, 970.2367706298828], [192.57323455810547, 944.6557769775391, 767.7667617797852, 970.4350128173828], [98.36842346191406, 970.0570220947266, 171.59876251220703, 995.5656890869141], [192.49422454833984, 970.5762481689453, 767.6656265258789, 995.9514312744141], [98.43919372558594, 995.6002960205078, 171.78203582763672, 1021.0264434814453], [192.0, 996.0, 332.0, 1021.0], [69.8836555480957, 1020.7754669189453, 98.41851806640625, 1070.8909454345703], [98.0, 1021.0, 157.0, 1046.0], [192.8943862915039, 1021.2036895751953, 767.5088882446289, 1046.2769927978516], [98.26200103759766, 1046.2501983642578, 171.79125213623047, 1071.0706939697266], [194.0, 1047.0, 606.0, 1071.0], [98.0, 1071.0, 157.0, 1096.0], [192.90511322021484, 1071.170913696289, 767.4835586547852, 1097.2211456298828], [69.85322570800781, 1096.073501586914, 98.37592315673828, 1147.1966094970703], [98.25988006591797, 1096.3916778564453, 171.74588775634766, 1147.5303497314453], [192.79666900634766, 1097.7312774658203, 767.8279190063477, 1121.6764678955078], [190.0, 1121.0, 367.0, 1148.0], [69.88856506347656, 1146.9983673095703, 98.33113861083984, 1172.5761260986328], [98.24514770507812, 1147.4197540283203, 171.70214080810547, 1198.2978057861328], [193.02886199951172, 1147.9369659423828, 767.5467910766602, 1172.8853302001953], [69.88562774658203, 1172.6021270751953, 98.39319610595703, 1198.1236114501953], [192.97913360595703, 1173.1453399658203, 767.7347183227539, 1198.9352569580078], [69.85247802734375, 1198.0777130126953, 98.3758316040039, 1223.9164581298828], [98.26731872558594, 1198.1923370361328, 171.74427032470703, 1223.9762725830078], [190.0, 1198.0, 456.0, 1225.0], [42.7127685546875, 1223.8318634033203, 69.8694953918457, 1249.5351104736328], [69.84239196777344, 1223.7697296142578, 98.37417602539062, 1249.5218048095703], [98.34410095214844, 1223.8319854736328, 171.68347930908203, 1249.8279571533203], [190.0, 1224.0, 260.0, 1251.0], [69.86659622192383, 1249.3502960205078, 98.31855773925781, 1275.0106964111328], [98.2762222290039, 1249.6527862548828, 171.6933364868164, 1300.7375030517578], [192.0, 1249.0, 430.0, 1273.0], [69.859375, 1275.0324249267578, 98.38597869873047, 1300.6099395751953], [192.7674331665039, 1275.5690460205078, 767.4787368774414, 1301.2284698486328], [42.72088813781738, 1300.6245880126953, 69.90762710571289, 1326.4799346923828], [69.84110641479492, 1300.5738067626953, 98.37611389160156, 1326.4193878173828], [98.2753677368164, 1300.6808624267578, 171.7738265991211, 1326.5077667236328], [192.61492156982422, 1301.3993682861328, 767.7212295532227, 1326.7670440673828], [42.73032569885254, 1326.3726348876953, 69.87916564941406, 1352.0370635986328], [69.8433723449707, 1326.3018341064453, 98.36466979980469, 1352.0052032470703], [98.30374908447266, 1326.2831573486328, 171.77704620361328, 1352.1833038330078], [194.0, 1328.0, 460.0, 1351.0], [42.73630332946777, 1351.9135284423828, 69.89299774169922, 1377.5270538330078], [69.88362884521484, 1351.8830108642578, 98.3192138671875, 1377.5337677001953], [98.25921630859375, 1352.0699005126953, 171.77603912353516, 1403.1664581298828], [192.57674407958984, 1352.4910430908203, 767.5164566040039, 1377.5150909423828], [69.87751007080078, 1377.5840606689453, 98.38174438476562, 1403.0894317626953], [192.6527328491211, 1378.0069122314453, 767.4876480102539, 1403.6110382080078], [42.703718185424805, 1403.0948028564453, 69.93405151367188, 1428.9608917236328], [69.84880447387695, 1403.0449981689453, 98.3741226196289, 1428.8900909423828], [98.2624740600586, 1403.0824737548828, 171.75533294677734, 1428.8687286376953], [192.0, 1403.0, 428.0, 1426.0], [42.705169677734375, 1428.8275909423828, 69.89802169799805, 1454.5154571533203], [69.85856628417969, 1428.7620391845703, 98.3856430053711, 1454.4679718017578], [98.3294906616211, 1428.6822052001953, 171.8061752319336, 1454.5613555908203], [192.93025970458984, 1429.0753936767578, 767.6718521118164, 1454.9998321533203], [42.7169189453125, 1454.4138946533203, 69.9067611694336, 1479.8629913330078], [69.88253784179688, 1454.3520050048828, 98.32794189453125, 1479.9151153564453], [98.2581558227539, 1454.4712677001953, 171.8003158569336, 1480.0054473876953], [192.0, 1455.0, 356.0, 1478.0], [98.29545593261719, 1480.0000762939453, 171.7685317993164, 1505.4545440673828], [192.7510757446289, 1480.1761016845703, 767.6612930297852, 1506.0552520751953], [42.68242263793945, 1505.3291778564453, 69.9442367553711, 1530.8980255126953], [69.8490104675293, 1505.3314971923828, 98.38207244873047, 1530.8325958251953], [98.28465270996094, 1505.3424835205078, 171.93810272216797, 1556.1040802001953], [192.7558364868164, 1506.1236114501953, 767.5316543579102, 1531.1094512939453], [69.85084915161133, 1530.7152862548828, 98.44108581542969, 1606.0345001220703], [192.96561431884766, 1531.2609405517578, 767.8279190063477, 1580.6265411376953], [42.777170181274414, 1555.9673614501953, 69.92398452758789, 1580.6885528564453], [98.2977066040039, 1556.0919952392578, 171.8841323852539, 1606.5308380126953], [192.98755645751953, 1581.0021514892578, 767.5687637329102, 1606.7798614501953], [42.78683280944824, 1605.9175567626953, 70.01541900634766, 1631.7401885986328], [69.89823532104492, 1605.8983917236328, 98.42371368408203, 1631.7088165283203], [98.28544616699219, 1606.3040313720703, 171.7196273803711, 1632.1243438720703], [192.83856964111328, 1606.9292755126953, 767.2432632446289, 1632.1316680908203], [69.89497375488281, 1631.6387481689453, 98.44168853759766, 1657.0982208251953], [98.35570526123047, 1631.7838897705078, 171.68529510498047, 1657.6155548095703], [193.1012954711914, 1632.0567169189453, 767.2128067016602, 1657.8446807861328], [69.99206161499023, 1656.8997344970703, 98.44955444335938, 1682.3205108642578], [98.3130874633789, 1657.2948760986328, 171.52025604248047, 1683.1324005126953], [192.9548568725586, 1657.7687530517578, 767.0754776000977, 1683.2096710205078], [100.0, 1682.0, 155.0, 1709.0], [192.0, 1684.0, 286.0, 1712.0]], "pred_html": "<html><body><table><tbody><tr><td>0.000 4.479</td><td>厦部第1腰椎水平截面</td><td></td></tr><tr><td></td><td></td><td>优化配置</td></tr><tr><td></td><td></td><td>虚拟模式-健康问题发展趋势列表：</td></tr><tr><td></td><td></td><td>0.054</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.072</td></tr><tr><td></td><td>0.074 0.075</td><td>脂肪酶*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.087</td></tr><tr><td></td><td></td><td>0.099</td></tr><tr><td></td><td></td><td>0.101</td></tr><tr><td></td><td>0.161</td><td>血钾PLASMAPOTASSIUM</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.095</td></tr><tr><td></td><td></td><td>0.097</td></tr><tr><td></td><td></td><td>0.103 0.105 0.107</td></tr><tr><td></td><td>PERIPHERIC BLOOD LEUCOCYTES</td><td></td></tr><tr><td></td><td>嗜碱性粒细胞BASOPHILS</td><td></td></tr><tr><td></td><td>0.107 0.114 0.131</td><td>血红血球ERYTHROCYTES</td></tr><tr><td></td><td>免疫球蛋白G*</td><td></td></tr><tr><td></td><td>0.132 0.133</td><td>免疫球蛋白M*</td></tr><tr><td></td><td>尿白血球URINELEUCOCYTES</td><td></td></tr><tr><td></td><td>0.135</td><td>单核细胞MONOCYTES</td></tr><tr><td></td><td>0.143</td><td>血清蛋白SERUMPROTEIN</td></tr><tr><td></td><td></td><td>0.147</td></tr><tr><td></td><td>0.201</td><td>锂*</td></tr><tr><td></td><td></td><td>0.092 0.093</td></tr><tr><td></td><td></td><td>ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.099</td></tr><tr><td></td><td>0.102</td><td>胰高血糖素</td></tr><tr><td></td><td></td><td>0.104</td></tr><tr><td></td><td>0.105 0.106</td><td>红细胞沉降率(ESR)</td></tr><tr><td></td><td></td><td>血清补体SERUMCOMPLEMENT</td></tr><tr><td></td><td>0.107</td><td>肿瘤标志物MELANOGENE在尿*</td></tr><tr><td></td><td></td><td>0.107</td></tr><tr><td></td><td>0.109</td><td>血细胞比容，全血</td></tr><tr><td>0.111</td><td>游离胆固醇FREEPLASMACHOLESTERIN</td><td></td></tr><tr><td>0.113</td><td>糖基化血红蛋白*</td><td></td></tr><tr><td></td><td>0.114</td><td>血清铜蓝蛋白SERUMCERULOPLASMIN</td></tr><tr><td>0.115</td><td>AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE</td><td></td></tr><tr><td></td><td>0.116 0.116</td><td>RHEUMOFACTOR*</td></tr><tr><td></td><td>0.117 0.117</td><td>血清淀粉酵素SERUMALPHAAMYLASE</td></tr><tr><td></td><td>尿肌酥URINECREATININE</td><td></td></tr><tr><td></td><td>0.117</td><td>伽马球蛋白GAMMA-GLOBULINS</td></tr><tr><td></td><td></td><td>0.117</td></tr><tr><td></td><td>0.118 0.118</td><td>嗜酸性粒细胞EOSINOPHILES</td></tr><tr><td></td><td>促肾上腺皮质激素CORTICOTROPIN</td><td></td></tr><tr><td></td><td></td><td>0.118</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.118</td></tr><tr><td></td><td></td><td>0.118 0.119</td></tr><tr><td></td><td>生长激素SOMATOTROPICHORMONE</td><td></td></tr><tr><td></td><td></td><td>0.119</td></tr><tr><td></td><td></td><td>0.119</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.120</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.120 0.120</td></tr><tr><td></td><td>0.121 0.121</td><td>ALPHA1-抗胰蛋白酶*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.123</td></tr><tr><td></td><td>0.126</td><td>DELTA氨基乙酰丙酸*</td></tr><tr><td></td><td>0.126</td><td>血浆磷脂PLASMA PHOSPHOTIDES</td></tr></tbody></table></body></html>", "table_ocr_pred": {"rec_polys": [[[100, 77], [157, 77], [157, 102], [100, 102]], [[192, 79], [384, 79], [384, 102], [192, 102]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[196, 157], [491, 157], [491, 175], [196, 175]], [[98, 179], [161, 179], [161, 204], [98, 204]], [[192, 181], [423, 181], [423, 204], [192, 204]], [[98, 204], [161, 204], [161, 229], [98, 229]], [[192, 204], [266, 204], [266, 231], [192, 231]], [[98, 229], [162, 229], [162, 256], [98, 256]], [[192, 231], [319, 227], [320, 254], [193, 258]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [556, 257], [556, 281], [192, 281]], [[98, 281], [161, 281], [161, 306], [98, 306]], [[196, 284], [310, 284], [310, 304], [196, 304]], [[98, 306], [161, 306], [161, 332], [98, 332]], [[194, 307], [727, 307], [727, 331], [194, 331]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [428, 336], [428, 354], [196, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[194, 359], [412, 359], [412, 383], [194, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[194, 384], [393, 384], [393, 408], [194, 408]], [[98, 403], [156, 408], [153, 435], [95, 430]], [[194, 409], [443, 409], [443, 433], [194, 433]], [[98, 434], [155, 434], [155, 459], [98, 459]], [[192, 434], [474, 434], [474, 458], [192, 458]], [[98, 458], [155, 458], [155, 484], [98, 484]], [[194, 463], [399, 463], [399, 481], [194, 481]], [[98, 484], [153, 484], [153, 509], [98, 509]], [[194, 486], [404, 486], [404, 509], [194, 509]], [[98, 511], [155, 511], [155, 536], [98, 536]], [[194, 511], [567, 511], [567, 534], [194, 534]], [[98, 536], [153, 536], [153, 561], [98, 561]], [[192, 536], [310, 536], [310, 561], [192, 561]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[192, 561], [310, 561], [310, 586], [192, 586]], [[98, 586], [155, 586], [155, 611], [98, 611]], [[194, 588], [438, 588], [438, 611], [194, 611]], [[98, 611], [155, 611], [155, 638], [98, 638]], [[192, 613], [377, 613], [377, 636], [192, 636]], [[98, 638], [155, 638], [155, 663], [98, 663]], [[192, 640], [408, 640], [408, 663], [192, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[192, 663], [301, 663], [301, 688], [192, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[188, 686], [227, 686], [227, 717], [188, 717]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[190, 713], [262, 713], [262, 740], [190, 740]], [[98, 740], [157, 740], [157, 765], [98, 765]], [[192, 740], [689, 740], [689, 763], [192, 763]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[192, 765], [325, 765], [325, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [294, 792], [294, 817], [192, 817]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[190, 815], [244, 815], [244, 844], [190, 844]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[190, 842], [364, 842], [364, 867], [190, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [447, 869], [447, 892], [192, 892]], [[98, 892], [155, 892], [155, 919], [98, 919]], [[192, 894], [449, 894], [449, 917], [192, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[194, 919], [441, 919], [441, 944], [194, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [349, 946], [349, 969], [194, 969]], [[98, 969], [155, 969], [155, 996], [98, 996]], [[192, 971], [524, 971], [524, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [332, 996], [332, 1021], [192, 1021]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[194, 1022], [508, 1022], [508, 1046], [194, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[194, 1047], [606, 1047], [606, 1071], [194, 1071]], [[98, 1071], [157, 1071], [157, 1096], [98, 1096]], [[192, 1072], [731, 1072], [731, 1096], [192, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [347, 1098], [347, 1121], [192, 1121]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[190, 1123], [367, 1121], [367, 1146], [190, 1148]], [[98, 1148], [155, 1148], [155, 1174], [98, 1174]], [[190, 1149], [504, 1148], [504, 1173], [190, 1174]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[194, 1174], [412, 1174], [412, 1198], [194, 1198]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [456, 1198], [456, 1223], [190, 1225]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[190, 1224], [260, 1224], [260, 1251], [190, 1251]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1249], [430, 1249], [430, 1273], [192, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[194, 1276], [482, 1276], [482, 1300], [194, 1300]], [[98, 1301], [157, 1301], [157, 1326], [98, 1326]], [[196, 1305], [639, 1305], [639, 1323], [196, 1323]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[194, 1328], [460, 1328], [460, 1351], [194, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [476, 1353], [476, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1378], [500, 1378], [500, 1401], [192, 1401]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [428, 1403], [428, 1426], [192, 1426]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[192, 1428], [327, 1428], [327, 1453], [192, 1453]], [[98, 1453], [155, 1453], [155, 1478], [98, 1478]], [[192, 1455], [356, 1455], [356, 1478], [192, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[192, 1480], [314, 1480], [314, 1505], [192, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[190, 1503], [242, 1503], [242, 1532], [190, 1532]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[192, 1530], [332, 1530], [332, 1555], [192, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[188, 1553], [264, 1553], [264, 1586], [188, 1586]], [[98, 1582], [157, 1582], [157, 1607], [98, 1607]], [[191, 1578], [370, 1584], [369, 1611], [190, 1605]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1607], [401, 1607], [401, 1632], [192, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[192, 1634], [375, 1634], [375, 1657], [192, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1659], [478, 1659], [478, 1682], [192, 1682]], [[100, 1682], [155, 1682], [155, 1709], [100, 1709]], [[192, 1684], [286, 1684], [286, 1712], [192, 1712]]], "rec_texts": ["0.000", "厦部第1腰椎水平截面", "4.479", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.054", "C反应蛋白C-REACTIVEPROTEIN", "0.072", "血尿酸SERUMURICACID", "0.074", "脂肪酶*", "0.075", "血管紧张素Ⅱ*", "0.087", "胆固醇COMMONPLASMA CHOLESTERIN", "0.099", "血管紧张素I", "0.101", "血浆丰配化脂肪酸NONETHERIZED FATTYACIDSOF PLASMA", "0.161", "血钾PLASMAPOTASSIUM", "0.095", "血清蛋白SERUM ALBUMEN", "0.097", "血红蛋白HAEMOGLOBIN", "0.103", "尿中蛋白质PROTEININURINE", "0.105", "PERIPHERIC BLOOD LEUCOCYTES", "0.107", "嗜碱性粒细胞BASOPHILS", "0.107", "血红血球ERYTHROCYTES", "0.114", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.131", "免疫球蛋白G*", "0.132", "免疫球蛋白M*", "0.133", "尿白血球URINELEUCOCYTES", "0.135", "单核细胞MONOCYTES", "0.143", "血清蛋白SERUMPROTEIN", "0.147", "BETA球蛋白", "0.201", "锂*", "0.092", "胆汁酸*", "0.093", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.099", "ALPHA2球蛋白*", "0.102", "胰高血糖素", "0.104", "糖苷*", "0.105", "红细胞沉降率(ESR)", "0.106", "血清补体SERUMCOMPLEMENT", "0.107", "肿瘤标志物MELANOGENE在尿*", "0.107", "血清溶菌酵SERUMLYSOZYME", "0.109", "血细胞比容，全血", "0.111", "游离胆固醇FREEPLASMACHOLESTERIN", "0.113", "糖基化血红蛋白*", "0.114", "血清铜蓝蛋白SERUMCERULOPLASMIN", "0.115", "肌酸磷酸酵素COMMON CREATINPHOSPHOKINASE", "0.115", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.116", "RHEUMOFACTOR*", "0.116", "肿瘤标志物胸苷激酶", "0.117", "血清淀粉酵素SERUMALPHAAMYLASE", "0.117", "尿肌酥URINECREATININE", "0.117", "伽马球蛋白GAMMA-GLOBULINS", "0.117", "铁蛋白*", "0.118", "嗜酸性粒细胞EOSINOPHILES", "0.118", "促肾上腺皮质激素CORTICOTROPIN", "0.118", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.118", "尿中肾上腺素URINEADRENALIN", "0.118", "嗜中性粒细胞STABNEUTROPHILS", "0.119", "生长激素SOMATOTROPICHORMONE", "0.119", "血组织胺BLOODHISTAMINE", "0.119", "ALPHA1球蛋白*", "0.120", "血糖BLOOD SUGAR", "0.120", "甲状腺球蛋白*", "0.120", "肾素*", "0.120", "抗链球菌溶血素*", "0.121", "催乳素*", "0.121", "ALPHA1-抗胰蛋白酶*", "0.123", "维生素B1（THIAMINE）", "0.126", "DELTA氨基乙酰丙酸*", "0.126", "血浆磷脂PLASMA PHOSPHOTIDES", "0.127", "维生素B6*"], "rec_scores": [0.9991452097892761, 0.9235565066337585, 0.9989500045776367, 0.9983981251716614, 0.9667975902557373, 0.9999244809150696, 0.9659162163734436, 0.9999293088912964, 0.9965614080429077, 0.9998855590820312, 0.9802283644676208, 0.9999462366104126, 0.9428358674049377, 0.999926745891571, 0.9795368313789368, 0.9999297857284546, 0.9789831638336182, 0.9998544454574585, 0.9332330822944641, 0.9999052286148071, 0.9812120795249939, 0.999720573425293, 0.9809539914131165, 0.9997221231460571, 0.9985331296920776, 0.9998520016670227, 0.9969345927238464, 0.9995471239089966, 0.9688208699226379, 0.999531626701355, 0.9848082661628723, 0.9992643594741821, 0.9961680173873901, 0.9993475675582886, 0.9929898977279663, 0.9995128512382507, 0.9875523447990417, 0.999610424041748, 0.9918586611747742, 0.9995530843734741, 0.9923917055130005, 0.9996044039726257, 0.9973767995834351, 0.9995597004890442, 0.9954931735992432, 0.9992172122001648, 0.992581307888031, 0.9994300603866577, 0.9606835842132568, 0.9998161196708679, 0.969925045967102, 0.9997072219848633, 0.9949657917022705, 0.9996426701545715, 0.9461562037467957, 0.9996402859687805, 0.9972966909408569, 0.9994826316833496, 0.973040759563446, 0.9995371103286743, 0.9220654964447021, 0.9993749856948853, 0.9972162842750549, 0.9995378255844116, 0.993736982345581, 0.9995242357254028, 0.9920037388801575, 0.9994146227836609, 0.9945255517959595, 0.9985436201095581, 0.9800680875778198, 0.9995222091674805, 0.9608697891235352, 0.9990524053573608, 0.9947469830513, 0.9994711875915527, 0.9837502241134644, 0.9992499351501465, 0.9963960647583008, 0.9994398951530457, 0.9896174669265747, 0.9991324543952942, 0.9871059060096741, 0.9992870092391968, 0.9952670931816101, 0.999285101890564, 0.9578018188476562, 0.9991861581802368, 0.9962862133979797, 0.9991371035575867, 0.908932089805603, 0.9994661211967468, 0.9942595362663269, 0.999498188495636, 0.9984483122825623, 0.9994878768920898, 0.9974063634872437, 0.9993160963058472, 0.9933616518974304, 0.9994661211967468, 0.9978678822517395, 0.9994169473648071, 0.9967248439788818, 0.9988118410110474, 0.9917929172515869, 0.9987851977348328, 0.9760702848434448, 0.9992280006408691, 0.9667790532112122, 0.99952632188797, 0.9408621191978455, 0.9994872212409973, 0.8973817825317383, 0.9993170499801636, 0.9738208055496216, 0.9995825886726379, 0.9844926595687866, 0.9994697570800781, 0.9883496165275574, 0.9995576739311218, 0.9556136727333069, 0.9991583824157715, 0.9539235234260559, 0.9992308616638184, 0.9760136008262634, 0.9992958903312683, 0.9817430377006531], "rec_boxes": [[100, 77, 157, 102], [192, 79, 384, 102], [98, 102, 157, 127], [192, 102, 270, 129], [194, 129, 462, 152], [98, 154, 161, 179], [196, 157, 491, 175], [98, 179, 161, 204], [192, 181, 423, 204], [98, 204, 161, 229], [192, 204, 266, 231], [98, 229, 162, 256], [192, 227, 320, 258], [98, 256, 161, 281], [192, 257, 556, 281], [98, 281, 161, 306], [196, 284, 310, 304], [98, 306, 161, 332], [194, 307, 727, 331], [98, 332, 161, 358], [196, 336, 428, 354], [98, 358, 157, 383], [194, 359, 412, 383], [98, 383, 155, 409], [194, 384, 393, 408], [95, 403, 156, 435], [194, 409, 443, 433], [98, 434, 155, 459], [192, 434, 474, 458], [98, 458, 155, 484], [194, 463, 399, 481], [98, 484, 153, 509], [194, 486, 404, 509], [98, 511, 155, 536], [194, 511, 567, 534], [98, 536, 153, 561], [192, 536, 310, 561], [98, 561, 155, 588], [192, 561, 310, 586], [98, 586, 155, 611], [194, 588, 438, 611], [98, 611, 155, 638], [192, 613, 377, 636], [98, 638, 155, 663], [192, 640, 408, 663], [98, 663, 155, 688], [192, 663, 301, 688], [98, 688, 155, 715], [188, 686, 227, 717], [98, 713, 157, 740], [190, 713, 262, 740], [98, 740, 157, 765], [192, 740, 689, 763], [98, 765, 157, 790], [192, 765, 325, 790], [98, 790, 157, 817], [192, 792, 294, 817], [98, 815, 157, 842], [190, 815, 244, 844], [98, 840, 157, 867], [190, 842, 364, 867], [98, 867, 157, 892], [192, 869, 447, 892], [98, 892, 155, 919], [192, 894, 449, 917], [98, 917, 155, 944], [194, 919, 441, 944], [98, 944, 157, 969], [194, 946, 349, 969], [98, 969, 155, 996], [192, 971, 524, 994], [98, 994, 157, 1021], [192, 996, 332, 1021], [98, 1021, 157, 1046], [194, 1022, 508, 1046], [98, 1046, 157, 1071], [194, 1047, 606, 1071], [98, 1071, 157, 1096], [192, 1072, 731, 1096], [98, 1096, 157, 1123], [192, 1098, 347, 1121], [98, 1123, 157, 1148], [190, 1121, 367, 1148], [98, 1148, 155, 1174], [190, 1148, 504, 1174], [98, 1173, 157, 1199], [194, 1174, 412, 1198], [98, 1198, 157, 1224], [190, 1198, 456, 1225], [98, 1224, 157, 1249], [190, 1224, 260, 1251], [98, 1249, 157, 1274], [192, 1249, 430, 1273], [98, 1274, 157, 1301], [194, 1276, 482, 1300], [98, 1301, 157, 1326], [196, 1305, 639, 1323], [98, 1326, 157, 1351], [194, 1328, 460, 1351], [98, 1351, 157, 1376], [192, 1353, 476, 1376], [98, 1376, 157, 1403], [192, 1378, 500, 1401], [100, 1403, 157, 1428], [192, 1403, 428, 1426], [100, 1428, 157, 1453], [192, 1428, 327, 1453], [98, 1453, 155, 1478], [192, 1455, 356, 1478], [98, 1478, 157, 1505], [192, 1480, 314, 1505], [98, 1505, 157, 1530], [190, 1503, 242, 1532], [98, 1530, 157, 1555], [192, 1530, 332, 1555], [98, 1555, 155, 1582], [188, 1553, 264, 1586], [98, 1582, 157, 1607], [190, 1578, 370, 1611], [98, 1607, 157, 1632], [192, 1607, 401, 1632], [100, 1632, 157, 1657], [192, 1634, 375, 1657], [100, 1657, 157, 1684], [192, 1659, 478, 1682], [100, 1682, 155, 1709], [192, 1684, 286, 1712]]}}]}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//7257b620-1784-4e96-a4bc-01c11b3c4757/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T09%3A15%3A50Z%2F-1%2F%2F53e42272c99daa798b11d09ec7a3cc8c1131302799a434687f472d3ab6f15b79", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//7257b620-1784-4e96-a4bc-01c11b3c4757/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T09%3A15%3A50Z%2F-1%2F%2F1d0fbc76af61052bc01fd68ee73583686d59a31dbe6e16951b2c729e764a6c73", "table_cell_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//7257b620-1784-4e96-a4bc-01c11b3c4757/table_cell_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T09%3A15%3A50Z%2F-1%2F%2F96802b22e172fa0bf7f1bdc18de8c91faf37aaf509e26d8ebd6490aff9285091"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//7257b620-1784-4e96-a4bc-01c11b3c4757/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T09%3A15%3A50Z%2F-1%2F%2F529513cbbdebd6ffefc1285262b8df7f3bb53bbb1e729875f583ebf17a698305"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}