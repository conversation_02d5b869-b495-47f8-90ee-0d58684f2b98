package services

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// SerialOCRProcessor 串行OCR处理器
// 专为单台设备设计，确保OCR请求按顺序执行，避免API服务器过载
type SerialOCRProcessor struct {
	ocrService  OCRInterface         // OCR服务
	resultChan  chan *OCRTaskResult  // 结果通道
	callbacks   []ResultCallback     // 结果回调函数列表
	ctx         context.Context      // 上下文
	cancel      context.CancelFunc   // 取消函数
	wg          sync.WaitGroup       // 等待组
	mu          sync.RWMutex         // 读写锁
	running     bool                 // 运行状态
	stats       *ProcessorStats      // 统计信息
	taskQueue   chan *ScreenshotTask // 任务队列
	currentUser string               // 当前处理的用户
	userQueue   []string             // 用户队列
	userMu      sync.Mutex           // 用户队列锁
	retryConfig RetryConfig          // 重试配置
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts       int     `json:"max_attempts"`       // 最大重试次数
	InitialDelayMs    int     `json:"initial_delay_ms"`   // 初始延迟（毫秒）
	MaxDelayMs        int     `json:"max_delay_ms"`       // 最大延迟（毫秒）
	BackoffMultiplier float64 `json:"backoff_multiplier"` // 退避倍数
}

// NewSerialOCRProcessor 创建新的串行OCR处理器
func NewSerialOCRProcessor(ocrService OCRInterface, configService ConfigServiceInterface) (*SerialOCRProcessor, error) {
	// 从配置中读取重试参数，使用更长的重试间隔
	config := configService.GetConfig()
	retryConfig := RetryConfig{
		MaxAttempts:       5,     // 增加最大重试次数到5次
		InitialDelayMs:    3000,  // 初始延迟从1秒增加到3秒
		MaxDelayMs:        30000, // 最大延迟从10秒增加到30秒
		BackoffMultiplier: 2.0,
	}

	// 如果配置中有重试设置，则使用配置值
	if config.Concurrency.Retry.MaxAttempts > 0 {
		retryConfig.MaxAttempts = config.Concurrency.Retry.MaxAttempts
	}
	if config.Concurrency.Retry.InitialDelayMs > 0 {
		retryConfig.InitialDelayMs = config.Concurrency.Retry.InitialDelayMs
	}
	if config.Concurrency.Retry.MaxDelayMs > 0 {
		retryConfig.MaxDelayMs = config.Concurrency.Retry.MaxDelayMs
	}
	if config.Concurrency.Retry.BackoffMultiplier > 0 {
		retryConfig.BackoffMultiplier = config.Concurrency.Retry.BackoffMultiplier
	}

	fmt.Printf("[串行OCR处理器] 初始化参数: maxAttempts=%d, initialDelay=%dms, maxDelay=%dms, backoff=%.1f\n",
		retryConfig.MaxAttempts, retryConfig.InitialDelayMs, retryConfig.MaxDelayMs, retryConfig.BackoffMultiplier)

	ctx, cancel := context.WithCancel(context.Background())

	processor := &SerialOCRProcessor{
		ocrService:  ocrService,
		resultChan:  make(chan *OCRTaskResult, 100), // 缓冲通道
		callbacks:   make([]ResultCallback, 0),
		ctx:         ctx,
		cancel:      cancel,
		running:     false,
		stats:       &ProcessorStats{StartTime: time.Now()},
		taskQueue:   make(chan *ScreenshotTask, 50), // 任务队列缓冲
		currentUser: "",
		userQueue:   make([]string, 0),
		retryConfig: retryConfig,
	}

	return processor, nil
}

// Start 启动串行OCR处理器
func (sop *SerialOCRProcessor) Start() error {
	sop.mu.Lock()
	defer sop.mu.Unlock()

	if sop.running {
		return fmt.Errorf("串行OCR处理器已在运行")
	}

	sop.running = true
	sop.stats.StartTime = time.Now()

	// 启动串行处理协程
	sop.wg.Add(1)
	go sop.serialProcessor()

	// 启动结果处理协程
	sop.wg.Add(1)
	go sop.resultProcessor()

	fmt.Println("[串行OCR处理器] 已启动")
	return nil
}

// Stop 停止串行OCR处理器
func (sop *SerialOCRProcessor) Stop() error {
	sop.mu.Lock()
	defer sop.mu.Unlock()

	if !sop.running {
		return fmt.Errorf("串行OCR处理器未运行")
	}

	sop.running = false
	sop.cancel()

	// 关闭任务队列
	close(sop.taskQueue)

	// 等待所有协程结束
	sop.wg.Wait()

	// 关闭结果通道
	close(sop.resultChan)

	fmt.Println("[串行OCR处理器] 已停止")
	return nil
}

// SubmitTask 提交任务到串行队列
func (sop *SerialOCRProcessor) SubmitTask(ctx context.Context, task *ScreenshotTask) error {
	sop.mu.RLock()
	running := sop.running
	sop.mu.RUnlock()

	if !running {
		return fmt.Errorf("串行OCR处理器未运行")
	}

	// 检查是否需要切换用户
	sop.userMu.Lock()
	if sop.currentUser != task.UserName {
		// 如果当前用户不同，加入用户队列
		if sop.currentUser != "" {
			// 当前有用户在处理，新用户需要排队
			found := false
			for _, user := range sop.userQueue {
				if user == task.UserName {
					found = true
					break
				}
			}
			if !found {
				sop.userQueue = append(sop.userQueue, task.UserName)
				fmt.Printf("[串行OCR] 用户 %s 已加入队列，当前队列长度: %d\n", task.UserName, len(sop.userQueue))
			}
			sop.userMu.Unlock()
			return fmt.Errorf("当前用户 %s 正在处理中，用户 %s 已加入队列", sop.currentUser, task.UserName)
		} else {
			// 没有当前用户，直接设置
			sop.currentUser = task.UserName
			fmt.Printf("[串行OCR] 开始处理用户: %s\n", task.UserName)
		}
	}
	sop.userMu.Unlock()

	// 提交任务到队列
	select {
	case sop.taskQueue <- task:
		sop.stats.mu.Lock()
		sop.stats.TotalSubmitted++
		sop.stats.mu.Unlock()
		fmt.Printf("[串行OCR] 任务已提交到队列: %s (用户: %s)\n", task.ID, task.UserName)
		return nil
	case <-ctx.Done():
		return ctx.Err()
	default:
		return fmt.Errorf("任务队列已满，请稍后重试")
	}
}

// serialProcessor 串行处理协程
func (sop *SerialOCRProcessor) serialProcessor() {
	defer sop.wg.Done()

	fmt.Println("[串行OCR] 串行处理协程已启动")

	for {
		select {
		case task, ok := <-sop.taskQueue:
			if !ok {
				fmt.Println("[串行OCR] 任务队列已关闭，退出处理协程")
				return
			}

			// 处理任务
			sop.processTaskSerial(task)

		case <-sop.ctx.Done():
			fmt.Println("[串行OCR] 收到停止信号，退出处理协程")
			return
		}
	}
}

// processTaskSerial 串行处理单个任务
func (sop *SerialOCRProcessor) processTaskSerial(task *ScreenshotTask) {
	startTime := time.Now()
	result := &OCRTaskResult{
		Task:      task,
		Processed: startTime,
	}

	fmt.Printf("[串行OCR] 开始处理任务: %s (用户: %s)\n", task.ID, task.UserName)

	// 实现带指数退避的重试机制
	var ocrResult *OCRResult
	var err error

	for attempt := 0; attempt < sop.retryConfig.MaxAttempts; attempt++ {
		select {
		case <-sop.ctx.Done():
			result.Error = sop.ctx.Err()
			sop.stats.mu.Lock()
			sop.stats.TotalFailed++
			sop.stats.mu.Unlock()
			fmt.Printf("[串行OCR] 任务被取消: %s\n", task.ID)
			sop.resultChan <- result
			return
		default:
		}

		// 调用OCR服务处理图片
		ocrResult, err = sop.ocrService.ProcessImageWithDetails(sop.ctx, task.ImagePath)
		if err == nil {
			break // 成功则跳出循环
		}

		fmt.Printf("[串行OCR] 任务处理失败 (尝试 %d/%d): %s, 错误: %v\n",
			attempt+1, sop.retryConfig.MaxAttempts, task.ID, err)

		if attempt < sop.retryConfig.MaxAttempts-1 {
			// 计算退避延迟
			delay := time.Duration(sop.retryConfig.InitialDelayMs) * time.Millisecond
			for i := 0; i < attempt; i++ {
				delay = time.Duration(float64(delay) * sop.retryConfig.BackoffMultiplier)
			}
			if delay > time.Duration(sop.retryConfig.MaxDelayMs)*time.Millisecond {
				delay = time.Duration(sop.retryConfig.MaxDelayMs) * time.Millisecond
			}

			fmt.Printf("[串行OCR] 等待 %v 后重试...\n", delay)
			time.Sleep(delay)
		}
	}

	if err != nil {
		result.Error = err
		sop.stats.mu.Lock()
		sop.stats.TotalFailed++
		sop.stats.mu.Unlock()
		fmt.Printf("[串行OCR] 任务最终失败: %s, 错误: %v\n", task.ID, err)
	} else {
		result.Result = ocrResult
		sop.stats.mu.Lock()
		sop.stats.TotalCompleted++
		sop.stats.mu.Unlock()
		fmt.Printf("[串行OCR] 任务处理成功: %s, 器官: %s, 耗时: %v\n",
			task.ID, ocrResult.OrganName, time.Since(startTime))
	}

	result.Duration = time.Since(startTime)

	// 更新平均处理时间
	sop.updateAverageTime(result.Duration)

	// 发送结果到通道
	select {
	case sop.resultChan <- result:
	case <-sop.ctx.Done():
		fmt.Printf("[串行OCR] 处理器已停止，丢弃任务结果: %s\n", task.ID)
	}
}

// resultProcessor 结果处理协程
func (sop *SerialOCRProcessor) resultProcessor() {
	defer sop.wg.Done()

	fmt.Println("[串行OCR] 结果处理协程已启动")

	for {
		select {
		case result, ok := <-sop.resultChan:
			if !ok {
				fmt.Println("[串行OCR] 结果通道已关闭，退出结果处理协程")
				return
			}

			// 调用所有回调函数
			sop.mu.RLock()
			callbacks := make([]ResultCallback, len(sop.callbacks))
			copy(callbacks, sop.callbacks)
			sop.mu.RUnlock()

			for _, callback := range callbacks {
				go func(cb ResultCallback, res *OCRTaskResult) {
					defer func() {
						if r := recover(); r != nil {
							fmt.Printf("[串行OCR] 回调函数异常: %v\n", r)
						}
					}()
					cb(res)
				}(callback, result)
			}

			// 检查是否完成当前用户的所有任务
			sop.checkUserCompletion(result.Task.UserName)

		case <-sop.ctx.Done():
			fmt.Println("[串行OCR] 收到停止信号，退出结果处理协程")
			return
		}
	}
}

// checkUserCompletion 检查用户任务完成情况
func (sop *SerialOCRProcessor) checkUserCompletion(userName string) {
	// 由于无法直接检查channel中的内容，我们使用一个简单的策略：
	// 如果队列为空，则认为当前用户的任务已完成
	if len(sop.taskQueue) == 0 {
		sop.userMu.Lock()
		if sop.currentUser == userName {
			fmt.Printf("[串行OCR] 用户 %s 的所有任务已完成\n", userName)

			// 切换到下一个用户
			if len(sop.userQueue) > 0 {
				nextUser := sop.userQueue[0]
				sop.userQueue = sop.userQueue[1:]
				sop.currentUser = nextUser
				fmt.Printf("[串行OCR] 切换到下一个用户: %s，队列剩余: %d\n", nextUser, len(sop.userQueue))
			} else {
				sop.currentUser = ""
				fmt.Println("[串行OCR] 所有用户任务已完成，等待新任务")
			}
		}
		sop.userMu.Unlock()
	}
}

// AddResultCallback 添加结果回调函数
func (sop *SerialOCRProcessor) AddResultCallback(callback ResultCallback) {
	sop.mu.Lock()
	defer sop.mu.Unlock()
	sop.callbacks = append(sop.callbacks, callback)
}

// GetStats 获取统计信息
func (sop *SerialOCRProcessor) GetStats() *ProcessorStats {
	sop.stats.mu.RLock()
	defer sop.stats.mu.RUnlock()

	stats := &ProcessorStats{
		TotalSubmitted: sop.stats.TotalSubmitted,
		TotalCompleted: sop.stats.TotalCompleted,
		TotalFailed:    sop.stats.TotalFailed,
		AverageTime:    sop.stats.AverageTime,
		StartTime:      sop.stats.StartTime,
	}
	return stats
}

// updateAverageTime 更新平均处理时间
func (sop *SerialOCRProcessor) updateAverageTime(duration time.Duration) {
	sop.stats.mu.Lock()
	defer sop.stats.mu.Unlock()

	if sop.stats.TotalCompleted == 1 {
		sop.stats.AverageTime = duration
	} else {
		// 计算移动平均值
		totalTime := sop.stats.AverageTime * time.Duration(sop.stats.TotalCompleted-1)
		totalTime += duration
		sop.stats.AverageTime = totalTime / time.Duration(sop.stats.TotalCompleted)
	}
}

// GetQueueStatus 获取队列状态
func (sop *SerialOCRProcessor) GetQueueStatus() map[string]interface{} {
	sop.userMu.Lock()
	defer sop.userMu.Unlock()

	return map[string]interface{}{
		"current_user":    sop.currentUser,
		"user_queue":      sop.userQueue,
		"queue_length":    len(sop.userQueue),
		"task_queue_size": len(sop.taskQueue),
		"running":         sop.running,
	}
}

// IsRunning 检查处理器是否运行中
func (sop *SerialOCRProcessor) IsRunning() bool {
	sop.mu.RLock()
	defer sop.mu.RUnlock()
	return sop.running
}

// GetCurrentUser 获取当前处理的用户
func (sop *SerialOCRProcessor) GetCurrentUser() string {
	sop.userMu.Lock()
	defer sop.userMu.Unlock()
	return sop.currentUser
}

// GetUserQueue 获取用户队列
func (sop *SerialOCRProcessor) GetUserQueue() []string {
	sop.userMu.Lock()
	defer sop.userMu.Unlock()
	return append([]string{}, sop.userQueue...)
}

// GetRemainingTasksInfo 获取剩余任务信息
func (sop *SerialOCRProcessor) GetRemainingTasksInfo() map[string]interface{} {
	sop.userMu.Lock()
	defer sop.userMu.Unlock()

	// 计算队列中的任务数
	queueSize := len(sop.taskQueue)

	// 计算预计剩余时间（每个任务35秒平均）
	averageTaskTime := 35 * time.Second
	estimatedTime := time.Duration(queueSize) * averageTaskTime

	return map[string]interface{}{
		"current_user":       sop.currentUser,
		"user_queue":         append([]string{}, sop.userQueue...),
		"remaining_tasks":    queueSize,
		"estimated_time_sec": int(estimatedTime.Seconds()),
		"estimated_time_str": formatDuration(estimatedTime),
		"is_processing":      sop.currentUser != "" || queueSize > 0,
	}
}

// formatDuration 格式化时间显示
func formatDuration(d time.Duration) string {
	minutes := int(d.Minutes())
	seconds := int(d.Seconds()) % 60

	if minutes > 0 {
		return fmt.Sprintf("%d分%d秒", minutes, seconds)
	}
	return fmt.Sprintf("%d秒", seconds)
}

// HasPendingTasks 检查是否有待处理任务
func (sop *SerialOCRProcessor) HasPendingTasks() bool {
	sop.userMu.Lock()
	defer sop.userMu.Unlock()

	return sop.currentUser != "" || len(sop.taskQueue) > 0 || len(sop.userQueue) > 0
}

// GracefulStop 优雅停止处理器
func (sop *SerialOCRProcessor) GracefulStop(timeout time.Duration) error {
	sop.mu.Lock()
	defer sop.mu.Unlock()

	if !sop.running {
		return fmt.Errorf("串行OCR处理器未运行")
	}

	fmt.Printf("[串行OCR] 开始优雅停止，等待当前任务完成...\n")

	// 设置停止标志但不立即取消
	sop.running = false

	// 创建超时上下文
	timeoutCtx, timeoutCancel := context.WithTimeout(context.Background(), timeout)
	defer timeoutCancel()

	// 等待所有任务完成或超时
	done := make(chan struct{})
	go func() {
		sop.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		fmt.Printf("[串行OCR] 所有任务已完成，处理器已停止\n")
		sop.cancel()
		return nil
	case <-timeoutCtx.Done():
		fmt.Printf("[串行OCR] 等待超时，强制停止处理器\n")
		sop.cancel()
		return fmt.Errorf("优雅停止超时")
	}
}
