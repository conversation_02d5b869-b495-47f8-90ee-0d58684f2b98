package api

import (
	"MagneticOperator/app/services"
)

// SimpleScreenshotAPI 简化的截图API
type SimpleScreenshotAPI struct {
	manager *services.SimpleScreenshotManager
}

// NewSimpleScreenshotAPI 创建简化的截图API
func NewSimpleScreenshotAPI(manager *services.SimpleScreenshotManager) *SimpleScreenshotAPI {
	return &SimpleScreenshotAPI{
		manager: manager,
	}
}

// StartScreenshotSession 开始截图会话
func (api *SimpleScreenshotAPI) StartScreenshotSession(userName string) error {
	return api.manager.Start(userName)
}

// TakeScreenshot 执行截图
func (api *SimpleScreenshotAPI) TakeScreenshot(userName, mode string) error {
	return api.manager.TakeScreenshot(userName, mode)
}

// GetProgress 获取进度
func (api *SimpleScreenshotAPI) GetProgress() services.ProgressInfo {
	return api.manager.GetProgress()
}

// GetTasks 获取所有任务
func (api *SimpleScreenshotAPI) GetTasks() []*services.SimpleScreenshotTask {
	return api.manager.GetTasks()
}

// GetTasksByOrgan 按器官分组获取任务
func (api *SimpleScreenshotAPI) GetTasksByOrgan() map[string][]*services.SimpleScreenshotTask {
	return api.manager.GetTasksByOrgan()
}

// IsRunning 检查是否运行中
func (api *SimpleScreenshotAPI) IsRunning() bool {
	return api.manager.IsRunning()
}

// IsCompleted 检查是否完成
func (api *SimpleScreenshotAPI) IsCompleted() bool {
	return api.manager.IsCompleted()
}

// StopSession 停止会话
func (api *SimpleScreenshotAPI) StopSession() error {
	return api.manager.Stop()
}
