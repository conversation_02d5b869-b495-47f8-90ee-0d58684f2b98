# 升级版颜色分析器 (Advanced Color Analyzer)

## 概述

升级版颜色分析器是基于原有增强版颜色分析器的进一步升级，集成了以下先进技术：

- **gocv图像预处理**：使用OpenCV的Go绑定进行专业图像处理
- **笔画加粗技术**：通过形态学膨胀操作增强细线条文字
- **多区域采样策略**：单元格左右分区采样，提高颜色识别准确率
- **优先级判定逻辑**：基于"红 > 橘 > 绿 > 蓝"的智能颜色优先级系统
- **自适应阈值处理**：增强文字与背景的对比度

## 核心功能特性

### 1. 图像预处理增强

#### 1.1 笔画加粗技术（形态学膨胀操作）

```go
// 设置膨胀参数
analyzer.SetDilationParameters(kernelSize, iterations)

// 应用笔画加粗
err := analyzer.ApplyStrokeThickening()
```

**技术原理：**
- 使用gocv.Dilate()函数进行形态学膨胀
- 支持矩形、圆形、十字形等多种结构元素
- 可调节核大小和迭代次数
- 有效增强细线条文字的像素密度

**参数说明：**
- `kernelSize`: 膨胀核大小（推荐3x3或5x5）
- `iterations`: 膨胀迭代次数（推荐1-2次）

#### 1.2 自适应阈值处理

```go
// 应用自适应阈值处理
err := analyzer.ApplyAdaptiveThresholding()
```

**功能：**
- 增强文字与背景的对比度
- 自动适应不同光照条件
- 提高颜色采样的准确性

### 2. 采样区域优化策略

#### 2.1 单元格内分区域采样

```go
// 提取单元格的左右区域
regions := analyzer.ExtractCellRegions(textElement)
```

**策略说明：**
- 将每个单元格水平等分为左右两个区域
- 数字列和文本列分别进行左右分区
- 每行从2个采样区域扩展到4个采样区域
- 提高局部颜色特征的捕捉能力

#### 2.2 多区域采样与优先级判定

```go
// 应用优先级规则
finalColor, confidence, priority := analyzer.ApplyPriorityRules(regions)
```

**优先级规则：**
1. **红色** (最高优先级)
2. **橘色**
3. **绿色**
4. **蓝色** (最低优先级)

**判定逻辑：**
- "一票否决"策略：只要任一区域识别出高优先级颜色，整行判定为该颜色
- 置信度过滤：低于阈值的识别结果将被忽略
- 同等优先级下选择置信度更高的结果

### 3. gocv库集成应用

#### 3.1 图像加载与处理

```go
// 使用gocv加载图像
err := analyzer.LoadImageWithGocv()

// 获取图像信息
rows := analyzer.gocvMat.Rows()
cols := analyzer.gocvMat.Cols()
```

#### 3.2 形态学操作

```go
// 创建结构元素
kernel := gocv.GetStructuringElement(gocv.MorphRect, image.Pt(3, 3))
defer kernel.Close()

// 执行膨胀操作
gocv.Dilate(srcMat, &dstMat, kernel, image.Point{-1, -1}, 1, gocv.BorderConstant, gocv.Scalar{})
```

#### 3.3 色彩空间转换

```go
// BGR到灰度转换
gocv.CvtColor(srcMat, &grayMat, gocv.ColorBGRToGray)

// 灰度到BGR转换
gocv.CvtColor(grayMat, &dstMat, gocv.ColorGrayToBGR)
```

## 安装与配置

### 1. 依赖安装

```bash
# 安装OpenCV (Windows)
# 下载并安装OpenCV 4.x
# 设置环境变量 OPENCV_DIR

# 安装Go依赖
go mod tidy
```

### 2. 环境配置

```bash
# 设置调试模式
export COLOR_DEBUG=1

# Windows PowerShell
$env:COLOR_DEBUG="1"
```

## 使用方法

### 1. 基本使用

```go
package main

import (
    "log"
)

func main() {
    imagePath := "path/to/image.jpg"
    ocrDataPath := "path/to/ocr_data.json"
    outputDir := "./output"
    
    err := RunAdvancedColorAnalysis(imagePath, ocrDataPath, outputDir)
    if err != nil {
        log.Fatalf("分析失败: %v", err)
    }
}
```

### 2. 高级配置

```go
// 创建分析器
analyzer := NewAdvancedColorAnalyzer(imagePath, ocrDataPath, outputDir)
defer analyzer.Close()

// 配置参数
analyzer.SetDilationParameters(5, 2)  // 5x5核，2次迭代
analyzer.SetPriorityRules([]string{"red", "blue", "green", "orange"})
analyzer.SetConfidenceThreshold(0.8)  // 提高置信度要求

// 执行分析
results, err := analyzer.ProcessTableWithAdvancedSampling()
```

### 3. 命令行使用

```bash
# 编译升级版程序
go build -o advanced_analyzer.exe advanced_main.go advanced_color_analyzer.go enhanced_color_analyzer.go

# 运行分析
./advanced_analyzer.exe ocr_data.json image.jpg ./output
```

## 输出结果

### 1. JSON报告 (advanced_analysis_report.json)

```json
{
  "timestamp": "2024-01-01 12:00:00",
  "image_path": "path/to/image.jpg",
  "processing_time": "150ms",
  "total_rows": 50,
  "successful_rows": 48,
  "average_confidence": 0.85,
  "color_distribution": {
    "red": 12,
    "blue": 20,
    "green": 8,
    "orange": 8
  },
  "color_percentages": {
    "red": 25.0,
    "blue": 41.7,
    "green": 16.7,
    "orange": 16.7
  },
  "parameters": {
    "dilation_kernel_size": 3,
    "dilation_iterations": 1,
    "confidence_threshold": 0.6,
    "priority_rules": ["red", "orange", "green", "blue"]
  },
  "performance_metrics": {
    "image_load_time": "10ms",
    "preprocessing_time": "25ms",
    "color_analysis_time": "80ms",
    "total_processing_time": "150ms"
  }
}
```

### 2. 文本报告 (advanced_analysis_summary.txt)

```
升级版颜色分析报告
===================

分析时间: 2024-01-01 12:00:00
图像路径: path/to/image.jpg

性能指标:
  总处理时间: 150ms
  预处理时间: 25ms
  分析时间: 80ms

分析结果:
  总行数: 50
  成功分析行数: 48
  平均置信度: 85.00%

颜色分布:
  red: 12 行 (25.0%)
  blue: 20 行 (41.7%)
  green: 8 行 (16.7%)
  orange: 8 行 (16.7%)

详细行分析:
  第1行: red (置信度: 0.89, 优先级: 4)
    数字left区域: red (0.85)
    数字right区域: blue (0.72)
    文本left区域: green (0.68)
    文本right区域: red (0.89)
  ...
```

## 单元测试

### 1. 运行所有测试

```bash
go test -v
```

### 2. 运行特定测试

```bash
# 测试颜色分类
go test -v -run TestAdvancedColorAnalyzer_ColorClassification

# 测试区域提取
go test -v -run TestAdvancedColorAnalyzer_RegionExtraction

# 测试优先级规则
go test -v -run TestAdvancedColorAnalyzer_PriorityRules
```

### 3. 性能基准测试

```bash
# 笔画加粗性能测试
go test -bench=BenchmarkAdvancedColorAnalyzer_StrokeThickening

# 完整工作流程测试
go test -v -run TestAdvancedColorAnalyzer_FullWorkflow
```

## 性能优化建议

### 1. 参数调优

- **膨胀核大小**：3x3适合一般文字，5x5适合极细文字
- **迭代次数**：1次通常足够，2次用于极细线条
- **置信度阈值**：0.6-0.8之间，根据图像质量调整

### 2. 内存管理

```go
// 及时释放gocv资源
defer analyzer.Close()

// 手动释放Mat
if !mat.Empty() {
    mat.Close()
}
```

### 3. 批处理优化

```go
// 批量处理多个文件时重用分析器
analyzer := NewAdvancedColorAnalyzer("", "", outputDir)
defer analyzer.Close()

for _, file := range files {
    analyzer.imagePath = file.ImagePath
    analyzer.ocrDataPath = file.OCRPath
    // 执行分析...
}
```

## 技术栈整合

### 1. 现有库兼容性

- **prominentcolor**: 继续用于K-means颜色聚类
- **imaging**: 保留用于基础图像处理
- **gocv**: 新增用于高级图像预处理

### 2. 升级路径

```go
// 从增强版升级到升级版
enhanced := NewEnhancedColorAnalyzer(imagePath, ocrDataPath, outputDir)
advanced := &AdvancedColorAnalyzer{
    EnhancedColorAnalyzer: enhanced,
    // 新增字段...
}
```

## 故障排除

### 1. 常见问题

**Q: gocv编译失败**
A: 确保OpenCV正确安装并设置环境变量

**Q: 图像加载失败**
A: 检查图像路径和格式支持

**Q: 内存泄漏**
A: 确保调用Close()方法释放资源

### 2. 调试模式

```bash
# 启用详细日志
export COLOR_DEBUG=1
go run advanced_main.go ocr_data.json
```

### 3. 性能监控

```go
// 监控处理时间
start := time.Now()
// 执行分析...
duration := time.Since(start)
log.Printf("处理耗时: %v", duration)
```

## 版本历史

- **v2.0**: 升级版发布，集成gocv和多区域采样
- **v1.5**: 增强版，添加医疗图像优化
- **v1.0**: 基础版，prominentcolor集成

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发团队。