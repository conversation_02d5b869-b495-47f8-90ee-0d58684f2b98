{"logId": "31fe010f-88c3-47ff-aa24-8ced3f0865c3", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 8, "label": "table", "score": 0.9863736629486084, "coordinate": [12.884788513183594, 75.30628967285156, 768, 1711.0108642578125]}, {"cls_id": 9, "label": "table_title", "score": 0.6587378978729248, "coordinate": [19.3919677734375, 27.21903419494629, 520.5076904296875, 62.94111251831055]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 77], [295, 77], [295, 102], [192, 102]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[190, 98], [271, 98], [271, 130], [190, 130]], [[190, 123], [462, 127], [461, 157], [190, 154]], [[98, 154], [162, 154], [162, 179], [98, 179]], [[192, 154], [319, 154], [319, 179], [192, 179]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[194, 179], [314, 179], [314, 204], [194, 204]], [[98, 204], [162, 204], [162, 229], [98, 229]], [[194, 206], [556, 206], [556, 229], [194, 229]], [[98, 231], [162, 231], [162, 256], [98, 256]], [[196, 234], [726, 234], [726, 252], [196, 252]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [415, 257], [415, 281], [192, 281]], [[98, 281], [155, 281], [155, 306], [98, 306]], [[191, 279], [305, 283], [304, 308], [190, 304]], [[98, 306], [155, 306], [155, 332], [98, 332]], [[194, 309], [306, 309], [306, 329], [194, 329]], [[98, 332], [155, 332], [155, 358], [98, 358]], [[192, 331], [406, 331], [406, 354], [192, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[190, 358], [260, 358], [260, 384], [190, 384]], [[98, 383], [157, 383], [157, 409], [98, 409]], [[192, 384], [689, 384], [689, 408], [192, 408]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[192, 409], [445, 409], [445, 433], [192, 433]], [[98, 433], [155, 433], [155, 459], [98, 459]], [[188, 431], [262, 431], [262, 463], [188, 463]], [[100, 458], [157, 458], [157, 484], [100, 484]], [[192, 459], [417, 459], [417, 484], [192, 484]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[194, 486], [456, 486], [456, 509], [194, 509]], [[98, 509], [155, 509], [155, 536], [98, 536]], [[194, 511], [441, 511], [441, 534], [194, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[190, 534], [246, 534], [246, 563], [190, 563]], [[98, 561], [155, 561], [155, 586], [98, 586]], [[185, 560], [243, 555], [247, 587], [189, 592]], [[98, 586], [157, 586], [157, 611], [98, 611]], [[192, 588], [476, 588], [476, 611], [192, 611]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[196, 617], [639, 617], [639, 635], [196, 635]], [[98, 636], [157, 636], [157, 663], [98, 663]], [[194, 640], [445, 640], [445, 663], [194, 663]], [[98, 663], [157, 663], [157, 688], [98, 688]], [[188, 663], [400, 661], [401, 686], [188, 688]], [[98, 688], [157, 688], [157, 715], [98, 715]], [[192, 690], [362, 690], [362, 713], [192, 713]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[194, 715], [731, 715], [731, 738], [194, 738]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[194, 740], [504, 740], [504, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[190, 765], [279, 765], [279, 792], [190, 792]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [522, 792], [522, 815], [192, 815]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [314, 817], [314, 842], [192, 842]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[192, 844], [606, 844], [606, 867], [192, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [430, 869], [430, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[190, 892], [386, 894], [386, 919], [190, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[190, 919], [474, 919], [474, 942], [190, 942]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[188, 944], [262, 944], [262, 971], [188, 971]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[192, 969], [412, 969], [412, 992], [192, 992]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [330, 996], [330, 1021], [192, 1021]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[190, 1019], [284, 1019], [284, 1047], [190, 1047]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[188, 1042], [264, 1042], [264, 1074], [188, 1074]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[190, 1071], [364, 1071], [364, 1096], [190, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [569, 1098], [569, 1121], [192, 1121]], [[98, 1121], [157, 1121], [157, 1148], [98, 1148]], [[192, 1124], [412, 1124], [412, 1148], [192, 1148]], [[98, 1148], [157, 1148], [157, 1173], [98, 1173]], [[190, 1148], [347, 1146], [347, 1171], [190, 1173]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[192, 1174], [393, 1174], [393, 1198], [192, 1198]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [297, 1199], [297, 1224], [190, 1224]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[192, 1224], [351, 1224], [351, 1249], [192, 1249]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1249], [438, 1249], [438, 1273], [192, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[192, 1276], [508, 1276], [508, 1300], [192, 1300]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[190, 1301], [260, 1301], [260, 1328], [190, 1328]], [[98, 1326], [155, 1326], [155, 1351], [98, 1351]], [[192, 1326], [332, 1326], [332, 1351], [192, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [500, 1353], [500, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1380], [473, 1380], [473, 1403], [192, 1403]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [297, 1403], [297, 1428], [192, 1428]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[194, 1428], [362, 1428], [362, 1453], [194, 1453]], [[98, 1453], [155, 1453], [155, 1480], [98, 1480]], [[194, 1455], [371, 1455], [371, 1478], [194, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[194, 1484], [412, 1484], [412, 1502], [194, 1502]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[194, 1509], [476, 1509], [476, 1527], [194, 1527]], [[98, 1530], [155, 1530], [155, 1555], [98, 1555]], [[194, 1532], [401, 1532], [401, 1555], [194, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[192, 1557], [367, 1557], [367, 1582], [192, 1582]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[190, 1582], [282, 1578], [283, 1605], [191, 1609]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[194, 1607], [478, 1607], [478, 1632], [194, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[194, 1636], [330, 1636], [330, 1655], [194, 1655]], [[98, 1657], [157, 1657], [157, 1684], [98, 1684]], [[188, 1655], [266, 1655], [266, 1687], [188, 1687]], [[100, 1682], [157, 1682], [157, 1709], [100, 1709]], [[192, 1684], [327, 1684], [327, 1709], [192, 1709]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["按照标准图谱相似度递减列表：", "0.000", "厦膜后脏器", "4.520", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.065", "血管紧张素Ⅱ*", "0.086", "血管紧张素I*", "0.096", "胆固醇COMMONPLASMA CHOLESTERIN", "0.115", "血浆丰酸化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA", "0.147", "血红血球ERYTHROCYTES", "0.154", "BETA球蛋白*", "0.163", "免疫球蛋白G*", "0.183", "血钾PLASMAPOTASSIUM", "0.112", "脂肪酶*", "0.115", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.116", "血清补体SERUMCOMPLEMENT", "0.117", "催乳素*", "0.129", "血肌酥SERUM CREATININE", "0.130", "伽马球蛋白GAMMA-GLOBULINS", "0.130", "血清溶菌酵SERUMLYSOZYME", "0.130", "肾素*", "0.131", "糖苷*", "0.133", "PERIPHERIC BLOOD LEUCOCYTES", "0.134", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.134", "肿瘤标志物MELANOGENE在尿", "0.135", "嗜碱性粒细胞BASOPHILS", "0.135", "甲状腺素结合球蛋白", "0.136", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.138", "血清淀粉酵素SERUMALPHAAMYLASE", "0.139", "醛固酮尿*", "0.139", "游离胆固醇FREEPLASMACHOLESTERIN", "0.139", "甲状腺球蛋白*", "0.140", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.140", "嗜酸性粒细胞EOSINOPHILES", "0.140", "17-血浆氧皮质类固醇类", "0.141", "嗜中性粒细胞STABNEUTROPHILS", "0.142", "唾液酸*", "0.142", "尿肌酥URINECREATININE", "0.143", "17-尿中酮类固醇", "0.143", "维生素B6*", "0.143", "多巴胺*", "0.143", "红细胞沉降率(ESR)", "0.143", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.144", "血清蛋白SERUMALBUMEN", "0.144", "RHEUMOFACTOR*", "0.144", "血红蛋白HAEMOGLOBIN", "0.144", "抗利尿激素*", "0.145", "血细胞比容，全血*", "0.147", "尿白血球URINE LEUCOCYTES", "0.147", "11 - PLASMA OXYCORTICOSTEROIDS", "0.147", "胰岛素*", "0.147", "抗链球菌溶血素*", "0.147", "生长激素SOMATOTROPICHORMONE", "0.149", "皮质醇SERUMHYDROCORTISONE", "0.149", "胰高血糖素*", "0.149", "血尿素BLOODUREA", "0.150", "总铁结合力（TIBC）", "0.150", "尿中尿酸URINEURICACID", "0.150", "酸性磷酸酵素ACIDPHOSPHATASE", "0.151", "血尿酸SERUMURICACID", "0.151", "肿瘤标志物胸苷激酶", "0.152", "维生素B2*", "0.152", "血浆磷脂PLASMA PHOSPHOTIDES", "0.152", "糖基化血红蛋白", "0.152", "备解素*", "0.152", "ALPHA2球蛋白*"], "rec_scores": [0.9955605864524841, 0.9992006421089172, 0.8574286699295044, 0.9966824650764465, 0.9888066649436951, 0.9718126654624939, 0.9999227523803711, 0.944828450679779, 0.9999481439590454, 0.9827127456665039, 0.9998941421508789, 0.9785137176513672, 0.9998438954353333, 0.9364428520202637, 0.9998653531074524, 0.9960286617279053, 0.9995157122612, 0.9764004945755005, 0.999607264995575, 0.9238863587379456, 0.9996045827865601, 0.9762188196182251, 0.9991083145141602, 0.9291876554489136, 0.9990711212158203, 0.9956688284873962, 0.9988662600517273, 0.9978330731391907, 0.9990885853767395, 0.9625581502914429, 0.9994887113571167, 0.9412553906440735, 0.9993056058883667, 0.9893432855606079, 0.9993315935134888, 0.9940602779388428, 0.9992548227310181, 0.9766652584075928, 0.9990667104721069, 0.9671332240104675, 0.9993680119514465, 0.9804553985595703, 0.9994457960128784, 0.996772289276123, 0.9993776082992554, 0.9919062256813049, 0.9992088079452515, 0.9986740350723267, 0.9994977712631226, 0.995712399482727, 0.9995177388191223, 0.9968676567077637, 0.9995840191841125, 0.9971523284912109, 0.9992262721061707, 0.97516930103302, 0.9994462728500366, 0.9961369037628174, 0.999477207660675, 0.9346173405647278, 0.9992429614067078, 0.997855544090271, 0.9990541338920593, 0.9977625608444214, 0.999261736869812, 0.9911320805549622, 0.9992793202400208, 0.9966058135032654, 0.9991812705993652, 0.9491941928863525, 0.9993795156478882, 0.9559463858604431, 0.9994878768920898, 0.9850637912750244, 0.9995259046554565, 0.993542492389679, 0.9992753863334656, 0.9426757097244263, 0.9994220733642578, 0.9424949884414673, 0.9994878768920898, 0.9951286911964417, 0.9993155598640442, 0.9986840486526489, 0.9990348815917969, 0.996536910533905, 0.9990319013595581, 0.9950233697891235, 0.9990326762199402, 0.977360725402832, 0.9991230964660645, 0.9724450707435608, 0.9993202090263367, 0.9693094491958618, 0.999464213848114, 0.964715301990509, 0.9992125630378723, 0.9526697397232056, 0.9990390539169312, 0.9675589799880981, 0.9993202090263367, 0.9962304830551147, 0.9994550943374634, 0.9967683553695679, 0.9988902807235718, 0.9693164825439453, 0.9988030195236206, 0.9903314709663391, 0.9994136095046997, 0.8903585076332092, 0.9995203018188477, 0.9971678256988525, 0.9994422197341919, 0.9950373768806458, 0.9991353154182434, 0.9990896582603455, 0.999468207359314, 0.9924169182777405, 0.9996203184127808, 0.9921790957450867, 0.9994977712631226, 0.9790579676628113, 0.9992698431015015, 0.9971086382865906, 0.9996511340141296, 0.9310247302055359, 0.9992640614509583, 0.9760513305664062], "rec_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 77], [295, 77], [295, 102], [192, 102]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[190, 98], [271, 98], [271, 130], [190, 130]], [[190, 123], [462, 127], [461, 157], [190, 154]], [[98, 154], [162, 154], [162, 179], [98, 179]], [[192, 154], [319, 154], [319, 179], [192, 179]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[194, 179], [314, 179], [314, 204], [194, 204]], [[98, 204], [162, 204], [162, 229], [98, 229]], [[194, 206], [556, 206], [556, 229], [194, 229]], [[98, 231], [162, 231], [162, 256], [98, 256]], [[196, 234], [726, 234], [726, 252], [196, 252]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [415, 257], [415, 281], [192, 281]], [[98, 281], [155, 281], [155, 306], [98, 306]], [[191, 279], [305, 283], [304, 308], [190, 304]], [[98, 306], [155, 306], [155, 332], [98, 332]], [[194, 309], [306, 309], [306, 329], [194, 329]], [[98, 332], [155, 332], [155, 358], [98, 358]], [[192, 331], [406, 331], [406, 354], [192, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[190, 358], [260, 358], [260, 384], [190, 384]], [[98, 383], [157, 383], [157, 409], [98, 409]], [[192, 384], [689, 384], [689, 408], [192, 408]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[192, 409], [445, 409], [445, 433], [192, 433]], [[98, 433], [155, 433], [155, 459], [98, 459]], [[188, 431], [262, 431], [262, 463], [188, 463]], [[100, 458], [157, 458], [157, 484], [100, 484]], [[192, 459], [417, 459], [417, 484], [192, 484]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[194, 486], [456, 486], [456, 509], [194, 509]], [[98, 509], [155, 509], [155, 536], [98, 536]], [[194, 511], [441, 511], [441, 534], [194, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[190, 534], [246, 534], [246, 563], [190, 563]], [[98, 561], [155, 561], [155, 586], [98, 586]], [[185, 560], [243, 555], [247, 587], [189, 592]], [[98, 586], [157, 586], [157, 611], [98, 611]], [[192, 588], [476, 588], [476, 611], [192, 611]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[196, 617], [639, 617], [639, 635], [196, 635]], [[98, 636], [157, 636], [157, 663], [98, 663]], [[194, 640], [445, 640], [445, 663], [194, 663]], [[98, 663], [157, 663], [157, 688], [98, 688]], [[188, 663], [400, 661], [401, 686], [188, 688]], [[98, 688], [157, 688], [157, 715], [98, 715]], [[192, 690], [362, 690], [362, 713], [192, 713]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[194, 715], [731, 715], [731, 738], [194, 738]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[194, 740], [504, 740], [504, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[190, 765], [279, 765], [279, 792], [190, 792]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [522, 792], [522, 815], [192, 815]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [314, 817], [314, 842], [192, 842]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[192, 844], [606, 844], [606, 867], [192, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [430, 869], [430, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[190, 892], [386, 894], [386, 919], [190, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[190, 919], [474, 919], [474, 942], [190, 942]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[188, 944], [262, 944], [262, 971], [188, 971]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[192, 969], [412, 969], [412, 992], [192, 992]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [330, 996], [330, 1021], [192, 1021]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[190, 1019], [284, 1019], [284, 1047], [190, 1047]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[188, 1042], [264, 1042], [264, 1074], [188, 1074]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[190, 1071], [364, 1071], [364, 1096], [190, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [569, 1098], [569, 1121], [192, 1121]], [[98, 1121], [157, 1121], [157, 1148], [98, 1148]], [[192, 1124], [412, 1124], [412, 1148], [192, 1148]], [[98, 1148], [157, 1148], [157, 1173], [98, 1173]], [[190, 1148], [347, 1146], [347, 1171], [190, 1173]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[192, 1174], [393, 1174], [393, 1198], [192, 1198]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [297, 1199], [297, 1224], [190, 1224]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[192, 1224], [351, 1224], [351, 1249], [192, 1249]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1249], [438, 1249], [438, 1273], [192, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[192, 1276], [508, 1276], [508, 1300], [192, 1300]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[190, 1301], [260, 1301], [260, 1328], [190, 1328]], [[98, 1326], [155, 1326], [155, 1351], [98, 1351]], [[192, 1326], [332, 1326], [332, 1351], [192, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [500, 1353], [500, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1380], [473, 1380], [473, 1403], [192, 1403]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [297, 1403], [297, 1428], [192, 1428]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[194, 1428], [362, 1428], [362, 1453], [194, 1453]], [[98, 1453], [155, 1453], [155, 1480], [98, 1480]], [[194, 1455], [371, 1455], [371, 1478], [194, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[194, 1484], [412, 1484], [412, 1502], [194, 1502]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[194, 1509], [476, 1509], [476, 1527], [194, 1527]], [[98, 1530], [155, 1530], [155, 1555], [98, 1555]], [[194, 1532], [401, 1532], [401, 1555], [194, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[192, 1557], [367, 1557], [367, 1582], [192, 1582]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[190, 1582], [282, 1578], [283, 1605], [191, 1609]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[194, 1607], [478, 1607], [478, 1632], [194, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[194, 1636], [330, 1636], [330, 1655], [194, 1655]], [[98, 1657], [157, 1657], [157, 1684], [98, 1684]], [[188, 1655], [266, 1655], [266, 1687], [188, 1687]], [[100, 1682], [157, 1682], [157, 1709], [100, 1709]], [[192, 1684], [327, 1684], [327, 1709], [192, 1709]]], "rec_boxes": [[20, 30, 519, 61], [98, 77, 157, 102], [192, 77, 295, 102], [98, 102, 157, 129], [190, 98, 271, 130], [190, 123, 462, 157], [98, 154, 162, 179], [192, 154, 319, 179], [98, 179, 162, 204], [194, 179, 314, 204], [98, 204, 162, 229], [194, 206, 556, 229], [98, 231, 162, 256], [196, 234, 726, 252], [98, 256, 161, 281], [192, 257, 415, 281], [98, 281, 155, 306], [190, 279, 305, 308], [98, 306, 155, 332], [194, 309, 306, 329], [98, 332, 155, 358], [192, 331, 406, 354], [98, 358, 157, 383], [190, 358, 260, 384], [98, 383, 157, 409], [192, 384, 689, 408], [98, 408, 157, 434], [192, 409, 445, 433], [98, 433, 155, 459], [188, 431, 262, 463], [100, 458, 157, 484], [192, 459, 417, 484], [98, 484, 157, 509], [194, 486, 456, 509], [98, 509, 155, 536], [194, 511, 441, 534], [98, 536, 155, 561], [190, 534, 246, 563], [98, 561, 155, 586], [185, 555, 247, 592], [98, 586, 157, 611], [192, 588, 476, 611], [98, 611, 157, 638], [196, 617, 639, 635], [98, 636, 157, 663], [194, 640, 445, 663], [98, 663, 157, 688], [188, 661, 401, 688], [98, 688, 157, 715], [192, 690, 362, 713], [98, 713, 157, 740], [194, 715, 731, 738], [98, 740, 157, 767], [194, 740, 504, 765], [98, 765, 157, 790], [190, 765, 279, 792], [98, 790, 157, 817], [192, 792, 522, 815], [98, 815, 157, 842], [192, 817, 314, 842], [98, 840, 157, 867], [192, 844, 606, 867], [98, 867, 157, 892], [192, 869, 430, 892], [98, 892, 157, 919], [190, 892, 386, 919], [98, 917, 155, 944], [190, 919, 474, 942], [98, 944, 157, 969], [188, 944, 262, 971], [98, 969, 157, 996], [192, 969, 412, 992], [98, 994, 157, 1021], [192, 996, 330, 1021], [98, 1019, 157, 1046], [190, 1019, 284, 1047], [98, 1046, 157, 1071], [188, 1042, 264, 1074], [98, 1071, 157, 1098], [190, 1071, 364, 1096], [98, 1096, 157, 1123], [192, 1098, 569, 1121], [98, 1121, 157, 1148], [192, 1124, 412, 1148], [98, 1148, 157, 1173], [190, 1146, 347, 1173], [98, 1173, 157, 1199], [192, 1174, 393, 1198], [98, 1198, 157, 1224], [190, 1199, 297, 1224], [98, 1224, 157, 1249], [192, 1224, 351, 1249], [98, 1249, 157, 1274], [192, 1249, 438, 1273], [98, 1274, 157, 1301], [192, 1276, 508, 1300], [98, 1300, 157, 1326], [190, 1301, 260, 1328], [98, 1326, 155, 1351], [192, 1326, 332, 1351], [98, 1351, 157, 1376], [192, 1353, 500, 1376], [98, 1376, 157, 1403], [192, 1380, 473, 1403], [100, 1403, 157, 1428], [192, 1403, 297, 1428], [100, 1428, 157, 1453], [194, 1428, 362, 1453], [98, 1453, 155, 1480], [194, 1455, 371, 1478], [98, 1478, 157, 1505], [194, 1484, 412, 1502], [98, 1505, 157, 1530], [194, 1509, 476, 1527], [98, 1530, 155, 1555], [194, 1532, 401, 1555], [98, 1555, 155, 1582], [192, 1557, 367, 1582], [98, 1580, 157, 1607], [190, 1578, 283, 1609], [98, 1607, 157, 1632], [194, 1607, 478, 1632], [100, 1632, 157, 1657], [194, 1636, 330, 1655], [98, 1657, 157, 1684], [188, 1655, 266, 1687], [100, 1682, 157, 1709], [192, 1684, 327, 1709]]}, "table_res_list": [{"cell_box_list": [[98.67396545410156, 76.09374380111694, 170.8250503540039, 103.0263786315918], [194.71497344970703, 76.29608976840973, 768.0, 103.18428993225098], [43.223344802856445, 102.43014717102051, 70.30054092407227, 128.02488708496094], [70.24338150024414, 102.54203987121582, 98.79581451416016, 128.20021438598633], [98.63180541992188, 102.49873542785645, 171.0105209350586, 128.26636123657227], [190.0, 98.0, 271.0, 130.0], [70.2071418762207, 128.23830795288086, 98.79552459716797, 178.93295288085938], [98.56417083740234, 128.1455955505371, 171.05028533935547, 179.10215759277344], [194.3816909790039, 127.52920150756836, 768.0, 153.92820739746094], [193.84146881103516, 153.7618637084961, 768.0, 179.4913787841797], [98.6174545288086, 178.8138427734375, 171.4666976928711, 229.71572875976562], [194.0, 179.0, 314.0, 204.0], [70.16670608520508, 203.79376220703125, 98.77057647705078, 229.22764587402344], [193.55440521240234, 204.68214416503906, 768.0, 229.61326599121094], [70.20297241210938, 229.3561248779297, 98.80496978759766, 280.46875], [98.70891571044922, 229.63336181640625, 171.71703338623047, 255.39288330078125], [193.42896270751953, 229.9905548095703, 768.0, 256.2137451171875], [98.62207794189453, 255.123046875, 171.8488540649414, 280.93902587890625], [192.0, 257.0, 415.0, 281.0], [70.15144729614258, 280.37355041503906, 98.76856231689453, 305.8481903076172], [98.6351318359375, 280.5951232910156, 171.98433685302734, 306.27239990234375], [193.33930206298828, 281.10472106933594, 768.0, 307.06756591796875], [43.14010047912598, 305.7178192138672, 70.21363067626953, 331.2439880371094], [70.15582656860352, 305.7126007080078, 98.80914306640625, 331.36524963378906], [98.62255096435547, 306.1439514160156, 172.0815658569336, 331.74623107910156], [194.0, 309.0, 306.0, 329.0], [43.144012451171875, 331.4332733154297, 70.30607986450195, 356.9175567626953], [70.20839309692383, 331.3959503173828, 98.84540557861328, 382.50428771972656], [98.72228240966797, 331.5842742919922, 172.14571380615234, 357.2517547607422], [193.38599395751953, 331.86537170410156, 768.0, 357.6365509033203], [43.18769073486328, 356.6368865966797, 70.31694412231445, 382.53688049316406], [98.64093017578125, 357.0182647705078, 171.94258880615234, 382.81459045410156], [193.45793914794922, 357.9536590576172, 768.0, 383.30372619628906], [43.117408752441406, 382.52305603027344, 70.2298469543457, 433.45750427246094], [70.16283416748047, 382.4484405517578, 98.76573944091797, 407.99317932128906], [98.654296875, 382.51637268066406, 172.02912139892578, 408.2899932861328], [192.0, 384.0, 689.0, 408.0], [70.15004348754883, 407.8874969482422, 98.79246520996094, 433.5095672607422], [98.6155776977539, 408.24195861816406, 172.03881072998047, 433.80943298339844], [193.29810333251953, 408.8007354736328, 768.0, 433.4663543701172], [43.16804885864258, 433.6635284423828, 70.31096267700195, 484.72325134277344], [70.20413208007812, 433.5794219970703, 98.81285858154297, 484.6482696533203], [98.67294311523438, 433.7093963623047, 172.0456314086914, 459.32566833496094], [193.49616241455078, 433.89427185058594, 768.0, 459.9343719482422], [98.6185302734375, 459.08506774902344, 171.92046356201172, 484.87205505371094], [192.0, 459.0, 417.0, 484.0], [43.13972473144531, 484.7289581298828, 70.20117568969727, 510.2688446044922], [70.14649963378906, 484.66795349121094, 98.76541137695312, 510.18980407714844], [98.67501068115234, 484.6459197998047, 171.99726104736328, 510.40199279785156], [193.2852554321289, 485.19346618652344, 768.0, 511.17506408691406], [43.19602394104004, 510.12730407714844, 70.18341827392578, 535.6860809326172], [70.12948989868164, 510.0674591064453, 98.78312683105469, 535.7137298583984], [98.59764862060547, 510.35362243652344, 171.95772552490234, 535.9640045166016], [194.0, 511.0, 441.0, 534.0], [43.18527603149414, 535.9094390869141, 70.28660583496094, 586.9656524658203], [70.1727523803711, 535.7188568115234, 98.80210876464844, 561.2443084716797], [98.66008758544922, 535.9711761474609, 171.94371795654297, 561.4891204833984], [193.26232147216797, 536.1511383056641, 768.0, 561.8989105224609], [70.1962661743164, 561.0196380615234, 98.74299621582031, 586.8872528076172], [98.60993957519531, 561.2765350341797, 171.8456039428711, 587.0655670166016], [192.89733123779297, 562.3055877685547, 768.0, 587.6204986572266], [43.17337417602539, 586.9521331787109, 70.2098388671875, 637.8354034423828], [70.1201057434082, 586.9535064697266, 98.78311920166016, 637.8756866455078], [98.69196319580078, 586.9089813232422, 171.89093780517578, 612.6195220947266], [192.0, 588.0, 476.0, 611.0], [98.61908721923828, 612.5323638916016, 171.85697174072266, 638.2328033447266], [193.08478546142578, 612.9114532470703, 768.0, 637.9099884033203], [43.202253341674805, 638.1103668212891, 70.28292083740234, 689.2066802978516], [70.16277694702148, 637.9342193603516, 98.79767608642578, 663.4702301025391], [98.59736633300781, 638.2042388916016, 171.8992691040039, 689.2552032470703], [193.0145492553711, 638.2075958251953, 768.0, 664.2805328369141], [70.18522262573242, 663.2570953369141, 98.7562026977539, 689.2068023681641], [188.0, 661.0, 401.0, 688.0], [43.16797065734863, 689.1653594970703, 70.20376205444336, 740.1356353759766], [70.12137985229492, 689.1225128173828, 98.79022979736328, 740.1717071533203], [98.67613983154297, 689.1620635986328, 171.87883758544922, 714.8412628173828], [192.0, 690.0, 362.0, 713.0], [98.61792755126953, 714.7610626220703, 171.83492279052734, 740.4462432861328], [194.0, 715.0, 731.0, 738.0], [43.20212936401367, 740.3665313720703, 70.2898178100586, 765.8191680908203], [70.16190719604492, 740.1513214111328, 98.79596710205078, 765.6792144775391], [98.64532470703125, 740.4431304931641, 171.90103912353516, 765.9876861572266], [193.1250228881836, 740.4931793212891, 768.0, 766.4975738525391], [43.220441818237305, 765.5065460205078, 70.28490447998047, 791.4776153564453], [70.19305801391602, 765.5362091064453, 98.75955200195312, 791.4871978759766], [98.62346649169922, 765.7547149658203, 171.83206939697266, 791.4624176025391], [190.0, 765.0, 279.0, 792.0], [43.16623497009277, 791.4602813720703, 70.21783065795898, 842.3455352783203], [70.14092254638672, 791.3896636962891, 98.78949737548828, 842.3396148681641], [98.68856048583984, 791.3759307861328, 171.9631118774414, 817.0721588134766], [192.0, 792.0, 522.0, 815.0], [98.60287475585938, 817.0325469970703, 171.9548568725586, 842.6392974853516], [192.0, 817.0, 314.0, 842.0], [43.19539260864258, 842.5456695556641, 70.32275009155273, 893.7231597900391], [70.19414138793945, 842.2754058837891, 98.78125, 867.8177032470703], [98.62796020507812, 842.7014923095703, 171.9393081665039, 868.0484771728516], [193.01648712158203, 842.9348907470703, 768.0, 868.6595611572266], [70.23447036743164, 867.6779937744141, 98.73946380615234, 893.6618194580078], [98.60003662109375, 867.9140777587891, 171.82573699951172, 893.6055450439453], [192.0, 869.0, 430.0, 892.0], [98.66432189941406, 893.5512237548828, 171.92618560791016, 919.4292755126953], [192.8521957397461, 894.6674957275391, 768.0, 919.2179718017578], [43.17406463623047, 919.0512237548828, 70.2250747680664, 944.5671539306641], [70.19976043701172, 919.1763458251953, 98.7444076538086, 944.6054229736328], [98.57827758789062, 919.4319000244141, 171.86275482177734, 970.1085357666016], [190.0, 919.0, 474.0, 942.0], [43.16107940673828, 944.7960968017578, 70.32453536987305, 970.2577667236328], [70.20840454101562, 944.4519805908203, 98.75898742675781, 970.0485992431641], [193.10492706298828, 945.1350250244141, 768.0, 970.8529815673828], [43.197235107421875, 969.9580841064453, 70.34088134765625, 995.9383697509766], [70.24007415771484, 969.9245758056641, 98.74183654785156, 995.8935089111328], [98.58756256103516, 969.9921417236328, 171.70378875732422, 995.8996124267578], [192.0, 969.0, 412.0, 992.0], [98.68399810791016, 995.7721710205078, 171.82477569580078, 1021.5418243408203], [192.0, 996.0, 330.0, 1021.0], [43.16180610656738, 1021.2871246337891, 70.23741149902344, 1046.805923461914], [70.20586395263672, 1021.4553375244141, 98.79288482666016, 1072.268081665039], [98.6175765991211, 1021.6229400634766, 171.88361358642578, 1072.236587524414], [192.90230560302734, 1022.0874176025391, 768.0, 1046.6297149658203], [193.21460723876953, 1047.4137115478516, 768.0, 1072.7296905517578], [70.23491668701172, 1072.1059112548828, 98.76718139648438, 1097.9976348876953], [98.62970733642578, 1072.1903228759766, 171.79651641845703, 1097.996841430664], [190.0, 1071.0, 364.0, 1096.0], [98.69780731201172, 1097.8195343017578, 171.84386444091797, 1123.5037994384766], [193.26966094970703, 1098.1170806884766, 768.0, 1124.0807037353516], [70.20923614501953, 1123.3416900634766, 98.82017517089844, 1173.5606842041016], [98.65386962890625, 1123.719009399414, 171.8570327758789, 1173.923843383789], [192.0, 1124.0, 412.0, 1148.0], [193.3568344116211, 1149.244888305664, 768.0, 1199.613052368164], [98.0, 1173.0, 157.0, 1199.0], [70.1617202758789, 1199.135025024414, 98.80257415771484, 1274.932632446289], [98.6309814453125, 1199.3939361572266, 171.6294174194336, 1224.8414459228516], [190.0, 1199.0, 297.0, 1224.0], [43.15286064147949, 1224.0558013916016, 70.20711898803711, 1249.6871490478516], [98.64680480957031, 1224.6978912353516, 171.6609115600586, 1300.815689086914], [193.38121795654297, 1225.2472076416016, 768.0, 1250.8084869384766], [192.0, 1249.0, 438.0, 1273.0], [193.25029754638672, 1275.329849243164, 768.0, 1301.363784790039], [70.1652717590332, 1300.230484008789, 98.77120208740234, 1325.9640045166016], [98.58052825927734, 1300.7962799072266, 171.6351547241211, 1326.413833618164], [193.23334503173828, 1301.769790649414, 768.0, 1326.3853912353516], [43.141849517822266, 1325.8307037353516, 70.1940803527832, 1351.4544830322266], [70.14147186279297, 1325.799331665039, 98.78885650634766, 1376.813247680664], [98.65596771240234, 1326.0770416259766, 171.63524627685547, 1351.981704711914], [192.0, 1326.0, 332.0, 1351.0], [98.6190414428711, 1351.8643951416016, 171.66944122314453, 1402.643814086914], [193.53020477294922, 1351.9071197509766, 768.0, 1377.5528717041016], [193.47904205322266, 1377.361587524414, 768.0, 1403.542007446289], [43.17696952819824, 1402.1978912353516, 70.26512908935547, 1428.0289459228516], [70.18172836303711, 1402.1600494384766, 98.777099609375, 1428.012954711914], [98.60382843017578, 1402.547134399414, 171.64229583740234, 1428.2621002197266], [192.0, 1403.0, 297.0, 1428.0], [43.12583541870117, 1427.8885650634766, 70.21095275878906, 1453.535659790039], [70.14899063110352, 1427.870620727539, 98.78064727783203, 1453.5182037353516], [98.69449615478516, 1428.003677368164, 171.70378875732422, 1453.881851196289], [193.28978729248047, 1428.8170318603516, 768.0, 1454.462417602539], [43.11015510559082, 1453.471694946289, 70.18724060058594, 1478.872329711914], [70.14986419677734, 1453.4434967041016, 98.72994995117188, 1478.920425415039], [98.63665771484375, 1453.7804107666016, 171.82183074951172, 1504.7142486572266], [194.0, 1455.0, 371.0, 1478.0], [70.14508056640625, 1479.0113677978516, 98.7555160522461, 1504.4256744384766], [193.11080169677734, 1479.309341430664, 768.0, 1505.346939086914], [43.158287048339844, 1504.4071197509766, 70.25943374633789, 1530.252212524414], [70.17459106445312, 1504.382583618164, 98.74862670898438, 1530.1959381103516], [98.6230697631836, 1504.5875396728516, 171.80638885498047, 1530.4088287353516], [192.8664779663086, 1505.3265533447266, 768.0, 1530.640151977539], [43.13395309448242, 1530.120864868164, 70.22274780273438, 1555.7005767822266], [70.1447982788086, 1530.0946197509766, 98.76551055908203, 1555.6712799072266], [98.68922424316406, 1530.1170806884766, 171.81998443603516, 1555.9762115478516], [194.0, 1532.0, 401.0, 1555.0], [43.12031555175781, 1555.6473541259766, 70.20134353637695, 1581.1031646728516], [70.14353561401367, 1555.636001586914, 98.71974182128906, 1581.1158599853516], [98.616943359375, 1555.892837524414, 171.86548614501953, 1581.486343383789], [192.0, 1557.0, 367.0, 1582.0], [70.15166091918945, 1581.206802368164, 98.75886535644531, 1606.630630493164], [98.64220428466797, 1581.3504791259766, 171.8671646118164, 1606.8800201416016], [193.13326263427734, 1581.5702056884766, 768.0, 1607.3595123291016], [43.18710136413574, 1606.6309967041016, 70.24625396728516, 1632.487319946289], [70.17781829833984, 1606.5987701416016, 98.74600219726562, 1632.4723052978516], [98.62681579589844, 1606.737564086914, 171.8444595336914, 1632.672378540039], [194.0, 1607.0, 478.0, 1632.0], [43.18567085266113, 1632.251724243164, 70.22015762329102, 1657.871841430664], [70.17052841186523, 1632.232925415039, 98.79428100585938, 1657.833023071289], [98.70196533203125, 1632.337905883789, 171.82799530029297, 1658.3128814697266], [193.2963638305664, 1632.573989868164, 768.0, 1658.604019165039], [98.61360931396484, 1658.062759399414, 171.7969741821289, 1683.5855865478516], [188.0, 1655.0, 266.0, 1687.0], [100.0, 1682.0, 157.0, 1709.0], [193.9975357055664, 1683.5584869384766, 768.0, 1709.985366821289]], "pred_html": "<html><body><table><tbody><tr><td>0.000</td><td>厦膜后脏器</td><td></td></tr><tr><td></td><td></td><td>4.520</td></tr><tr><td></td><td>0.065</td><td>虚拟模式-健康问题发展趋势列表：</td></tr><tr><td>血管紧张素Ⅱ*</td><td>0.086 0.096</td><td>血管紧张素I*</td></tr><tr><td></td><td>胆固醇COMMONPLASMA CHOLESTERIN</td><td></td></tr><tr><td></td><td>0.115</td><td>血浆丰酸化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA</td></tr><tr><td>0.147</td><td>血红血球ERYTHROCYTES</td><td></td></tr><tr><td></td><td>0.154</td><td>BETA球蛋白*</td></tr><tr><td></td><td></td><td>0.163</td></tr><tr><td></td><td></td><td>0.183</td></tr><tr><td></td><td>0.112</td><td>脂肪酶*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.115</td></tr><tr><td></td><td>0.116</td><td>血清补体SERUMCOMPLEMENT</td></tr><tr><td></td><td></td><td>0.117</td></tr><tr><td>0.129</td><td>血肌酥SERUM CREATININE</td><td></td></tr><tr><td></td><td></td><td>0.130</td></tr><tr><td></td><td></td><td>0.130</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.130</td></tr><tr><td></td><td>0.131</td><td></td></tr><tr><td></td><td></td><td>0.133</td></tr><tr><td>0.134</td><td>血清中的氨基酸NITROGENOFAMINOACIDSINSERUM</td><td></td></tr><tr><td></td><td></td><td>0.134 0.135</td></tr><tr><td></td><td>嗜碱性粒细胞BASOPHILS</td><td></td></tr><tr><td></td><td></td><td>0.135</td></tr><tr><td>0.136</td><td>AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE</td><td></td></tr><tr><td></td><td></td><td>0.138</td></tr><tr><td></td><td></td><td>0.139</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.139</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.140</td></tr><tr><td></td><td>0.140</td><td>嗜酸性粒细胞EOSINOPHILES</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.141 0.142</td></tr><tr><td></td><td></td><td>唾液酸*</td></tr><tr><td></td><td></td><td>0.142</td></tr><tr><td>0.143</td><td>17-尿中酮类固醇</td><td></td></tr><tr><td></td><td></td><td>0.143 0.143</td></tr><tr><td></td><td>0.143</td><td>红细胞沉降率(ESR)</td></tr><tr><td>0.143</td><td>分段的中性粒细胞SEGMENTEDNEUTROPHILS</td><td></td></tr><tr><td></td><td>0.144 0.144</td><td>血清蛋白SERUMALBUMEN</td></tr><tr><td>0.144</td><td></td><td></td></tr><tr><td></td><td>0.144</td><td>抗利尿激素*</td></tr><tr><td></td><td>0.145 0.147 0.147</td><td>血细胞比容，全血*</td></tr><tr><td></td><td>0.147</td><td>胰岛素*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.147</td></tr><tr><td>皮质醇SERUMHYDROCORTISONE</td><td></td><td></td></tr><tr><td></td><td></td><td>0.149</td></tr><tr><td></td><td></td><td>0.149</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.150 0.150</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.150</td></tr><tr><td></td><td></td><td>0.151</td></tr><tr><td></td><td></td><td>0.151</td></tr><tr><td></td><td>0.152</td><td>维生素B2*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.152</td></tr><tr><td></td><td></td><td>0.152</td></tr><tr><td>0.152</td><td>备解素*</td><td>0.152</td></tr></tbody></table></body></html>", "table_ocr_pred": {"rec_polys": [[[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 77], [295, 77], [295, 102], [192, 102]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[190, 98], [271, 98], [271, 130], [190, 130]], [[190, 123], [462, 127], [461, 157], [190, 154]], [[98, 154], [162, 154], [162, 179], [98, 179]], [[192, 154], [319, 154], [319, 179], [192, 179]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[194, 179], [314, 179], [314, 204], [194, 204]], [[98, 204], [162, 204], [162, 229], [98, 229]], [[194, 206], [556, 206], [556, 229], [194, 229]], [[98, 231], [162, 231], [162, 256], [98, 256]], [[196, 234], [726, 234], [726, 252], [196, 252]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [415, 257], [415, 281], [192, 281]], [[98, 281], [155, 281], [155, 306], [98, 306]], [[191, 279], [305, 283], [304, 308], [190, 304]], [[98, 306], [155, 306], [155, 332], [98, 332]], [[194, 309], [306, 309], [306, 329], [194, 329]], [[98, 332], [155, 332], [155, 358], [98, 358]], [[192, 331], [406, 331], [406, 354], [192, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[190, 358], [260, 358], [260, 384], [190, 384]], [[98, 383], [157, 383], [157, 409], [98, 409]], [[192, 384], [689, 384], [689, 408], [192, 408]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[192, 409], [445, 409], [445, 433], [192, 433]], [[98, 433], [155, 433], [155, 459], [98, 459]], [[188, 431], [262, 431], [262, 463], [188, 463]], [[100, 458], [157, 458], [157, 484], [100, 484]], [[192, 459], [417, 459], [417, 484], [192, 484]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[194, 486], [456, 486], [456, 509], [194, 509]], [[98, 509], [155, 509], [155, 536], [98, 536]], [[194, 511], [441, 511], [441, 534], [194, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[190, 534], [246, 534], [246, 563], [190, 563]], [[98, 561], [155, 561], [155, 586], [98, 586]], [[185, 560], [243, 555], [247, 587], [189, 592]], [[98, 586], [157, 586], [157, 611], [98, 611]], [[192, 588], [476, 588], [476, 611], [192, 611]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[196, 617], [639, 617], [639, 635], [196, 635]], [[98, 636], [157, 636], [157, 663], [98, 663]], [[194, 640], [445, 640], [445, 663], [194, 663]], [[98, 663], [157, 663], [157, 688], [98, 688]], [[188, 663], [400, 661], [401, 686], [188, 688]], [[98, 688], [157, 688], [157, 715], [98, 715]], [[192, 690], [362, 690], [362, 713], [192, 713]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[194, 715], [731, 715], [731, 738], [194, 738]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[194, 740], [504, 740], [504, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[190, 765], [279, 765], [279, 792], [190, 792]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [522, 792], [522, 815], [192, 815]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [314, 817], [314, 842], [192, 842]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[192, 844], [606, 844], [606, 867], [192, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [430, 869], [430, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[190, 892], [386, 894], [386, 919], [190, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[190, 919], [474, 919], [474, 942], [190, 942]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[188, 944], [262, 944], [262, 971], [188, 971]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[192, 969], [412, 969], [412, 992], [192, 992]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [330, 996], [330, 1021], [192, 1021]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[190, 1019], [284, 1019], [284, 1047], [190, 1047]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[188, 1042], [264, 1042], [264, 1074], [188, 1074]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[190, 1071], [364, 1071], [364, 1096], [190, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [569, 1098], [569, 1121], [192, 1121]], [[98, 1121], [157, 1121], [157, 1148], [98, 1148]], [[192, 1124], [412, 1124], [412, 1148], [192, 1148]], [[98, 1148], [157, 1148], [157, 1173], [98, 1173]], [[190, 1148], [347, 1146], [347, 1171], [190, 1173]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[192, 1174], [393, 1174], [393, 1198], [192, 1198]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [297, 1199], [297, 1224], [190, 1224]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[192, 1224], [351, 1224], [351, 1249], [192, 1249]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1249], [438, 1249], [438, 1273], [192, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[192, 1276], [508, 1276], [508, 1300], [192, 1300]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[190, 1301], [260, 1301], [260, 1328], [190, 1328]], [[98, 1326], [155, 1326], [155, 1351], [98, 1351]], [[192, 1326], [332, 1326], [332, 1351], [192, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [500, 1353], [500, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1380], [473, 1380], [473, 1403], [192, 1403]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [297, 1403], [297, 1428], [192, 1428]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[194, 1428], [362, 1428], [362, 1453], [194, 1453]], [[98, 1453], [155, 1453], [155, 1480], [98, 1480]], [[194, 1455], [371, 1455], [371, 1478], [194, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[194, 1484], [412, 1484], [412, 1502], [194, 1502]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[194, 1509], [476, 1509], [476, 1527], [194, 1527]], [[98, 1530], [155, 1530], [155, 1555], [98, 1555]], [[194, 1532], [401, 1532], [401, 1555], [194, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[192, 1557], [367, 1557], [367, 1582], [192, 1582]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[190, 1582], [282, 1578], [283, 1605], [191, 1609]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[194, 1607], [478, 1607], [478, 1632], [194, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[194, 1636], [330, 1636], [330, 1655], [194, 1655]], [[98, 1657], [157, 1657], [157, 1684], [98, 1684]], [[188, 1655], [266, 1655], [266, 1687], [188, 1687]], [[100, 1682], [157, 1682], [157, 1709], [100, 1709]], [[192, 1684], [327, 1684], [327, 1709], [192, 1709]]], "rec_texts": ["0.000", "厦膜后脏器", "4.520", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.065", "血管紧张素Ⅱ*", "0.086", "血管紧张素I*", "0.096", "胆固醇COMMONPLASMA CHOLESTERIN", "0.115", "血浆丰酸化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA", "0.147", "血红血球ERYTHROCYTES", "0.154", "BETA球蛋白*", "0.163", "免疫球蛋白G*", "0.183", "血钾PLASMAPOTASSIUM", "0.112", "脂肪酶*", "0.115", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.116", "血清补体SERUMCOMPLEMENT", "0.117", "催乳素*", "0.129", "血肌酥SERUM CREATININE", "0.130", "伽马球蛋白GAMMA-GLOBULINS", "0.130", "血清溶菌酵SERUMLYSOZYME", "0.130", "肾素*", "0.131", "糖苷*", "0.133", "PERIPHERIC BLOOD LEUCOCYTES", "0.134", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.134", "肿瘤标志物MELANOGENE在尿", "0.135", "嗜碱性粒细胞BASOPHILS", "0.135", "甲状腺素结合球蛋白", "0.136", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.138", "血清淀粉酵素SERUMALPHAAMYLASE", "0.139", "醛固酮尿*", "0.139", "游离胆固醇FREEPLASMACHOLESTERIN", "0.139", "甲状腺球蛋白*", "0.140", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.140", "嗜酸性粒细胞EOSINOPHILES", "0.140", "17-血浆氧皮质类固醇类", "0.141", "嗜中性粒细胞STABNEUTROPHILS", "0.142", "唾液酸*", "0.142", "尿肌酥URINECREATININE", "0.143", "17-尿中酮类固醇", "0.143", "维生素B6*", "0.143", "多巴胺*", "0.143", "红细胞沉降率(ESR)", "0.143", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.144", "血清蛋白SERUMALBUMEN", "0.144", "RHEUMOFACTOR*", "0.144", "血红蛋白HAEMOGLOBIN", "0.144", "抗利尿激素*", "0.145", "血细胞比容，全血*", "0.147", "尿白血球URINE LEUCOCYTES", "0.147", "11 - PLASMA OXYCORTICOSTEROIDS", "0.147", "胰岛素*", "0.147", "抗链球菌溶血素*", "0.147", "生长激素SOMATOTROPICHORMONE", "0.149", "皮质醇SERUMHYDROCORTISONE", "0.149", "胰高血糖素*", "0.149", "血尿素BLOODUREA", "0.150", "总铁结合力（TIBC）", "0.150", "尿中尿酸URINEURICACID", "0.150", "酸性磷酸酵素ACIDPHOSPHATASE", "0.151", "血尿酸SERUMURICACID", "0.151", "肿瘤标志物胸苷激酶", "0.152", "维生素B2*", "0.152", "血浆磷脂PLASMA PHOSPHOTIDES", "0.152", "糖基化血红蛋白", "0.152", "备解素*", "0.152", "ALPHA2球蛋白*"], "rec_scores": [0.9992006421089172, 0.8574286699295044, 0.9966824650764465, 0.9888066649436951, 0.9718126654624939, 0.9999227523803711, 0.944828450679779, 0.9999481439590454, 0.9827127456665039, 0.9998941421508789, 0.9785137176513672, 0.9998438954353333, 0.9364428520202637, 0.9998653531074524, 0.9960286617279053, 0.9995157122612, 0.9764004945755005, 0.999607264995575, 0.9238863587379456, 0.9996045827865601, 0.9762188196182251, 0.9991083145141602, 0.9291876554489136, 0.9990711212158203, 0.9956688284873962, 0.9988662600517273, 0.9978330731391907, 0.9990885853767395, 0.9625581502914429, 0.9994887113571167, 0.9412553906440735, 0.9993056058883667, 0.9893432855606079, 0.9993315935134888, 0.9940602779388428, 0.9992548227310181, 0.9766652584075928, 0.9990667104721069, 0.9671332240104675, 0.9993680119514465, 0.9804553985595703, 0.9994457960128784, 0.996772289276123, 0.9993776082992554, 0.9919062256813049, 0.9992088079452515, 0.9986740350723267, 0.9994977712631226, 0.995712399482727, 0.9995177388191223, 0.9968676567077637, 0.9995840191841125, 0.9971523284912109, 0.9992262721061707, 0.97516930103302, 0.9994462728500366, 0.9961369037628174, 0.999477207660675, 0.9346173405647278, 0.9992429614067078, 0.997855544090271, 0.9990541338920593, 0.9977625608444214, 0.999261736869812, 0.9911320805549622, 0.9992793202400208, 0.9966058135032654, 0.9991812705993652, 0.9491941928863525, 0.9993795156478882, 0.9559463858604431, 0.9994878768920898, 0.9850637912750244, 0.9995259046554565, 0.993542492389679, 0.9992753863334656, 0.9426757097244263, 0.9994220733642578, 0.9424949884414673, 0.9994878768920898, 0.9951286911964417, 0.9993155598640442, 0.9986840486526489, 0.9990348815917969, 0.996536910533905, 0.9990319013595581, 0.9950233697891235, 0.9990326762199402, 0.977360725402832, 0.9991230964660645, 0.9724450707435608, 0.9993202090263367, 0.9693094491958618, 0.999464213848114, 0.964715301990509, 0.9992125630378723, 0.9526697397232056, 0.9990390539169312, 0.9675589799880981, 0.9993202090263367, 0.9962304830551147, 0.9994550943374634, 0.9967683553695679, 0.9988902807235718, 0.9693164825439453, 0.9988030195236206, 0.9903314709663391, 0.9994136095046997, 0.8903585076332092, 0.9995203018188477, 0.9971678256988525, 0.9994422197341919, 0.9950373768806458, 0.9991353154182434, 0.9990896582603455, 0.999468207359314, 0.9924169182777405, 0.9996203184127808, 0.9921790957450867, 0.9994977712631226, 0.9790579676628113, 0.9992698431015015, 0.9971086382865906, 0.9996511340141296, 0.9310247302055359, 0.9992640614509583, 0.9760513305664062], "rec_boxes": [[98, 77, 157, 102], [192, 77, 295, 102], [98, 102, 157, 129], [190, 98, 271, 130], [190, 123, 462, 157], [98, 154, 162, 179], [192, 154, 319, 179], [98, 179, 162, 204], [194, 179, 314, 204], [98, 204, 162, 229], [194, 206, 556, 229], [98, 231, 162, 256], [196, 234, 726, 252], [98, 256, 161, 281], [192, 257, 415, 281], [98, 281, 155, 306], [190, 279, 305, 308], [98, 306, 155, 332], [194, 309, 306, 329], [98, 332, 155, 358], [192, 331, 406, 354], [98, 358, 157, 383], [190, 358, 260, 384], [98, 383, 157, 409], [192, 384, 689, 408], [98, 408, 157, 434], [192, 409, 445, 433], [98, 433, 155, 459], [188, 431, 262, 463], [100, 458, 157, 484], [192, 459, 417, 484], [98, 484, 157, 509], [194, 486, 456, 509], [98, 509, 155, 536], [194, 511, 441, 534], [98, 536, 155, 561], [190, 534, 246, 563], [98, 561, 155, 586], [185, 555, 247, 592], [98, 586, 157, 611], [192, 588, 476, 611], [98, 611, 157, 638], [196, 617, 639, 635], [98, 636, 157, 663], [194, 640, 445, 663], [98, 663, 157, 688], [188, 661, 401, 688], [98, 688, 157, 715], [192, 690, 362, 713], [98, 713, 157, 740], [194, 715, 731, 738], [98, 740, 157, 767], [194, 740, 504, 765], [98, 765, 157, 790], [190, 765, 279, 792], [98, 790, 157, 817], [192, 792, 522, 815], [98, 815, 157, 842], [192, 817, 314, 842], [98, 840, 157, 867], [192, 844, 606, 867], [98, 867, 157, 892], [192, 869, 430, 892], [98, 892, 157, 919], [190, 892, 386, 919], [98, 917, 155, 944], [190, 919, 474, 942], [98, 944, 157, 969], [188, 944, 262, 971], [98, 969, 157, 996], [192, 969, 412, 992], [98, 994, 157, 1021], [192, 996, 330, 1021], [98, 1019, 157, 1046], [190, 1019, 284, 1047], [98, 1046, 157, 1071], [188, 1042, 264, 1074], [98, 1071, 157, 1098], [190, 1071, 364, 1096], [98, 1096, 157, 1123], [192, 1098, 569, 1121], [98, 1121, 157, 1148], [192, 1124, 412, 1148], [98, 1148, 157, 1173], [190, 1146, 347, 1173], [98, 1173, 157, 1199], [192, 1174, 393, 1198], [98, 1198, 157, 1224], [190, 1199, 297, 1224], [98, 1224, 157, 1249], [192, 1224, 351, 1249], [98, 1249, 157, 1274], [192, 1249, 438, 1273], [98, 1274, 157, 1301], [192, 1276, 508, 1300], [98, 1300, 157, 1326], [190, 1301, 260, 1328], [98, 1326, 155, 1351], [192, 1326, 332, 1351], [98, 1351, 157, 1376], [192, 1353, 500, 1376], [98, 1376, 157, 1403], [192, 1380, 473, 1403], [100, 1403, 157, 1428], [192, 1403, 297, 1428], [100, 1428, 157, 1453], [194, 1428, 362, 1453], [98, 1453, 155, 1480], [194, 1455, 371, 1478], [98, 1478, 157, 1505], [194, 1484, 412, 1502], [98, 1505, 157, 1530], [194, 1509, 476, 1527], [98, 1530, 155, 1555], [194, 1532, 401, 1555], [98, 1555, 155, 1582], [192, 1557, 367, 1582], [98, 1580, 157, 1607], [190, 1578, 283, 1609], [98, 1607, 157, 1632], [194, 1607, 478, 1632], [100, 1632, 157, 1657], [194, 1636, 330, 1655], [98, 1657, 157, 1684], [188, 1655, 266, 1687], [100, 1682, 157, 1709], [192, 1684, 327, 1709]]}}]}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//31fe010f-88c3-47ff-aa24-8ced3f0865c3/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-30T01%3A28%3A02Z%2F-1%2F%2F06bc0dfb4fa6a53c9881359444940579416e4133bdd5e8329eca1973f2534731", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//31fe010f-88c3-47ff-aa24-8ced3f0865c3/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-30T01%3A28%3A02Z%2F-1%2F%2F104db1bc099c55b7ff15419d07fe6737229cc3486d7bd1e22cce789d9b3ef560", "table_cell_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//31fe010f-88c3-47ff-aa24-8ced3f0865c3/table_cell_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-30T01%3A28%3A02Z%2F-1%2F%2F7cd9da417cd76226558775d65295d5da5a15486b51dc537a5ea10e5c14e1702d"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//31fe010f-88c3-47ff-aa24-8ced3f0865c3/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-30T01%3A28%3A02Z%2F-1%2F%2Ff6715f75d686af043ad0a5935b37770b3f2df81d063cd65f5c7e5ae9dc48f2c1"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}