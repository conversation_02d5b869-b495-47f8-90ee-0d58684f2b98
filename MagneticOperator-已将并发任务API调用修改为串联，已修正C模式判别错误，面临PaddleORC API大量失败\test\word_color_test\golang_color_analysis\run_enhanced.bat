@echo off
echo ========================================
echo 增强版颜色分析工具
echo ========================================
echo.

REM 设置调试模式（可选）
REM set COLOR_DEBUG=1

echo [信息] 正在下载依赖包...
go mod tidy
if %errorlevel% neq 0 (
    echo [错误] 依赖包下载失败
    pause
    exit /b 1
)

echo.
echo [信息] 正在编译程序...
go build -o enhanced_analyzer.exe .
if %errorlevel% neq 0 (
    echo [错误] 编译失败
    pause
    exit /b 1
)

echo.
echo [信息] 运行增强版分析...
enhanced_analyzer.exe enhanced

echo.
echo [完成] 分析完成，请查看 enhanced_output 目录中的结果
pause