{"environment": "production", "debug": false, "api_keys": {"ocr": {"api_url": "https://api.example.com/ocr", "table_api_url": "https://api.example.com/table-ocr", "token": "prod-ocr-token"}, "coze": {"api_url": "https://api.example.com/coze", "token": "prod-coze-token", "workflow_id_post_pic": "7501680491335614516", "workflow_id_post_registration": "7501680491335614516", "workflow_id_user_info": "7501680491335614516", "space_id": "7331689003143544832", "app_id": "7496871719090077733"}, "cloud_function": {"registrations_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice", "screenshot_records_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/screenshot-records/createOrUpdateScreenshotRecord", "siteInfoByDeviceMAC_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC"}}, "color_detection": {"debug_mode": false, "save_debug_files": false}}