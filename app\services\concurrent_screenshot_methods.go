package services

import (
	"MagneticOperator/app/middleware"
	"MagneticOperator/app/utils"
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/wailsapp/wails/v2/pkg/runtime"
	"go.uber.org/zap"
)

// ConcurrentScreenshotService 提供了并发截图相关的功能
type ConcurrentScreenshotService struct {
	ctx                         context.Context
	integratedScreenshotService *IntegratedScreenshotService
	screenshotService           *ScreenshotService
}

// NewConcurrentScreenshotService 创建一个新的 ConcurrentScreenshotService 实例
func NewConcurrentScreenshotService(ctx context.Context, integratedScreenshotService *IntegratedScreenshotService, screenshotService *ScreenshotService) *ConcurrentScreenshotService {
	return &ConcurrentScreenshotService{
		ctx:                         ctx,
		integratedScreenshotService: integratedScreenshotService,
		screenshotService:           screenshotService,
	}
}

// ===== 并发截图相关方法 =====

// ProgressUpdate 进度更新结构
type ProgressUpdate struct {
	Round    int    `json:"round"`
	Mode     string `json:"mode"`
	Progress int    `json:"progress"`
	Message  string `json:"message"`
}

// sendProgressEvent 发送进度事件到前端
func (s *ConcurrentScreenshotService) sendProgressEvent(progress int, mode, message string, round int) {
	if s.ctx == nil {
		return
	}

	runtime.EventsEmit(s.ctx, "concurrent-screenshot-progress", map[string]interface{}{
		"round":     round,
		"mode":      mode,
		"progress":  progress,
		"message":   message,
		"timestamp": time.Now().Unix(),
	})

	utils.LogInfo("发送进度事件", zap.Int("progress", progress), zap.String("mode", mode), zap.String("message", message))
}

// sendOCRProgressEvent 发送OCR进度事件到前端
func (s *ConcurrentScreenshotService) sendOCRProgressEvent(organName string, confidence, progress int, message string) {
	if s.ctx == nil {
		return
	}

	runtime.EventsEmit(s.ctx, "ocr-processing-progress", map[string]interface{}{
		"organName":  organName,
		"confidence": confidence,
		"progress":   progress,
		"message":    message,
		"timestamp":  time.Now().Unix(),
	})

	utils.LogInfo("发送OCR进度事件", zap.String("organName", organName), zap.Int("confidence", confidence))
}

// sendTaskCompletedEvent 发送任务完成事件到前端
func (s *ConcurrentScreenshotService) sendTaskCompletedEvent(taskType, message string, duration time.Duration, result interface{}) {
	if s.ctx == nil {
		return
	}

	runtime.EventsEmit(s.ctx, "task-completed", map[string]interface{}{
		"taskType":  taskType,
		"message":   message,
		"duration":  duration.Milliseconds(),
		"result":    result,
		"timestamp": time.Now().Unix(),
	})

	utils.LogInfo("发送任务完成事件", zap.String("taskType", taskType), zap.String("message", message))
}

// sendTaskErrorEvent 发送任务错误事件到前端
func (s *ConcurrentScreenshotService) sendTaskErrorEvent(taskType, errorMsg string) {
	if s.ctx == nil {
		return
	}

	runtime.EventsEmit(s.ctx, "task-error", map[string]interface{}{
		"taskType":  taskType,
		"error":     errorMsg,
		"message":   fmt.Sprintf("%s执行失败: %s", taskType, errorMsg),
		"timestamp": time.Now().Unix(),
	})

	utils.LogError("发送任务错误事件", taskType, fmt.Errorf(errorMsg))
}

// sendSystemStatusEvent 发送系统状态事件到前端
func (s *ConcurrentScreenshotService) sendSystemStatusEvent(status, message string, details interface{}) {
	if s.ctx == nil {
		return
	}

	runtime.EventsEmit(s.ctx, "system-status-update", map[string]interface{}{
		"status":    status,
		"message":   message,
		"details":   details,
		"timestamp": time.Now().Unix(),
	})

	utils.LogInfo("发送系统状态事件", zap.String("status", status), zap.String("message", message))
}

// processScreenshotSteps 处理截图步骤
func (s *ConcurrentScreenshotService) processScreenshotSteps(roundNumber int, mode, modeDisplay string) error {
	// 模拟截图处理过程
	for i := 1; i <= 5; i++ {
		progress := i * 20 // 每步20%
		message := fmt.Sprintf("第%d轮%s截图处理中... (%d/5)", roundNumber, modeDisplay, i)

		s.sendProgressEvent(progress, mode, message, roundNumber)

		// 模拟可能的错误
		if i == 3 && rand.Float32() < 0.1 { // 10%概率出错
			return utils.NewAppError(
				utils.ErrScreenshotProcess,
				"截图处理失败",
				"模拟的处理错误",
				nil,
			)
		}

		// 模拟处理时间
		time.Sleep(200 * time.Millisecond)
	}

	// 模拟OCR处理
	return s.processOCRSteps()
}

// processOCRSteps 处理OCR步骤
func (s *ConcurrentScreenshotService) processOCRSteps() error {
	organNames := []string{"肝脏", "心脏", "肺部", "肾脏", "脾脏"}
	for i, organName := range organNames {
		confidence := 75 + (i * 5) // 模拟置信度递增
		progress := (i + 1) * 20
		message := fmt.Sprintf("OCR识别: %s (置信度: %d%%)", organName, confidence)

		s.sendOCRProgressEvent(organName, confidence, progress, message)

		// 模拟OCR可能的错误
		if confidence < 80 && rand.Float32() < 0.05 { // 低置信度时5%概率出错
			return utils.NewAppError(
				utils.ErrOCRProcess,
				"OCR识别失败",
				fmt.Sprintf("器官%s识别置信度过低: %d%%", organName, confidence),
				nil,
			)
		}

		// 模拟OCR处理时间
		time.Sleep(150 * time.Millisecond)
	}

	return nil
}

// StartNewScreenshotRound 开始新的截图轮次
func (s *ConcurrentScreenshotService) StartNewScreenshotRound(userName string) (map[string]interface{}, error) {
	utils.LogOperation("开始新截图轮次", userName, "")

	// 检查并发截图服务是否可用
	if s.integratedScreenshotService == nil {
		return nil, fmt.Errorf("并发截图服务未初始化")
	}

	if !s.integratedScreenshotService.IsRunning() {
		return nil, fmt.Errorf("并发截图服务未运行")
	}

	// 开始新轮次
	round, err := s.integratedScreenshotService.StartNewRound(userName)
	if err != nil {
		utils.LogError("开始新轮次失败", userName, err)
		return nil, err
	}

	result := map[string]interface{}{
		"success":      true,
		"round_number": round.RoundNumber,
		"user_name":    round.UserName,
		"start_time":   round.StartTime,
		"status":       round.Status,
		"message":      fmt.Sprintf("第%d轮截图已开始", round.RoundNumber),
	}

	utils.LogInfo(fmt.Sprintf("新轮次%d已开始，用户: %s", round.RoundNumber, userName))
	return result, nil
}

// TakeConcurrentScreenshot 并发截图方法（优化版）
func (s *ConcurrentScreenshotService) TakeConcurrentScreenshot(roundNumber int, mode string) (map[string]interface{}, error) {
	ctx := context.Background()
	errorHandler := middleware.NewErrorHandler(utils.Logger)

	utils.LogInfo("开始并发截图", zap.Int("roundNumber", roundNumber), zap.String("mode", mode))

	// 参数验证
	if roundNumber <= 0 {
		err := utils.NewAppError(
			utils.ErrBusinessValidation,
			"参数错误",
			"轮次编号必须大于0",
			nil,
		).WithOperation("TakeConcurrentScreenshot")
		s.sendTaskErrorEvent("concurrent-screenshot", err.Error())
		return nil, err
	}

	if mode != "B" && mode != "C" {
		err := utils.NewAppError(
			utils.ErrBusinessValidation,
			"参数错误",
			"模式必须是B或C",
			nil,
		).WithOperation("TakeConcurrentScreenshot")
		s.sendTaskErrorEvent("concurrent-screenshot", err.Error())
		return nil, err
	}

	// 记录开始时间
	startTime := time.Now()

	// 发送开始事件
	modeDisplay := "B02生化分析"
	if mode == "C" || mode == "C03" {
		modeDisplay = "C03病理分析"
	}

	s.sendProgressEvent(0, mode, fmt.Sprintf("开始第%d轮%s截图...", roundNumber, modeDisplay), roundNumber)

	// 使用重试机制执行截图处理
	err := utils.RetryWithConfigAndCallback(ctx, utils.ScreenshotRetryConfig, func() error {
		return s.processScreenshotSteps(roundNumber, mode, modeDisplay)
	}, func(attempt int, err error) {
		utils.LogWarning("截图处理重试", zap.Int("attempt", attempt), zap.Int("roundNumber", roundNumber), zap.String("mode", mode), zap.Error(err))
		s.sendProgressEvent(0, mode, fmt.Sprintf("第%d次重试截图处理...", attempt), roundNumber)
	})

	if err != nil {
		appErr := errorHandler.HandleError(ctx, err, "TakeConcurrentScreenshot")
		s.sendTaskErrorEvent("concurrent-screenshot", appErr.Error())
		return nil, appErr
	}

	// 计算处理时间
	duration := time.Since(startTime)

	// 构建结果
	result := map[string]interface{}{
		"success":      true,
		"round_number": roundNumber,
		"mode":         mode,
		"user_name":    "测试用户",
		"status":       "completed",
		"message":      fmt.Sprintf("第%d轮%s截图已完成", roundNumber, modeDisplay),
		"duration":     duration.Milliseconds(),
		"timestamp":    time.Now().Unix(),
	}

	// 发送完成事件
	s.sendTaskCompletedEvent("concurrent-screenshot",
		fmt.Sprintf("第%d轮%s截图处理完成", roundNumber, modeDisplay),
		duration, result)

	utils.LogInfo("并发截图完成", zap.Int("roundNumber", roundNumber), zap.Duration("duration", duration))

	return result, nil
}

// fallbackToTraditionalScreenshot 回退到传统截图方式
func (s *ConcurrentScreenshotService) fallbackToTraditionalScreenshot(mode string) (map[string]interface{}, error) {
	// 获取当前用户信息
	userName := "默认用户" // 这里应该从当前状态获取用户名
	// TODO: 实现患者信息获取逻辑
	// if a.config != nil && len(a.config.PatientList) > 0 && a.currentPatientIndex < len(a.config.PatientList) {
	//	userName = a.config.PatientList[a.currentPatientIndex].Name
	// }

	// 执行传统截图
	imagePath, err := s.screenshotService.TakeScreenshot(mode, userName)
	if err != nil {
		return map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		}, err
	}

	return map[string]interface{}{
		"success":    true,
		"image_path": imagePath,
		"mode":       mode,
		"message":    "传统截图完成",
		"timestamp":  time.Now(),
		"concurrent": false,
	}, nil
}

// GetScreenshotRoundStatus 获取指定轮次状态
func (s *ConcurrentScreenshotService) GetScreenshotRoundStatus(roundNumber int) (map[string]interface{}, error) {
	if s.integratedScreenshotService == nil {
		return nil, fmt.Errorf("并发截图服务未初始化")
	}

	round, exists := s.integratedScreenshotService.GetRound(roundNumber)
	if !exists {
		return map[string]interface{}{
			"success": false,
			"error":   fmt.Sprintf("轮次 %d 不存在", roundNumber),
		}, fmt.Errorf("轮次 %d 不存在", roundNumber)
	}

	round.RLock()
	defer round.RUnlock()

	// 计算进度
	progress := 0.0
	if len(round.Screenshots) > 0 {
		progress = float64(len(round.Results)) / float64(len(round.Screenshots))
	}

	result := map[string]interface{}{
		"success":             true,
		"round_number":        round.RoundNumber,
		"user_name":           round.UserName,
		"status":              round.Status,
		"screenshots_count":   len(round.Screenshots),
		"results_count":       len(round.Results),
		"errors_count":        len(round.Errors),
		"start_time":          round.StartTime,
		"completed_time":      round.CompletedTime,
		"progress":            progress,
		"progress_percentage": progress * 100,
	}

	// 添加详细结果信息
	if len(round.Results) > 0 {
		results := make([]map[string]interface{}, len(round.Results))
		for i, result := range round.Results {
			resultInfo := map[string]interface{}{
				"task_id":   result.Task.ID,
				"mode":      result.Task.Mode,
				"duration":  result.Duration.Milliseconds(),
				"timestamp": result.Processed,
			}

			if result.Error != nil {
				resultInfo["success"] = false
				resultInfo["error"] = result.Error.Error()
			} else {
				resultInfo["success"] = true
				resultInfo["organ_name"] = result.Result.OrganName
				resultInfo["confidence"] = result.Result.Confidence
			}

			results[i] = resultInfo
		}
		result["results"] = results
	}

	// 添加错误信息
	if len(round.Errors) > 0 {
		result["errors"] = round.Errors
	}

	return result, nil
}

// GetScreenshotOverallProgress 获取截图整体进度
func (s *ConcurrentScreenshotService) GetScreenshotOverallProgress() (map[string]interface{}, error) {
	if s.integratedScreenshotService == nil {
		return map[string]interface{}{
			"success": false,
			"error":   "并发截图服务未初始化",
		}, fmt.Errorf("并发截图服务未初始化")
	}

	progress := s.integratedScreenshotService.GetOverallProgress()
	progress["success"] = true
	return progress, nil
}

// GetAllScreenshotRounds 获取所有轮次信息
func (s *ConcurrentScreenshotService) GetAllScreenshotRounds() (map[string]interface{}, error) {
	if s.integratedScreenshotService == nil {
		return nil, fmt.Errorf("并发截图服务未初始化")
	}

	rounds := s.integratedScreenshotService.GetAllRounds()
	return map[string]interface{}{
		"success": true,
		"rounds":  rounds,
		"total":   len(rounds),
	}, nil
}

// TestConcurrentScreenshots 测试B模式和C模式并发截图
func (s *ConcurrentScreenshotService) TestConcurrentScreenshots() (map[string]interface{}, error) {
	utils.LogOperation("并发截图测试", "B+C模式", "")

	// 检查并发截图服务是否可用
	if s.integratedScreenshotService == nil {
		return map[string]interface{}{
			"success": false,
			"error":   "并发截图服务未初始化",
		}, fmt.Errorf("并发截图服务未初始化")
	}

	if !s.integratedScreenshotService.IsRunning() {
		return map[string]interface{}{
			"success": false,
			"error":   "并发截图服务未运行",
		}, fmt.Errorf("并发截图服务未运行")
	}

	// 开始新轮次
	userName := "并发测试用户"
	// TODO: 实现患者信息获取逻辑
	// if a.config != nil && len(a.config.PatientList) > 0 && a.currentPatientIndex < len(a.config.PatientList) {
	//	userName = a.config.PatientList[a.currentPatientIndex].Name
	// }

	roundResult, err := s.integratedScreenshotService.StartNewRound(userName)
	if err != nil {
		return map[string]interface{}{
			"success": false,
			"error":   fmt.Sprintf("开始新轮次失败: %v", err),
		}, err
	}

	roundNumber := roundResult.RoundNumber
	utils.LogInfo(fmt.Sprintf("[并发测试] 开始轮次%d，用户: %s", roundNumber, userName))

	// 记录开始时间
	startTime := time.Now()

	// 同时启动B模式和C模式截图（真正并发）
	utils.LogInfo("[并发测试] 同时启动B模式和C模式截图...")

	// 使用goroutine确保真正并发执行
	var wg sync.WaitGroup
	var bErr, cErr error
	var bResult, cResult map[string]interface{}

	wg.Add(2)

	// B模式截图
	go func() {
		defer wg.Done()
		utils.LogInfo("[并发测试] B模式截图开始")
		bResult, bErr = s.TakeConcurrentScreenshot(roundNumber, "B")
		if bErr != nil {
			utils.LogError("[并发测试] B模式截图失败", "", bErr)
		} else {
			utils.LogInfo("[并发测试] B模式截图已提交")
		}
	}()

	// C模式截图
	go func() {
		defer wg.Done()
		utils.LogInfo("[并发测试] C模式截图开始")
		cResult, cErr = s.TakeConcurrentScreenshot(roundNumber, "C")
		if cErr != nil {
			utils.LogError("[并发测试] C模式截图失败", "", cErr)
		} else {
			utils.LogInfo("[并发测试] C模式截图已提交")
		}
	}()

	// 等待两个截图任务都提交完成
	wg.Wait()
	submissionTime := time.Since(startTime)

	utils.LogInfo(fmt.Sprintf("[并发测试] 两个截图任务提交完成，耗时: %v", submissionTime))

	// 构建返回结果
	result := map[string]interface{}{
		"success":         true,
		"round_number":    roundNumber,
		"user_name":       userName,
		"submission_time": submissionTime.String(),
		"start_time":      startTime,
		"b_mode_result":   bResult,
		"c_mode_result":   cResult,
		"message":         "B模式和C模式并发截图测试已启动",
	}

	// 检查是否有错误
	if bErr != nil || cErr != nil {
		result["errors"] = map[string]interface{}{
			"b_mode_error": bErr,
			"c_mode_error": cErr,
		}
		if bErr != nil && cErr != nil {
			result["success"] = false
			result["message"] = "B模式和C模式截图都失败"
		} else {
			result["message"] = "部分截图模式失败，请查看错误详情"
		}
	}

	utils.LogInfo(fmt.Sprintf("[并发测试] 测试完成，轮次%d", roundNumber))
	return result, nil
}

// GetConcurrentScreenshotServiceStatus 获取并发截图服务状态
func (s *ConcurrentScreenshotService) GetConcurrentScreenshotServiceStatus() map[string]interface{} {
	if s.integratedScreenshotService == nil {
		return map[string]interface{}{
			"initialized": false,
			"running":     false,
			"error":       "服务未初始化",
		}
	}

	isRunning := s.integratedScreenshotService.IsRunning()
	// currentRound := a.integratedScreenshotService.GetCurrentRound()
	// stats := a.integratedScreenshotService.GetOCRProcessorStats()

	// 注释：暂时跳过患者列表检查
	// TODO: 实现患者列表获取逻辑

	return map[string]interface{}{
		"initialized":   true,
		"running":       isRunning,
		"current_round": 1, // 暂时硬编码
		"ocr_stats": map[string]interface{}{
			"total_submitted":  0,
			"total_completed":  0,
			"total_failed":     0,
			"active_workers":   0,
			"queue_size":       0,
			"success_rate":     0.0,
			"average_duration": 0,
		},
	}
}

// WaitForScreenshotRoundCompletion 等待指定轮次完成（可选的阻塞方法）
func (s *ConcurrentScreenshotService) WaitForScreenshotRoundCompletion(roundNumber int, timeoutSeconds int) (map[string]interface{}, error) {
	if s.integratedScreenshotService == nil {
		return nil, fmt.Errorf("并发截图服务未初始化")
	}

	timeout := time.Duration(timeoutSeconds) * time.Second
	round, err := s.integratedScreenshotService.WaitForRoundCompletion(roundNumber, timeout)
	if err != nil {
		return map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		}, err
	}

	round.RLock()
	defer round.RUnlock()

	result := map[string]interface{}{
		"success":        true,
		"round_number":   round.RoundNumber,
		"status":         round.Status,
		"completed_time": round.CompletedTime,
		"duration":       round.CompletedTime.Sub(round.StartTime).Milliseconds(),
	}

	return result, nil
}

// TakeScreenshot 执行截图（兼容性方法）
func (s *ConcurrentScreenshotService) TakeScreenshot(roundNumber int, mode string) error {
	// 调用现有的 TakeConcurrentScreenshot 方法，忽略返回值
	_, err := s.TakeConcurrentScreenshot(roundNumber, mode)
	return err
}

// GetOCRProcessorStats 获取OCR处理器统计信息
func (s *ConcurrentScreenshotService) GetOCRProcessorStats() *ProcessorStats {
	// 返回默认的统计信息，因为当前结构体中没有直接的OCR处理器
	return &ProcessorStats{
		TotalSubmitted: 0,
		TotalCompleted: 0,
		TotalFailed:    0,
		AverageTime:    0,
		StartTime:      time.Now(),
	}
}

// ResetConcurrentScreenshotService 重置并发截图服务
func (s *ConcurrentScreenshotService) ResetConcurrentScreenshotService() (map[string]interface{}, error) {
	utils.LogOperation("重置并发截图服务", "", "")

	if s.integratedScreenshotService == nil {
		return nil, fmt.Errorf("并发截图服务未初始化")
	}

	// 停止服务
	if err := s.integratedScreenshotService.Shutdown(); err != nil {
		utils.LogError("停止并发截图服务失败", "", err)
	}

	// 重新初始化服务
	// 注释：暂时简化集成服务初始化
	// TODO: 实现完整的集成服务初始化
	// a.integratedScreenshotService = services.NewIntegratedScreenshotService(roundService, screenshotService, roundManager)
	// a.integratedScreenshotService = services.NewIntegratedScreenshotService(a.configService)
	// if a.integratedScreenshotService == nil {
	//	err := fmt.Errorf("创建集成截图服务失败：服务创建返回nil")
	//	utils.LogError("TakeConcurrentScreenshot", err.Error())
	//	return map[string]interface{}{
	//		"success": false,
	//		"error":   err.Error(),
	//	}, err
	// } else if err := a.integratedScreenshotService.Initialize(); err != nil {
	//	utils.LogError("重新初始化并发截图服务失败", "", err)
	//	return map[string]interface{}{
	//		"success": false,
	//		"error":   err.Error(),
	//	}, err
	// }

	// 设置最大轮次数
	s.integratedScreenshotService.SetMaxRounds(10)

	utils.LogInfo("并发截图服务已重置")
	return map[string]interface{}{
		"success": true,
		"message": "并发截图服务已重置",
	}, nil
}
