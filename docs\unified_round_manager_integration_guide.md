# 统一轮次管理器集成指南

## 概述

本指南详细说明如何将统一轮次管理器集成到现有的 `app.go` 文件中，实现三种轮次管理业务逻辑的统一。

## 集成步骤

### 第一步：修改App结构体

在 `app.go` 文件中的 `App` 结构体中添加统一轮次管理器字段：

```go
type App struct {
    ctx context.Context

    // 配置相关
    configService *services.ConfigService

    // 截图相关服务
    screenshotService       *services.ScreenshotService
    integratedService       *services.IntegratedScreenshotService
    concurrentOCRProcessor  *services.ConcurrentOCRProcessor
    screenshotRoundManager  *services.ScreenshotRoundManager
    roundService            *services.RoundService

    // 快捷键服务
    hotkeyService *services.HotkeyService

    // 云服务
    cloudService *services.CloudService

    // 统一轮次管理器 - 新的统一轮次管理系统
    unifiedRoundIntegration *services.UnifiedRoundIntegration

    // 轮次管理 - 用于跟踪每个用户的轮次状态 (已弃用，保留用于兼容性)
    roundManager map[string]*RoundStatus

    // 10轮检测数据收集 (已弃用，保留用于兼容性)
    currentUserCheckingInfo map[string]*models.CurrentUserCheckingInfo
    checkingInfoMutex       sync.RWMutex // 保护用户检测信息的并发访问 (已弃用，保留用于兼容性)

    // OCR任务追踪
    ocrTaskContexts map[string]*OCRTaskContext
    ocrTaskMutex    sync.RWMutex               // 保护OCR任务上下文的并发访问
}
```

### 第二步：修改NewApp函数

在 `NewApp` 函数中初始化统一轮次管理器：

```go
func NewApp() *App {
    app := &App{
        currentUserCheckingInfo: make(map[string]*models.CurrentUserCheckingInfo),
        ocrTaskContexts:         make(map[string]*OCRTaskContext),
        roundManager:           make(map[string]*RoundStatus), // 保留用于兼容性
    }
    
    // 初始化统一轮次管理器
    app.unifiedRoundIntegration = services.NewUnifiedRoundIntegration(app)
    
    return app
}
```

### 第三步：修改startup函数

在 `startup` 函数中启动统一轮次管理器：

```go
func (a *App) startup(ctx context.Context) {
    a.ctx = ctx
    
    // 现有的初始化逻辑...
    
    // 启动统一轮次管理器
    if a.unifiedRoundIntegration != nil {
        if err := a.unifiedRoundIntegration.Start(); err != nil {
            utils.LogError("启动统一轮次管理器失败", zap.Error(err))
        } else {
            utils.LogInfo("统一轮次管理器启动成功")
            
            // 从旧系统迁移数据
            if err := a.unifiedRoundIntegration.MigrateFromLegacyRoundManager(); err != nil {
                utils.LogWarn("迁移轮次数据失败", zap.Error(err))
            } else {
                utils.LogInfo("轮次数据迁移成功")
            }
        }
    }
    
    // 其他现有的启动逻辑...
}
```

### 第四步：添加兼容性方法

在 `app.go` 文件中添加以下兼容性方法：

```go
// markModeCompleted 兼容性方法 - 使用统一轮次管理器
func (a *App) markModeCompletedUnified(userName, mode string) (int, bool, int) {
    if a.unifiedRoundIntegration != nil {
        return a.unifiedRoundIntegration.GetLegacyCompatibleMethods().MarkModeCompleted(userName, mode)
    }
    // 回退到旧方法
    return a.markModeCompleted(userName, mode)
}

// getCurrentRound 兼容性方法 - 使用统一轮次管理器
func (a *App) getCurrentRoundUnified(userName string) int {
    if a.unifiedRoundIntegration != nil {
        return a.unifiedRoundIntegration.GetLegacyCompatibleMethods().GetCurrentRound(userName)
    }
    // 回退到旧方法
    return a.getCurrentRound(userName)
}

// GetCurrentRoundNumber 兼容性方法 - 使用统一轮次管理器
func (a *App) GetCurrentRoundNumberUnified(userName string) int {
    if a.unifiedRoundIntegration != nil {
        return a.unifiedRoundIntegration.GetLegacyCompatibleMethods().GetCurrentRoundNumber(userName)
    }
    // 回退到旧方法
    return a.GetCurrentRoundNumber(userName)
}

// generateUserKey 生成用户键
func (a *App) generateUserKey(userName string) string {
    return fmt.Sprintf("%s_%s", userName, time.Now().Format("2006-01-02"))
}
```

### 第五步：添加管理方法

添加用于管理统一轮次管理器的方法：

```go
// GetUnifiedRoundManager 获取统一轮次管理器
func (a *App) GetUnifiedRoundManager() *services.UnifiedRoundManager {
    if a.unifiedRoundIntegration != nil {
        return a.unifiedRoundIntegration.GetManager()
    }
    return nil
}

// GetUnifiedRoundStatus 获取统一轮次状态
func (a *App) GetUnifiedRoundStatus(userName string) *services.UnifiedRoundStatus {
    if manager := a.GetUnifiedRoundManager(); manager != nil {
        return manager.GetRoundStatus(userName)
    }
    return nil
}

// IsUnifiedRoundManagerEnabled 检查统一轮次管理器是否启用
func (a *App) IsUnifiedRoundManagerEnabled() bool {
    return a.unifiedRoundIntegration != nil && a.unifiedRoundIntegration.GetManager().IsRunning()
}
```

### 第六步：逐步替换现有调用

逐步将现有代码中对旧轮次管理方法的调用替换为新的统一方法：

#### 替换 markModeCompleted 调用

**旧代码：**
```go
currentRound, roundCompleted, nextRound := a.markModeCompleted(userName, mode)
```

**新代码：**
```go
currentRound, roundCompleted, nextRound := a.markModeCompletedUnified(userName, mode)
```

#### 替换 getCurrentRound 调用

**旧代码：**
```go
currentRound := a.getCurrentRound(userName)
```

**新代码：**
```go
currentRound := a.getCurrentRoundUnified(userName)
```

#### 替换 GetCurrentRoundNumber 调用

**旧代码：**
```go
currentRound := a.GetCurrentRoundNumber(userName)
```

**新代码：**
```go
currentRound := a.GetCurrentRoundNumberUnified(userName)
```

### 第七步：更新updateCurrentUserCheckingInfo方法

修改 `updateCurrentUserCheckingInfo` 方法以使用统一轮次管理器：

```go
func (a *App) updateCurrentUserCheckingInfo(userName, mode, imagePath, organName string, ocrResult *services.OCRResult, dHealthTrend map[string]string, currentUser *models.Registration) error {
    // 如果统一轮次管理器可用，使用新的数据管理方式
    if a.unifiedRoundIntegration != nil {
        userKey := a.generateUserKey(userName)
        return a.unifiedRoundIntegration.GetLegacyCompatibleMethods().UpdateCurrentUserCheckingInfo(userKey, func(info *models.CurrentUserCheckingInfo) {
            // 将原有的更新逻辑迁移到这里
            // 这里需要实现具体的数据更新逻辑
            a.updateUserCheckingInfoData(info, userName, mode, imagePath, organName, ocrResult, dHealthTrend, currentUser)
        })
    }
    
    // 回退到原有方法（保留兼容性）
    return a.updateCurrentUserCheckingInfoLegacy(userName, mode, imagePath, organName, ocrResult, dHealthTrend, currentUser)
}

// updateUserCheckingInfoData 更新用户检测信息数据
func (a *App) updateUserCheckingInfoData(info *models.CurrentUserCheckingInfo, userName, mode, imagePath, organName string, ocrResult *services.OCRResult, dHealthTrend map[string]string, currentUser *models.Registration) {
    // 实现具体的数据更新逻辑
    // 这里需要将原有的updateCurrentUserCheckingInfo中的核心逻辑迁移过来
}

// updateCurrentUserCheckingInfoLegacy 原有的更新方法（重命名保留）
func (a *App) updateCurrentUserCheckingInfoLegacy(userName, mode, imagePath, organName string, ocrResult *services.OCRResult, dHealthTrend map[string]string, currentUser *models.Registration) error {
    // 原有的updateCurrentUserCheckingInfo实现
    // ...
}
```

## 迁移策略

### 阶段一：并行运行（推荐）

1. 保留现有的轮次管理逻辑
2. 同时运行统一轮次管理器
3. 在新的方法中优先使用统一管理器，失败时回退到旧方法
4. 逐步验证统一管理器的稳定性

### 阶段二：逐步替换

1. 确认统一管理器运行稳定后
2. 逐个模块替换对旧方法的调用
3. 保留旧方法作为备用

### 阶段三：完全迁移

1. 移除对旧方法的调用
2. 将旧字段标记为已弃用
3. 在后续版本中完全移除旧代码

## 验证和测试

### 功能验证

1. **轮次推进验证**：确认轮次能正确从1推进到10
2. **模式完成验证**：确认B02和C03模式完成状态正确管理
3. **数据同步验证**：确认用户检测数据正确同步
4. **事件通知验证**：确认Toast通知和进度条更新正常

### 性能测试

1. **并发测试**：多用户同时操作的并发安全性
2. **内存测试**：长时间运行的内存使用情况
3. **响应测试**：轮次操作的响应时间

### 兼容性测试

1. **向后兼容**：确认现有功能不受影响
2. **数据迁移**：确认旧数据能正确迁移到新系统
3. **错误恢复**：确认在统一管理器失败时能正确回退

## 监控和调试

### 日志监控

```go
// 添加详细的日志记录
utils.LogInfo("轮次管理器状态",
    zap.Bool("unified_enabled", a.IsUnifiedRoundManagerEnabled()),
    zap.String("user_name", userName),
    zap.Int("current_round", currentRound),
    zap.String("operation", "mode_completed"),
)
```

### 性能指标

```go
// 获取性能指标
if manager := a.GetUnifiedRoundManager(); manager != nil {
    metrics := manager.GetMetrics()
    utils.LogInfo("轮次管理器性能指标",
        zap.Int64("total_rounds", metrics.TotalRounds),
        zap.Int64("concurrent_users", metrics.ConcurrentUsers),
        zap.Duration("avg_round_duration", metrics.AvgRoundDuration),
    )
}
```

## 故障排除

### 常见问题

1. **统一管理器启动失败**
   - 检查依赖服务是否正确初始化
   - 查看错误日志确定具体原因

2. **数据迁移失败**
   - 检查旧数据格式是否正确
   - 确认用户键生成逻辑一致

3. **轮次状态不同步**
   - 检查事件总线是否正常工作
   - 确认数据同步逻辑正确执行

### 回退方案

如果统一轮次管理器出现问题，可以通过以下方式回退：

```go
// 临时禁用统一轮次管理器
func (a *App) DisableUnifiedRoundManager() {
    if a.unifiedRoundIntegration != nil {
        a.unifiedRoundIntegration.Stop()
        a.unifiedRoundIntegration = nil
        utils.LogWarn("统一轮次管理器已禁用，回退到旧系统")
    }
}
```

## 总结

通过以上步骤，可以成功将统一轮次管理器集成到现有系统中，实现三种轮次管理业务逻辑的统一。集成过程采用渐进式迁移策略，确保系统稳定性和向后兼容性。