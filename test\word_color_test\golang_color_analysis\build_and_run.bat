@echo off
chcp 65001 >nul
echo ========================================
echo Build and Run Go Color Analysis Tool
echo ========================================

REM Set Go environment
set GOOS=windows
set GOARCH=amd64

REM Build project
echo [BUILD] Compiling Go project...
go build -o color_analysis.exe .
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Build failed
    pause
    exit /b 1
)

echo [BUILD] Build successful!
echo.

REM Run program
echo [RUN] Starting color analysis...
color_analysis.exe

echo.
echo [DONE] Analysis completed, check output directory for results
pause