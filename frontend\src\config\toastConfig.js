/**
 * Toast通知系统配置文件
 * 集中管理所有Toast相关的配置选项
 */

// Toast类型定义
export const TOAST_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
}

// Toast位置选项
export const TOAST_POSITIONS = {
  TOP_RIGHT: 'top-right',
  TOP_LEFT: 'top-left',
  TOP_CENTER: 'top-center',
  BOTTOM_RIGHT: 'bottom-right',
  BOTTOM_LEFT: 'bottom-left',
  BOTTOM_CENTER: 'bottom-center'
}

// 默认持续时间（毫秒）
export const DEFAULT_DURATIONS = {
  [TOAST_TYPES.SUCCESS]: 5000,
  [TOAST_TYPES.ERROR]: 8000,
  [TOAST_TYPES.WARNING]: 6000,
  [TOAST_TYPES.INFO]: 5000
}

// Toast通知配置
export const TOAST_CONFIG = {
  // 基础配置
  maxToasts: 5,                    // 最大同时显示的Toast数量
  defaultDuration: 6000,           // 默认显示时长
  position: TOAST_POSITIONS.TOP_RIGHT, // 默认位置
  enableQueue: true,               // 启用队列管理
  
  // Wails运行时配置
  retryAttempts: 10,               // 最大重试次数
  retryDelay: 100,                 // 重试间隔（毫秒）
  
  // 调试配置
  debug: process.env.NODE_ENV === 'development', // 开发环境启用调试
  
  // 动画配置
  animation: {
    enter: 'toast-enter',          // 进入动画类名
    leave: 'toast-leave',          // 离开动画类名
    duration: 400                  // 动画持续时间
  },
  
  // 样式配置
  styling: {
    borderRadius: '12px',          // 圆角大小
    backdropBlur: '20px',          // 背景模糊
    shadow: '0 8px 32px rgba(0, 0, 0, 0.12)', // 阴影
    minWidth: '320px',             // 最小宽度
    maxWidth: '90vw'               // 最大宽度
  },
  
  // 可访问性配置
  accessibility: {
    announceToScreenReader: true,   // 向屏幕阅读器宣布
    focusOnShow: false,            // 显示时是否获取焦点
    keyboardDismiss: true          // 支持键盘关闭
  },
  
  // 行为配置
  behavior: {
    pauseOnHover: true,            // 鼠标悬停时暂停自动关闭
    closeOnClick: false,           // 点击内容区域关闭
    showCloseButton: true,         // 显示关闭按钮
    showProgress: false,           // 显示进度条
    stackNewest: 'top'             // 新Toast显示位置：'top' | 'bottom'
  }
}

// 预定义的Toast模板
export const TOAST_TEMPLATES = {
  // 操作成功
  OPERATION_SUCCESS: {
    type: TOAST_TYPES.SUCCESS,
    title: '操作成功',
    duration: DEFAULT_DURATIONS[TOAST_TYPES.SUCCESS]
  },
  
  // 操作失败
  OPERATION_ERROR: {
    type: TOAST_TYPES.ERROR,
    title: '操作失败',
    duration: DEFAULT_DURATIONS[TOAST_TYPES.ERROR]
  },
  
  // 网络错误
  NETWORK_ERROR: {
    type: TOAST_TYPES.ERROR,
    title: '网络错误',
    message: '请检查网络连接后重试',
    duration: DEFAULT_DURATIONS[TOAST_TYPES.ERROR]
  },
  
  // 权限错误
  PERMISSION_ERROR: {
    type: TOAST_TYPES.WARNING,
    title: '权限不足',
    message: '您没有执行此操作的权限',
    duration: DEFAULT_DURATIONS[TOAST_TYPES.WARNING]
  },
  
  // 数据保存成功
  SAVE_SUCCESS: {
    type: TOAST_TYPES.SUCCESS,
    title: '保存成功',
    message: '数据已成功保存',
    duration: DEFAULT_DURATIONS[TOAST_TYPES.SUCCESS]
  },
  
  // 数据加载失败
  LOAD_ERROR: {
    type: TOAST_TYPES.ERROR,
    title: '加载失败',
    message: '数据加载失败，请重试',
    duration: DEFAULT_DURATIONS[TOAST_TYPES.ERROR]
  },
  
  // 系统维护
  SYSTEM_MAINTENANCE: {
    type: TOAST_TYPES.INFO,
    title: '系统维护',
    message: '系统正在维护中，部分功能可能受限',
    duration: 10000
  },
  
  // 文件上传成功
  UPLOAD_SUCCESS: {
    type: TOAST_TYPES.SUCCESS,
    title: '上传成功',
    duration: DEFAULT_DURATIONS[TOAST_TYPES.SUCCESS]
  },
  
  // 文件上传失败
  UPLOAD_ERROR: {
    type: TOAST_TYPES.ERROR,
    title: '上传失败',
    duration: DEFAULT_DURATIONS[TOAST_TYPES.ERROR]
  },
  
  // 连接断开
  CONNECTION_LOST: {
    type: TOAST_TYPES.WARNING,
    title: '连接断开',
    message: '与服务器的连接已断开，正在尝试重连...',
    duration: 0 // 不自动关闭
  },
  
  // 连接恢复
  CONNECTION_RESTORED: {
    type: TOAST_TYPES.SUCCESS,
    title: '连接恢复',
    message: '与服务器的连接已恢复',
    duration: DEFAULT_DURATIONS[TOAST_TYPES.SUCCESS]
  }
}

// 特定业务场景的Toast配置
export const BUSINESS_TOAST_CONFIG = {
  // 磁共振操作相关
  MRI_OPERATION: {
    SCAN_START: {
      type: TOAST_TYPES.INFO,
      title: '扫描开始',
      message: '磁共振扫描已开始，请保持静止',
      duration: 8000,
      showProgress: true
    },
    
    SCAN_COMPLETE: {
      type: TOAST_TYPES.SUCCESS,
      title: '扫描完成',
      message: '磁共振扫描已完成',
      duration: DEFAULT_DURATIONS[TOAST_TYPES.SUCCESS]
    },
    
    SCAN_ERROR: {
      type: TOAST_TYPES.ERROR,
      title: '扫描异常',
      message: '扫描过程中发生异常，请联系技术人员',
      duration: 0 // 需要手动关闭
    },
    
    PATIENT_MOVEMENT: {
      type: TOAST_TYPES.WARNING,
      title: '检测到移动',
      message: '检测到患者移动，可能影响扫描质量',
      duration: DEFAULT_DURATIONS[TOAST_TYPES.WARNING]
    }
  },
  
  // 患者管理相关
  PATIENT_MANAGEMENT: {
    REGISTRATION_SUCCESS: {
      type: TOAST_TYPES.SUCCESS,
      title: '登记成功',
      message: '患者信息已成功登记',
      duration: DEFAULT_DURATIONS[TOAST_TYPES.SUCCESS]
    },
    
    REGISTRATION_ERROR: {
      type: TOAST_TYPES.ERROR,
      title: '登记失败',
      duration: DEFAULT_DURATIONS[TOAST_TYPES.ERROR]
    },
    
    INFO_UPDATE_SUCCESS: {
      type: TOAST_TYPES.SUCCESS,
      title: '信息更新成功',
      duration: DEFAULT_DURATIONS[TOAST_TYPES.SUCCESS]
    },
    
    DUPLICATE_PATIENT: {
      type: TOAST_TYPES.WARNING,
      title: '患者信息重复',
      message: '检测到重复的患者信息，请确认',
      duration: DEFAULT_DURATIONS[TOAST_TYPES.WARNING]
    }
  },
  
  // 设备状态相关
  DEVICE_STATUS: {
    DEVICE_READY: {
      type: TOAST_TYPES.SUCCESS,
      title: '设备就绪',
      message: '磁共振设备已准备就绪',
      duration: DEFAULT_DURATIONS[TOAST_TYPES.SUCCESS]
    },
    
    DEVICE_ERROR: {
      type: TOAST_TYPES.ERROR,
      title: '设备故障',
      message: '设备检测到故障，请联系维护人员',
      duration: 0 // 需要手动关闭
    },
    
    DEVICE_MAINTENANCE: {
      type: TOAST_TYPES.INFO,
      title: '设备维护',
      message: '设备正在进行例行维护',
      duration: DEFAULT_DURATIONS[TOAST_TYPES.INFO]
    },
    
    CALIBRATION_COMPLETE: {
      type: TOAST_TYPES.SUCCESS,
      title: '校准完成',
      message: '设备校准已完成',
      duration: DEFAULT_DURATIONS[TOAST_TYPES.SUCCESS]
    }
  }
}

// 获取Toast配置的工具函数
export function getToastConfig(template, overrides = {}) {
  if (typeof template === 'string') {
    // 从预定义模板获取
    const templateConfig = TOAST_TEMPLATES[template] || 
                          BUSINESS_TOAST_CONFIG.MRI_OPERATION[template] ||
                          BUSINESS_TOAST_CONFIG.PATIENT_MANAGEMENT[template] ||
                          BUSINESS_TOAST_CONFIG.DEVICE_STATUS[template]
    
    if (!templateConfig) {
      console.warn(`Toast模板 '${template}' 不存在`)
      return { ...TOAST_TEMPLATES.OPERATION_SUCCESS, ...overrides }
    }
    
    return { ...templateConfig, ...overrides }
  }
  
  // 直接使用配置对象
  return { ...template, ...overrides }
}

// 验证Toast配置的工具函数
export function validateToastConfig(config) {
  const errors = []
  
  if (!config.title && !config.message) {
    errors.push('Toast必须包含标题或消息')
  }
  
  if (config.type && !Object.values(TOAST_TYPES).includes(config.type)) {
    errors.push(`无效的Toast类型: ${config.type}`)
  }
  
  if (config.duration !== undefined && (typeof config.duration !== 'number' || config.duration < 0)) {
    errors.push('持续时间必须是非负数')
  }
  
  if (config.position && !Object.values(TOAST_POSITIONS).includes(config.position)) {
    errors.push(`无效的Toast位置: ${config.position}`)
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

export default TOAST_CONFIG