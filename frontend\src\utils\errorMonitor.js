/**
 * 错误监控和报告服务
 */

import errorHandler from './errorHandler.js'

class ErrorMonitor {
  constructor() {
    this.isInitialized = false
    this.errorQueue = []
    this.config = {
      maxQueueSize: 100,
      reportInterval: 30000, // 30秒
      enableConsoleCapture: true,
      enableUnhandledRejection: true,
      enableResourceError: true,
      enablePerformanceMonitoring: true,
      reportEndpoint: null,
      apiKey: null,
      userId: null,
      sessionId: this.generateSessionId(),
      environment: process.env.NODE_ENV || 'development'
    }
    this.performanceData = {
      pageLoadTime: 0,
      resourceErrors: [],
      jsErrors: [],
      networkErrors: []
    }
  }

  /**
   * 初始化错误监控
   * @param {Object} config 配置选项
   */
  init(config = {}) {
    if (this.isInitialized) {
      console.warn('ErrorMonitor already initialized')
      return
    }

    this.config = { ...this.config, ...config }
    this.isInitialized = true

    // 监听全局错误
    this.setupGlobalErrorHandlers()
    
    // 监听性能数据
    if (this.config.enablePerformanceMonitoring) {
      this.setupPerformanceMonitoring()
    }
    
    // 定期报告错误
    this.startReporting()
    
    console.log('ErrorMonitor initialized', this.config)
  }

  /**
   * 设置全局错误处理器
   */
  setupGlobalErrorHandlers() {
    // JavaScript 错误
    window.addEventListener('error', (event) => {
      this.captureError({
        type: 'javascript',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
        stack: event.error?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent
      })
    })

    // Promise 未处理的拒绝
    if (this.config.enableUnhandledRejection) {
      window.addEventListener('unhandledrejection', (event) => {
        this.captureError({
          type: 'unhandled-promise',
          message: event.reason?.message || 'Unhandled Promise Rejection',
          reason: event.reason,
          stack: event.reason?.stack,
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent
        })
      })
    }

    // 资源加载错误
    if (this.config.enableResourceError) {
      window.addEventListener('error', (event) => {
        if (event.target !== window) {
          this.captureError({
            type: 'resource',
            message: `Failed to load resource: ${event.target.src || event.target.href}`,
            element: event.target.tagName,
            source: event.target.src || event.target.href,
            timestamp: Date.now(),
            url: window.location.href
          })
        }
      }, true)
    }

    // 控制台错误捕获
    if (this.config.enableConsoleCapture) {
      this.setupConsoleCapture()
    }
  }

  /**
   * 设置控制台捕获
   */
  setupConsoleCapture() {
    const originalConsoleError = console.error
    const originalConsoleWarn = console.warn

    console.error = (...args) => {
      this.captureError({
        type: 'console-error',
        message: args.join(' '),
        args: args,
        timestamp: Date.now(),
        url: window.location.href,
        stack: new Error().stack
      })
      originalConsoleError.apply(console, args)
    }

    console.warn = (...args) => {
      this.captureError({
        type: 'console-warn',
        message: args.join(' '),
        args: args,
        timestamp: Date.now(),
        url: window.location.href,
        stack: new Error().stack
      })
      originalConsoleWarn.apply(console, args)
    }
  }

  /**
   * 设置性能监控
   */
  setupPerformanceMonitoring() {
    // 页面加载时间
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0]
        if (navigation) {
          this.performanceData.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart
        }
      }, 0)
    })

    // 监听性能条目
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'measure' || entry.entryType === 'mark') {
              // 记录自定义性能标记
              this.recordPerformance(entry)
            }
          }
        })
        observer.observe({ entryTypes: ['measure', 'mark'] })
      } catch (e) {
        console.warn('PerformanceObserver not supported', e)
      }
    }
  }

  /**
   * 捕获错误
   * @param {Object} errorData 错误数据
   */
  captureError(errorData) {
    const enrichedError = {
      ...errorData,
      sessionId: this.config.sessionId,
      userId: this.config.userId,
      environment: this.config.environment,
      timestamp: errorData.timestamp || Date.now(),
      id: this.generateErrorId()
    }

    // 添加到队列
    this.errorQueue.push(enrichedError)

    // 限制队列大小
    if (this.errorQueue.length > this.config.maxQueueSize) {
      this.errorQueue.shift()
    }

    // 立即处理严重错误
    if (this.isCriticalError(errorData)) {
      this.reportImmediately([enrichedError])
    }

    console.log('Error captured:', enrichedError)
  }

  /**
   * 手动报告错误
   * @param {Error} error 错误对象
   * @param {Object} context 上下文信息
   */
  reportError(error, context = {}) {
    const errorData = {
      type: 'manual',
      message: error.message,
      stack: error.stack,
      name: error.name,
      context: context,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent
    }

    this.captureError(errorData)
  }

  /**
   * 记录性能数据
   * @param {PerformanceEntry} entry 性能条目
   */
  recordPerformance(entry) {
    const performanceData = {
      name: entry.name,
      entryType: entry.entryType,
      startTime: entry.startTime,
      duration: entry.duration,
      timestamp: Date.now()
    }

    console.log('Performance recorded:', performanceData)
  }

  /**
   * 开始定期报告
   */
  startReporting() {
    setInterval(() => {
      if (this.errorQueue.length > 0) {
        this.reportErrors()
      }
    }, this.config.reportInterval)
  }

  /**
   * 报告错误
   */
  async reportErrors() {
    if (this.errorQueue.length === 0) return

    const errorsToReport = [...this.errorQueue]
    this.errorQueue = []

    try {
      await this.sendErrorReport(errorsToReport)
      console.log(`Reported ${errorsToReport.length} errors`)
    } catch (error) {
      console.error('Failed to report errors:', error)
      // 将错误重新加入队列
      this.errorQueue.unshift(...errorsToReport)
    }
  }

  /**
   * 立即报告错误
   * @param {Array} errors 错误数组
   */
  async reportImmediately(errors) {
    try {
      await this.sendErrorReport(errors)
      console.log('Critical errors reported immediately')
    } catch (error) {
      console.error('Failed to report critical errors:', error)
    }
  }

  /**
   * 发送错误报告
   * @param {Array} errors 错误数组
   */
  async sendErrorReport(errors) {
    const reportData = {
      errors: errors,
      performance: this.performanceData,
      metadata: {
        sessionId: this.config.sessionId,
        userId: this.config.userId,
        environment: this.config.environment,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        screen: {
          width: window.screen.width,
          height: window.screen.height
        }
      }
    }

    // 如果配置了报告端点，发送到服务器
    if (this.config.reportEndpoint) {
      const response = await fetch(this.config.reportEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
        },
        body: JSON.stringify(reportData)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } else {
      // 开发模式下输出到控制台
      console.group('Error Report')
      console.log('Errors:', errors)
      console.log('Performance:', this.performanceData)
      console.log('Metadata:', reportData.metadata)
      console.groupEnd()
    }
  }

  /**
   * 判断是否为严重错误
   * @param {Object} errorData 错误数据
   */
  isCriticalError(errorData) {
    const criticalPatterns = [
      /network error/i,
      /server error/i,
      /database/i,
      /authentication/i,
      /authorization/i,
      /security/i
    ]

    return criticalPatterns.some(pattern => 
      pattern.test(errorData.message) || 
      pattern.test(errorData.type)
    )
  }

  /**
   * 生成会话ID
   */
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 生成错误ID
   */
  generateErrorId() {
    return 'error_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 设置用户ID
   * @param {string} userId 用户ID
   */
  setUserId(userId) {
    this.config.userId = userId
  }

  /**
   * 添加上下文信息
   * @param {Object} context 上下文
   */
  addContext(context) {
    this.config.context = { ...this.config.context, ...context }
  }

  /**
   * 清空错误队列
   */
  clearErrorQueue() {
    this.errorQueue = []
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      total: this.errorQueue.length,
      byType: {},
      recent: this.errorQueue.slice(-10)
    }

    this.errorQueue.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1
    })

    return stats
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.isInitialized = false
    this.clearErrorQueue()
    // 这里可以移除事件监听器，但由于我们修改了全局对象，实际上很难完全清理
    console.log('ErrorMonitor destroyed')
  }
}

// 创建全局实例
const errorMonitor = new ErrorMonitor()

// 自动初始化（在生产环境中）
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  errorMonitor.init()
}

export default errorMonitor

// 导出类以便创建多个实例
export { ErrorMonitor }