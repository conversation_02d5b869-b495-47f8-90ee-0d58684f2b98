{"logId": "6d8995d7-90b1-45ff-a468-1b9c2e2a33c3", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 8, "label": "table", "score": 0.9865087866783142, "coordinate": [14.382545471191406, 74.5787124633789, 767.7635498046875, 1713.2388916015625]}, {"cls_id": 9, "label": "table_title", "score": 0.6379661560058594, "coordinate": [19.690658569335938, 27.134984970092773, 520.662841796875, 63.04995346069336]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.5128249526023865, "coordinate": [19.690658569335938, 27.134984970092773, 520.662841796875, 63.04995346069336]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[100, 77], [157, 77], [157, 102], [100, 102]], [[190, 77], [257, 77], [257, 104], [190, 104]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 127], [462, 127], [462, 152], [194, 152]], [[98, 154], [162, 154], [162, 179], [98, 179]], [[194, 156], [260, 156], [260, 181], [194, 181]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[192, 181], [498, 181], [498, 204], [192, 204]], [[98, 204], [162, 204], [162, 231], [98, 231]], [[194, 206], [449, 206], [449, 229], [194, 229]], [[98, 229], [161, 229], [161, 256], [98, 256]], [[196, 234], [432, 234], [432, 252], [196, 252]], [[98, 256], [155, 256], [155, 281], [98, 281]], [[194, 257], [404, 257], [404, 281], [194, 281]], [[98, 281], [155, 281], [155, 306], [98, 306]], [[194, 282], [569, 282], [569, 306], [194, 306]], [[98, 306], [155, 306], [155, 332], [98, 332]], [[192, 307], [364, 307], [364, 331], [192, 331]], [[98, 331], [155, 331], [155, 358], [98, 358]], [[194, 332], [406, 332], [406, 357], [194, 357]], [[98, 358], [155, 358], [155, 383], [98, 383]], [[190, 358], [260, 358], [260, 384], [190, 384]], [[98, 383], [157, 383], [157, 409], [98, 409]], [[192, 383], [460, 383], [460, 406], [192, 406]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[192, 409], [393, 409], [393, 433], [192, 433]], [[98, 433], [157, 433], [157, 459], [98, 459]], [[192, 434], [358, 434], [358, 459], [192, 459]], [[98, 458], [157, 458], [157, 484], [98, 484]], [[194, 463], [330, 463], [330, 483], [194, 483]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[192, 486], [441, 486], [441, 509], [192, 509]], [[98, 509], [157, 509], [157, 536], [98, 536]], [[188, 508], [262, 508], [262, 540], [188, 540]], [[98, 536], [157, 536], [157, 561], [98, 561]], [[192, 538], [401, 538], [401, 561], [192, 561]], [[98, 561], [157, 561], [157, 586], [98, 586]], [[194, 563], [559, 563], [559, 586], [194, 586]], [[98, 586], [157, 586], [157, 613], [98, 613]], [[194, 588], [449, 588], [449, 611], [194, 611]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[192, 611], [500, 611], [500, 635], [192, 635]], [[98, 636], [157, 636], [157, 663], [98, 663]], [[192, 638], [401, 638], [401, 661], [192, 661]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[188, 661], [299, 661], [299, 692], [188, 692]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[190, 690], [371, 686], [371, 711], [191, 715]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[192, 715], [314, 715], [314, 740], [192, 740]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[194, 740], [456, 740], [456, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[192, 767], [733, 767], [733, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [395, 792], [395, 815], [192, 815]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [330, 817], [330, 842], [192, 842]], [[98, 842], [157, 842], [157, 867], [98, 867]], [[190, 842], [279, 842], [279, 869], [190, 869]], [[98, 867], [157, 867], [157, 894], [98, 894]], [[188, 865], [360, 865], [360, 896], [188, 896]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[190, 892], [279, 892], [279, 919], [190, 919]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[188, 917], [291, 915], [292, 942], [189, 944]], [[98, 944], [155, 944], [155, 969], [98, 969]], [[190, 942], [377, 942], [377, 967], [190, 967]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[190, 969], [262, 969], [262, 996], [190, 996]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[188, 992], [264, 992], [264, 1024], [188, 1024]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[190, 1021], [332, 1021], [332, 1046], [190, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[188, 1044], [312, 1044], [312, 1074], [188, 1074]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[192, 1071], [360, 1071], [360, 1094], [192, 1094]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [349, 1098], [349, 1123], [192, 1123]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[188, 1119], [286, 1119], [286, 1149], [188, 1149]], [[98, 1148], [157, 1148], [157, 1174], [98, 1174]], [[192, 1148], [323, 1148], [323, 1173], [192, 1173]], [[98, 1173], [155, 1173], [155, 1199], [98, 1199]], [[192, 1174], [474, 1174], [474, 1198], [192, 1198]], [[98, 1199], [155, 1199], [155, 1224], [98, 1224]], [[194, 1203], [687, 1203], [687, 1221], [194, 1221]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[186, 1223], [263, 1218], [265, 1251], [188, 1255]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[190, 1249], [306, 1249], [306, 1274], [190, 1274]], [[98, 1274], [155, 1274], [155, 1301], [98, 1301]], [[194, 1276], [696, 1276], [696, 1300], [194, 1300]], [[98, 1301], [155, 1301], [155, 1326], [98, 1326]], [[194, 1305], [522, 1305], [522, 1323], [194, 1323]], [[98, 1326], [155, 1326], [155, 1351], [98, 1351]], [[190, 1326], [258, 1326], [258, 1353], [190, 1353]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[190, 1351], [593, 1351], [593, 1376], [190, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[188, 1376], [244, 1376], [244, 1405], [188, 1405]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[188, 1400], [264, 1400], [264, 1432], [188, 1432]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[192, 1428], [367, 1428], [367, 1453], [192, 1453]], [[98, 1453], [157, 1453], [157, 1478], [98, 1478]], [[194, 1457], [323, 1457], [323, 1476], [194, 1476]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[196, 1484], [534, 1484], [534, 1502], [196, 1502]], [[98, 1505], [155, 1505], [155, 1530], [98, 1530]], [[192, 1507], [594, 1507], [594, 1530], [192, 1530]], [[98, 1530], [155, 1530], [155, 1555], [98, 1555]], [[192, 1532], [445, 1532], [445, 1555], [192, 1555]], [[98, 1555], [157, 1555], [157, 1582], [98, 1582]], [[188, 1557], [242, 1557], [242, 1584], [188, 1584]], [[100, 1582], [157, 1582], [157, 1607], [100, 1607]], [[190, 1580], [375, 1582], [375, 1607], [190, 1605]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1607], [310, 1607], [310, 1632], [192, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[190, 1632], [244, 1632], [244, 1661], [190, 1661]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[194, 1661], [325, 1661], [325, 1680], [194, 1680]], [[100, 1684], [157, 1684], [157, 1709], [100, 1709]], [[192, 1684], [262, 1684], [262, 1711], [192, 1711]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["按照标准图谱相似度递减列表：", "0.000", "胃后壁", "2.829", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.054", "脂肪酶", "0.070", "PERIPHERIC BLOOD LEUCOCYTES", "0.072", "血组织胺BLOOD HISTAMINE", "0.079", "血清蛋白SERUMALBUMEN", "0.070", "血红血球ERYTHROCYTES", "0.079", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.086", "红细胞沉降率（ESR)", "0.093", "血清蛋白SERUM PROTEIN", "0.094", "多巴胺*", "0.060", "GLUTAMATEDEHYDROGENASE*", "0.064", "血红蛋白HAEMOGLOBIN", "0.065", "血清铁SERUM IRON", "0.070", "糖基化血红蛋白", "0.070", "血清溶菌酵SERUMLYSOZYME", "0.072", "催乳素*", "0.076", "前列腺特异性抗原（PSA）", "0.076", "血小板PERIPHERIC BLOOD THROMBOCYTES", "0.076", "血清补体SERUMCOMPLEMENT", "0.078", "血清淀粉酵素SERUMALPHAAMYLASE", "0.080", "嗜碱性粒细胞BASOPHILS", "0.081", "抗利尿激素*", "0.081", "总铁结合力（TIBC)", "0.083", "甲状腺球蛋白*", "0.083", "伽马球蛋白GAMMA-GLOBULINS", "0.084", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.084", "淋巴细胞LYMPHOCYTES", "0.084", "香草酚TEST*", "0.084", "甲状旁腺*", "0.085", "腺苷-血3.5环磷酸*", "0.086", "肌红蛋白*", "0.087", "维生素B12*", "0.087", "单核细胞MONOCYTES", "0.088", "铁蛋白*", "0.088", "备解素*", "0.088", "抗链球菌溶血素*", "0.088", "游离甲状腺素", "0.088", "血尿素BLOODUREA", "0.088", "血管紧张素转换酶", "0.089", "维生素B2*", "0.089", "GAMMA谷氨酰*", "0.090", "嗜中性粒细胞STABNEUTROPHILS", "0.090", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.090", "蛋白C*", "0.090", "BETA球蛋白*", "0.091", "血浆非酯化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA", "0.091", "游离胆固醇FREEPLASMACHOLESTERIN", "0.091", "降钙素", "0.092", "乳酸脱氢酵素COMMONLACTADEHYDROGENASE", "0.092", "睾酮*", "0.092", "胰岛素*", "0.093", "甲状腺素结合球蛋白", "0.093", "ALPHA2球蛋白", "0.094", "血浆中性脂肪NEUTRALFATSOFPLASMA", "0.094", "网织红细胞PERIPHERICBLOODRETICULOCYTES", "0.094", "肿瘤标志物MELANOGENE在尿", "0.094", "糖苷*", "0.094", "DELTA-氨基乙酰丙酸*", "0.094", "免疫球蛋白G*", "0.095", "肾素*", "0.095", "ALPHA1球蛋白*", "0.095", "胆汁酸*"], "rec_scores": [0.9954331517219543, 0.9991452097892761, 0.9793572425842285, 0.9994626045227051, 0.9983981251716614, 0.9706200361251831, 0.9999210238456726, 0.9962863922119141, 0.9999279975891113, 0.9762078523635864, 0.9999393224716187, 0.9765939116477966, 0.9999545812606812, 0.9804778099060059, 0.9995309114456177, 0.9493137001991272, 0.9996275901794434, 0.9976005554199219, 0.9995980262756348, 0.9187324047088623, 0.9997873306274414, 0.9770432710647583, 0.9995997548103333, 0.9273971319198608, 0.9997529983520508, 0.971610963344574, 0.9997116327285767, 0.995011568069458, 0.9997177124023438, 0.9696007966995239, 0.9996522665023804, 0.9965105056762695, 0.9996242523193359, 0.9968544840812683, 0.9997766613960266, 0.9764953851699829, 0.9996940493583679, 0.9535383582115173, 0.9996644854545593, 0.9680094718933105, 0.9997709393501282, 0.9975549578666687, 0.9997966885566711, 0.9958542585372925, 0.9997112154960632, 0.963718831539154, 0.9996433258056641, 0.981870174407959, 0.999719500541687, 0.9113099575042725, 0.9998405575752258, 0.9408621191978455, 0.999783992767334, 0.9952353239059448, 0.9996143579483032, 0.9958996772766113, 0.9997631311416626, 0.9943255186080933, 0.9997628927230835, 0.9872453212738037, 0.9996064305305481, 0.9626888036727905, 0.999744713306427, 0.9730015397071838, 0.9997943639755249, 0.958957850933075, 0.9997703433036804, 0.9916802644729614, 0.9996609687805176, 0.9985314011573792, 0.9996929168701172, 0.980333149433136, 0.9997851252555847, 0.9331592321395874, 0.9998102188110352, 0.9764025211334229, 0.9996644258499146, 0.9957056641578674, 0.9996929168701172, 0.994683027267456, 0.9997851252555847, 0.9982747435569763, 0.9996135830879211, 0.9526893496513367, 0.9997170567512512, 0.9852026700973511, 0.9996737241744995, 0.9952682256698608, 0.9995824098587036, 0.9850760102272034, 0.9995684623718262, 0.913559079170227, 0.9995887875556946, 0.9705867767333984, 0.9997507333755493, 0.9840340614318848, 0.9996320605278015, 0.996050238609314, 0.9996284246444702, 0.9981526732444763, 0.999703049659729, 0.9966501593589783, 0.9998161196708679, 0.9106177687644958, 0.9996370077133179, 0.9678974747657776, 0.9996572732925415, 0.9966065883636475, 0.999719500541687, 0.9932368397712708, 0.9998073577880859, 0.9928359389305115, 0.9996101260185242, 0.997238039970398, 0.9996415972709656, 0.9948570132255554, 0.9997735023498535, 0.8556062579154968, 0.9995912313461304, 0.9371978044509888, 0.9996482133865356, 0.9848665595054626, 0.9996343851089478, 0.9597945809364319, 0.9995914697647095, 0.9518951177597046, 0.9996017217636108, 0.9694901704788208], "rec_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[100, 77], [157, 77], [157, 102], [100, 102]], [[190, 77], [257, 77], [257, 104], [190, 104]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 127], [462, 127], [462, 152], [194, 152]], [[98, 154], [162, 154], [162, 179], [98, 179]], [[194, 156], [260, 156], [260, 181], [194, 181]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[192, 181], [498, 181], [498, 204], [192, 204]], [[98, 204], [162, 204], [162, 231], [98, 231]], [[194, 206], [449, 206], [449, 229], [194, 229]], [[98, 229], [161, 229], [161, 256], [98, 256]], [[196, 234], [432, 234], [432, 252], [196, 252]], [[98, 256], [155, 256], [155, 281], [98, 281]], [[194, 257], [404, 257], [404, 281], [194, 281]], [[98, 281], [155, 281], [155, 306], [98, 306]], [[194, 282], [569, 282], [569, 306], [194, 306]], [[98, 306], [155, 306], [155, 332], [98, 332]], [[192, 307], [364, 307], [364, 331], [192, 331]], [[98, 331], [155, 331], [155, 358], [98, 358]], [[194, 332], [406, 332], [406, 357], [194, 357]], [[98, 358], [155, 358], [155, 383], [98, 383]], [[190, 358], [260, 358], [260, 384], [190, 384]], [[98, 383], [157, 383], [157, 409], [98, 409]], [[192, 383], [460, 383], [460, 406], [192, 406]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[192, 409], [393, 409], [393, 433], [192, 433]], [[98, 433], [157, 433], [157, 459], [98, 459]], [[192, 434], [358, 434], [358, 459], [192, 459]], [[98, 458], [157, 458], [157, 484], [98, 484]], [[194, 463], [330, 463], [330, 483], [194, 483]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[192, 486], [441, 486], [441, 509], [192, 509]], [[98, 509], [157, 509], [157, 536], [98, 536]], [[188, 508], [262, 508], [262, 540], [188, 540]], [[98, 536], [157, 536], [157, 561], [98, 561]], [[192, 538], [401, 538], [401, 561], [192, 561]], [[98, 561], [157, 561], [157, 586], [98, 586]], [[194, 563], [559, 563], [559, 586], [194, 586]], [[98, 586], [157, 586], [157, 613], [98, 613]], [[194, 588], [449, 588], [449, 611], [194, 611]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[192, 611], [500, 611], [500, 635], [192, 635]], [[98, 636], [157, 636], [157, 663], [98, 663]], [[192, 638], [401, 638], [401, 661], [192, 661]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[188, 661], [299, 661], [299, 692], [188, 692]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[190, 690], [371, 686], [371, 711], [191, 715]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[192, 715], [314, 715], [314, 740], [192, 740]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[194, 740], [456, 740], [456, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[192, 767], [733, 767], [733, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [395, 792], [395, 815], [192, 815]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [330, 817], [330, 842], [192, 842]], [[98, 842], [157, 842], [157, 867], [98, 867]], [[190, 842], [279, 842], [279, 869], [190, 869]], [[98, 867], [157, 867], [157, 894], [98, 894]], [[188, 865], [360, 865], [360, 896], [188, 896]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[190, 892], [279, 892], [279, 919], [190, 919]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[188, 917], [291, 915], [292, 942], [189, 944]], [[98, 944], [155, 944], [155, 969], [98, 969]], [[190, 942], [377, 942], [377, 967], [190, 967]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[190, 969], [262, 969], [262, 996], [190, 996]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[188, 992], [264, 992], [264, 1024], [188, 1024]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[190, 1021], [332, 1021], [332, 1046], [190, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[188, 1044], [312, 1044], [312, 1074], [188, 1074]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[192, 1071], [360, 1071], [360, 1094], [192, 1094]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [349, 1098], [349, 1123], [192, 1123]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[188, 1119], [286, 1119], [286, 1149], [188, 1149]], [[98, 1148], [157, 1148], [157, 1174], [98, 1174]], [[192, 1148], [323, 1148], [323, 1173], [192, 1173]], [[98, 1173], [155, 1173], [155, 1199], [98, 1199]], [[192, 1174], [474, 1174], [474, 1198], [192, 1198]], [[98, 1199], [155, 1199], [155, 1224], [98, 1224]], [[194, 1203], [687, 1203], [687, 1221], [194, 1221]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[186, 1223], [263, 1218], [265, 1251], [188, 1255]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[190, 1249], [306, 1249], [306, 1274], [190, 1274]], [[98, 1274], [155, 1274], [155, 1301], [98, 1301]], [[194, 1276], [696, 1276], [696, 1300], [194, 1300]], [[98, 1301], [155, 1301], [155, 1326], [98, 1326]], [[194, 1305], [522, 1305], [522, 1323], [194, 1323]], [[98, 1326], [155, 1326], [155, 1351], [98, 1351]], [[190, 1326], [258, 1326], [258, 1353], [190, 1353]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[190, 1351], [593, 1351], [593, 1376], [190, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[188, 1376], [244, 1376], [244, 1405], [188, 1405]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[188, 1400], [264, 1400], [264, 1432], [188, 1432]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[192, 1428], [367, 1428], [367, 1453], [192, 1453]], [[98, 1453], [157, 1453], [157, 1478], [98, 1478]], [[194, 1457], [323, 1457], [323, 1476], [194, 1476]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[196, 1484], [534, 1484], [534, 1502], [196, 1502]], [[98, 1505], [155, 1505], [155, 1530], [98, 1530]], [[192, 1507], [594, 1507], [594, 1530], [192, 1530]], [[98, 1530], [155, 1530], [155, 1555], [98, 1555]], [[192, 1532], [445, 1532], [445, 1555], [192, 1555]], [[98, 1555], [157, 1555], [157, 1582], [98, 1582]], [[188, 1557], [242, 1557], [242, 1584], [188, 1584]], [[100, 1582], [157, 1582], [157, 1607], [100, 1607]], [[190, 1580], [375, 1582], [375, 1607], [190, 1605]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1607], [310, 1607], [310, 1632], [192, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[190, 1632], [244, 1632], [244, 1661], [190, 1661]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[194, 1661], [325, 1661], [325, 1680], [194, 1680]], [[100, 1684], [157, 1684], [157, 1709], [100, 1709]], [[192, 1684], [262, 1684], [262, 1711], [192, 1711]]], "rec_boxes": [[20, 30, 519, 61], [100, 77, 157, 102], [190, 77, 257, 104], [98, 102, 157, 127], [192, 102, 270, 129], [194, 127, 462, 152], [98, 154, 162, 179], [194, 156, 260, 181], [98, 179, 162, 204], [192, 181, 498, 204], [98, 204, 162, 231], [194, 206, 449, 229], [98, 229, 161, 256], [196, 234, 432, 252], [98, 256, 155, 281], [194, 257, 404, 281], [98, 281, 155, 306], [194, 282, 569, 306], [98, 306, 155, 332], [192, 307, 364, 331], [98, 331, 155, 358], [194, 332, 406, 357], [98, 358, 155, 383], [190, 358, 260, 384], [98, 383, 157, 409], [192, 383, 460, 406], [98, 408, 157, 434], [192, 409, 393, 433], [98, 433, 157, 459], [192, 434, 358, 459], [98, 458, 157, 484], [194, 463, 330, 483], [98, 484, 157, 509], [192, 486, 441, 509], [98, 509, 157, 536], [188, 508, 262, 540], [98, 536, 157, 561], [192, 538, 401, 561], [98, 561, 157, 586], [194, 563, 559, 586], [98, 586, 157, 613], [194, 588, 449, 611], [98, 611, 157, 638], [192, 611, 500, 635], [98, 636, 157, 663], [192, 638, 401, 661], [98, 663, 155, 688], [188, 661, 299, 692], [98, 688, 155, 715], [190, 686, 371, 715], [98, 713, 157, 740], [192, 715, 314, 740], [98, 740, 157, 767], [194, 740, 456, 765], [98, 765, 157, 790], [192, 767, 733, 790], [98, 790, 157, 817], [192, 792, 395, 815], [98, 815, 157, 842], [192, 817, 330, 842], [98, 842, 157, 867], [190, 842, 279, 869], [98, 867, 157, 894], [188, 865, 360, 896], [98, 892, 157, 919], [190, 892, 279, 919], [98, 917, 155, 944], [188, 915, 292, 944], [98, 944, 155, 969], [190, 942, 377, 967], [98, 969, 157, 996], [190, 969, 262, 996], [98, 994, 157, 1021], [188, 992, 264, 1024], [98, 1019, 157, 1046], [190, 1021, 332, 1046], [98, 1046, 157, 1071], [188, 1044, 312, 1074], [98, 1071, 157, 1098], [192, 1071, 360, 1094], [98, 1096, 157, 1123], [192, 1098, 349, 1123], [98, 1123, 157, 1148], [188, 1119, 286, 1149], [98, 1148, 157, 1174], [192, 1148, 323, 1173], [98, 1173, 155, 1199], [192, 1174, 474, 1198], [98, 1199, 155, 1224], [194, 1203, 687, 1221], [98, 1224, 157, 1249], [186, 1218, 265, 1255], [98, 1249, 157, 1274], [190, 1249, 306, 1274], [98, 1274, 155, 1301], [194, 1276, 696, 1300], [98, 1301, 155, 1326], [194, 1305, 522, 1323], [98, 1326, 155, 1351], [190, 1326, 258, 1353], [98, 1351, 157, 1376], [190, 1351, 593, 1376], [98, 1376, 157, 1403], [188, 1376, 244, 1405], [100, 1403, 157, 1428], [188, 1400, 264, 1432], [100, 1428, 157, 1453], [192, 1428, 367, 1453], [98, 1453, 157, 1478], [194, 1457, 323, 1476], [98, 1478, 157, 1505], [196, 1484, 534, 1502], [98, 1505, 155, 1530], [192, 1507, 594, 1530], [98, 1530, 155, 1555], [192, 1532, 445, 1555], [98, 1555, 157, 1582], [188, 1557, 242, 1584], [100, 1582, 157, 1607], [190, 1580, 375, 1607], [98, 1607, 157, 1632], [192, 1607, 310, 1632], [100, 1632, 157, 1657], [190, 1632, 244, 1661], [100, 1657, 157, 1684], [194, 1661, 325, 1680], [100, 1684, 157, 1709], [192, 1684, 262, 1711]]}, "table_res_list": [{"cell_box_list": [[100.0, 77.0, 157.0, 102.0], [193.86138153076172, 76.71678614616394, 766.8755264282227, 104.09708023071289], [42.73975944519043, 103.17174530029297, 69.68904876708984, 128.46234130859375], [69.66990661621094, 103.12642478942871, 98.0906753540039, 128.40447616577148], [97.97242736816406, 103.06602096557617, 170.2901382446289, 128.42341232299805], [192.0, 102.0, 270.0, 129.0], [42.72980308532715, 128.57262420654297, 69.65565490722656, 154.15440368652344], [69.64442825317383, 128.50184631347656, 98.07597351074219, 154.17868041992188], [97.96751403808594, 128.50413131713867, 170.31702423095703, 154.1014862060547], [193.71430206298828, 128.0753402709961, 767.2032241821289, 154.02787017822266], [17.46170949935913, 153.99200439453125, 42.67424011230469, 179.87130737304688], [42.71805763244629, 154.02303314208984, 69.65270614624023, 179.88812255859375], [69.6229476928711, 154.08341217041016, 98.11174011230469, 179.92577362060547], [97.98098754882812, 154.00145721435547, 170.40438079833984, 179.92623901367188], [193.30419158935547, 153.91565704345703, 767.1496963500977, 179.56256866455078], [42.708295822143555, 179.70673370361328, 69.66380310058594, 205.4706802368164], [69.66596221923828, 179.7162857055664, 98.11549377441406, 205.5334701538086], [98.08768463134766, 179.73522186279297, 170.55455780029297, 205.6161117553711], [192.0, 181.0, 498.0, 204.0], [42.733455657958984, 205.29109954833984, 69.6783561706543, 230.8576431274414], [69.68868255615234, 205.32215118408203, 98.12936401367188, 230.86847686767578], [98.02796173095703, 205.5027084350586, 170.56250762939453, 231.01297760009766], [192.63773345947266, 205.47834014892578, 767.1033096313477, 230.62700653076172], [69.70370864868164, 230.95279693603516, 98.18558502197266, 256.55261993408203], [98.10227966308594, 231.04462432861328, 170.84561920166016, 256.5843734741211], [192.81652069091797, 230.90706634521484, 767.0989151000977, 256.95829010009766], [42.69477081298828, 256.42269134521484, 69.6860580444336, 307.39171600341797], [69.66244888305664, 256.4337387084961, 98.17196655273438, 282.06946563720703], [98.03727722167969, 256.43949127197266, 171.23194122314453, 307.54520416259766], [194.0, 257.0, 404.0, 281.0], [69.67666625976562, 281.89017486572266, 98.19441223144531, 332.4413528442383], [192.71045684814453, 281.9751663208008, 766.9397964477539, 308.1328353881836], [98.06044006347656, 307.53694915771484, 171.17316436767578, 383.23197174072266], [192.0, 307.0, 364.0, 331.0], [192.6796646118164, 332.8746109008789, 767.2272720336914, 358.13965606689453], [42.74639892578125, 357.44409942626953, 69.69416427612305, 383.00098419189453], [69.67137145996094, 357.4484329223633, 98.22572326660156, 433.5496597290039], [190.0, 358.0, 260.0, 384.0], [98.10462188720703, 382.89688873291016, 171.03380584716797, 408.4852981567383], [192.0, 383.0, 460.0, 406.0], [42.809892654418945, 408.1313247680664, 69.71707153320312, 433.47391510009766], [98.04244995117188, 408.4885940551758, 170.94315338134766, 459.60266876220703], [192.7472152709961, 408.8417434692383, 767.1195449829102, 433.39990997314453], [42.85173988342285, 433.7434768676758, 69.7782974243164, 459.2024917602539], [69.7491226196289, 433.59757232666016, 98.2059555053711, 459.1337661743164], [192.75594329833984, 433.9904251098633, 767.1579360961914, 460.0837173461914], [42.745100021362305, 459.02857208251953, 69.72653579711914, 484.86573028564453], [69.68010711669922, 458.9578628540039, 98.17550659179688, 484.7578811645508], [98.05597686767578, 459.36975860595703, 170.89620208740234, 510.5490493774414], [194.0, 463.0, 330.0, 483.0], [42.69510078430176, 484.9216079711914, 69.68523788452148, 510.39331817626953], [69.70650863647461, 484.8147659301758, 98.1896743774414, 510.35440826416016], [192.0, 486.0, 441.0, 509.0], [42.771854400634766, 510.2859573364258, 69.7182731628418, 535.8459243774414], [69.69193649291992, 510.2451858520508, 98.15918731689453, 535.8765335083008], [98.02127075195312, 510.58133697509766, 170.84662628173828, 536.286262512207], [188.0, 508.0, 262.0, 540.0], [42.774885177612305, 536.0985794067383, 69.78278350830078, 587.1975479125977], [69.67236709594727, 535.9187393188477, 98.18568420410156, 587.0992202758789], [98.09095764160156, 536.2214431762695, 170.79352569580078, 561.8183059692383], [192.29859161376953, 536.4528274536133, 767.1557388305664, 562.1779556274414], [98.06349182128906, 561.616584777832, 170.92598724365234, 612.8030166625977], [192.39386749267578, 562.3464736938477, 767.3825454711914, 587.8219985961914], [42.724693298339844, 587.2954483032227, 69.72048950195312, 638.1666030883789], [69.67433166503906, 587.1036148071289, 98.15415954589844, 612.6471328735352], [194.0, 588.0, 449.0, 611.0], [69.68070602416992, 612.6061782836914, 98.14350891113281, 638.1802139282227], [98.01838684082031, 612.8519058227539, 170.91667938232422, 638.3973770141602], [192.44922637939453, 613.3013076782227, 767.3825454711914, 638.1253433227539], [42.776174545288086, 638.4114151000977, 69.7784309387207, 689.5806045532227], [69.7480239868164, 638.2587661743164, 98.19171142578125, 663.7490005493164], [98.10697937011719, 638.4912490844727, 170.87995147705078, 663.8082046508789], [192.2426528930664, 638.6428604125977, 767.0510635375977, 664.3642959594727], [69.67106628417969, 663.6152725219727, 98.14724731445312, 689.5242691040039], [98.04298400878906, 663.6628189086914, 170.88919830322266, 689.5807876586914], [188.0, 661.0, 299.0, 692.0], [42.70100212097168, 689.6752700805664, 69.72040939331055, 740.6515274047852], [69.6935806274414, 689.5190200805664, 98.1540756225586, 715.2652359008789], [98.13162231445312, 689.5298843383789, 171.04607391357422, 715.0908584594727], [192.44257354736328, 689.9731216430664, 767.3825454711914, 715.3303604125977], [69.70106506347656, 715.1371231079102, 98.16515350341797, 766.0865249633789], [98.04759979248047, 715.2493057250977, 170.94525909423828, 740.6951675415039], [192.0, 715.0, 314.0, 740.0], [42.833024978637695, 740.8883438110352, 69.77779006958008, 766.3236465454102], [98.11524963378906, 740.8973159790039, 170.94396209716797, 766.2017593383789], [192.46411895751953, 741.1362686157227, 767.2232437133789, 766.8796157836914], [42.77475547790527, 766.1625747680664, 69.73504638671875, 792.0501480102539], [69.65522003173828, 766.0870132446289, 98.15031433105469, 792.0303115844727], [98.04434967041016, 766.1849746704102, 170.9731216430664, 792.0684585571289], [192.58426666259766, 766.7409439086914, 767.3648452758789, 792.4464492797852], [69.6727294921875, 792.0417861938477, 98.16593170166016, 817.8415908813477], [98.13627624511719, 791.9710464477539, 171.0971908569336, 817.5009536743164], [192.0, 792.0, 395.0, 815.0], [42.80057907104492, 817.5398330688477, 69.72493743896484, 843.1380386352539], [69.68931198120117, 817.6631240844727, 98.19315338134766, 868.6194839477539], [98.07935333251953, 817.6612930297852, 171.02754974365234, 843.0210342407227], [192.69562530517578, 817.8356704711914, 767.3825454711914, 842.6480484008789], [98.11198425292969, 843.1377334594727, 171.0622787475586, 868.6387100219727], [192.55587005615234, 843.0799942016602, 767.2529067993164, 869.0254898071289], [69.59999465942383, 868.5292129516602, 98.17265319824219, 894.2324600219727], [98.05824279785156, 868.5649795532227, 171.0755386352539, 894.3607559204102], [188.0, 865.0, 360.0, 896.0], [69.56955337524414, 894.2433242797852, 98.21398162841797, 969.5318374633789], [98.0, 894.3411026000977, 171.14688873291016, 944.0], [192.72264862060547, 894.4043960571289, 767.2690811157227, 920.0749282836914], [188.0, 915.0, 292.0, 944.0], [98.06300354003906, 945.0727920532227, 171.09139251708984, 969.7423477172852], [192.55146026611328, 945.0846939086914, 767.3624649047852, 995.3926773071289], [98.0, 969.0, 157.0, 996.0], [69.60794448852539, 994.8526992797852, 98.14903259277344, 1045.726417541504], [98.00446319580078, 995.1945571899414, 170.9512710571289, 1046.1058731079102], [188.0, 992.0, 264.0, 1024.0], [42.73398017883301, 1020.0375137329102, 69.67312240600586, 1045.713722229004], [190.0, 1021.0, 332.0, 1046.0], [69.64690399169922, 1045.5546035766602, 98.10838317871094, 1071.1129531860352], [98.03502655029297, 1045.9903945922852, 170.90276336669922, 1096.8984756469727], [188.0, 1044.0, 312.0, 1074.0], [69.65742874145508, 1071.143165588379, 98.15567779541016, 1096.7062149047852], [192.66478729248047, 1071.6422500610352, 767.2169570922852, 1097.490577697754], [42.71234703063965, 1096.6327285766602, 69.6839828491211, 1122.445167541504], [69.6430549621582, 1096.6484756469727, 98.15410614013672, 1122.4440689086914], [98.03975677490234, 1096.8544082641602, 171.05506134033203, 1122.6183853149414], [192.0, 1098.0, 349.0, 1123.0], [42.71350288391113, 1122.3022232055664, 69.66257858276367, 1147.999122619629], [69.65298080444336, 1122.300636291504, 98.14710998535156, 1148.037940979004], [98.09960174560547, 1122.408546447754, 171.11896514892578, 1148.3288345336914], [188.0, 1119.0, 286.0, 1149.0], [42.74192428588867, 1147.850196838379, 69.67367553710938, 1173.499610900879], [69.66565322875977, 1147.872901916504, 98.11944580078125, 1173.520118713379], [98.04088592529297, 1148.207130432129, 171.0654525756836, 1173.8227310180664], [192.66890716552734, 1148.467384338379, 767.3516006469727, 1173.6000747680664], [69.67061233520508, 1173.5297622680664, 98.15478515625, 1199.080665588379], [98.08566284179688, 1173.7863540649414, 171.10538482666016, 1199.2883071899414], [192.5778579711914, 1173.993019104004, 767.3825454711914, 1199.9069595336914], [42.69764518737793, 1199.0507583618164, 69.68977737426758, 1224.8534927368164], [69.64641571044922, 1199.0400161743164, 98.15557098388672, 1224.8415298461914], [98.02615356445312, 1199.1833267211914, 171.29940032958984, 1224.9548110961914], [194.0, 1203.0, 687.0, 1221.0], [42.682870864868164, 1224.7319107055664, 69.66288375854492, 1275.815773010254], [69.65240859985352, 1224.7055435180664, 98.15348815917969, 1250.4152603149414], [98.11751556396484, 1224.787452697754, 171.3142318725586, 1250.6860122680664], [192.7336654663086, 1225.4162368774414, 767.3825454711914, 1251.038429260254], [69.66600799560547, 1250.2675552368164, 98.1073226928711, 1275.9125747680664], [98.03778839111328, 1250.5375747680664, 171.13282012939453, 1276.2223892211914], [190.0, 1249.0, 306.0, 1274.0], [69.6704330444336, 1275.936134338379, 98.15473937988281, 1301.5011978149414], [98.09671783447266, 1276.177101135254, 171.07794952392578, 1301.695167541504], [192.83264923095703, 1276.548194885254, 767.3825454711914, 1302.4567642211914], [42.68669128417969, 1301.468605041504, 69.68728637695312, 1327.298683166504], [69.65074157714844, 1301.4770278930664, 98.1568374633789, 1327.280616760254], [98.02995300292969, 1301.6462173461914, 171.0727767944336, 1327.411231994629], [192.83879852294922, 1302.445655822754, 767.3825454711914, 1327.6469497680664], [42.683021545410156, 1327.197364807129, 69.65176773071289, 1352.8161392211914], [69.64760971069336, 1327.166847229004, 98.13033294677734, 1352.8254165649414], [98.08245086669922, 1327.263526916504, 171.07938385009766, 1353.091163635254], [190.0, 1326.0, 258.0, 1353.0], [42.725332260131836, 1352.7507095336914, 69.68276596069336, 1378.3581314086914], [69.67032241821289, 1352.733253479004, 98.09353637695312, 1378.3859634399414], [98.00511932373047, 1352.952491760254, 171.0643081665039, 1378.550636291504], [192.89090728759766, 1353.348731994629, 767.3825454711914, 1378.426856994629], [69.67114639282227, 1378.417091369629, 98.13671875, 1403.931739807129], [98.06205749511719, 1378.514015197754, 171.11048126220703, 1404.0407485961914], [192.80388641357422, 1378.8661880493164, 767.0671768188477, 1404.3473892211914], [42.694780349731445, 1403.885841369629, 69.69303894042969, 1429.7128677368164], [69.63317108154297, 1403.8864517211914, 98.1400146484375, 1429.6686782836914], [98.01280975341797, 1403.970802307129, 171.2564926147461, 1455.214210510254], [188.0, 1400.0, 264.0, 1432.0], [42.678836822509766, 1429.568214416504, 69.64983367919922, 1455.1713638305664], [69.63218307495117, 1429.5344009399414, 98.12997436523438, 1455.142189025879], [193.03482818603516, 1429.8512954711914, 767.3825454711914, 1455.6288833618164], [42.717918395996094, 1455.098243713379, 69.65675735473633, 1480.484718322754], [69.64045333862305, 1455.0488052368164, 98.07515716552734, 1480.541603088379], [97.98931121826172, 1455.1835708618164, 171.19525909423828, 1505.9687271118164], [194.0, 1457.0, 323.0, 1476.0], [69.6036148071289, 1480.6020278930664, 98.12236785888672, 1556.114356994629], [192.74793243408203, 1480.782325744629, 767.3713150024414, 1506.539894104004], [42.66989326477051, 1505.768409729004, 69.68678665161133, 1531.148292541504], [98.00335693359375, 1505.929542541504, 171.17366790771484, 1581.5741958618164], [192.0, 1507.0, 594.0, 1530.0], [192.0, 1532.0, 445.0, 1555.0], [42.77241134643555, 1556.1445083618164, 69.6934585571289, 1581.015724182129], [69.67086029052734, 1556.1003189086914, 98.13153076171875, 1606.5390396118164], [192.68323516845703, 1557.409278869629, 767.3600234985352, 1607.776954650879], [98.05443572998047, 1581.480567932129, 171.00569915771484, 1607.068214416504], [42.76947021484375, 1606.530128479004, 69.73588180541992, 1632.4255142211914], [69.64072036743164, 1606.450782775879, 98.14688110351562, 1632.335060119629], [98.03324890136719, 1606.893898010254, 170.97928619384766, 1632.809669494629], [192.0, 1607.0, 310.0, 1632.0], [69.64063262939453, 1632.2836685180664, 98.19648742675781, 1682.9203872680664], [98.1187515258789, 1632.4789810180664, 170.95067596435547, 1658.1227798461914], [192.92174530029297, 1632.939552307129, 767.2856216430664, 1658.446876525879], [98.12615966796875, 1657.8735122680664, 170.68518829345703, 1683.641456604004], [194.0, 1661.0, 325.0, 1680.0], [100.0, 1684.0, 157.0, 1709.0], [192.0, 1684.0, 262.0, 1711.0]], "pred_html": "<html><body><table><tbody><tr><td>0.000</td><td>胃后壁</td><td></td></tr><tr><td></td><td></td><td>2.829</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.070</td></tr><tr><td></td><td></td><td>0.072</td></tr><tr><td></td><td>0.079</td><td>血清蛋白SERUMALBUMEN</td></tr><tr><td></td><td></td><td>0.070 0.079</td></tr><tr><td></td><td>分段的中性粒细胞SEGMENTEDNEUTROPHILS</td><td></td></tr><tr><td>0.086 0.093 0.094</td><td>红细胞沉降率（ESR)</td><td>血清蛋白SERUM PROTEIN</td></tr><tr><td></td><td></td><td>多巴胺*</td></tr><tr><td>0.060</td><td>GLUTAMATEDEHYDROGENASE*</td><td></td></tr><tr><td></td><td>0.064 0.065</td><td>血红蛋白HAEMOGLOBIN</td></tr><tr><td></td><td></td><td>血清铁SERUM IRON</td></tr><tr><td></td><td></td><td>0.070 0.070</td></tr><tr><td></td><td></td><td>血清溶菌酵SERUMLYSOZYME</td></tr><tr><td></td><td></td><td>0.072</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.076</td></tr><tr><td></td><td></td><td>血清补体SERUMCOMPLEMENT</td></tr><tr><td></td><td>0.078</td><td>血清淀粉酵素SERUMALPHAAMYLASE</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.080</td></tr><tr><td></td><td>0.081</td><td>抗利尿激素*</td></tr><tr><td></td><td></td><td>0.081</td></tr><tr><td></td><td>0.083</td><td>甲状腺球蛋白*</td></tr><tr><td></td><td>0.083</td><td>伽马球蛋白GAMMA-GLOBULINS</td></tr><tr><td></td><td></td><td>0.084</td></tr><tr><td></td><td>0.084</td><td>淋巴细胞LYMPHOCYTES</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.084</td></tr><tr><td></td><td>0.085</td><td>腺苷-血3.5环磷酸*</td></tr><tr><td></td><td>0.086 0.087</td><td>肌红蛋白*</td></tr><tr><td>维生素B12*</td><td>0.087</td><td>单核细胞MONOCYTES 铁蛋白*</td></tr><tr><td>0.088</td><td></td><td></td></tr><tr><td></td><td>0.088 0.088</td><td>备解素*</td></tr><tr><td></td><td>0.088 0.088</td><td>游离甲状腺素</td></tr><tr><td></td><td>血尿素BLOODUREA</td><td></td></tr><tr><td></td><td></td><td>0.088</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.089</td></tr><tr><td></td><td></td><td>0.089</td></tr><tr><td></td><td>0.090</td><td>嗜中性粒细胞STABNEUTROPHILS</td></tr><tr><td></td><td></td><td>0.090</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.090</td></tr><tr><td></td><td>0.090</td><td>BETA球蛋白*</td></tr><tr><td></td><td>0.091</td><td>血浆非酯化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA</td></tr><tr><td></td><td></td><td>0.091</td></tr><tr><td></td><td></td><td>0.091</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.092</td></tr><tr><td></td><td>0.092</td><td>睾酮*</td></tr><tr><td></td><td></td><td>0.092 0.093</td></tr><tr><td></td><td></td><td>甲状腺素结合球蛋白</td></tr><tr><td></td><td></td><td>0.093 0.094</td></tr><tr><td></td><td>血浆中性脂肪NEUTRALFATSOFPLASMA</td><td></td></tr><tr><td></td><td>0.094 0.094 0.094</td><td>网织红细胞PERIPHERICBLOODRETICULOCYTES</td></tr><tr><td></td><td></td><td>糖苷* DELTA-氨基乙酰丙酸*</td></tr><tr><td>0.094</td><td></td><td></td></tr><tr><td></td><td></td><td>0.094</td></tr><tr><td></td><td>0.095</td><td>肾素*</td></tr><tr><td>0.095</td><td>ALPHA1球蛋白*</td><td>0.095</td></tr></tbody></table></body></html>", "table_ocr_pred": {"rec_polys": [[[100, 77], [157, 77], [157, 102], [100, 102]], [[190, 77], [257, 77], [257, 104], [190, 104]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 127], [462, 127], [462, 152], [194, 152]], [[98, 154], [162, 154], [162, 179], [98, 179]], [[194, 156], [260, 156], [260, 181], [194, 181]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[192, 181], [498, 181], [498, 204], [192, 204]], [[98, 204], [162, 204], [162, 231], [98, 231]], [[194, 206], [449, 206], [449, 229], [194, 229]], [[98, 229], [161, 229], [161, 256], [98, 256]], [[196, 234], [432, 234], [432, 252], [196, 252]], [[98, 256], [155, 256], [155, 281], [98, 281]], [[194, 257], [404, 257], [404, 281], [194, 281]], [[98, 281], [155, 281], [155, 306], [98, 306]], [[194, 282], [569, 282], [569, 306], [194, 306]], [[98, 306], [155, 306], [155, 332], [98, 332]], [[192, 307], [364, 307], [364, 331], [192, 331]], [[98, 331], [155, 331], [155, 358], [98, 358]], [[194, 332], [406, 332], [406, 357], [194, 357]], [[98, 358], [155, 358], [155, 383], [98, 383]], [[190, 358], [260, 358], [260, 384], [190, 384]], [[98, 383], [157, 383], [157, 409], [98, 409]], [[192, 383], [460, 383], [460, 406], [192, 406]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[192, 409], [393, 409], [393, 433], [192, 433]], [[98, 433], [157, 433], [157, 459], [98, 459]], [[192, 434], [358, 434], [358, 459], [192, 459]], [[98, 458], [157, 458], [157, 484], [98, 484]], [[194, 463], [330, 463], [330, 483], [194, 483]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[192, 486], [441, 486], [441, 509], [192, 509]], [[98, 509], [157, 509], [157, 536], [98, 536]], [[188, 508], [262, 508], [262, 540], [188, 540]], [[98, 536], [157, 536], [157, 561], [98, 561]], [[192, 538], [401, 538], [401, 561], [192, 561]], [[98, 561], [157, 561], [157, 586], [98, 586]], [[194, 563], [559, 563], [559, 586], [194, 586]], [[98, 586], [157, 586], [157, 613], [98, 613]], [[194, 588], [449, 588], [449, 611], [194, 611]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[192, 611], [500, 611], [500, 635], [192, 635]], [[98, 636], [157, 636], [157, 663], [98, 663]], [[192, 638], [401, 638], [401, 661], [192, 661]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[188, 661], [299, 661], [299, 692], [188, 692]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[190, 690], [371, 686], [371, 711], [191, 715]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[192, 715], [314, 715], [314, 740], [192, 740]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[194, 740], [456, 740], [456, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[192, 767], [733, 767], [733, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [395, 792], [395, 815], [192, 815]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [330, 817], [330, 842], [192, 842]], [[98, 842], [157, 842], [157, 867], [98, 867]], [[190, 842], [279, 842], [279, 869], [190, 869]], [[98, 867], [157, 867], [157, 894], [98, 894]], [[188, 865], [360, 865], [360, 896], [188, 896]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[190, 892], [279, 892], [279, 919], [190, 919]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[188, 917], [291, 915], [292, 942], [189, 944]], [[98, 944], [155, 944], [155, 969], [98, 969]], [[190, 942], [377, 942], [377, 967], [190, 967]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[190, 969], [262, 969], [262, 996], [190, 996]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[188, 992], [264, 992], [264, 1024], [188, 1024]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[190, 1021], [332, 1021], [332, 1046], [190, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[188, 1044], [312, 1044], [312, 1074], [188, 1074]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[192, 1071], [360, 1071], [360, 1094], [192, 1094]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [349, 1098], [349, 1123], [192, 1123]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[188, 1119], [286, 1119], [286, 1149], [188, 1149]], [[98, 1148], [157, 1148], [157, 1174], [98, 1174]], [[192, 1148], [323, 1148], [323, 1173], [192, 1173]], [[98, 1173], [155, 1173], [155, 1199], [98, 1199]], [[192, 1174], [474, 1174], [474, 1198], [192, 1198]], [[98, 1199], [155, 1199], [155, 1224], [98, 1224]], [[194, 1203], [687, 1203], [687, 1221], [194, 1221]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[186, 1223], [263, 1218], [265, 1251], [188, 1255]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[190, 1249], [306, 1249], [306, 1274], [190, 1274]], [[98, 1274], [155, 1274], [155, 1301], [98, 1301]], [[194, 1276], [696, 1276], [696, 1300], [194, 1300]], [[98, 1301], [155, 1301], [155, 1326], [98, 1326]], [[194, 1305], [522, 1305], [522, 1323], [194, 1323]], [[98, 1326], [155, 1326], [155, 1351], [98, 1351]], [[190, 1326], [258, 1326], [258, 1353], [190, 1353]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[190, 1351], [593, 1351], [593, 1376], [190, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[188, 1376], [244, 1376], [244, 1405], [188, 1405]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[188, 1400], [264, 1400], [264, 1432], [188, 1432]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[192, 1428], [367, 1428], [367, 1453], [192, 1453]], [[98, 1453], [157, 1453], [157, 1478], [98, 1478]], [[194, 1457], [323, 1457], [323, 1476], [194, 1476]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[196, 1484], [534, 1484], [534, 1502], [196, 1502]], [[98, 1505], [155, 1505], [155, 1530], [98, 1530]], [[192, 1507], [594, 1507], [594, 1530], [192, 1530]], [[98, 1530], [155, 1530], [155, 1555], [98, 1555]], [[192, 1532], [445, 1532], [445, 1555], [192, 1555]], [[98, 1555], [157, 1555], [157, 1582], [98, 1582]], [[188, 1557], [242, 1557], [242, 1584], [188, 1584]], [[100, 1582], [157, 1582], [157, 1607], [100, 1607]], [[190, 1580], [375, 1582], [375, 1607], [190, 1605]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1607], [310, 1607], [310, 1632], [192, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[190, 1632], [244, 1632], [244, 1661], [190, 1661]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[194, 1661], [325, 1661], [325, 1680], [194, 1680]], [[100, 1684], [157, 1684], [157, 1709], [100, 1709]], [[192, 1684], [262, 1684], [262, 1711], [192, 1711]]], "rec_texts": ["0.000", "胃后壁", "2.829", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.054", "脂肪酶", "0.070", "PERIPHERIC BLOOD LEUCOCYTES", "0.072", "血组织胺BLOOD HISTAMINE", "0.079", "血清蛋白SERUMALBUMEN", "0.070", "血红血球ERYTHROCYTES", "0.079", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.086", "红细胞沉降率（ESR)", "0.093", "血清蛋白SERUM PROTEIN", "0.094", "多巴胺*", "0.060", "GLUTAMATEDEHYDROGENASE*", "0.064", "血红蛋白HAEMOGLOBIN", "0.065", "血清铁SERUM IRON", "0.070", "糖基化血红蛋白", "0.070", "血清溶菌酵SERUMLYSOZYME", "0.072", "催乳素*", "0.076", "前列腺特异性抗原（PSA）", "0.076", "血小板PERIPHERIC BLOOD THROMBOCYTES", "0.076", "血清补体SERUMCOMPLEMENT", "0.078", "血清淀粉酵素SERUMALPHAAMYLASE", "0.080", "嗜碱性粒细胞BASOPHILS", "0.081", "抗利尿激素*", "0.081", "总铁结合力（TIBC)", "0.083", "甲状腺球蛋白*", "0.083", "伽马球蛋白GAMMA-GLOBULINS", "0.084", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.084", "淋巴细胞LYMPHOCYTES", "0.084", "香草酚TEST*", "0.084", "甲状旁腺*", "0.085", "腺苷-血3.5环磷酸*", "0.086", "肌红蛋白*", "0.087", "维生素B12*", "0.087", "单核细胞MONOCYTES", "0.088", "铁蛋白*", "0.088", "备解素*", "0.088", "抗链球菌溶血素*", "0.088", "游离甲状腺素", "0.088", "血尿素BLOODUREA", "0.088", "血管紧张素转换酶", "0.089", "维生素B2*", "0.089", "GAMMA谷氨酰*", "0.090", "嗜中性粒细胞STABNEUTROPHILS", "0.090", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.090", "蛋白C*", "0.090", "BETA球蛋白*", "0.091", "血浆非酯化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA", "0.091", "游离胆固醇FREEPLASMACHOLESTERIN", "0.091", "降钙素", "0.092", "乳酸脱氢酵素COMMONLACTADEHYDROGENASE", "0.092", "睾酮*", "0.092", "胰岛素*", "0.093", "甲状腺素结合球蛋白", "0.093", "ALPHA2球蛋白", "0.094", "血浆中性脂肪NEUTRALFATSOFPLASMA", "0.094", "网织红细胞PERIPHERICBLOODRETICULOCYTES", "0.094", "肿瘤标志物MELANOGENE在尿", "0.094", "糖苷*", "0.094", "DELTA-氨基乙酰丙酸*", "0.094", "免疫球蛋白G*", "0.095", "肾素*", "0.095", "ALPHA1球蛋白*", "0.095", "胆汁酸*"], "rec_scores": [0.9991452097892761, 0.9793572425842285, 0.9994626045227051, 0.9983981251716614, 0.9706200361251831, 0.9999210238456726, 0.9962863922119141, 0.9999279975891113, 0.9762078523635864, 0.9999393224716187, 0.9765939116477966, 0.9999545812606812, 0.9804778099060059, 0.9995309114456177, 0.9493137001991272, 0.9996275901794434, 0.9976005554199219, 0.9995980262756348, 0.9187324047088623, 0.9997873306274414, 0.9770432710647583, 0.9995997548103333, 0.9273971319198608, 0.9997529983520508, 0.971610963344574, 0.9997116327285767, 0.995011568069458, 0.9997177124023438, 0.9696007966995239, 0.9996522665023804, 0.9965105056762695, 0.9996242523193359, 0.9968544840812683, 0.9997766613960266, 0.9764953851699829, 0.9996940493583679, 0.9535383582115173, 0.9996644854545593, 0.9680094718933105, 0.9997709393501282, 0.9975549578666687, 0.9997966885566711, 0.9958542585372925, 0.9997112154960632, 0.963718831539154, 0.9996433258056641, 0.981870174407959, 0.999719500541687, 0.9113099575042725, 0.9998405575752258, 0.9408621191978455, 0.999783992767334, 0.9952353239059448, 0.9996143579483032, 0.9958996772766113, 0.9997631311416626, 0.9943255186080933, 0.9997628927230835, 0.9872453212738037, 0.9996064305305481, 0.9626888036727905, 0.999744713306427, 0.9730015397071838, 0.9997943639755249, 0.958957850933075, 0.9997703433036804, 0.9916802644729614, 0.9996609687805176, 0.9985314011573792, 0.9996929168701172, 0.980333149433136, 0.9997851252555847, 0.9331592321395874, 0.9998102188110352, 0.9764025211334229, 0.9996644258499146, 0.9957056641578674, 0.9996929168701172, 0.994683027267456, 0.9997851252555847, 0.9982747435569763, 0.9996135830879211, 0.9526893496513367, 0.9997170567512512, 0.9852026700973511, 0.9996737241744995, 0.9952682256698608, 0.9995824098587036, 0.9850760102272034, 0.9995684623718262, 0.913559079170227, 0.9995887875556946, 0.9705867767333984, 0.9997507333755493, 0.9840340614318848, 0.9996320605278015, 0.996050238609314, 0.9996284246444702, 0.9981526732444763, 0.999703049659729, 0.9966501593589783, 0.9998161196708679, 0.9106177687644958, 0.9996370077133179, 0.9678974747657776, 0.9996572732925415, 0.9966065883636475, 0.999719500541687, 0.9932368397712708, 0.9998073577880859, 0.9928359389305115, 0.9996101260185242, 0.997238039970398, 0.9996415972709656, 0.9948570132255554, 0.9997735023498535, 0.8556062579154968, 0.9995912313461304, 0.9371978044509888, 0.9996482133865356, 0.9848665595054626, 0.9996343851089478, 0.9597945809364319, 0.9995914697647095, 0.9518951177597046, 0.9996017217636108, 0.9694901704788208], "rec_boxes": [[100, 77, 157, 102], [190, 77, 257, 104], [98, 102, 157, 127], [192, 102, 270, 129], [194, 127, 462, 152], [98, 154, 162, 179], [194, 156, 260, 181], [98, 179, 162, 204], [192, 181, 498, 204], [98, 204, 162, 231], [194, 206, 449, 229], [98, 229, 161, 256], [196, 234, 432, 252], [98, 256, 155, 281], [194, 257, 404, 281], [98, 281, 155, 306], [194, 282, 569, 306], [98, 306, 155, 332], [192, 307, 364, 331], [98, 331, 155, 358], [194, 332, 406, 357], [98, 358, 155, 383], [190, 358, 260, 384], [98, 383, 157, 409], [192, 383, 460, 406], [98, 408, 157, 434], [192, 409, 393, 433], [98, 433, 157, 459], [192, 434, 358, 459], [98, 458, 157, 484], [194, 463, 330, 483], [98, 484, 157, 509], [192, 486, 441, 509], [98, 509, 157, 536], [188, 508, 262, 540], [98, 536, 157, 561], [192, 538, 401, 561], [98, 561, 157, 586], [194, 563, 559, 586], [98, 586, 157, 613], [194, 588, 449, 611], [98, 611, 157, 638], [192, 611, 500, 635], [98, 636, 157, 663], [192, 638, 401, 661], [98, 663, 155, 688], [188, 661, 299, 692], [98, 688, 155, 715], [190, 686, 371, 715], [98, 713, 157, 740], [192, 715, 314, 740], [98, 740, 157, 767], [194, 740, 456, 765], [98, 765, 157, 790], [192, 767, 733, 790], [98, 790, 157, 817], [192, 792, 395, 815], [98, 815, 157, 842], [192, 817, 330, 842], [98, 842, 157, 867], [190, 842, 279, 869], [98, 867, 157, 894], [188, 865, 360, 896], [98, 892, 157, 919], [190, 892, 279, 919], [98, 917, 155, 944], [188, 915, 292, 944], [98, 944, 155, 969], [190, 942, 377, 967], [98, 969, 157, 996], [190, 969, 262, 996], [98, 994, 157, 1021], [188, 992, 264, 1024], [98, 1019, 157, 1046], [190, 1021, 332, 1046], [98, 1046, 157, 1071], [188, 1044, 312, 1074], [98, 1071, 157, 1098], [192, 1071, 360, 1094], [98, 1096, 157, 1123], [192, 1098, 349, 1123], [98, 1123, 157, 1148], [188, 1119, 286, 1149], [98, 1148, 157, 1174], [192, 1148, 323, 1173], [98, 1173, 155, 1199], [192, 1174, 474, 1198], [98, 1199, 155, 1224], [194, 1203, 687, 1221], [98, 1224, 157, 1249], [186, 1218, 265, 1255], [98, 1249, 157, 1274], [190, 1249, 306, 1274], [98, 1274, 155, 1301], [194, 1276, 696, 1300], [98, 1301, 155, 1326], [194, 1305, 522, 1323], [98, 1326, 155, 1351], [190, 1326, 258, 1353], [98, 1351, 157, 1376], [190, 1351, 593, 1376], [98, 1376, 157, 1403], [188, 1376, 244, 1405], [100, 1403, 157, 1428], [188, 1400, 264, 1432], [100, 1428, 157, 1453], [192, 1428, 367, 1453], [98, 1453, 157, 1478], [194, 1457, 323, 1476], [98, 1478, 157, 1505], [196, 1484, 534, 1502], [98, 1505, 155, 1530], [192, 1507, 594, 1530], [98, 1530, 155, 1555], [192, 1532, 445, 1555], [98, 1555, 157, 1582], [188, 1557, 242, 1584], [100, 1582, 157, 1607], [190, 1580, 375, 1607], [98, 1607, 157, 1632], [192, 1607, 310, 1632], [100, 1632, 157, 1657], [190, 1632, 244, 1661], [100, 1657, 157, 1684], [194, 1661, 325, 1680], [100, 1684, 157, 1709], [192, 1684, 262, 1711]]}}]}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//6d8995d7-90b1-45ff-a468-1b9c2e2a33c3/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-02T02%3A50%3A39Z%2F-1%2F%2F0932faccfe0b362ef3215aba991a36069561469ec915b9b719a77cdb8165c015", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//6d8995d7-90b1-45ff-a468-1b9c2e2a33c3/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-02T02%3A50%3A39Z%2F-1%2F%2Fda8cdeaa0b3e48208eeb33aca09d48db33048eeef01bb2c1ccf125dd0f1b636f", "table_cell_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//6d8995d7-90b1-45ff-a468-1b9c2e2a33c3/table_cell_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-02T02%3A50%3A39Z%2F-1%2F%2F286d7c07ffc7c0e61a0e85718d4b9f5c4bc8976a1212d754efb526cafea2700f"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//6d8995d7-90b1-45ff-a468-1b9c2e2a33c3/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-02T02%3A50%3A39Z%2F-1%2F%2F3b1a5736d0eb3737cf4f21236cedbd5718bbb37852d00a36a9bffccda216dda1"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}