package services

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	json "github.com/goccy/go-json"
	"fmt"
	"log"
	"net"
	"os"
	"strings"
	"time"

	"MagneticOperator/app/models"
	"MagneticOperator/app/utils"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// ConfigServiceInterface 定义配置服务的接口
type ConfigServiceInterface interface {
	GetConfig() *models.AppConfig
	LoadConfig() (*models.AppConfig, error)
	UpdateSiteInfo() error
	IsSiteInfoChanged() bool
	GetLastUpdateTime() time.Time
}

// ConfigService 配置服务
type ConfigService struct {
	config          *models.AppConfig
	apiService      *APIService            // 添加API服务引用
	ctx             context.Context        // 添加上下文
	lastSiteInfo    map[string]interface{} // 缓存的站点信息
	siteInfoHash    string                 // 站点信息的哈希值
	lastUpdateTime  time.Time              // 最后更新时间
	siteInfoChanged bool                   // 站点信息是否有变化
	// 环境配置管理器
	configManager *utils.ConfigManager // 配置管理器
}

// NewConfigService 创建新的配置服务
func NewConfigService(ctx context.Context) *ConfigService {
	// 从环境变量获取环境配置，默认为development
	environment := os.Getenv("APP_ENV")
	if environment == "" {
		environment = "development"
	}

	// 创建支持环境配置的配置管理器
	configManager := utils.NewConfigManagerWithEnv("config", environment)

	return &ConfigService{
		ctx:           ctx,
		configManager: configManager,
	}
}

// NewConfigServiceWithEnv 创建指定环境的配置服务
func NewConfigServiceWithEnv(ctx context.Context, environment string) *ConfigService {
	configManager := utils.NewConfigManagerWithEnv("config", environment)

	return &ConfigService{
		ctx:           ctx,
		configManager: configManager,
	}
}

// SetAPIService 设置API服务引用（用于避免循环依赖）
func (cs *ConfigService) SetAPIService(apiService *APIService) {
	cs.apiService = apiService
}

// LoadConfig 加载配置文件
func (cs *ConfigService) LoadConfig() (*models.AppConfig, error) {
	// 使用环境配置管理器加载配置
	config, err := cs.configManager.LoadConfig()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %v", err)
	}

	// 获取当前MAC地址
	currentMAC, err := cs.getMACAddress()
	if err != nil {
		return nil, fmt.Errorf("获取MAC地址失败: %v", err)
	}

	// 检查并更新MAC地址
	if config.DeviceInfo.MACAddress == "" {
		config.DeviceInfo.MACAddress = currentMAC
		// 使用配置管理器保存配置
		if err := cs.configManager.SaveConfig(config); err != nil {
			return nil, fmt.Errorf("保存配置失败: %v", err)
		}
	}

	// // 清空站点信息，等待从API加载
	// // 站点信息现在完全从API动态获取，不再从配置文件读取
	// config.SiteInfo = struct {
	// 	SiteID    string `json:"site_id"`
	// 	SiteName  string `json:"site_name"`
	// 	SiteType  string `json:"site_type"`
	// 	ParentOrg string `json:"parent_org"`
	// 	Location  struct {
	// 		Province string `json:"province"`
	// 		City     string `json:"city"`
	// 		District string `json:"district"`
	// 		Address  string `json:"address"`
	// 	} `json:"location"`
	// 	Contact struct {
	// 		Manager string `json:"manager"`
	// 		Phone   string `json:"phone"`
	// 	} `json:"contact"`
	// }{}

	cs.config = config
	return config, nil
}

// LoadSiteInfoFromAPI 从API加载站点信息
func (cs *ConfigService) LoadSiteInfoFromAPI() error {
	return cs.LoadSiteInfoFromAPIWithCache(false)
}

// LoadSiteInfoFromAPIWithCache 从API加载站点信息（带缓存机制）
func (cs *ConfigService) LoadSiteInfoFromAPIWithCache(forceUpdate bool) error {
	if cs.config == nil {
		return fmt.Errorf("配置未加载")
	}

	if cs.apiService == nil {
		return fmt.Errorf("API服务未设置")
	}

	// 使用当前设备的MAC地址获取站点信息
	macAddress := cs.config.DeviceInfo.MACAddress
	if macAddress == "" {
		return fmt.Errorf("设备MAC地址未设置")
	}
	log.Println("————————————————————————")
	log.Println("MAC地址:", macAddress)

	// 调用API获取站点信息
	siteInfo, err := cs.apiService.GetSiteInfoByDeviceMAC(macAddress)
	if err != nil {
		return fmt.Errorf("获取站点信息失败: %v", err)
	}
	log.Println("————————————————————————")
	log.Println("调用API获取站点信息，siteInfo:", siteInfo)

	// 检查站点信息是否有变化
	cs.siteInfoChanged = forceUpdate || cs.isSiteInfoChanged(siteInfo)
	if cs.siteInfoChanged {
		log.Println("站点信息有变化，将更新缓存")
	} else {
		log.Println("站点信息无变化，使用缓存")
		return nil
	}

	// 更新缓存
	cs.lastSiteInfo = siteInfo
	cs.siteInfoHash = cs.calculateSiteInfoHash(siteInfo)
	cs.lastUpdateTime = time.Now()

	// 更新配置中的站点信息
	// 从 organization_id 数组中获取站点信息
	if orgArray, ok := siteInfo["organization_id"].([]interface{}); ok && len(orgArray) > 0 {
		if orgInfo, ok := orgArray[0].(map[string]interface{}); ok {
			if siteID, ok := orgInfo["site_id"].(string); ok {
				cs.config.SiteInfo.SiteID = siteID
			}
			if siteName, ok := orgInfo["site_name"].(string); ok {
				cs.config.SiteInfo.SiteName = siteName
			}
			if siteType, ok := orgInfo["site_type"].(string); ok {
				cs.config.SiteInfo.SiteType = siteType
			}
			if address, ok := orgInfo["address"].(string); ok {
				cs.config.SiteInfo.Location.Address = address
			}
			if contactPerson, ok := orgInfo["contact_person"].(string); ok {
				cs.config.SiteInfo.Contact.Manager = contactPerson
			}
			if contactPhone, ok := orgInfo["contact_phone"].(string); ok {
				cs.config.SiteInfo.Contact.Phone = contactPhone
			}
		}
	}

	// 将更新后的站点信息发送给前端
	runtime.EventsEmit(cs.ctx, "siteInfoUpdated", cs.config.SiteInfo)

	// 重置站点信息变化标志，避免后续误判
	cs.siteInfoChanged = false

	// 站点信息已从API加载到内存，无需保存到本地配置文件
	return nil
}

// GetConfig 获取当前配置
func (cs *ConfigService) GetConfig() *models.AppConfig {
	return cs.config
}

// UpdateSiteInfo 更新站点信息
func (cs *ConfigService) UpdateSiteInfo() error {
	return cs.LoadSiteInfoFromAPI()
}

// UpdateCropSettings 更新裁剪设置
func (cs *ConfigService) UpdateCropSettings(cropSettings models.AppConfig) error {
	if cs.config == nil {
		return fmt.Errorf("配置未加载")
	}

	// 更新紧凑和展开模式的裁剪设置
	cs.config.NormalWindowsSetting = cropSettings.NormalWindowsSetting
	cs.config.CropSettings = cropSettings.CropSettings
	cs.config.ExpandedCropSettings = cropSettings.ExpandedCropSettings
	return cs.config.SaveConfig()
}

// UpdateNotificationMode 更新通知模式
func (cs *ConfigService) UpdateNotificationMode(useSystemNotification bool) error {
	if cs.config == nil {
		return fmt.Errorf("配置未加载")
	}

	cs.config.UseSystemNotification = useSystemNotification
	return cs.config.SaveConfig()
}

// getMACAddress 获取MAC地址
func (cs *ConfigService) getMACAddress() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	for _, iface := range interfaces {
		// 跳过回环接口和虚拟接口
		if iface.Flags&net.FlagLoopback != 0 || iface.Flags&net.FlagUp == 0 {
			continue
		}

		// 跳过虚拟网卡（通常包含这些关键词）
		name := strings.ToLower(iface.Name)
		if strings.Contains(name, "virtual") ||
			strings.Contains(name, "vmware") ||
			strings.Contains(name, "vbox") ||
			strings.Contains(name, "hyper-v") ||
			strings.Contains(name, "docker") ||
			strings.Contains(name, "vethernet") {
			continue
		}

		// 获取MAC地址
		mac := iface.HardwareAddr.String()
		if mac != "" && mac != "00:00:00:00:00:00" {
			return strings.ReplaceAll(mac, ":", ""), nil
		}
	}

	return "", fmt.Errorf("未找到有效的MAC地址")
}

// GetModeConfig 获取模式配置
func (cs *ConfigService) GetModeConfig() map[string]models.ModeInfo {
	return map[string]models.ModeInfo{
		"A":        {"A01", "器官问题来源分析"},
		"B":        {"B02", "生化平衡分析"},
		"C":        {"C03", "病理形态学分析"},
		"器官问题来源分析": {"A01", "器官问题来源分析"},
		"生化平衡分析":   {"B02", "生化平衡分析"},
		"病理形态学分析":  {"C03", "病理形态学分析"},
	}
}

// isSiteInfoChanged 检查站点信息是否有变化
func (cs *ConfigService) isSiteInfoChanged(newSiteInfo map[string]interface{}) bool {
	if cs.lastSiteInfo == nil {
		return true
	}
	newHash := cs.calculateSiteInfoHash(newSiteInfo)
	return cs.siteInfoHash != newHash
}

// calculateSiteInfoHash 计算站点信息哈希值
func (cs *ConfigService) calculateSiteInfoHash(siteInfo map[string]interface{}) string {
	data, err := json.Marshal(siteInfo)
	if err != nil {
		return ""
	}
	hash := sha256.Sum256(data)
	return hex.EncodeToString(hash[:])
}

// IsSiteInfoChanged 获取站点信息是否有变化（供外部调用）
func (cs *ConfigService) IsSiteInfoChanged() bool {
	return cs.siteInfoChanged
}

// GetLastUpdateTime 获取最后更新时间
func (cs *ConfigService) GetLastUpdateTime() time.Time {
	return cs.lastUpdateTime
}

// GetEnvironment 获取当前环境
func (cs *ConfigService) GetEnvironment() string {
	if cs.configManager != nil {
		return cs.configManager.GetEnvironment()
	}
	return "unknown"
}

// IsProduction 是否是生产环境
func (cs *ConfigService) IsProduction() bool {
	if cs.configManager != nil {
		return cs.configManager.IsProduction()
	}
	return false
}

// IsDevelopment 是否是开发环境
func (cs *ConfigService) IsDevelopment() bool {
	if cs.configManager != nil {
		return cs.configManager.IsDevelopment()
	}
	return false
}

// IsDebugMode 是否是调试模式
func (cs *ConfigService) IsDebugMode() bool {
	if cs.configManager != nil {
		return cs.configManager.IsDebugMode()
	}
	return false
}

// SetConfig 设置配置（用于默认配置场景）
func (cs *ConfigService) SetConfig(config *models.AppConfig) {
	cs.config = config
}
