{"logId": "912ebccb-f863-4669-95c2-cca0c078ef7f", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 8, "label": "table", "score": 0.9886482357978821, "coordinate": [14.01123046875, 71.5833969116211, 767.5036010742188, 1714.4365234375]}, {"cls_id": 9, "label": "table_title", "score": 0.6792432069778442, "coordinate": [19.7296142578125, 27.130123138427734, 520.5859375, 62.96539306640625]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.5060552954673767, "coordinate": [19.7296142578125, 27.130123138427734, 520.5859375, 62.96539306640625]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[100, 77], [157, 77], [157, 102], [100, 102]], [[192, 79], [493, 79], [493, 102], [192, 102]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[194, 102], [270, 102], [270, 129], [194, 129]], [[194, 129], [463, 129], [463, 152], [194, 152]], [[100, 154], [157, 154], [157, 179], [100, 179]], [[192, 154], [694, 152], [694, 177], [192, 179]], [[98, 179], [157, 179], [157, 204], [98, 204]], [[194, 181], [443, 181], [443, 204], [194, 204]], [[100, 204], [157, 204], [157, 229], [100, 229]], [[196, 209], [532, 209], [532, 227], [196, 227]], [[100, 231], [155, 231], [155, 256], [100, 256]], [[194, 234], [522, 234], [522, 252], [194, 252]], [[100, 256], [157, 256], [157, 281], [100, 281]], [[194, 257], [449, 257], [449, 281], [194, 281]], [[100, 281], [155, 281], [155, 306], [100, 306]], [[194, 282], [362, 282], [362, 306], [194, 306]], [[98, 306], [157, 306], [157, 331], [98, 331]], [[194, 307], [456, 307], [456, 331], [194, 331]], [[100, 332], [157, 332], [157, 358], [100, 358]], [[192, 332], [292, 332], [292, 358], [192, 358]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[192, 358], [373, 356], [373, 381], [192, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[192, 384], [524, 382], [524, 406], [192, 408]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[196, 413], [639, 413], [639, 431], [196, 431]], [[98, 434], [157, 434], [157, 459], [98, 459]], [[192, 436], [502, 436], [502, 459], [192, 459]], [[100, 459], [157, 459], [157, 484], [100, 484]], [[192, 459], [305, 459], [305, 484], [192, 484]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[192, 484], [286, 484], [286, 513], [192, 513]], [[98, 509], [155, 509], [155, 536], [98, 536]], [[194, 511], [733, 511], [733, 534], [194, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[196, 540], [498, 540], [498, 558], [196, 558]], [[98, 561], [157, 561], [157, 586], [98, 586]], [[194, 563], [393, 563], [393, 586], [194, 586]], [[98, 586], [155, 586], [155, 613], [98, 613]], [[192, 588], [395, 588], [395, 611], [192, 611]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[190, 611], [360, 613], [360, 638], [190, 636]], [[98, 638], [155, 638], [155, 663], [98, 663]], [[194, 642], [474, 642], [474, 660], [194, 660]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[194, 665], [500, 665], [500, 688], [194, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[192, 688], [279, 688], [279, 715], [192, 715]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[192, 715], [334, 715], [334, 740], [192, 740]], [[98, 740], [157, 740], [157, 765], [98, 765]], [[194, 740], [471, 740], [471, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[194, 767], [408, 767], [408, 790], [194, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[194, 792], [473, 792], [473, 815], [194, 815]], [[98, 817], [157, 817], [157, 842], [98, 842]], [[192, 817], [406, 817], [406, 840], [192, 840]], [[98, 842], [157, 842], [157, 867], [98, 867]], [[192, 842], [378, 842], [378, 865], [192, 865]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [401, 869], [401, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[192, 894], [594, 894], [594, 917], [192, 917]], [[98, 917], [157, 917], [157, 944], [98, 944]], [[190, 917], [364, 919], [364, 944], [190, 942]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [559, 946], [559, 969], [194, 969]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[191, 967], [312, 971], [311, 998], [190, 994]], [[98, 994], [155, 994], [155, 1021], [98, 1021]], [[192, 996], [476, 996], [476, 1019], [192, 1019]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[192, 1022], [401, 1022], [401, 1046], [192, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[192, 1047], [569, 1047], [569, 1071], [192, 1071]], [[98, 1071], [155, 1071], [155, 1098], [98, 1098]], [[194, 1074], [314, 1074], [314, 1094], [194, 1094]], [[98, 1096], [155, 1096], [155, 1123], [98, 1123]], [[190, 1096], [591, 1096], [591, 1119], [190, 1119]], [[98, 1123], [155, 1123], [155, 1148], [98, 1148]], [[192, 1123], [462, 1123], [462, 1146], [192, 1146]], [[98, 1148], [155, 1148], [155, 1174], [98, 1174]], [[188, 1142], [263, 1146], [261, 1180], [186, 1176]], [[98, 1173], [155, 1173], [155, 1199], [98, 1199]], [[192, 1173], [325, 1173], [325, 1198], [192, 1198]], [[98, 1198], [155, 1198], [155, 1224], [98, 1224]], [[190, 1199], [277, 1199], [277, 1226], [190, 1226]], [[98, 1224], [155, 1224], [155, 1249], [98, 1249]], [[192, 1224], [364, 1224], [364, 1249], [192, 1249]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1248], [323, 1248], [323, 1273], [192, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[188, 1273], [260, 1273], [260, 1305], [188, 1305]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[192, 1303], [447, 1303], [447, 1326], [192, 1326]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[190, 1325], [305, 1325], [305, 1355], [190, 1355]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [356, 1353], [356, 1378], [192, 1378]], [[98, 1378], [157, 1378], [157, 1403], [98, 1403]], [[186, 1375], [261, 1370], [263, 1403], [188, 1407]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [319, 1403], [319, 1428], [192, 1428]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[186, 1427], [263, 1422], [265, 1456], [188, 1461]], [[100, 1453], [155, 1453], [155, 1478], [100, 1478]], [[194, 1455], [308, 1455], [308, 1480], [194, 1480]], [[98, 1480], [157, 1480], [157, 1505], [98, 1505]], [[194, 1482], [689, 1482], [689, 1505], [194, 1505]], [[98, 1505], [155, 1505], [155, 1530], [98, 1530]], [[192, 1505], [279, 1505], [279, 1530], [192, 1530]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[190, 1530], [277, 1530], [277, 1557], [190, 1557]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[190, 1557], [452, 1557], [452, 1580], [190, 1580]], [[98, 1582], [157, 1582], [157, 1607], [98, 1607]], [[190, 1582], [262, 1582], [262, 1609], [190, 1609]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1609], [606, 1609], [606, 1634], [192, 1634]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[190, 1632], [297, 1632], [297, 1657], [190, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1659], [430, 1659], [430, 1682], [192, 1682]], [[100, 1684], [157, 1684], [157, 1709], [100, 1709]], [[194, 1686], [412, 1686], [412, 1709], [194, 1709]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["按照标准图谱相似度递减列表：", "0.000", "消化系统—夷腺;汁二指肠正面图", "2.167", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.048", "血浆非酯化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA", "0.054", "血清溶菌酵SERUMLYSOZYME", "0.054", "血浆中性脂肪NEUTRALFATSOFPLASMA", "0.057", "游离胆固醇FREEPLASMACHOLESTERIN", "0.060", "血清补体SERUM COMPLEMENT", "0.060", "血尿素BLOODUREA", "0.062", "伽马球蛋白GAMMA-GLOBULINS", "0.062", "抗利尿激素", "0.063", "总铁结合力（TIBC）*", "0.064", "胆固醇COMMON PLASMA CHOLESTERIN", "0.064", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.065", "血清淀粉酵素SERUMALPHAAMYLASE", "0.066", "BETA球蛋白*", "0.066", "维生素D3*", "0.067", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.067", "血清甘油三酯SERUMTRIGLYCERIDES", "0.068", "血红蛋白HAEMOGLOBIN", "0.069", "淋巴细胞LYMPHOCYTES", "0.069", "甲状腺素结合球蛋白", "0.071", "PERIPHERICBLOODLEUCOCYTES", "0.071", "血胆红素COMMONBLOODBILIRUBIN", "0.071", "甲状旁腺*", "0.072", "糖基化血红蛋白*", "0.072", "血脂COMMON LIPIDS OF PLASMA", "0.073", "血清蛋白SERUMPROTEIN", "0.073", "C反应蛋白C-REACTIVEPROTEIN", "0.073", "血红血球ERYTHROCYTES", "0.073", "单核细胞MONOCYTES", "0.075", "嗜碱性粒细胞BASOPHILS", "0.075", "网织红细胞PERIPHERICBLOODRETICULOCYTES", "0.075", "红细胞沉降率(ESR)", "0.076", "血小板PERIPHERICBLOOD THROMBOCYTES", "0.076", "游离甲状腺素", "0.077", "血浆磷脂PLASMA PHOSPHOTIDES", "0.077", "前列腺特异性抗原（PSA）", "0.078", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.081", "甲状腺球蛋白*", "0.081", "乳酸脱氢酵素COMMONLACTADEHYDROGENASE", "0.081", "GLUTAMATEDEHYDROGENASE*", "0.081", "备解素*", "0.081", "ALPHA1球蛋白*", "0.081", "肌红蛋白", "0.081", "维生素A（视黄醇）*", "0.082", "ALPHA2球蛋白", "0.082", "催乳素*", "0.082", "肿瘤标志物MELANOGENE在尿*", "0.083", "血管紧张素I*", "0.083", "血清铁SERUM IRON", "0.084", "蛋白C*", "0.084", "GAMMA谷氨酰", "0.084", "胰岛素*", "0.084", "血管紧张素I*", "0.084", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.084", "胰蛋白酶*", "0.085", "转铁蛋白", "0.087", "凝血酵素PROTHROMBININDEX", "0.089", "降钙素*", "0.090", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.090", "红血球啉*", "0.090", "嗜酸性粒细胞EOSINOPHILES", "0.090", "血清蛋白SERUMABUMEN"], "rec_scores": [0.9954331517219543, 0.9991452097892761, 0.8610752820968628, 0.9996070861816406, 0.9987521767616272, 0.9706943035125732, 0.9993985891342163, 0.9831214547157288, 0.9996549487113953, 0.9967777729034424, 0.9996512532234192, 0.9924781322479248, 0.9997230768203735, 0.996050238609314, 0.9996001124382019, 0.9756301641464233, 0.9996358156204224, 0.9978327751159668, 0.9996463656425476, 0.9902769923210144, 0.9997104406356812, 0.9988042712211609, 0.9996620416641235, 0.9067788124084473, 0.9997125864028931, 0.9621079564094543, 0.9997116327285767, 0.9974311590194702, 0.9997149705886841, 0.9982032179832458, 0.9995217323303223, 0.973098874092102, 0.999625563621521, 0.9890671372413635, 0.9997604489326477, 0.995054304599762, 0.9996060132980347, 0.9844906330108643, 0.9996688961982727, 0.9976869225502014, 0.9997493624687195, 0.9943255186080933, 0.9998126029968262, 0.9914366602897644, 0.9996027946472168, 0.9963062405586243, 0.9995591044425964, 0.9975655674934387, 0.999690055847168, 0.9565446972846985, 0.9997766613960266, 0.986910343170166, 0.9997081756591797, 0.9662355780601501, 0.9996716380119324, 0.9693771600723267, 0.9997776746749878, 0.9902097582817078, 0.9996320009231567, 0.9885869026184082, 0.9997103810310364, 0.9965924620628357, 0.9996727108955383, 0.979997992515564, 0.9997857213020325, 0.99727863073349, 0.9998120069503784, 0.9180747866630554, 0.9996940493583679, 0.9820930361747742, 0.9996798634529114, 0.9969080090522766, 0.9996496438980103, 0.9782581329345703, 0.9994654655456543, 0.9582250118255615, 0.9997159838676453, 0.9955940246582031, 0.9997001886367798, 0.9527922868728638, 0.999719500541687, 0.9894920587539673, 0.9996111989021301, 0.9896750450134277, 0.9997746348381042, 0.939625084400177, 0.9997745752334595, 0.9439484477043152, 0.9997280836105347, 0.9982782006263733, 0.9996433258056641, 0.9253336787223816, 0.9996979832649231, 0.9653288722038269, 0.9998078346252441, 0.9433668255805969, 0.9996959567070007, 0.9686256051063538, 0.9997018575668335, 0.9553508162498474, 0.999719500541687, 0.959416925907135, 0.999545693397522, 0.9443367719650269, 0.9996317028999329, 0.9913325309753418, 0.999615490436554, 0.9698299169540405, 0.9995887875556946, 0.9378418326377869, 0.999545693397522, 0.9952952861785889, 0.9995848536491394, 0.974666953086853, 0.9996746778488159, 0.9983257055282593, 0.9997642636299133, 0.9941462278366089, 0.9996135830879211, 0.9587330222129822, 0.9995995759963989, 0.9806501865386963, 0.9995878338813782, 0.8803703188896179, 0.9993335008621216, 0.997796356678009, 0.9994867444038391, 0.9833138585090637], "rec_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[100, 77], [157, 77], [157, 102], [100, 102]], [[192, 79], [493, 79], [493, 102], [192, 102]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[194, 102], [270, 102], [270, 129], [194, 129]], [[194, 129], [463, 129], [463, 152], [194, 152]], [[100, 154], [157, 154], [157, 179], [100, 179]], [[192, 154], [694, 152], [694, 177], [192, 179]], [[98, 179], [157, 179], [157, 204], [98, 204]], [[194, 181], [443, 181], [443, 204], [194, 204]], [[100, 204], [157, 204], [157, 229], [100, 229]], [[196, 209], [532, 209], [532, 227], [196, 227]], [[100, 231], [155, 231], [155, 256], [100, 256]], [[194, 234], [522, 234], [522, 252], [194, 252]], [[100, 256], [157, 256], [157, 281], [100, 281]], [[194, 257], [449, 257], [449, 281], [194, 281]], [[100, 281], [155, 281], [155, 306], [100, 306]], [[194, 282], [362, 282], [362, 306], [194, 306]], [[98, 306], [157, 306], [157, 331], [98, 331]], [[194, 307], [456, 307], [456, 331], [194, 331]], [[100, 332], [157, 332], [157, 358], [100, 358]], [[192, 332], [292, 332], [292, 358], [192, 358]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[192, 358], [373, 356], [373, 381], [192, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[192, 384], [524, 382], [524, 406], [192, 408]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[196, 413], [639, 413], [639, 431], [196, 431]], [[98, 434], [157, 434], [157, 459], [98, 459]], [[192, 436], [502, 436], [502, 459], [192, 459]], [[100, 459], [157, 459], [157, 484], [100, 484]], [[192, 459], [305, 459], [305, 484], [192, 484]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[192, 484], [286, 484], [286, 513], [192, 513]], [[98, 509], [155, 509], [155, 536], [98, 536]], [[194, 511], [733, 511], [733, 534], [194, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[196, 540], [498, 540], [498, 558], [196, 558]], [[98, 561], [157, 561], [157, 586], [98, 586]], [[194, 563], [393, 563], [393, 586], [194, 586]], [[98, 586], [155, 586], [155, 613], [98, 613]], [[192, 588], [395, 588], [395, 611], [192, 611]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[190, 611], [360, 613], [360, 638], [190, 636]], [[98, 638], [155, 638], [155, 663], [98, 663]], [[194, 642], [474, 642], [474, 660], [194, 660]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[194, 665], [500, 665], [500, 688], [194, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[192, 688], [279, 688], [279, 715], [192, 715]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[192, 715], [334, 715], [334, 740], [192, 740]], [[98, 740], [157, 740], [157, 765], [98, 765]], [[194, 740], [471, 740], [471, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[194, 767], [408, 767], [408, 790], [194, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[194, 792], [473, 792], [473, 815], [194, 815]], [[98, 817], [157, 817], [157, 842], [98, 842]], [[192, 817], [406, 817], [406, 840], [192, 840]], [[98, 842], [157, 842], [157, 867], [98, 867]], [[192, 842], [378, 842], [378, 865], [192, 865]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [401, 869], [401, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[192, 894], [594, 894], [594, 917], [192, 917]], [[98, 917], [157, 917], [157, 944], [98, 944]], [[190, 917], [364, 919], [364, 944], [190, 942]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [559, 946], [559, 969], [194, 969]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[191, 967], [312, 971], [311, 998], [190, 994]], [[98, 994], [155, 994], [155, 1021], [98, 1021]], [[192, 996], [476, 996], [476, 1019], [192, 1019]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[192, 1022], [401, 1022], [401, 1046], [192, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[192, 1047], [569, 1047], [569, 1071], [192, 1071]], [[98, 1071], [155, 1071], [155, 1098], [98, 1098]], [[194, 1074], [314, 1074], [314, 1094], [194, 1094]], [[98, 1096], [155, 1096], [155, 1123], [98, 1123]], [[190, 1096], [591, 1096], [591, 1119], [190, 1119]], [[98, 1123], [155, 1123], [155, 1148], [98, 1148]], [[192, 1123], [462, 1123], [462, 1146], [192, 1146]], [[98, 1148], [155, 1148], [155, 1174], [98, 1174]], [[188, 1142], [263, 1146], [261, 1180], [186, 1176]], [[98, 1173], [155, 1173], [155, 1199], [98, 1199]], [[192, 1173], [325, 1173], [325, 1198], [192, 1198]], [[98, 1198], [155, 1198], [155, 1224], [98, 1224]], [[190, 1199], [277, 1199], [277, 1226], [190, 1226]], [[98, 1224], [155, 1224], [155, 1249], [98, 1249]], [[192, 1224], [364, 1224], [364, 1249], [192, 1249]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1248], [323, 1248], [323, 1273], [192, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[188, 1273], [260, 1273], [260, 1305], [188, 1305]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[192, 1303], [447, 1303], [447, 1326], [192, 1326]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[190, 1325], [305, 1325], [305, 1355], [190, 1355]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [356, 1353], [356, 1378], [192, 1378]], [[98, 1378], [157, 1378], [157, 1403], [98, 1403]], [[186, 1375], [261, 1370], [263, 1403], [188, 1407]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [319, 1403], [319, 1428], [192, 1428]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[186, 1427], [263, 1422], [265, 1456], [188, 1461]], [[100, 1453], [155, 1453], [155, 1478], [100, 1478]], [[194, 1455], [308, 1455], [308, 1480], [194, 1480]], [[98, 1480], [157, 1480], [157, 1505], [98, 1505]], [[194, 1482], [689, 1482], [689, 1505], [194, 1505]], [[98, 1505], [155, 1505], [155, 1530], [98, 1530]], [[192, 1505], [279, 1505], [279, 1530], [192, 1530]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[190, 1530], [277, 1530], [277, 1557], [190, 1557]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[190, 1557], [452, 1557], [452, 1580], [190, 1580]], [[98, 1582], [157, 1582], [157, 1607], [98, 1607]], [[190, 1582], [262, 1582], [262, 1609], [190, 1609]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1609], [606, 1609], [606, 1634], [192, 1634]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[190, 1632], [297, 1632], [297, 1657], [190, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1659], [430, 1659], [430, 1682], [192, 1682]], [[100, 1684], [157, 1684], [157, 1709], [100, 1709]], [[194, 1686], [412, 1686], [412, 1709], [194, 1709]]], "rec_boxes": [[20, 30, 519, 61], [100, 77, 157, 102], [192, 79, 493, 102], [98, 102, 157, 129], [194, 102, 270, 129], [194, 129, 463, 152], [100, 154, 157, 179], [192, 152, 694, 179], [98, 179, 157, 204], [194, 181, 443, 204], [100, 204, 157, 229], [196, 209, 532, 227], [100, 231, 155, 256], [194, 234, 522, 252], [100, 256, 157, 281], [194, 257, 449, 281], [100, 281, 155, 306], [194, 282, 362, 306], [98, 306, 157, 331], [194, 307, 456, 331], [100, 332, 157, 358], [192, 332, 292, 358], [98, 358, 157, 383], [192, 356, 373, 383], [98, 383, 155, 409], [192, 382, 524, 408], [98, 408, 157, 434], [196, 413, 639, 431], [98, 434, 157, 459], [192, 436, 502, 459], [100, 459, 157, 484], [192, 459, 305, 484], [98, 484, 157, 509], [192, 484, 286, 513], [98, 509, 155, 536], [194, 511, 733, 534], [98, 536, 155, 561], [196, 540, 498, 558], [98, 561, 157, 586], [194, 563, 393, 586], [98, 586, 155, 613], [192, 588, 395, 611], [98, 611, 157, 638], [190, 611, 360, 638], [98, 638, 155, 663], [194, 642, 474, 660], [98, 663, 155, 688], [194, 665, 500, 688], [98, 688, 155, 715], [192, 688, 279, 715], [98, 713, 157, 740], [192, 715, 334, 740], [98, 740, 157, 765], [194, 740, 471, 765], [98, 765, 157, 790], [194, 767, 408, 790], [98, 790, 157, 817], [194, 792, 473, 815], [98, 817, 157, 842], [192, 817, 406, 840], [98, 842, 157, 867], [192, 842, 378, 865], [98, 867, 157, 892], [192, 869, 401, 892], [98, 892, 157, 919], [192, 894, 594, 917], [98, 917, 157, 944], [190, 917, 364, 944], [98, 944, 157, 969], [194, 946, 559, 969], [98, 969, 157, 996], [190, 967, 312, 998], [98, 994, 155, 1021], [192, 996, 476, 1019], [98, 1021, 157, 1046], [192, 1022, 401, 1046], [98, 1046, 157, 1071], [192, 1047, 569, 1071], [98, 1071, 155, 1098], [194, 1074, 314, 1094], [98, 1096, 155, 1123], [190, 1096, 591, 1119], [98, 1123, 155, 1148], [192, 1123, 462, 1146], [98, 1148, 155, 1174], [186, 1142, 263, 1180], [98, 1173, 155, 1199], [192, 1173, 325, 1198], [98, 1198, 155, 1224], [190, 1199, 277, 1226], [98, 1224, 155, 1249], [192, 1224, 364, 1249], [98, 1249, 157, 1274], [192, 1248, 323, 1273], [98, 1274, 157, 1301], [188, 1273, 260, 1305], [98, 1300, 157, 1326], [192, 1303, 447, 1326], [98, 1326, 157, 1351], [190, 1325, 305, 1355], [98, 1351, 157, 1376], [192, 1353, 356, 1378], [98, 1378, 157, 1403], [186, 1370, 263, 1407], [100, 1403, 157, 1428], [192, 1403, 319, 1428], [100, 1428, 157, 1453], [186, 1422, 265, 1461], [100, 1453, 155, 1478], [194, 1455, 308, 1480], [98, 1480, 157, 1505], [194, 1482, 689, 1505], [98, 1505, 155, 1530], [192, 1505, 279, 1530], [98, 1530, 157, 1555], [190, 1530, 277, 1557], [98, 1555, 155, 1582], [190, 1557, 452, 1580], [98, 1582, 157, 1607], [190, 1582, 262, 1609], [98, 1607, 157, 1632], [192, 1609, 606, 1634], [100, 1632, 157, 1657], [190, 1632, 297, 1657], [100, 1657, 157, 1684], [192, 1659, 430, 1682], [100, 1684, 157, 1709], [194, 1686, 412, 1709]]}, "table_res_list": [{"cell_box_list": [[97.52774047851562, 76.78093194961548, 169.60426330566406, 102.33987236022949], [192.82728576660156, 75.97935628890991, 766.1401977539062, 102.78861045837402], [42.41290473937988, 102.50078010559082, 69.291259765625, 128.30590057373047], [69.32738876342773, 102.44957542419434, 97.69384002685547, 128.22208786010742], [97.5720443725586, 102.3587875366211, 169.82337951660156, 127.94317245483398], [194.0, 102.0, 270.0, 129.0], [42.353172302246094, 128.14421463012695, 69.28159713745117, 154.25235748291016], [69.29667282104492, 128.17869186401367, 97.64739227294922, 154.29560089111328], [194.0, 129.0, 463.0, 152.0], [69.31939315795898, 154.0436248779297, 97.76394653320312, 205.23700714111328], [97.68878936767578, 153.79158782958984, 170.04075622558594, 179.6127471923828], [191.70855712890625, 153.4019012451172, 766.77099609375, 179.42229461669922], [42.370317459106445, 179.66868591308594, 69.31375122070312, 205.17615509033203], [97.65251922607422, 179.64116668701172, 170.0073699951172, 204.99286651611328], [194.0, 181.0, 443.0, 204.0], [69.35554122924805, 205.18706512451172, 97.77246856689453, 230.96793365478516], [97.6756820678711, 205.28910064697266, 170.0460968017578, 230.98192596435547], [189.91981506347656, 205.1946029663086, 766.4764404296875, 231.26459503173828], [42.34766960144043, 230.8766860961914, 69.32039642333984, 256.8054733276367], [69.28164291381836, 230.84093475341797, 97.8177261352539, 256.77161407470703], [97.70623779296875, 230.84387969970703, 170.4477081298828, 281.93836212158203], [190.6292724609375, 231.58373260498047, 766.47265625, 256.84229278564453], [69.26945114135742, 256.68982696533203, 97.83611297607422, 282.0851516723633], [194.0, 257.0, 449.0, 281.0], [69.27852249145508, 281.99263763427734, 97.80137634277344, 357.19136810302734], [97.6985855102539, 282.1973342895508, 170.4933319091797, 307.4208755493164], [190.6908721923828, 282.39485931396484, 766.3389892578125, 306.9548873901367], [42.40345764160156, 306.8701705932617, 69.32461547851562, 331.89669036865234], [97.68354034423828, 307.61629486083984, 170.62522888183594, 357.41991424560547], [194.0, 307.0, 456.0, 331.0], [190.70729064941406, 332.02745819091797, 766.4229736328125, 357.4932174682617], [42.32892417907715, 357.15218353271484, 69.27283477783203, 408.2509994506836], [69.26724243164062, 357.2147750854492, 97.75878143310547, 382.6935348510742], [97.62040710449219, 357.50045013427734, 170.5284423828125, 408.6825485229492], [191.31204223632812, 357.91451263427734, 766.5960693359375, 383.19103240966797], [69.28352355957031, 382.50594329833984, 97.75263214111328, 408.30074310302734], [192.0, 382.0, 524.0, 408.0], [42.36493492126465, 408.18575286865234, 69.27398300170898, 433.7085189819336], [69.29991912841797, 408.16109466552734, 97.73888397216797, 433.8075485229492], [97.63019561767578, 408.5189437866211, 170.46864318847656, 434.2128219604492], [191.49588012695312, 408.6489486694336, 766.2903442382812, 434.41463470458984], [69.3171272277832, 433.8469467163086, 97.77113342285156, 459.4954147338867], [97.65306854248047, 434.17931365966797, 170.46929931640625, 459.7218246459961], [191.3083953857422, 434.5704879760742, 766.2875366210938, 460.20384979248047], [42.315895080566406, 459.4053268432617, 69.27221298217773, 485.22118377685547], [69.2690544128418, 459.41365814208984, 97.74922943115234, 485.21385955810547], [97.60392761230469, 459.6160202026367, 170.623046875, 485.41222381591797], [192.0, 459.0, 305.0, 484.0], [42.28683853149414, 485.1368637084961, 69.24919891357422, 510.8379135131836], [69.2784538269043, 485.1128463745117, 97.75023651123047, 510.8486862182617], [97.63509368896484, 485.18611907958984, 170.66522216796875, 511.1720504760742], [191.349853515625, 485.3569869995117, 766.452392578125, 511.67745208740234], [42.32116508483887, 510.76937103271484, 69.26675796508789, 536.3829574584961], [69.29421997070312, 510.7434616088867, 97.71540069580078, 536.4469528198242], [97.5988540649414, 511.0307846069336, 170.6121826171875, 536.7242965698242], [194.0, 511.0, 733.0, 534.0], [69.29358291625977, 536.4337997436523, 97.73790740966797, 562.1076278686523], [97.62405395507812, 536.7003707885742, 170.55711364746094, 562.2702865600586], [191.52906799316406, 537.0713119506836, 766.5150146484375, 562.7132186889648], [42.31339645385742, 562.1053085327148, 69.26406478881836, 587.9294052124023], [69.24195098876953, 562.1022262573242, 97.72235107421875, 587.9046249389648], [97.58056640625, 562.1770553588867, 170.64669799804688, 588.0318832397461], [191.77206420898438, 562.8366622924805, 766.8047485351562, 588.1682357788086], [42.30404281616211, 587.8468856811523, 69.25651931762695, 639.0064926147461], [69.25502014160156, 587.7900009155273, 97.71468353271484, 613.5790634155273], [97.59656524658203, 587.7721786499023, 170.6235809326172, 613.6403427124023], [192.0, 588.0, 395.0, 611.0], [69.26330184936523, 613.4482650756836, 97.6823501586914, 638.9824447631836], [97.56346130371094, 613.5799789428711, 170.622802734375, 639.0290145874023], [191.2019805908203, 613.7817611694336, 766.3624877929688, 638.8131332397461], [97.61897277832031, 639.0027084350586, 170.55511474609375, 688.0], [191.5910186767578, 639.1693954467773, 766.578369140625, 664.6716537475586], [69.22266006469727, 664.2713241577148, 97.7208251953125, 714.3958358764648], [194.0, 665.0, 500.0, 688.0], [97.57044982910156, 689.4450912475586, 170.4970245361328, 765.5926742553711], [191.6075439453125, 689.8467025756836, 766.4765014648438, 715.1373519897461], [42.41058349609375, 714.3254623413086, 69.29058456420898, 739.5232162475586], [69.28751754760742, 714.3299179077148, 97.72610473632812, 739.5535507202148], [192.0, 715.0, 334.0, 740.0], [69.3101921081543, 739.6304550170898, 97.7450942993164, 765.0852890014648], [191.49899291992188, 740.0431137084961, 766.8619384765625, 765.8633651733398], [42.37113952636719, 764.9833602905273, 69.32205200195312, 790.8325424194336], [69.24163055419922, 764.8978500366211, 97.73454284667969, 790.6832504272461], [97.63900756835938, 765.2733993530273, 170.4567413330078, 816.5185165405273], [194.0, 767.0, 408.0, 790.0], [69.2375717163086, 790.8329696655273, 97.74374389648438, 816.4368515014648], [194.0, 792.0, 473.0, 815.0], [42.396209716796875, 816.3681869506836, 69.29469299316406, 841.9825668334961], [69.28445816040039, 816.3127670288086, 97.73788452148438, 841.9849472045898], [97.59757232666016, 816.5331039428711, 170.53721618652344, 842.1228866577148], [191.33531188964844, 816.7509384155273, 766.591552734375, 841.6504135131836], [69.32705688476562, 842.0072250366211, 97.77960968017578, 867.5815048217773], [97.68936920166016, 842.1405868530273, 170.4951934814453, 867.7808456420898], [190.8135986328125, 842.2025375366211, 766.43896484375, 868.1111068725586], [42.36825370788574, 867.4156112670898, 69.33380126953125, 893.3751449584961], [69.258056640625, 867.3694076538086, 97.76356506347656, 893.2489852905273], [97.65172576904297, 867.5562362670898, 170.5113525390625, 893.5000228881836], [192.0, 869.0, 401.0, 892.0], [69.2393684387207, 893.3359603881836, 97.76860046386719, 918.9911727905273], [97.69586944580078, 893.3662338256836, 170.65660095214844, 919.0322494506836], [191.2410125732422, 893.5630722045898, 766.6795654296875, 919.4724960327148], [42.37105178833008, 918.9431991577148, 69.29420471191406, 944.5692367553711], [69.28485870361328, 918.8955307006836, 97.74609375, 944.6129989624023], [97.64552307128906, 919.1515121459961, 170.61744689941406, 944.7461776733398], [190.0, 917.0, 364.0, 944.0], [69.31206512451172, 944.6787338256836, 97.79513549804688, 970.3687973022461], [97.7098159790039, 944.8377914428711, 170.6151580810547, 970.4468002319336], [190.93359375, 945.1596908569336, 766.702880859375, 970.6029891967773], [42.3596248626709, 970.1285018920898, 69.3226318359375, 996.1286239624023], [69.23154830932617, 970.1233139038086, 97.78507995605469, 996.0597763061523], [97.65994262695312, 970.2486801147461, 170.68016052246094, 996.1332626342773], [191.31129455566406, 971.2659530639648, 766.9248657226562, 996.3568954467773], [69.20871353149414, 996.1259994506836, 97.78328704833984, 1021.7220687866211], [97.73070526123047, 996.0325546264648, 170.8151397705078, 1021.6270980834961], [192.0, 996.0, 476.0, 1019.0], [69.25113296508789, 1021.6092758178711, 97.79287719726562, 1071.7132186889648], [97.62142944335938, 1021.7690658569336, 170.81149291992188, 1072.262596130371], [191.90199279785156, 1021.7922592163086, 766.4628295898438, 1046.1834335327148], [192.01307678222656, 1047.1820907592773, 766.5449829101562, 1072.618553161621], [98.0, 1071.0, 155.0, 1098.0], [194.0, 1074.0, 314.0, 1094.0], [42.32229995727539, 1097.145652770996, 69.27088165283203, 1147.7549057006836], [69.23637771606445, 1097.2324447631836, 97.72605895996094, 1173.1528549194336], [97.58161163330078, 1096.6533432006836, 170.81114196777344, 1122.349266052246], [191.99176025390625, 1097.345848083496, 766.9968872070312, 1122.6506576538086], [97.68072509765625, 1122.3813705444336, 170.606201171875, 1148.0884017944336], [192.0, 1123.0, 462.0, 1146.0], [97.62632751464844, 1148.0012435913086, 170.4383087158203, 1199.0056381225586], [192.13841247558594, 1147.7063217163086, 766.5781860351562, 1173.3159408569336], [69.2696647644043, 1173.2290267944336, 97.75220489501953, 1198.8186264038086], [191.98709106445312, 1173.811424255371, 766.524658203125, 1199.404930114746], [42.306365966796875, 1198.7773666381836, 69.28498077392578, 1224.625877380371], [69.25902557373047, 1198.791160583496, 97.7663345336914, 1224.617820739746], [97.62138366699219, 1198.949851989746, 170.60145568847656, 1224.746482849121], [190.0, 1199.0, 277.0, 1226.0], [42.28909683227539, 1224.505027770996, 69.25690078735352, 1275.7080307006836], [69.25993728637695, 1224.483543395996, 97.74691772460938, 1250.2707748413086], [97.68341827392578, 1224.5559310913086, 170.66360473632812, 1250.514060974121], [192.07992553710938, 1224.8681869506836, 766.4060668945312, 1250.8884506225586], [69.27894973754883, 1250.1277084350586, 97.71217346191406, 1275.8203353881836], [97.62911987304688, 1250.381736755371, 170.6156463623047, 1276.021873474121], [192.0, 1248.0, 323.0, 1273.0], [69.27689743041992, 1275.842185974121, 97.74833679199219, 1301.452781677246], [97.65733337402344, 1276.009422302246, 170.68235778808594, 1301.600730895996], [192.19277954101562, 1276.242820739746, 766.8551025390625, 1302.0381088256836], [42.29331398010254, 1301.4489974975586, 69.28374481201172, 1327.3142318725586], [69.2656021118164, 1301.460838317871, 97.75532531738281, 1327.299949645996], [97.60023498535156, 1301.509422302246, 170.82191467285156, 1327.2866439819336], [192.28912353515625, 1302.023826599121, 766.8152465820312, 1327.505516052246], [42.3021297454834, 1327.224021911621, 69.2691879272461, 1378.489158630371], [69.25350189208984, 1327.201072692871, 97.71936798095703, 1352.9465560913086], [97.62854766845703, 1327.1692123413086, 170.7853240966797, 1353.0591049194336], [190.0, 1325.0, 305.0, 1355.0], [69.26983642578125, 1352.8405990600586, 97.68573760986328, 1378.509422302246], [97.58336639404297, 1352.994041442871, 170.78790283203125, 1403.982810974121], [192.45059204101562, 1353.3401107788086, 766.7608642578125, 1378.366111755371], [191.9357147216797, 1379.0847396850586, 766.3731079101562, 1404.486717224121], [69.24219512939453, 1403.9355697631836, 97.71263122558594, 1429.389793395996], [97.59716796875, 1403.859519958496, 170.757568359375, 1454.5886459350586], [192.0, 1403.0, 319.0, 1428.0], [69.23336410522461, 1429.245018005371, 97.73766326904297, 1504.573875427246], [192.12905883789062, 1429.540428161621, 766.68017578125, 1455.316551208496], [97.60092163085938, 1454.636619567871, 170.60984802246094, 1505.029685974121], [194.0, 1455.0, 308.0, 1480.0], [191.90699768066406, 1479.906150817871, 766.791748046875, 1505.7724838256836], [42.326650619506836, 1504.586814880371, 69.33232116699219, 1530.348289489746], [69.25358963012695, 1504.4885482788086, 97.73371124267578, 1530.225486755371], [97.6278305053711, 1504.8845443725586, 170.53431701660156, 1555.972801208496], [192.0, 1505.0, 279.0, 1530.0], [69.24169540405273, 1530.2478256225586, 97.75483703613281, 1581.323875427246], [190.0, 1530.0, 277.0, 1557.0], [42.38867378234863, 1555.8181381225586, 69.31301498413086, 1581.3303451538086], [97.62462615966797, 1556.0053939819336, 170.41387939453125, 1607.180809020996], [192.1088104248047, 1557.3864974975586, 766.7021484375, 1607.6064682006836], [42.427873611450195, 1581.588523864746, 69.37203979492188, 1607.031150817871], [69.3405990600586, 1581.380516052246, 97.79127502441406, 1606.967918395996], [42.387617111206055, 1607.0168685913086, 69.35275268554688, 1633.022117614746], [69.26585388183594, 1606.9331283569336, 97.7628402709961, 1632.9270248413086], [97.6585464477539, 1607.0791244506836, 170.33470153808594, 1633.1006088256836], [192.0, 1609.0, 606.0, 1634.0], [69.26463317871094, 1632.859031677246, 97.79811096191406, 1683.484764099121], [97.70986938476562, 1632.8718490600586, 170.43218994140625, 1658.4968490600586], [191.8493194580078, 1633.2905502319336, 766.6382446289062, 1658.9416732788086], [42.45895957946777, 1658.342918395996, 69.35959243774414, 1683.713768005371], [97.76457977294922, 1658.4807357788086, 170.2631378173828, 1684.1267318725586], [192.0, 1659.0, 430.0, 1682.0], [100.0, 1684.0, 157.0, 1709.0], [194.0, 1686.0, 412.0, 1709.0]], "pred_html": "<html><body><table><tbody><tr><td>0.000</td><td>消化系统—夷腺;汁二指肠正面图</td><td></td></tr><tr><td></td><td rowspan=\"2\"></td><td>2.167</td></tr><tr><td></td><td></td></tr><tr><td></td><td></td><td>虚拟模式-健康问题发展趋势列表：</td></tr><tr><td></td><td>0.048</td><td>血浆非酯化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA</td></tr><tr><td></td><td>0.054</td><td>血清溶菌酵SERUMLYSOZYME</td></tr><tr><td></td><td>0.054</td><td>血浆中性脂肪NEUTRALFATSOFPLASMA</td></tr><tr><td></td><td></td><td>0.057 0.060</td></tr><tr><td></td><td>血清补体SERUM COMPLEMENT</td><td></td></tr><tr><td></td><td>0.060</td><td>血尿素BLOODUREA</td></tr><tr><td></td><td>0.062 0.062</td><td>伽马球蛋白GAMMA-GLOBULINS</td></tr><tr><td></td><td></td><td>0.063 0.064</td></tr><tr><td></td><td>胆固醇COMMON PLASMA CHOLESTERIN</td><td></td></tr><tr><td></td><td></td><td>0.064</td></tr><tr><td></td><td>0.065</td><td>血清淀粉酵素SERUMALPHAAMYLASE</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.066</td></tr><tr><td></td><td></td><td>0.066</td></tr><tr><td></td><td></td><td>0.067</td></tr><tr><td></td><td>0.067</td><td>血清甘油三酯SERUMTRIGLYCERIDES</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.068</td></tr><tr><td></td><td></td><td>0.069</td></tr><tr><td></td><td>0.069</td><td>甲状腺素结合球蛋白</td></tr><tr><td>0.071 0.071</td><td>PERIPHERICBLOODLEUCOCYTES</td><td></td></tr><tr><td>0.071 0.072 0.072</td><td>甲状旁腺*</td><td></td></tr><tr><td></td><td></td><td>糖基化血红蛋白*</td></tr><tr><td></td><td>血脂COMMON LIPIDS OF PLASMA</td><td></td></tr><tr><td></td><td></td><td>0.073 0.073</td></tr><tr><td></td><td>C反应蛋白C-REACTIVEPROTEIN</td><td></td></tr><tr><td></td><td></td><td>0.073</td></tr><tr><td></td><td>0.073</td><td>单核细胞MONOCYTES</td></tr><tr><td></td><td></td><td>0.075</td></tr><tr><td></td><td>0.075</td><td>网织红细胞PERIPHERICBLOODRETICULOCYTES</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.075</td></tr><tr><td></td><td>0.076</td><td>血小板PERIPHERICBLOOD THROMBOCYTES</td></tr><tr><td></td><td></td><td>0.076</td></tr><tr><td></td><td>0.077</td><td>血浆磷脂PLASMA PHOSPHOTIDES</td></tr><tr><td></td><td>0.077 0.078</td><td>前列腺特异性抗原（PSA）</td></tr><tr><td>0.081</td><td>甲状腺球蛋白*</td><td></td></tr><tr><td></td><td></td><td>0.081</td></tr><tr><td>0.081</td><td>GLUTAMATEDEHYDROGENASE*</td><td></td></tr><tr><td>0.081 0.081</td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.081</td></tr><tr><td></td><td></td><td>0.081</td></tr><tr><td></td><td>0.082</td><td>ALPHA2球蛋白</td></tr><tr><td></td><td>0.082</td><td>催乳素*</td></tr><tr><td></td><td></td><td>0.082</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.083</td></tr><tr><td></td><td>0.083 0.084</td><td>血清铁SERUM IRON</td></tr><tr><td></td><td>0.084 0.084</td><td>GAMMA谷氨酰</td></tr><tr><td></td><td></td><td>0.084 0.084</td></tr><tr><td>ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM</td><td></td><td></td></tr><tr><td></td><td></td><td>0.084 0.085</td></tr><tr><td></td><td>转铁蛋白</td><td></td></tr><tr><td></td><td>0.087 0.089</td><td>凝血酵素PROTHROMBININDEX 降钙素*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.090</td></tr><tr><td></td><td>0.090</td><td>红血球啉*</td></tr><tr><td></td><td>0.090</td><td>嗜酸性粒细胞EOSINOPHILES</td></tr></tbody></table></body></html>", "table_ocr_pred": {"rec_polys": [[[100, 77], [157, 77], [157, 102], [100, 102]], [[192, 79], [493, 79], [493, 102], [192, 102]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[194, 102], [270, 102], [270, 129], [194, 129]], [[194, 129], [463, 129], [463, 152], [194, 152]], [[100, 154], [157, 154], [157, 179], [100, 179]], [[192, 154], [694, 152], [694, 177], [192, 179]], [[98, 179], [157, 179], [157, 204], [98, 204]], [[194, 181], [443, 181], [443, 204], [194, 204]], [[100, 204], [157, 204], [157, 229], [100, 229]], [[196, 209], [532, 209], [532, 227], [196, 227]], [[100, 231], [155, 231], [155, 256], [100, 256]], [[194, 234], [522, 234], [522, 252], [194, 252]], [[100, 256], [157, 256], [157, 281], [100, 281]], [[194, 257], [449, 257], [449, 281], [194, 281]], [[100, 281], [155, 281], [155, 306], [100, 306]], [[194, 282], [362, 282], [362, 306], [194, 306]], [[98, 306], [157, 306], [157, 331], [98, 331]], [[194, 307], [456, 307], [456, 331], [194, 331]], [[100, 332], [157, 332], [157, 358], [100, 358]], [[192, 332], [292, 332], [292, 358], [192, 358]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[192, 358], [373, 356], [373, 381], [192, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[192, 384], [524, 382], [524, 406], [192, 408]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[196, 413], [639, 413], [639, 431], [196, 431]], [[98, 434], [157, 434], [157, 459], [98, 459]], [[192, 436], [502, 436], [502, 459], [192, 459]], [[100, 459], [157, 459], [157, 484], [100, 484]], [[192, 459], [305, 459], [305, 484], [192, 484]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[192, 484], [286, 484], [286, 513], [192, 513]], [[98, 509], [155, 509], [155, 536], [98, 536]], [[194, 511], [733, 511], [733, 534], [194, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[196, 540], [498, 540], [498, 558], [196, 558]], [[98, 561], [157, 561], [157, 586], [98, 586]], [[194, 563], [393, 563], [393, 586], [194, 586]], [[98, 586], [155, 586], [155, 613], [98, 613]], [[192, 588], [395, 588], [395, 611], [192, 611]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[190, 611], [360, 613], [360, 638], [190, 636]], [[98, 638], [155, 638], [155, 663], [98, 663]], [[194, 642], [474, 642], [474, 660], [194, 660]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[194, 665], [500, 665], [500, 688], [194, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[192, 688], [279, 688], [279, 715], [192, 715]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[192, 715], [334, 715], [334, 740], [192, 740]], [[98, 740], [157, 740], [157, 765], [98, 765]], [[194, 740], [471, 740], [471, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[194, 767], [408, 767], [408, 790], [194, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[194, 792], [473, 792], [473, 815], [194, 815]], [[98, 817], [157, 817], [157, 842], [98, 842]], [[192, 817], [406, 817], [406, 840], [192, 840]], [[98, 842], [157, 842], [157, 867], [98, 867]], [[192, 842], [378, 842], [378, 865], [192, 865]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [401, 869], [401, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[192, 894], [594, 894], [594, 917], [192, 917]], [[98, 917], [157, 917], [157, 944], [98, 944]], [[190, 917], [364, 919], [364, 944], [190, 942]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [559, 946], [559, 969], [194, 969]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[191, 967], [312, 971], [311, 998], [190, 994]], [[98, 994], [155, 994], [155, 1021], [98, 1021]], [[192, 996], [476, 996], [476, 1019], [192, 1019]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[192, 1022], [401, 1022], [401, 1046], [192, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[192, 1047], [569, 1047], [569, 1071], [192, 1071]], [[98, 1071], [155, 1071], [155, 1098], [98, 1098]], [[194, 1074], [314, 1074], [314, 1094], [194, 1094]], [[98, 1096], [155, 1096], [155, 1123], [98, 1123]], [[190, 1096], [591, 1096], [591, 1119], [190, 1119]], [[98, 1123], [155, 1123], [155, 1148], [98, 1148]], [[192, 1123], [462, 1123], [462, 1146], [192, 1146]], [[98, 1148], [155, 1148], [155, 1174], [98, 1174]], [[188, 1142], [263, 1146], [261, 1180], [186, 1176]], [[98, 1173], [155, 1173], [155, 1199], [98, 1199]], [[192, 1173], [325, 1173], [325, 1198], [192, 1198]], [[98, 1198], [155, 1198], [155, 1224], [98, 1224]], [[190, 1199], [277, 1199], [277, 1226], [190, 1226]], [[98, 1224], [155, 1224], [155, 1249], [98, 1249]], [[192, 1224], [364, 1224], [364, 1249], [192, 1249]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1248], [323, 1248], [323, 1273], [192, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[188, 1273], [260, 1273], [260, 1305], [188, 1305]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[192, 1303], [447, 1303], [447, 1326], [192, 1326]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[190, 1325], [305, 1325], [305, 1355], [190, 1355]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [356, 1353], [356, 1378], [192, 1378]], [[98, 1378], [157, 1378], [157, 1403], [98, 1403]], [[186, 1375], [261, 1370], [263, 1403], [188, 1407]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [319, 1403], [319, 1428], [192, 1428]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[186, 1427], [263, 1422], [265, 1456], [188, 1461]], [[100, 1453], [155, 1453], [155, 1478], [100, 1478]], [[194, 1455], [308, 1455], [308, 1480], [194, 1480]], [[98, 1480], [157, 1480], [157, 1505], [98, 1505]], [[194, 1482], [689, 1482], [689, 1505], [194, 1505]], [[98, 1505], [155, 1505], [155, 1530], [98, 1530]], [[192, 1505], [279, 1505], [279, 1530], [192, 1530]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[190, 1530], [277, 1530], [277, 1557], [190, 1557]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[190, 1557], [452, 1557], [452, 1580], [190, 1580]], [[98, 1582], [157, 1582], [157, 1607], [98, 1607]], [[190, 1582], [262, 1582], [262, 1609], [190, 1609]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1609], [606, 1609], [606, 1634], [192, 1634]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[190, 1632], [297, 1632], [297, 1657], [190, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1659], [430, 1659], [430, 1682], [192, 1682]], [[100, 1684], [157, 1684], [157, 1709], [100, 1709]], [[194, 1686], [412, 1686], [412, 1709], [194, 1709]]], "rec_texts": ["0.000", "消化系统—夷腺;汁二指肠正面图", "2.167", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.048", "血浆非酯化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA", "0.054", "血清溶菌酵SERUMLYSOZYME", "0.054", "血浆中性脂肪NEUTRALFATSOFPLASMA", "0.057", "游离胆固醇FREEPLASMACHOLESTERIN", "0.060", "血清补体SERUM COMPLEMENT", "0.060", "血尿素BLOODUREA", "0.062", "伽马球蛋白GAMMA-GLOBULINS", "0.062", "抗利尿激素", "0.063", "总铁结合力（TIBC）*", "0.064", "胆固醇COMMON PLASMA CHOLESTERIN", "0.064", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.065", "血清淀粉酵素SERUMALPHAAMYLASE", "0.066", "BETA球蛋白*", "0.066", "维生素D3*", "0.067", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.067", "血清甘油三酯SERUMTRIGLYCERIDES", "0.068", "血红蛋白HAEMOGLOBIN", "0.069", "淋巴细胞LYMPHOCYTES", "0.069", "甲状腺素结合球蛋白", "0.071", "PERIPHERICBLOODLEUCOCYTES", "0.071", "血胆红素COMMONBLOODBILIRUBIN", "0.071", "甲状旁腺*", "0.072", "糖基化血红蛋白*", "0.072", "血脂COMMON LIPIDS OF PLASMA", "0.073", "血清蛋白SERUMPROTEIN", "0.073", "C反应蛋白C-REACTIVEPROTEIN", "0.073", "血红血球ERYTHROCYTES", "0.073", "单核细胞MONOCYTES", "0.075", "嗜碱性粒细胞BASOPHILS", "0.075", "网织红细胞PERIPHERICBLOODRETICULOCYTES", "0.075", "红细胞沉降率(ESR)", "0.076", "血小板PERIPHERICBLOOD THROMBOCYTES", "0.076", "游离甲状腺素", "0.077", "血浆磷脂PLASMA PHOSPHOTIDES", "0.077", "前列腺特异性抗原（PSA）", "0.078", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.081", "甲状腺球蛋白*", "0.081", "乳酸脱氢酵素COMMONLACTADEHYDROGENASE", "0.081", "GLUTAMATEDEHYDROGENASE*", "0.081", "备解素*", "0.081", "ALPHA1球蛋白*", "0.081", "肌红蛋白", "0.081", "维生素A（视黄醇）*", "0.082", "ALPHA2球蛋白", "0.082", "催乳素*", "0.082", "肿瘤标志物MELANOGENE在尿*", "0.083", "血管紧张素I*", "0.083", "血清铁SERUM IRON", "0.084", "蛋白C*", "0.084", "GAMMA谷氨酰", "0.084", "胰岛素*", "0.084", "血管紧张素I*", "0.084", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.084", "胰蛋白酶*", "0.085", "转铁蛋白", "0.087", "凝血酵素PROTHROMBININDEX", "0.089", "降钙素*", "0.090", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.090", "红血球啉*", "0.090", "嗜酸性粒细胞EOSINOPHILES", "0.090", "血清蛋白SERUMABUMEN"], "rec_scores": [0.9991452097892761, 0.8610752820968628, 0.9996070861816406, 0.9987521767616272, 0.9706943035125732, 0.9993985891342163, 0.9831214547157288, 0.9996549487113953, 0.9967777729034424, 0.9996512532234192, 0.9924781322479248, 0.9997230768203735, 0.996050238609314, 0.9996001124382019, 0.9756301641464233, 0.9996358156204224, 0.9978327751159668, 0.9996463656425476, 0.9902769923210144, 0.9997104406356812, 0.9988042712211609, 0.9996620416641235, 0.9067788124084473, 0.9997125864028931, 0.9621079564094543, 0.9997116327285767, 0.9974311590194702, 0.9997149705886841, 0.9982032179832458, 0.9995217323303223, 0.973098874092102, 0.999625563621521, 0.9890671372413635, 0.9997604489326477, 0.995054304599762, 0.9996060132980347, 0.9844906330108643, 0.9996688961982727, 0.9976869225502014, 0.9997493624687195, 0.9943255186080933, 0.9998126029968262, 0.9914366602897644, 0.9996027946472168, 0.9963062405586243, 0.9995591044425964, 0.9975655674934387, 0.999690055847168, 0.9565446972846985, 0.9997766613960266, 0.986910343170166, 0.9997081756591797, 0.9662355780601501, 0.9996716380119324, 0.9693771600723267, 0.9997776746749878, 0.9902097582817078, 0.9996320009231567, 0.9885869026184082, 0.9997103810310364, 0.9965924620628357, 0.9996727108955383, 0.979997992515564, 0.9997857213020325, 0.99727863073349, 0.9998120069503784, 0.9180747866630554, 0.9996940493583679, 0.9820930361747742, 0.9996798634529114, 0.9969080090522766, 0.9996496438980103, 0.9782581329345703, 0.9994654655456543, 0.9582250118255615, 0.9997159838676453, 0.9955940246582031, 0.9997001886367798, 0.9527922868728638, 0.999719500541687, 0.9894920587539673, 0.9996111989021301, 0.9896750450134277, 0.9997746348381042, 0.939625084400177, 0.9997745752334595, 0.9439484477043152, 0.9997280836105347, 0.9982782006263733, 0.9996433258056641, 0.9253336787223816, 0.9996979832649231, 0.9653288722038269, 0.9998078346252441, 0.9433668255805969, 0.9996959567070007, 0.9686256051063538, 0.9997018575668335, 0.9553508162498474, 0.999719500541687, 0.959416925907135, 0.999545693397522, 0.9443367719650269, 0.9996317028999329, 0.9913325309753418, 0.999615490436554, 0.9698299169540405, 0.9995887875556946, 0.9378418326377869, 0.999545693397522, 0.9952952861785889, 0.9995848536491394, 0.974666953086853, 0.9996746778488159, 0.9983257055282593, 0.9997642636299133, 0.9941462278366089, 0.9996135830879211, 0.9587330222129822, 0.9995995759963989, 0.9806501865386963, 0.9995878338813782, 0.8803703188896179, 0.9993335008621216, 0.997796356678009, 0.9994867444038391, 0.9833138585090637], "rec_boxes": [[100, 77, 157, 102], [192, 79, 493, 102], [98, 102, 157, 129], [194, 102, 270, 129], [194, 129, 463, 152], [100, 154, 157, 179], [192, 152, 694, 179], [98, 179, 157, 204], [194, 181, 443, 204], [100, 204, 157, 229], [196, 209, 532, 227], [100, 231, 155, 256], [194, 234, 522, 252], [100, 256, 157, 281], [194, 257, 449, 281], [100, 281, 155, 306], [194, 282, 362, 306], [98, 306, 157, 331], [194, 307, 456, 331], [100, 332, 157, 358], [192, 332, 292, 358], [98, 358, 157, 383], [192, 356, 373, 383], [98, 383, 155, 409], [192, 382, 524, 408], [98, 408, 157, 434], [196, 413, 639, 431], [98, 434, 157, 459], [192, 436, 502, 459], [100, 459, 157, 484], [192, 459, 305, 484], [98, 484, 157, 509], [192, 484, 286, 513], [98, 509, 155, 536], [194, 511, 733, 534], [98, 536, 155, 561], [196, 540, 498, 558], [98, 561, 157, 586], [194, 563, 393, 586], [98, 586, 155, 613], [192, 588, 395, 611], [98, 611, 157, 638], [190, 611, 360, 638], [98, 638, 155, 663], [194, 642, 474, 660], [98, 663, 155, 688], [194, 665, 500, 688], [98, 688, 155, 715], [192, 688, 279, 715], [98, 713, 157, 740], [192, 715, 334, 740], [98, 740, 157, 765], [194, 740, 471, 765], [98, 765, 157, 790], [194, 767, 408, 790], [98, 790, 157, 817], [194, 792, 473, 815], [98, 817, 157, 842], [192, 817, 406, 840], [98, 842, 157, 867], [192, 842, 378, 865], [98, 867, 157, 892], [192, 869, 401, 892], [98, 892, 157, 919], [192, 894, 594, 917], [98, 917, 157, 944], [190, 917, 364, 944], [98, 944, 157, 969], [194, 946, 559, 969], [98, 969, 157, 996], [190, 967, 312, 998], [98, 994, 155, 1021], [192, 996, 476, 1019], [98, 1021, 157, 1046], [192, 1022, 401, 1046], [98, 1046, 157, 1071], [192, 1047, 569, 1071], [98, 1071, 155, 1098], [194, 1074, 314, 1094], [98, 1096, 155, 1123], [190, 1096, 591, 1119], [98, 1123, 155, 1148], [192, 1123, 462, 1146], [98, 1148, 155, 1174], [186, 1142, 263, 1180], [98, 1173, 155, 1199], [192, 1173, 325, 1198], [98, 1198, 155, 1224], [190, 1199, 277, 1226], [98, 1224, 155, 1249], [192, 1224, 364, 1249], [98, 1249, 157, 1274], [192, 1248, 323, 1273], [98, 1274, 157, 1301], [188, 1273, 260, 1305], [98, 1300, 157, 1326], [192, 1303, 447, 1326], [98, 1326, 157, 1351], [190, 1325, 305, 1355], [98, 1351, 157, 1376], [192, 1353, 356, 1378], [98, 1378, 157, 1403], [186, 1370, 263, 1407], [100, 1403, 157, 1428], [192, 1403, 319, 1428], [100, 1428, 157, 1453], [186, 1422, 265, 1461], [100, 1453, 155, 1478], [194, 1455, 308, 1480], [98, 1480, 157, 1505], [194, 1482, 689, 1505], [98, 1505, 155, 1530], [192, 1505, 279, 1530], [98, 1530, 157, 1555], [190, 1530, 277, 1557], [98, 1555, 155, 1582], [190, 1557, 452, 1580], [98, 1582, 157, 1607], [190, 1582, 262, 1609], [98, 1607, 157, 1632], [192, 1609, 606, 1634], [100, 1632, 157, 1657], [190, 1632, 297, 1657], [100, 1657, 157, 1684], [192, 1659, 430, 1682], [100, 1684, 157, 1709], [194, 1686, 412, 1709]]}}]}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//912ebccb-f863-4669-95c2-cca0c078ef7f/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T15%3A04%3A02Z%2F-1%2F%2Fe28d2f20f3b659e134133409c4e19b15b8a7628108dd7646061eed2d7ed9deb4", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//912ebccb-f863-4669-95c2-cca0c078ef7f/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T15%3A04%3A02Z%2F-1%2F%2F2efb3a624e163cd259f4bf345e6c13ffbf89211bbcb6928add919bf406269924", "table_cell_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//912ebccb-f863-4669-95c2-cca0c078ef7f/table_cell_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T15%3A04%3A02Z%2F-1%2F%2Ff8362375406e69fd1c08c0c48a84b5c122b41f1c1b73e4877e06c9d45f7ac629"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//912ebccb-f863-4669-95c2-cca0c078ef7f/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T15%3A04%3A02Z%2F-1%2F%2F9c75cf8e7ead61bdba119a8dca0015c413b50d7d497c2c76d5effa7ab7a8c1b4"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}