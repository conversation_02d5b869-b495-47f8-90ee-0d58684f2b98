package main

import (
	"encoding/base64"
	"fmt"
)

func main() {
	// 原始编码的密钥
	encodedKey := "TnpneE5EWXhPRGcyTkRBME5EazVPRGhoTkRFd05HRXhaREJrTXpjMFlUQT0="
	
	fmt.Printf("原始编码密钥: %s\n", encodedKey)
	
	// 第一次解码
	decoded1, err1 := base64.StdEncoding.DecodeString(encodedKey)
	if err1 != nil {
		fmt.Printf("第一次解码失败: %v\n", err1)
		return
	}
	
	decoded1Str := string(decoded1)
	fmt.Printf("第一次解码结果: %s\n", decoded1Str)
	fmt.Printf("第一次解码结果长度: %d\n", len(decoded1Str))
	
	// 检查是否需要填充
	paddedStr := decoded1Str
	if len(paddedStr)%4 != 0 {
		paddedStr += "="
		fmt.Printf("添加填充后: %s\n", paddedStr)
	}
	
	// 第二次解码
	decoded2, err2 := base64.StdEncoding.DecodeString(paddedStr)
	if err2 != nil {
		fmt.Printf("第二次解码失败: %v\n", err2)
		return
	}
	
	finalKey := string(decoded2)
	fmt.Printf("最终解码结果: %s\n", finalKey)
	fmt.Printf("最终解码结果长度: %d\n", len(finalKey))
}
