import { defineStore } from 'pinia'
import { 
  GetConfig, 
  GetWindowState, 
  SetCompactWindow, 
  SetExpandedWindow, 
  SetWindowPosition, 
  MinimizeWindow,
  SetAlwaysOnTop,
  UpdateCropSettings,
  GetSiteInfo
} from '../../wailsjs/go/main/App'

export const useDeviceStore = defineStore('device', {
  state: () => ({
    // 配置信息
    config: {
      SiteInfo: {
        SiteName: '',
        SiteID: ''
      },
      device_info: {
        mac_address: '',
        device_no: ''
      }
    },
    // 窗体状态
    windowState: {
      isExpanded: false,
      position: 'left'
    },
    // 系统状态
    systemStatus: '正常',
    // 模式配置
    modeConfig: {},
    // 通知模式
    useSystemNotification: true,
    // 配置监听器定时器
    configWatcherTimer: null
  }),

  getters: {
    // 站点信息
    siteInfo: (state) => {
      if (!state.config || !state.config.SiteInfo || !state.config.SiteInfo.SiteName) {
        return '加载中...'
      }
      return `${state.config.SiteInfo.SiteName} (${state.config.SiteInfo.SiteID})`
    },

    // 设备编号
    deviceNumber: (state) => {
      return state.config?.device_info?.device_no || '未设置'
    },

    // MAC地址
    macAddress: (state) => {
      return state.config?.device_info?.mac_address || '未设置'
    }
  },

  actions: {
    // 加载配置
    async loadConfig() {
      try {
        this.config = await GetConfig()
        
        // 处理device_no字段，从mac_address生成去除冒号的版本
        if (this.config.device_info && this.config.device_info.mac_address) {
          this.config.device_info.device_no = this.config.device_info.mac_address.replace(/:/g, '')
        }
        
        // 加载通知模式设置
        if (this.config.use_system_notification !== undefined) {
          this.useSystemNotification = this.config.use_system_notification
        }

        return { success: true, config: this.config }
      } catch (error) {
        console.error('加载配置失败:', error)
        return { success: false, error }
      }
    },

    // 获取站点信息
    async fetchSiteInfo() {
      try {
        const siteInfo = await GetSiteInfo()
        
        // 更新配置中的站点信息
        this.config.SiteInfo = {
          SiteName: siteInfo.site_name,
          SiteID: siteInfo.site_id,
          SiteType: siteInfo.site_type,
          ParentOrg: siteInfo.parent_org,
          Location: siteInfo.location,
          Contact: siteInfo.contact
        }
        
        return { 
          success: true, 
          message: `站点信息已加载: ${siteInfo.site_name || '未知站点'}`,
          siteInfo 
        }
      } catch (error) {
        console.error('获取站点信息失败:', error)
        return { 
          success: false, 
          message: '获取站点信息失败，请检查网络连接',
          error 
        }
      }
    },

    // 更新站点信息（通过事件）
    updateSiteInfo(newSiteInfo) {
      if (newSiteInfo && typeof newSiteInfo === 'object') {
        this.config.SiteInfo = {
          SiteName: newSiteInfo.site_name || newSiteInfo.SiteName,
          SiteID: newSiteInfo.site_id || newSiteInfo.SiteID,
          SiteType: newSiteInfo.site_type || newSiteInfo.SiteType,
          ParentOrg: newSiteInfo.parent_org || newSiteInfo.ParentOrg,
          Location: newSiteInfo.location || newSiteInfo.Location,
          Contact: newSiteInfo.contact || newSiteInfo.Contact
        }
        return {
          success: true,
          message: `站点信息已更新: ${newSiteInfo.site_name || newSiteInfo.SiteName || '未知站点'}`
        }
      } else {
        console.warn('updateSiteInfo - newSiteInfo is not an object or is null:', newSiteInfo)
        return { success: false, message: '站点信息格式错误' }
      }
    },

    // 初始化窗体状态
    async initializeWindowState() {
      try {
        this.windowState = await GetWindowState()
        return { success: true, windowState: this.windowState }
      } catch (error) {
        console.error('初始化窗体状态失败:', error)
        return { success: false, error }
      }
    },

    // 切换窗体大小
    async toggleWindowSize() {
      try {
        // 根据当前状态切换到相应模式
        if (this.windowState.isExpanded) {
          await SetCompactWindow()
        } else {
          await SetExpandedWindow()
        }
        const state = await GetWindowState()
        this.windowState = state
        return { success: true, windowState: state }
      } catch (error) {
        console.error('切换窗体大小失败:', error)
        return { success: false, message: `切换窗体大小失败: ${error}` }
      }
    },

    // 切换窗体位置
    async toggleWindowPosition() {
      try {
        const newPosition = this.windowState.position === 'left' ? 'right' : 'left'
        await SetWindowPosition(newPosition)
        this.windowState.position = newPosition
        return { success: true, position: newPosition }
      } catch (error) {
        console.error('切换窗体位置失败:', error)
        return { success: false, message: `切换窗体位置失败: ${error}` }
      }
    },

    // 最小化窗体
    async minimizeWindow() {
      try {
        await MinimizeWindow()
        return { success: true }
      } catch (error) {
        console.error('最小化窗体失败:', error)
        return { success: false, message: `最小化窗体失败: ${error}` }
      }
    },

    // 设置窗体置顶
    async setAlwaysOnTop(alwaysOnTop = true) {
      try {
        await SetAlwaysOnTop(alwaysOnTop)
        return { success: true }
      } catch (error) {
        console.error('设置窗体置顶失败:', error)
        return { success: false, error }
      }
    },

    // 更新裁剪设置
    async updateCropSettings(cropSettings) {
      try {
        await UpdateCropSettings(cropSettings)
        await this.loadConfig() // 重新加载配置
        return { success: true, message: '裁剪设置更新成功' }
      } catch (error) {
        console.error('更新裁剪设置失败:', error)
        return { success: false, message: `更新裁剪设置失败: ${error}` }
      }
    },

    // 更新通知模式
    async updateNotificationMode() {
      try {
        await window.go.main.App.UpdateNotificationMode(this.useSystemNotification)
        return {
          success: true,
          message: this.useSystemNotification ? '已切换到系统级通知模式' : '已切换到应用内通知模式'
        }
      } catch (error) {
        console.error('更新通知模式失败:', error)
        return { success: false, message: `更新通知模式失败: ${error}` }
      }
    },

    // 检查配置是否有变化
    hasConfigChanged(newConfig) {
      if (!this.config || !newConfig) return true
      
      const oldSiteInfo = this.config.SiteInfo || {}
      const newSiteInfo = newConfig.SiteInfo || {}
      
      return (
        oldSiteInfo.SiteID !== newSiteInfo.SiteID ||
        oldSiteInfo.SiteName !== newSiteInfo.SiteName ||
        oldSiteInfo.SiteType !== newSiteInfo.SiteType ||
        oldSiteInfo.ParentOrg !== newSiteInfo.ParentOrg
      )
    },

    // 启动配置监听器
    startConfigWatcher() {
      // 目前配置监听器已移除定时获取逻辑，仅在初始化时获取配置
      // 保留此方法以备将来需要
      console.log('配置监听器已启动（仅事件监听模式）')
    },

    // 停止配置监听器
    stopConfigWatcher() {
      if (this.configWatcherTimer) {
        clearInterval(this.configWatcherTimer)
        this.configWatcherTimer = null
      }
    }
  }
})