{"level":"INFO","timestamp":"2025-06-30T10:54:19.211+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T10:54:19.211+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T10:54:19.211+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T10:54:19.211+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T10:54:19.211+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:54:19.212+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T10:54:19.212+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:54:19.217+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":29560}
{"level":"INFO","timestamp":"2025-06-30T10:54:19.265+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T10:54:19.265+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":5,"modified":"2025-06-30T10:54:19.217+0800"}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"29560"}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":29560}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":29560}
{"level":"WARN","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:103","msg":"查找进程失败","pid":29560,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":29560}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.719+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":12368}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.772+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T10:55:02.772+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T10:55:04.401+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:55:04.401+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T10:55:04.401+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:55:23.275+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T10:55:23.275+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T10:55:23.275+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T10:55:23.275+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T10:55:23.275+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:55:23.275+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T10:55:23.275+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:55:23.275+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":13388}
{"level":"INFO","timestamp":"2025-06-30T10:55:23.317+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T10:55:23.317+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T10:55:25.304+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:55:25.304+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T10:55:25.304+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:56:53.989+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T10:56:53.989+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T10:56:53.989+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T10:56:53.989+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T10:56:53.989+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:56:53.989+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T10:56:53.989+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:56:53.989+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":19416}
{"level":"INFO","timestamp":"2025-06-30T10:56:54.034+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T10:56:54.035+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T10:56:55.564+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:56:55.564+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T10:56:55.564+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:58:14.201+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T10:58:14.201+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T10:58:14.201+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T10:58:14.201+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T10:58:14.201+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:58:14.202+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T10:58:14.202+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:58:14.202+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":26240}
{"level":"INFO","timestamp":"2025-06-30T10:58:14.253+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T10:58:14.253+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T10:58:16.808+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:58:16.808+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T10:58:16.808+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:58:34.617+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T10:58:34.617+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T10:58:34.617+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T10:58:34.617+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T10:58:34.617+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:58:34.617+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T10:58:34.617+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:58:34.617+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":28968}
{"level":"INFO","timestamp":"2025-06-30T10:58:34.653+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T10:58:34.653+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T10:58:37.163+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:58:37.163+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T10:58:37.163+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.339+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.339+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.339+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.339+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.339+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.339+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.339+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.340+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":29620}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.375+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.376+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.376+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.376+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-06-30T10:59:12.376+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-06-30T10:59:14.517+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:59:14.517+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T10:59:14.517+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.257+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.257+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.257+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.257+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.257+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.257+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.257+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.257+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":27808}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.788+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.788+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.788+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.788+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:01:24.788+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-06-30T11:01:28.348+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:01:28.348+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T11:01:28.348+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.963+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.963+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.963+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.963+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.963+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.963+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.963+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.964+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":22844}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.992+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.993+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.993+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.993+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:02:16.993+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-06-30T11:02:20.180+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:02:20.180+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T11:02:20.181+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.002+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.002+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.002+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.002+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.002+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.002+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.002+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.002+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":9108}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.049+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.049+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.049+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.049+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:03:18.049+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-06-30T11:03:19.739+0800","caller":"utils/logger.go:96","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-06-30T11:03:19.739+0800","caller":"utils/logger.go:96","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-06-30T11:03:19.739+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:03:19.739+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T11:03:19.739+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.097+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.097+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.097+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.097+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.097+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.097+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.097+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.098+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":27716}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.136+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.137+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.137+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.137+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:05:15.137+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-06-30T11:05:26.310+0800","caller":"utils/logger.go:96","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-06-30T11:05:26.310+0800","caller":"utils/logger.go:96","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-06-30T11:05:26.310+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:05:26.310+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T11:05:26.310+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\MagneticOperator.lock"}
