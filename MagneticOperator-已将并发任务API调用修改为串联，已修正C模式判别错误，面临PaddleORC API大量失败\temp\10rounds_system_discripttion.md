## 轮次判断：截图轮次控制的机制
### 1. 主要轮次管理代码位置
核心文件： `app.go`

关键函数：

- `getCurrentRound` (第1692行)
- `markModeCompleted` (第1717行)
### 2. 轮次判断逻辑详解 轮次状态结构体
```
type RoundStatus struct {
    CurrentRound int  // 当前轮次 (1-10)
    B02Completed bool // B02模式是否已完成
    C03Completed bool // C03模式是否已完成
}
``` 轮次确定机制
用户标识生成：

- 使用 用户名_日期 格式作为唯一标识
- 例如： 张三_20250630
- 每天重新开始计算轮次
轮次获取逻辑：

1. 新用户或新日期： 从第1轮开始
2. 已存在用户： 返回当前轮次状态
3. 轮次范围： 1-10轮，每轮包含B02和C03两个模式 轮次推进机制
模式完成标记：

- B02模式完成： roundStatus.B02Completed = true
- C03模式完成： roundStatus.C03Completed = true
轮次推进条件：

- 当前轮次的B02和C03都完成时，进入下一轮
- 轮次推进： roundStatus.CurrentRound++
- 重置状态： B02Completed = false, C03Completed = false
### 3. 截图操作中的轮次使用
截图文件命名中的轮次：

- 位置： `screenshot_service.go` 第475行
- 格式： R%02d (如 R01, R02, R03...)
- 调用： a.getCurrentRound(patientName) 获取当前轮次
轮次状态更新：

- 位置： `app.go` 第688行
- 每次截图完成后调用 markModeCompleted 更新状态
### 4. 轮次管理的关键特点
1. 内存存储： 轮次状态存储在 roundManager map[string]*RoundStatus 中
2. 日期隔离： 每天的轮次独立计算
3. 双模式机制： 每轮需要完成B02和C03两个模式
4. 自动推进： 轮次完成后自动进入下一轮
5. 上限控制： 最多10轮，完成后触发扣子API分析
### 5. 相关服务组件
轮次管理器： `screenshot_round_manager.go`

- 提供更高级的轮次管理功能
- 支持并发截图的轮次协调
集成截图服务： `integrated_screenshot_service.go`

- 协调轮次管理和截图执行
这套轮次判断机制确保了每个用户每天的10轮检测能够有序进行，并在完成后触发相应的后续处理流程。

## 器官名称提取
器官名称校准, 将校准逻辑统一到 `ProcessImageWithDetails` 函数中
- `extractOCRDataOptimized` 和 `extractOCRDataFallback` 现在只负责提取原始数据

## 数据持久化：是否需要在每轮完成后保存到本地文件，防止程序崩溃丢失数据？
暂时不需要

## 错误处理：如果某轮OCR失败，如何处理？是否允许重试？
如果某轮OCR失败，允许重试，每次重试就将识别结果数据记录在当前轮次即可。
需要在wails 前端提示用户当前轮次“检测结果分析不成功，请重试本轮”


## 扣子API接口：具体调用哪个扣子API接口？参数格式是什么？
### 在config/app_config.json中的 第42-50行，即
    "coze": {
      "token": "pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0",
      "workflow_id_post_pic": "7496900622433812531",
      "workflow_id_post_registration": "7501566019660939279",
      "workflow_id_user_info": "7501680491335614516",
      "space_id": "7331689003143544832",
      "app_id": "7496871719090077733"
    },
### 注意事项：这个接口是 development.json 开发模式的设置，在生产模式下不能使用，代码应用能够处理是什么模式的API接口参数。
参数格式先用假的占位代码

### 上传扣子API完成后，要在wails界面前端发起Toast消息通知，告诉用户正在“进行AI大模型检测结果分析，并生成健康评估报告”

## 云函数接口：需要在10轮完成后，将组装好并即将传递完整的 currentUserCheckingInfo 数据，同时调用扣子API接口和云函数API接口。
    "cloud_function": {
      "registrations_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice",
      "screenshot_records_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/screenshot-records/createOrUpdateScreenshotRecord",
      "siteInfoByDeviceMAC_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC",
      "mark_patient_completed_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/markPatientCompleted",
      "user_detect_raw_result_data_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/saveUserDetectRawResultData"
    }
其中，"user_detect_raw_result_data_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/saveUserDetectRawResultData"就是云函数接口。
参数格式先用假的占位代码

## 需要在代码中设置必要的测试打印和在logs目录下相关文件进行日志记录






