/**
 * 全局Toast通知管理器
 * 提供统一的Toast通知接口和管理功能
 */

import { TOAST_TEMPLATES, BUSINESS_TOAST_CONFIG, getToastConfig } from '../config/toastConfig.js'

class ToastManager {
  constructor() {
    this.toastComponent = null
    this.isInitialized = false
    this.pendingToasts = []
    this.eventListeners = new Map()
  }

  /**
   * 初始化Toast管理器
   * @param {Object} toastComponent Toast组件实例
   */
  init(toastComponent) {
    this.toastComponent = toastComponent
    this.isInitialized = true
    
    // 处理待发送的Toast
    this.processPendingToasts()
    
    console.log('[ToastManager] 已初始化')
  }

  /**
   * 检查是否已初始化
   */
  checkInitialized() {
    if (!this.isInitialized || !this.toastComponent) {
      console.warn('[ToastManager] 尚未初始化，Toast将被加入待处理队列')
      return false
    }
    return true
  }

  /**
   * 处理待发送的Toast
   */
  processPendingToasts() {
    if (this.pendingToasts.length > 0) {
      console.log(`[ToastManager] 处理 ${this.pendingToasts.length} 个待发送的Toast`)
      
      this.pendingToasts.forEach(({ method, args }) => {
        this[method](...args)
      })
      
      this.pendingToasts = []
    }
  }

  /**
   * 添加待处理的Toast
   */
  addPendingToast(method, args) {
    this.pendingToasts.push({ method, args })
  }

  /**
   * 显示Toast通知
   * @param {Object|String} config 配置对象或模板名称
   * @param {Object} overrides 覆盖配置
   * @returns {Number|null} Toast ID
   */
  show(config, overrides = {}) {
    if (!this.checkInitialized()) {
      this.addPendingToast('show', [config, overrides])
      return null
    }

    try {
      const toastConfig = getToastConfig(config, overrides)
      return this.toastComponent.showToast(toastConfig)
    } catch (error) {
      console.error('[ToastManager] 显示Toast时发生错误:', error)
      return null
    }
  }

  /**
   * 显示成功通知
   * @param {String} message 消息内容
   * @param {String} title 标题
   * @param {Object} options 额外选项
   * @returns {Number|null} Toast ID
   */
  success(message, title = '操作成功', options = {}) {
    return this.show(TOAST_TEMPLATES.OPERATION_SUCCESS, {
      title,
      message,
      ...options
    })
  }

  /**
   * 显示错误通知
   * @param {String} message 消息内容
   * @param {String} title 标题
   * @param {Object} options 额外选项
   * @returns {Number|null} Toast ID
   */
  error(message, title = '操作失败', options = {}) {
    return this.show(TOAST_TEMPLATES.OPERATION_ERROR, {
      title,
      message,
      ...options
    })
  }

  /**
   * 显示警告通知
   * @param {String} message 消息内容
   * @param {String} title 标题
   * @param {Object} options 额外选项
   * @returns {Number|null} Toast ID
   */
  warning(message, title = '警告', options = {}) {
    return this.show(TOAST_TEMPLATES.OPERATION_ERROR, {
      type: 'warning',
      title,
      message,
      ...options
    })
  }

  /**
   * 显示信息通知
   * @param {String} message 消息内容
   * @param {String} title 标题
   * @param {Object} options 额外选项
   * @returns {Number|null} Toast ID
   */
  info(message, title = '提示', options = {}) {
    return this.show(TOAST_TEMPLATES.OPERATION_SUCCESS, {
      type: 'info',
      title,
      message,
      ...options
    })
  }

  /**
   * 显示进度通知
   * @param {String} message 消息内容
   * @param {String} title 标题
   * @param {Number} initialProgress 初始进度
   * @param {Object} options 额外选项
   * @returns {Number|null} Toast ID
   */
  progress(message, title = '处理中', initialProgress = 0, options = {}) {
    return this.show({
      type: 'info',
      title,
      message,
      duration: 0, // 不自动关闭
      showProgress: true,
      progress: initialProgress,
      ...options
    })
  }

  /**
   * 更新进度通知
   * @param {Number} toastId Toast ID
   * @param {Number} progress 进度值 (0-100)
   * @param {String} message 可选的新消息
   */
  updateProgress(toastId, progress, message) {
    if (!this.checkInitialized()) {
      console.warn('[ToastManager] 无法更新进度，Toast组件未初始化')
      return
    }

    try {
      this.toastComponent.updateToastProgress(toastId, progress, message)
    } catch (error) {
      console.error('[ToastManager] 更新进度时发生错误:', error)
    }
  }

  /**
   * 移除指定Toast
   * @param {Number} toastId Toast ID
   */
  remove(toastId) {
    if (!this.checkInitialized()) {
      console.warn('[ToastManager] 无法移除Toast，组件未初始化')
      return
    }

    try {
      this.toastComponent.removeToast(toastId)
    } catch (error) {
      console.error('[ToastManager] 移除Toast时发生错误:', error)
    }
  }

  /**
   * 清除所有Toast
   */
  clear() {
    if (!this.checkInitialized()) {
      console.warn('[ToastManager] 无法清除Toast，组件未初始化')
      return
    }

    try {
      this.toastComponent.clearAllToasts()
    } catch (error) {
      console.error('[ToastManager] 清除所有Toast时发生错误:', error)
    }
  }

  /**
   * 获取Toast组件状态
   * @returns {Object} 状态信息
   */
  getStatus() {
    if (!this.checkInitialized()) {
      return {
        initialized: false,
        error: 'Toast组件未初始化'
      }
    }

    try {
      return {
        initialized: true,
        ...this.toastComponent.healthCheck()
      }
    } catch (error) {
      console.error('[ToastManager] 获取状态时发生错误:', error)
      return {
        initialized: true,
        error: error.message
      }
    }
  }

  // 业务相关的便捷方法

  /**
   * 显示磁共振扫描开始通知
   * @param {String} patientName 患者姓名
   * @returns {Number|null} Toast ID
   */
  scanStart(patientName = '') {
    return this.show(BUSINESS_TOAST_CONFIG.MRI_OPERATION.SCAN_START, {
      message: patientName ? `${patientName} 的磁共振扫描已开始，请保持静止` : '磁共振扫描已开始，请保持静止'
    })
  }

  /**
   * 显示磁共振扫描完成通知
   * @param {String} patientName 患者姓名
   * @returns {Number|null} Toast ID
   */
  scanComplete(patientName = '') {
    return this.show(BUSINESS_TOAST_CONFIG.MRI_OPERATION.SCAN_COMPLETE, {
      message: patientName ? `${patientName} 的磁共振扫描已完成` : '磁共振扫描已完成'
    })
  }

  /**
   * 显示扫描异常通知
   * @param {String} errorMessage 错误信息
   * @returns {Number|null} Toast ID
   */
  scanError(errorMessage = '扫描过程中发生异常，请联系技术人员') {
    return this.show(BUSINESS_TOAST_CONFIG.MRI_OPERATION.SCAN_ERROR, {
      message: errorMessage
    })
  }

  /**
   * 显示患者移动警告
   * @param {String} patientName 患者姓名
   * @returns {Number|null} Toast ID
   */
  patientMovement(patientName = '') {
    return this.show(BUSINESS_TOAST_CONFIG.MRI_OPERATION.PATIENT_MOVEMENT, {
      message: patientName ? `检测到 ${patientName} 移动，可能影响扫描质量` : '检测到患者移动，可能影响扫描质量'
    })
  }

  /**
   * 显示患者登记成功通知
   * @param {String} patientName 患者姓名
   * @returns {Number|null} Toast ID
   */
  registrationSuccess(patientName = '') {
    return this.show(BUSINESS_TOAST_CONFIG.PATIENT_MANAGEMENT.REGISTRATION_SUCCESS, {
      message: patientName ? `${patientName} 的信息已成功登记` : '患者信息已成功登记'
    })
  }

  /**
   * 显示患者登记失败通知
   * @param {String} errorMessage 错误信息
   * @returns {Number|null} Toast ID
   */
  registrationError(errorMessage = '患者信息登记失败，请重试') {
    return this.show(BUSINESS_TOAST_CONFIG.PATIENT_MANAGEMENT.REGISTRATION_ERROR, {
      message: errorMessage
    })
  }

  /**
   * 显示设备就绪通知
   * @returns {Number|null} Toast ID
   */
  deviceReady() {
    return this.show(BUSINESS_TOAST_CONFIG.DEVICE_STATUS.DEVICE_READY)
  }

  /**
   * 显示设备故障通知
   * @param {String} errorMessage 故障信息
   * @returns {Number|null} Toast ID
   */
  deviceError(errorMessage = '设备检测到故障，请联系维护人员') {
    return this.show(BUSINESS_TOAST_CONFIG.DEVICE_STATUS.DEVICE_ERROR, {
      message: errorMessage
    })
  }

  /**
   * 显示网络错误通知
   * @returns {Number|null} Toast ID
   */
  networkError() {
    return this.show(TOAST_TEMPLATES.NETWORK_ERROR)
  }

  /**
   * 显示连接断开通知
   * @returns {Number|null} Toast ID
   */
  connectionLost() {
    return this.show(TOAST_TEMPLATES.CONNECTION_LOST)
  }

  /**
   * 显示连接恢复通知
   * @returns {Number|null} Toast ID
   */
  connectionRestored() {
    return this.show(TOAST_TEMPLATES.CONNECTION_RESTORED)
  }

  /**
   * 注册事件监听器
   * @param {String} event 事件名称
   * @param {Function} handler 处理函数
   */
  on(event, handler) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(handler)
  }

  /**
   * 移除事件监听器
   * @param {String} event 事件名称
   * @param {Function} handler 处理函数
   */
  off(event, handler) {
    if (this.eventListeners.has(event)) {
      const handlers = this.eventListeners.get(event)
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   * @param {String} event 事件名称
   * @param {*} data 事件数据
   */
  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`[ToastManager] 事件处理器错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.toastComponent = null
    this.isInitialized = false
    this.pendingToasts = []
    this.eventListeners.clear()
    console.log('[ToastManager] 已销毁')
  }
}

// 创建全局实例
const toastManager = new ToastManager()

// 导出实例和类
export { ToastManager }
export default toastManager