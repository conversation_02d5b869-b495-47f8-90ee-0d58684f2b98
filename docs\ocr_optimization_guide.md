# OCR优化指南

## 优化概述

本次优化主要解决了OCR测试中遇到的时序问题，通过实现健康检查机制和增加重试间隔来提高系统的稳定性和可靠性。

## 问题背景

在之前的测试中发现：
- 客户端在15:41开始OCR请求
- OCR API服务器在15:47:55才完成初始化
- 导致75%的OCR请求失败（12个请求中只有3个成功）

## 优化内容

### 1. 健康检查机制

#### 新增功能
- 在发送OCR请求前先检查API服务器状态
- 带重试的健康检查，确保服务器已准备就绪
- 避免在服务器未就绪时发送请求

#### 实现细节
```go
// 健康检查相关常量
HealthCheckTimeout    = 5 * time.Second  // 健康检查超时时间
HealthCheckMaxRetries = 3                // 健康检查最大重试次数
HealthCheckInterval   = 2 * time.Second  // 健康检查重试间隔

// 带重试的OCR API健康检查
func (o *OCRService) checkOCRAPIHealthWithRetry(apiURL string) error {
    for attempt := 0; attempt < HealthCheckMaxRetries; attempt++ {
        if o.checkOCRAPIAvailability(apiURL) {
            return nil // 健康检查通过
        }
        
        if attempt < HealthCheckMaxRetries-1 {
            fmt.Printf("[健康检查] OCR API服务器未就绪 (尝试 %d/%d)，%v 后重试...\n", 
                attempt+1, HealthCheckMaxRetries, HealthCheckInterval)
            time.Sleep(HealthCheckInterval)
        }
    }
    
    return fmt.Errorf("OCR API服务器健康检查失败，经过 %d 次尝试后仍无法连接", HealthCheckMaxRetries)
}
```

#### 集成到处理流程
```go
// 在ProcessImageWithDetails函数中添加健康检查
func (o *OCRService) ProcessImageWithDetails(ctx context.Context, imagePath string) (*OCRResult, error) {
    // ... 获取配置 ...
    
    // 0. 在发送OCR请求前先进行健康检查
    fmt.Printf("[OCR处理] 开始健康检查，确保API服务器就绪...\n")
    if err := o.checkOCRAPIHealthWithRetry(apiURL); err != nil {
        return nil, fmt.Errorf("OCR API健康检查失败: %w", err)
    }
    fmt.Printf("[OCR处理] 健康检查通过，开始处理图片: %s\n", imagePath)
    
    // ... 继续处理 ...
}
```

### 2. 更长的重试间隔

#### OCR API客户端优化
```go
// 优化前的配置
MaxRetries    = 3                // 最大重试次数
BaseDelay     = 1 * time.Second  // 基础延迟时间
MaxDelay      = 30 * time.Second // 最大延迟时间

// 优化后的配置
MaxRetries    = 5                // 最大重试次数（增加到5次）
BaseDelay     = 3 * time.Second  // 基础延迟时间（从1秒增加到3秒）
MaxDelay      = 60 * time.Second // 最大延迟时间（从30秒增加到60秒）
```

#### 串行OCR处理器优化
```go
// 优化前的配置
retryConfig := RetryConfig{
    MaxAttempts:       3,
    InitialDelayMs:    1000,
    MaxDelayMs:        10000,
    BackoffMultiplier: 2.0,
}

// 优化后的配置
retryConfig := RetryConfig{
    MaxAttempts:       5,     // 增加最大重试次数到5次
    InitialDelayMs:    3000,  // 初始延迟从1秒增加到3秒
    MaxDelayMs:        30000, // 最大延迟从10秒增加到30秒
    BackoffMultiplier: 2.0,
}
```

#### 并发OCR处理器优化
```go
// 优化前的配置
maxRetries := 3
baseDelay := 500 * time.Millisecond

// 优化后的配置
maxRetries := 5                    // 增加最大重试次数到5次
baseDelay := 3 * time.Second       // 基础延迟从500ms增加到3秒

// 改进的指数退避算法
if attempt < maxRetries-1 {
    // 计算指数退避延迟，设置最大延迟限制
    delay := baseDelay
    for i := 0; i < attempt; i++ {
        delay = time.Duration(float64(delay) * 2.0) // 使用2.0倍数退避
    }
    if delay > 60*time.Second { // 最大延迟60秒
        delay = 60 * time.Second
    }
    fmt.Printf("[并发OCR] 等待 %v 后重试...\n", delay)
    time.Sleep(delay)
}
```

## 优化效果

### 1. 提高成功率
- 健康检查确保只在服务器就绪时发送请求
- 避免因服务器未启动导致的请求失败
- 减少无效的网络请求

### 2. 增强稳定性
- 更长的重试间隔给服务器更多恢复时间
- 指数退避算法避免对服务器造成压力
- 更多的重试次数提高最终成功率

### 3. 更好的用户体验
- 详细的日志输出，便于问题排查
- 清晰的状态提示，用户了解处理进度
- 优雅的错误处理，避免系统崩溃

## 使用方法

### 1. 测试优化效果

运行测试程序：
```bash
go run test_ocr_optimization.go
```

### 2. 配置参数调整

如需调整重试参数，可以修改配置文件 `config/app_config.json`：
```json
{
  "concurrency": {
    "retry": {
      "max_attempts": 5,
      "initial_delay_ms": 3000,
      "max_delay_ms": 30000,
      "backoff_multiplier": 2.0
    }
  }
}
```

### 3. 监控和调试

查看日志输出，关注以下关键信息：
- `[健康检查]` - 健康检查状态
- `[OCR处理]` - OCR处理进度
- `[串行OCR]` / `[并发OCR]` - 处理器状态
- 重试次数和延迟时间

## 注意事项

1. **网络环境**：确保网络连接稳定，避免因网络问题导致健康检查失败
2. **服务器启动时间**：如果OCR API服务器启动时间较长，可以适当增加健康检查的重试次数
3. **资源消耗**：更长的重试间隔会增加总处理时间，需要在稳定性和效率之间平衡
4. **并发控制**：在高并发场景下，建议使用串行模式以避免对API服务器造成过大压力

## 故障排查

### 常见问题

1. **健康检查失败**
   - 检查OCR API服务器是否正常启动
   - 确认API URL配置是否正确
   - 检查网络连接是否正常

2. **重试次数过多**
   - 检查服务器负载是否过高
   - 确认API认证信息是否正确
   - 考虑增加重试间隔

3. **处理时间过长**
   - 检查网络延迟
   - 考虑减少重试次数或缩短重试间隔
   - 优化图片大小和格式

### 日志分析

关键日志模式：
```
[健康检查] OCR API服务器未就绪 (尝试 1/3)，2s 后重试...
[OCR处理] 健康检查通过，开始处理图片: xxx.jpg
[串行OCR] 任务处理失败 (尝试 1/5): task-001, 错误: xxx
[串行OCR] 等待 3s 后重试...
```

## 总结

本次优化通过实现健康检查机制和增加重试间隔，有效解决了OCR测试中的时序问题。优化后的系统具有更好的稳定性和可靠性，能够在各种网络环境和服务器状态下正常工作。

建议在生产环境中根据实际情况调整相关参数，以达到最佳的性能和稳定性平衡。