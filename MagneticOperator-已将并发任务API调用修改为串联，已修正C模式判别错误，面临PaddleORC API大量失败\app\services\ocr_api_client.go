// Package services 提供了一个OCR API客户端库
package services

import (
	"context"
	"encoding/base64"
	"fmt"
	"math"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	json "github.com/goccy/go-json"

	"MagneticOperator/app/utils"

	"github.com/valyala/fasthttp"
)

// 网络重试配置常量
const (
	MaxRetries    = 5                // 最大重试次数（增加到5次）
	BaseDelay     = 3 * time.Second  // 基础延迟时间（从1秒增加到3秒）
	MaxDelay      = 60 * time.Second // 最大延迟时间（从30秒增加到60秒）
	BackoffFactor = 2.0              // 指数退避因子

	// 健康检查相关常量
	HealthCheckTimeout    = 5 * time.Second // 健康检查超时时间
	HealthCheckMaxRetries = 3               // 健康检查最大重试次数
	HealthCheckInterval   = 2 * time.Second // 健康检查重试间隔
)

// RetryableHTTPClient 带重试机制的HTTP客户端（基于fasthttp）
type RetryableHTTPClient struct {
	client *utils.FastHTTPClient
}

// NewRetryableHTTPClient 创建带重试机制的HTTP客户端
func NewRetryableHTTPClient(timeout time.Duration) *RetryableHTTPClient {
	return &RetryableHTTPClient{
		client: utils.NewFastHTTPClient(timeout),
	}
}

// DoWithRetry 执行HTTP请求并在失败时重试（使用fasthttp）
func (r *RetryableHTTPClient) DoWithRetry(method, url string, body []byte, headers map[string]string) (*fasthttp.Response, error) {
	var lastErr error

	for attempt := 0; attempt <= MaxRetries; attempt++ {
		// 如果不是第一次尝试，等待一段时间
		if attempt > 0 {
			delay := time.Duration(math.Pow(BackoffFactor, float64(attempt-1))) * BaseDelay
			if delay > MaxDelay {
				delay = MaxDelay
			}
			fmt.Printf("[网络重试] 第%d次重试，等待%v...\n", attempt, delay)
			time.Sleep(delay)
		}

		if body != nil {
			fmt.Printf("[网络重试] 第%d次尝试，请求体大小: %d 字节\n", attempt+1, len(body))
		} else {
			fmt.Printf("[网络重试] 第%d次尝试，无请求体\n", attempt+1)
		}

		resp, err := r.client.DoWithRetry(method, url, body, headers)
		if err == nil {
			// 检查HTTP状态码
			if resp.StatusCode() >= 200 && resp.StatusCode() < 300 {
				return resp, nil
			}
			// 如果是客户端错误（4xx），不重试
			if resp.StatusCode() >= 400 && resp.StatusCode() < 500 {
				fasthttp.ReleaseResponse(resp)
				return nil, fmt.Errorf("客户端错误，状态码: %d", resp.StatusCode())
			}
			// 服务器错误（5xx）可以重试
			fasthttp.ReleaseResponse(resp)
			lastErr = fmt.Errorf("服务器错误，状态码: %d", resp.StatusCode())
		} else {
			lastErr = err
		}

		fmt.Printf("[网络重试] 第%d次尝试失败: %v\n", attempt+1, lastErr)
	}

	return nil, fmt.Errorf("网络请求失败，已重试%d次: %w", MaxRetries, lastErr)
}

// OCRInterface 定义OCR服务的接口
// OCRResult 定义OCR处理结果的结构
type OCRResult struct {
	// FullText      string            `json:"full_text"`
	OrganName     string            `json:"organ_name"`
	Confidence    float64           `json:"confidence"`
	ImagePath     string            `json:"image_path"`
	KeyValuePairs map[string]string `json:"key_value_pairs,omitempty"` // 从rec_texts提取的键值对数据
	RawResponse   json.RawMessage   `json:"raw_response,omitempty"`    // 原始OCR响应数据
}

type OCRInterface interface {
	// ValidateOCREnvironment 验证OCR环境
	ValidateOCREnvironment() error
	// ProcessImageWithDetails 处理图片并返回详细结果（统一入口）
	ProcessImageWithDetails(ctx context.Context, imagePath string) (*OCRResult, error)
	// Close 关闭OCR服务
	Close()
}

// OCRService 实现OCRInterface接口
type OCRService struct {
	configService ConfigServiceInterface
	organDB       *OrganDatabase
	app           AppInterface // 添加App接口引用以支持Wails通知
	// 器官名称校准缓存
	organNameCache map[string]string
	cacheMutex     sync.RWMutex
}

// NewOCRService 创建新的OCR服务实例
func NewOCRService(configService ConfigServiceInterface, app AppInterface) OCRInterface {
	service := &OCRService{
		configService:  configService,
		app:            app,
		organNameCache: make(map[string]string),
	}

	// 加载器官名称库
	if err := service.loadOrganDatabase(); err != nil {
		// 如果加载失败，记录错误但不阻止服务创建
		fmt.Printf("警告：无法加载器官名称库: %v\n", err)
	}

	return service
}

// 配置常量已移除，现在从配置文件读取

// APIResponse 定义了预期的API响应结构
// 为了通用性，我们这里先用 map[string]interface{} 来接收任意JSON结构
type APIResponse map[string]interface{}

// OCRExtractionResult 合并提取结果
type OCRExtractionResult struct {
	Confidence    float64           `json:"confidence"`
	KeyValuePairs map[string]string `json:"key_value_pairs"`
	OrganName     string            `json:"organ_name"`
	Error         error             `json:"-"`
}

// OptimizedOCRResponse 高性能OCR响应结构体
type OptimizedOCRResponse struct {
	Result struct {
		TableRecResults []struct {
			PrunedResult struct {
				OverallOcrRes struct {
					RecTexts []string `json:"rec_texts"`
				} `json:"overall_ocr_res"`
			} `json:"prunedResult"`
		} `json:"tableRecResults"`
	} `json:"result"`
	Data struct {
		OcrResult [][]interface{} `json:"ocr_result"`
		Result    []struct {
			PrunedResult struct {
				RecTexts []string `json:"rec_texts"`
			} `json:"prunedResult"`
		} `json:"result"`
	} `json:"data"`
	PrunedResult struct {
		RecTexts []string `json:"rec_texts"`
	} `json:"prunedResult"`
	RecTexts []string `json:"rec_texts"`
	Results  []struct {
		TextData []struct {
			Confidence float64  `json:"confidence"`
			RecTexts   []string `json:"rec_texts"`
		} `json:"text_data"`
	} `json:"results"`
}

// OrganPart 表示器官/人体部位的标准名称
type OrganPart struct {
	ID     string `json:"id"`
	Name   string `json:"名称"`
	Gender string `json:"性别"`
}

// OrganDatabase 器官名称数据库
type OrganDatabase struct {
	Organs []OrganPart
}

// TableRequestPayload 定义了发送给表格识别API的请求负载结构
// 根据AI Studio API文档添加完整的参数配置
type TableRequestPayload struct {
	File      string `json:"file"`       // Base64编码的图像数据
	FileType  int    `json:"fileType"`   // 文件类型：0=PDF, 1=图像
	TableType string `json:"table_type"` // 表格类型

	// 模型功能相关参数 - 启用所有识别功能以获得最佳效果
	UseDocOrientationClassify bool `json:"useDocOrientationClassify"` // 使用文档方向分类模块
	UseDocUnwarping           bool `json:"useDocUnwarping"`           // 使用文档扭曲矫正模块
	UseTextlineOrientation    bool `json:"useTextlineOrientation"`    // 使用文本行方向分类模块
	UseGeneralOcr             bool `json:"useGeneralOcr"`             // 使用OCR子产线
	UseSealRecognition        bool `json:"useSealRecognition"`        // 使用印章识别子产线
	UseTableRecognition       bool `json:"useTableRecognition"`       // 使用表格识别子产线
	UseFormulaRecognition     bool `json:"useFormulaRecognition"`     // 使用公式识别子产线

	// 其他优化参数
	LayoutThreshold float64 `json:"layoutThreshold"` // 版面模型得分阈值
	LayoutNms       bool    `json:"layoutNms"`       // 版面区域检测模型使用NMS后处理
}

// extractKeyValuePairsFromRecTexts 从OCR API响应的rec_texts字段中提取键值对数据
// 只提取以"0."开头且小数点后有三位数字的数值文本，以及其后的文字内容
// 例如：{"0.000":"厦部第1腰椎水平截面", "0.054":"C反应蛋白"}
// extractOCRDataOptimized 高性能合并提取函数
// 一次解析JSON，同时提取置信度、键值对和器官名称
func (o *OCRService) extractOCRDataOptimized(responseBody []byte) *OCRExtractionResult {
	result := &OCRExtractionResult{
		KeyValuePairs: make(map[string]string),
		Confidence:    0.0,
		OrganName:     "未知器官",
	}

	// 首先尝试使用高性能结构体解析
	var optimizedResp OptimizedOCRResponse
	if err := json.Unmarshal(responseBody, &optimizedResp); err != nil {
		// 如果结构体解析失败，回退到通用解析
		fmt.Printf("[信息] 结构体解析失败，回退到通用解析: %v\n", err)
		return o.extractOCRDataFallback(responseBody)
	}

	// 提取rec_texts数据
	var recTexts []string
	var confidenceData [][]interface{}
	found := false

	// 检查各种可能的路径并同时收集置信度数据
	if len(optimizedResp.Result.TableRecResults) > 0 {
		for _, tableResult := range optimizedResp.Result.TableRecResults {
			if len(tableResult.PrunedResult.OverallOcrRes.RecTexts) > 0 {
				recTexts = tableResult.PrunedResult.OverallOcrRes.RecTexts
				found = true
				break
			}
		}
	}

	if !found && len(optimizedResp.Data.Result) > 0 {
		for _, item := range optimizedResp.Data.Result {
			if len(item.PrunedResult.RecTexts) > 0 {
				recTexts = item.PrunedResult.RecTexts
				found = true
				break
			}
		}
	}

	if !found && len(optimizedResp.PrunedResult.RecTexts) > 0 {
		recTexts = optimizedResp.PrunedResult.RecTexts
		found = true
	}

	if !found && len(optimizedResp.RecTexts) > 0 {
		recTexts = optimizedResp.RecTexts
		found = true
	}

	// 提取置信度数据
	if len(optimizedResp.Data.OcrResult) > 0 {
		confidenceData = optimizedResp.Data.OcrResult
	} else if len(optimizedResp.Results) > 0 {
		// 从旧格式提取置信度
		var totalConfidence float64
		var count int
		for _, item := range optimizedResp.Results {
			for _, textItem := range item.TextData {
				totalConfidence += textItem.Confidence
				count++
			}
		}
		if count > 0 {
			result.Confidence = totalConfidence / float64(count)
		}
	}

	// 处理data.ocr_result格式的置信度
	if len(confidenceData) > 0 {
		var totalConfidence float64
		var count int
		for _, item := range confidenceData {
			if len(item) > 2 {
				if confidence, ok := item[2].(float64); ok {
					totalConfidence += confidence
					count++
				}
			}
		}
		if count > 0 {
			result.Confidence = totalConfidence / float64(count)
		}
	}

	if !found {
		fmt.Printf("[警告] 未找到rec_texts字段\n")
		return result
	}

	fmt.Printf("[信息] 找到rec_texts字段，共%d个元素\n", len(recTexts))

	// 在提取键值对之前清理干扰数据
	cleanedRecTexts := cleanRecTexts(recTexts)
	fmt.Printf("[信息] 清理后rec_texts字段，共%d个元素\n", len(cleanedRecTexts))

	// 一次遍历同时提取键值对
	result.KeyValuePairs = extractKeyValuePairsOptimized(cleanedRecTexts)

	// 提取器官名称（原始数据，不进行校准）
	if rawOrganName, exists := result.KeyValuePairs["0.000"]; exists {
		fmt.Printf("[器官名称提取] 通过0.000标志找到器官名称: %s\n", rawOrganName)
		result.OrganName = rawOrganName
	}

	return result
}

// extractOCRDataFallback 回退到通用解析方法
func (o *OCRService) extractOCRDataFallback(responseBody []byte) *OCRExtractionResult {
	result := &OCRExtractionResult{
		KeyValuePairs: make(map[string]string),
		Confidence:    0.0,
		OrganName:     "未知器官",
	}

	var apiResp APIResponse
	if err := json.Unmarshal(responseBody, &apiResp); err != nil {
		fmt.Printf("[警告] 解析OCR响应JSON失败: %v\n", err)
		result.Error = err
		return result
	}

	// 查找rec_texts字段
	var recTexts []interface{}
	found := false

	// 检查各种可能的路径
	if result_data, ok := apiResp["result"].(map[string]interface{}); ok {
		if tableRecResults, ok := result_data["tableRecResults"].([]interface{}); ok {
			for _, tableResult := range tableRecResults {
				if tableMap, ok := tableResult.(map[string]interface{}); ok {
					if prunedResult, ok := tableMap["prunedResult"].(map[string]interface{}); ok {
						if overallOcrRes, ok := prunedResult["overall_ocr_res"].(map[string]interface{}); ok {
							if texts, ok := overallOcrRes["rec_texts"].([]interface{}); ok {
								recTexts = texts
								found = true
								break
							}
						}
					}
				}
				if found {
					break
				}
			}
		}
	}

	if !found {
		if data, ok := apiResp["data"].(map[string]interface{}); ok {
			if result_array, ok := data["result"].([]interface{}); ok {
				for _, item := range result_array {
					if itemMap, ok := item.(map[string]interface{}); ok {
						if prunedResult, ok := itemMap["prunedResult"].(map[string]interface{}); ok {
							if texts, ok := prunedResult["rec_texts"].([]interface{}); ok {
								recTexts = texts
								found = true
								break
							}
						}
					}
				}
			}
		}
	}

	if !found {
		if prunedResult, ok := apiResp["prunedResult"].(map[string]interface{}); ok {
			if texts, ok := prunedResult["rec_texts"].([]interface{}); ok {
				recTexts = texts
				found = true
			}
		}
	}

	if !found {
		if texts, ok := apiResp["rec_texts"].([]interface{}); ok {
			recTexts = texts
			found = true
		}
	}

	if !found {
		fmt.Printf("[警告] 未找到rec_texts字段\n")
		return result
	}

	// 提取置信度
	if data, ok := apiResp["data"].(map[string]interface{}); ok {
		if ocrResult, ok := data["ocr_result"].([]interface{}); ok {
			var totalConfidence float64
			var count int
			for _, item := range ocrResult {
				if line, ok := item.([]interface{}); ok && len(line) > 2 {
					if confidence, ok := line[2].(float64); ok {
						totalConfidence += confidence
						count++
					}
				}
			}
			if count > 0 {
				result.Confidence = totalConfidence / float64(count)
			}
		}
	}

	fmt.Printf("[信息] 找到rec_texts字段，共%d个元素\n", len(recTexts))

	// 提取键值对
	// 将 []interface{} 转换为 []string
	recTextsStr := make([]string, len(recTexts))
	for i, v := range recTexts {
		if str, ok := v.(string); ok {
			recTextsStr[i] = str
		}
	}

	// 在提取键值对之前清理干扰数据
	cleanedRecTexts := cleanRecTexts(recTextsStr)
	fmt.Printf("[信息] 清理后rec_texts字段，共%d个元素\n", len(cleanedRecTexts))

	result.KeyValuePairs = extractKeyValuePairsOptimized(cleanedRecTexts)

	// 提取器官名称（原始数据，不进行校准）
	if rawOrganName, exists := result.KeyValuePairs["0.000"]; exists {
		fmt.Printf("[器官名称提取] 通过0.000标志找到器官名称: %s\n", rawOrganName)
		result.OrganName = rawOrganName
	}

	return result
}

// calibrateOrganName 统一的器官名称校准函数（带缓存）
// 对提取的器官名称进行标准化校验，返回校准后的器官名称
func (o *OCRService) calibrateOrganName(rawOrganName string) string {
	if rawOrganName == "" {
		return "未知器官"
	}

	// 先检查缓存
	o.cacheMutex.RLock()
	if cachedResult, exists := o.organNameCache[rawOrganName]; exists {
		o.cacheMutex.RUnlock()
		return cachedResult
	}
	o.cacheMutex.RUnlock()

	// 缓存中没有，进行校准计算
	bestMatch, similarity := o.findBestMatchOrgan(rawOrganName)

	var result string
	if similarity > 0.6 {
		// 如果匹配度高于阈值，使用标准名称
		fmt.Printf("器官名称校验: OCR识别='%s' -> 标准名称='%s' (相似度: %.2f)\n", rawOrganName, bestMatch, similarity)
		result = bestMatch
	} else {
		result = rawOrganName
	}

	// 将结果存入缓存
	o.cacheMutex.Lock()
	o.organNameCache[rawOrganName] = result
	o.cacheMutex.Unlock()

	return result
}

// ClearOrganNameCache 清理器官名称校准缓存
func (o *OCRService) ClearOrganNameCache() {
	o.cacheMutex.Lock()
	defer o.cacheMutex.Unlock()
	o.organNameCache = make(map[string]string)
	fmt.Printf("器官名称校准缓存已清理\n")
}

// GetCacheSize 获取缓存大小
func (o *OCRService) GetCacheSize() int {
	o.cacheMutex.RLock()
	defer o.cacheMutex.RUnlock()
	return len(o.organNameCache)
}

// extractKeyValuePairsOptimized 优化的键值对提取函数（处理string数组）
// 解析rec_texts数组，提取符合特定条件的键值对
// 只提取以"0."开头且小数点后有三位数字的数值文本（如"0.000"），以及其后的文字内容
func extractKeyValuePairsOptimized(recTexts []string) map[string]string {
	extractedData := make(map[string]string)
	// 扩展正则表达式以匹配更多数字格式：0.xxx, 1.xxx, 2.xxx等，以及整数8等
	pattern := regexp.MustCompile(`^(\d+\.\d+|\d+)$`)

	// 一次遍历提取所有键值对
	for i := 0; i < len(recTexts)-1; i++ {
		text := recTexts[i]
		if pattern.MatchString(text) {
			nextText := recTexts[i+1]
			// 确保下一个文本不是数字格式，避免误匹配
			if !isNumericFormat(nextText) {
				// 过滤特定内容
				if shouldFilterText(nextText) {
					continue
				}

				// 检查是否为"优化配置"，如果是则同时过滤前面的键值对
				if nextText == "优化配置" && i > 0 {
					prevText := recTexts[i-1]
					if pattern.MatchString(prevText) {
						// 从已提取的数据中删除前面的键值对
						delete(extractedData, prevText)
					}
					continue
				}

				// 添加调试日志
				fmt.Printf("[KeyValuePairs提取] 发现数字键值对: %s = %s\n", text, nextText)
				extractedData[text] = nextText
			}
		}
	}

	fmt.Printf("[KeyValuePairs提取] 总共提取到%d个键值对\n", len(extractedData))
	return extractedData
}

// isAllDigits 检查字符串是否全部由数字组成
func isAllDigits(s string) bool {
	for _, r := range s {
		if r < '0' || r > '9' {
			return false
		}
	}
	return len(s) > 0
}

// isNumericFormat 检查字符串是否为数字格式（包括小数）
func isNumericFormat(s string) bool {
	_, err := strconv.ParseFloat(s, 64)
	return err == nil
}

// cleanRecTexts 在OCR数据提取的最早阶段清理干扰数据
// 移除特定的干扰文本和对应的数字键，但保护"0.000"作为器官名称标志
func cleanRecTexts(recTexts []string) []string {
	var cleaned []string
	skipNext := false

	for _, text := range recTexts {
		// 如果上一轮标记了跳过下一个元素，则跳过当前元素
		if skipNext {
			skipNext = false
			fmt.Printf("[数据清理] 跳过干扰数据的值: %s\n", text)
			continue
		}

		// 特殊保护：绝对不能过滤"0.000"，它是器官名称的重要标志
		if text == "0.000" {
			fmt.Printf("[数据清理] 保护器官名称标志: %s\n", text)
			cleaned = append(cleaned, text)
			continue
		}

		// 检查当前文本是否为"优化配置"，如果是，需要同时移除前面的数字
		if text == "优化配置" || strings.Contains(text, "优化配置") {
			fmt.Printf("[数据清理] 发现'优化配置'干扰文本: %s\n", text)
			// 移除前面的数字键（如果存在且不是"0.000"）
			if len(cleaned) > 0 {
				lastItem := cleaned[len(cleaned)-1]
				if isNumericKey(lastItem) && lastItem != "0.000" {
					cleaned = cleaned[:len(cleaned)-1]
					fmt.Printf("[数据清理] 移除'优化配置'前的干扰数字: %s\n", lastItem)
				}
			}
			continue
		}

		// 检查当前文本是否为其他需要过滤的干扰数据
		if shouldFilterTextStrict(text) {
			fmt.Printf("[数据清理] 过滤干扰文本: %s\n", text)
			continue
		}

		// 保留正常数据
		cleaned = append(cleaned, text)
	}

	return cleaned
}

// isNumericKey 检查文本是否为数字键（如"3.896", "0.000"等）
func isNumericKey(text string) bool {
	pattern := regexp.MustCompile(`^\d+\.\d+$`)
	return pattern.MatchString(text)
}

// shouldFilterTextStrict 严格检查文本是否应该被过滤
// 专门用于cleanRecTexts函数的严格过滤，不包括"优化配置"（单独处理）
func shouldFilterTextStrict(text string) bool {
	// 绝对保护"0.000"，它是器官名称的重要标志
	if text == "0.000" {
		return false
	}

	// 定义需要严格过滤的文本（不包括"优化配置"，因为它需要特殊处理）
	strictFilterTexts := []string{
		"按照标准图谱相似度递减列表：",
		"按照标准图谱相似度递减列表",
		"虚拟模式-健康问题发展趋势列表：",
		"虚拟模式-健康问题发展趋势列表",
	}

	// 精确匹配
	for _, filterText := range strictFilterTexts {
		if text == filterText {
			return true
		}
	}

	// 包含匹配
	for _, filterText := range strictFilterTexts {
		if strings.Contains(text, filterText) {
			return true
		}
	}

	return false
}

// shouldFilterText 检查文本是否应该被过滤
// 过滤以下内容：
// 1. "按照标准图谱相似度递减列表："或与之相似度高的文字
// 2. "优化配置"（在主循环中单独处理）
// 3. "虚拟模式-健康问题发展趋势列表："或与之相似度高的文字
func shouldFilterText(text string) bool {
	// 定义需要过滤的关键词和模式
	filterPatterns := []string{
		"按照标准图谱相似度递减列表",
		"标准图谱相似度递减列表",
		"相似度递减列表",
		"虚拟模式-健康问题发展趋势列表",
		"健康问题发展趋势列表",
		"发展趋势列表",
		"优化配置",
	}

	// 精确匹配
	for _, pattern := range filterPatterns {
		if strings.Contains(text, pattern) {
			return true
		}
	}

	// 相似度匹配（简单的字符串相似度检查）
	if calculateStringSimilarity(text, "按照标准图谱相似度递减列表：") > 0.7 {
		return true
	}

	if calculateStringSimilarity(text, "虚拟模式-健康问题发展趋势列表：") > 0.7 {
		return true
	}

	return false
}

// calculateStringSimilarity 计算两个字符串的相似度（简单的Jaccard相似度）
func calculateStringSimilarity(s1, s2 string) float64 {
	if len(s1) == 0 && len(s2) == 0 {
		return 1.0
	}
	if len(s1) == 0 || len(s2) == 0 {
		return 0.0
	}

	// 转换为字符集合
	set1 := make(map[rune]bool)
	set2 := make(map[rune]bool)

	for _, r := range s1 {
		set1[r] = true
	}

	for _, r := range s2 {
		set2[r] = true
	}

	// 计算交集
	intersection := 0
	for r := range set1 {
		if set2[r] {
			intersection++
		}
	}

	// 计算并集
	union := len(set1) + len(set2) - intersection

	if union == 0 {
		return 0.0
	}

	return float64(intersection) / float64(union)
}

// isSpecificNumericFormat 检查字符串是否为特定的数字格式：以"0."开头且小数点后有三位数字
// 例如："0.000", "0.123", "0.456" 等
func isSpecificNumericFormat(s string) bool {
	// 检查是否以"0."开头
	if !strings.HasPrefix(s, "0.") {
		return false
	}

	// 检查小数点后是否恰好有三位数字
	parts := strings.Split(s, ".")
	if len(parts) != 2 {
		return false
	}

	// 检查小数部分是否恰好是三位数字
	decimalPart := parts[1]
	if len(decimalPart) != 3 {
		return false
	}

	// 检查小数部分是否全部是数字
	for _, r := range decimalPart {
		if r < '0' || r > '9' {
			return false
		}
	}

	return true
}

// ValidateOCREnvironment 验证OCR环境
func (o *OCRService) ValidateOCREnvironment() error {
	// OCR环境检测已在应用启动时完成，这里直接返回成功
	// 如果启动时检测失败，用户已经收到相应的通知
	return nil
}

// checkNetworkConnectivity 检查网络连接
func (o *OCRService) checkNetworkConnectivity() bool {
	// 尝试连接到公共DNS服务器来检查网络连接
	client := utils.NewFastHTTPClient(5 * time.Second)

	// 尝试多个公共服务来确保网络连接
	testURLs := []string{
		"https://www.baidu.com",
	}

	for _, url := range testURLs {
		resp, err := client.Get(url, nil)
		if err == nil {
			fasthttp.ReleaseResponse(resp)
			return true
		}
	}

	return false
}

// checkOCRAPIAvailability 检查OCR API可用性
func (o *OCRService) checkOCRAPIAvailability(apiURL string) bool {
	client := utils.NewFastHTTPClient(10 * time.Second)

	// 发送一个简单的HEAD请求来检查API是否可达
	resp, err := client.Head(apiURL, nil)
	if err != nil {
		return false
	}
	defer fasthttp.ReleaseResponse(resp)

	// 检查状态码：
	// - 200-299: 正常响应
	// - 403: 需要认证，但服务器可达（对于需要认证的API这是正常的）
	// - 405: 方法不允许，但服务器可达
	statusCode := resp.StatusCode()
	return (statusCode >= 200 && statusCode < 300) || statusCode == 403 || statusCode == 405
}

// checkOCRAPIHealthWithRetry 带重试的OCR API健康检查
// 在发送OCR请求前先检查API服务器状态，确保服务器已准备就绪
func (o *OCRService) checkOCRAPIHealthWithRetry(apiURL string) error {
	for attempt := 0; attempt < HealthCheckMaxRetries; attempt++ {
		if o.checkOCRAPIAvailability(apiURL) {
			return nil // 健康检查通过
		}

		if attempt < HealthCheckMaxRetries-1 {
			fmt.Printf("[健康检查] OCR API服务器未就绪 (尝试 %d/%d)，%v 后重试...\n",
				attempt+1, HealthCheckMaxRetries, HealthCheckInterval)
			time.Sleep(HealthCheckInterval)
		}
	}

	return fmt.Errorf("OCR API服务器健康检查失败，经过 %d 次尝试后仍无法连接", HealthCheckMaxRetries)
}

// showWindowsNotification 显示现代化Wails通知
// 通知会显示在应用内部，具有更好的视觉效果和用户体验
func (o *OCRService) showWindowsNotification(title, message string) {
	go func() {
		// 使用Wails事件系统发送通知到前端
		if o.app != nil {
			// 调用App的ShowSuccessNotification方法
			o.app.ShowSuccessNotification(title, message, 5000)
		} else {
			// 如果app实例不可用，回退到控制台输出
			fmt.Printf("[通知] %s: %s\n", title, message)
		}
	}()
}

// ProcessImageWithDetails 是调用OCR API的核心函数。
// 它负责读取图片、进行Base64编码、构建请求、发送请求（包含重试机制），
// 并解析响应以提取器官名称、所有文本和置信度。
func (o *OCRService) ProcessImageWithDetails(ctx context.Context, imagePath string) (*OCRResult, error) {
	config := o.configService.GetConfig()
	apiURL := config.APIKeys.OCR.APIURL
	if apiURL == "" {
		return nil, fmt.Errorf("OCR API URL未在配置中设置")
	}

	// 0. 在发送OCR请求前先进行健康检查
	fmt.Printf("[OCR处理] 开始健康检查，确保API服务器就绪...\n")
	if err := o.checkOCRAPIHealthWithRetry(apiURL); err != nil {
		return nil, fmt.Errorf("OCR API健康检查失败: %w", err)
	}
	fmt.Printf("[OCR处理] 健康检查通过，开始处理图片: %s\n", imagePath)

	// 1. 读取图片文件
	imageBytes, err := os.ReadFile(imagePath)
	if err != nil {
		return nil, fmt.Errorf("读取图片文件失败: %w", err)
	}

	// 2. Base64编码图片数据
	base64Image := base64.StdEncoding.EncodeToString(imageBytes)

	// 3. 创建JSON请求体，根据AI Studio API文档添加完整的参数配置
	requestPayload := map[string]interface{}{
		"file":     base64Image,
		"fileType": 1, // 图像类型：0=PDF文件，1=图像文件

		// 模型功能相关参数 - 启用所有识别功能以获得最佳效果
		"useDocOrientationClassify": false, // 使用文档方向分类模块
		"useDocUnwarping":           false, // 使用文档扭曲矫正模块
		"useTextlineOrientation":    false, // 使用文本行方向分类模块
		"useGeneralOcr":             true,  // 使用OCR子产线
		"useSealRecognition":        false, // 使用印章识别子产线
		"useTableRecognition":       true,  // 使用表格识别子产线（重要：用于医疗报告表格数据提取）
		"useFormulaRecognition":     false, // 使用公式识别子产线

		// 其他优化参数
		"layoutThreshold": 0.5,   // 版面模型得分阈值
		"layoutNms":       false, // 版面区域检测模型使用NMS后处理
	}

	jsonData, err := json.Marshal(requestPayload)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 4. 准备请求头
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	// 添加认证Token
	if config.APIKeys.OCR.Token != "" {
		headers["Authorization"] = "token " + config.APIKeys.OCR.Token
	}

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	// 5. 使用带重试的客户端发送请求
	client := NewRetryableHTTPClient(60 * time.Second) // 增加超时时间以应对可能的慢速网络和处理
	resp, err := client.DoWithRetry("POST", apiURL, jsonData, headers)
	if err != nil {
		return nil, fmt.Errorf("请求OCR API失败: %w", err)
	}
	defer fasthttp.ReleaseResponse(resp)

	// 6. 读取响应体
	responseBody := resp.Body()

	// 7. 检查响应状态码
	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("[警告] OCR API返回错误状态码 %d: %s", resp.StatusCode(), string(responseBody))
	} else {
		// 解析JSON响应
		var apiResp map[string]interface{}
		if err = json.Unmarshal(responseBody, &apiResp); err != nil {
			return nil, fmt.Errorf("解析JSON响应失败: %w", err)
		} else {
			// fmt.Printf("[OCR_API响应返回值] 从OCR响应返回JSON值responseBody: %v\n", string(responseBody))
			fmt.Printf("[OCR_API响应返回值] 已经从OCR响应返回JSON值responseBody，太长注释了暂不显示。")

		}
	}

	// 调试：打印响应内容（仅在测试时启用）
	// fmt.Printf("[调试] OCR API响应: %s\n", string(responseBody))

	// 8. 解析响应并提取信息（使用高性能合并函数）
	ocrResult := o.extractOCRDataOptimized(responseBody)
	if ocrResult.Error != nil {
		return nil, fmt.Errorf("[警告] OCR API返回 提取数据失败: %v", ocrResult.Error)
	}

	// 获取提取的数据
	confidence := ocrResult.Confidence
	extractedData := ocrResult.KeyValuePairs
	var organName string

	if len(extractedData) > 0 {
		fmt.Printf("[OCR_API响应返回值] 从 rec_texts 提取的键值对数据: %v\n", extractedData)
	} else {
		fmt.Printf("[警告] 未从rec_texts字段中提取到有效的键值对数据\n")
	}

	// 从提取的键值对中获取原始器官名称并进行统一校准
	if rawOrganName, exists := extractedData["0.000"]; exists {
		// 使用统一的校准函数进行器官名称校准
		organName = o.calibrateOrganName(rawOrganName)
		// 更新extractedData中的器官名称为校准后的名称
		extractedData["0.000"] = organName
		fmt.Printf("[解析响应并提取信息] 原始器官名称: %s -> 校准后器官名称: %s\n", rawOrganName, organName)
	} else {
		fmt.Printf("[警告] 未找到器官名称标志(0.000)\n")
		organName = "未知器官"
	}

	// 9. 创建并返回结果
	result := &OCRResult{
		// FullText:      fullText,
		OrganName:     organName,
		Confidence:    confidence,
		ImagePath:     imagePath,
		KeyValuePairs: extractedData,
		RawResponse:   responseBody,
	}

	return result, nil
}

// containsChinese 检查字符串是否包含中文字符
func containsChinese(s string) bool {
	for _, r := range s {
		if r >= 0x4e00 && r <= 0x9fff {
			return true
		}
	}
	return false
}

// loadOrganDatabase 加载标准器官部位名称库
func (o *OCRService) loadOrganDatabase() error {
	// 获取配置文件路径
	configPath := filepath.Join("config", "detection_Indicator_organ_part.json")

	// 读取JSON文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("无法读取器官名称库文件: %w", err)
	}

	// 解析JSON数据
	var organs []OrganPart
	if err := json.Unmarshal(data, &organs); err != nil {
		return fmt.Errorf("无法解析器官名称库JSON: %w", err)
	}

	o.organDB = &OrganDatabase{Organs: organs}
	return nil
}

// calculateSimilarity 计算两个字符串的相似度（使用编辑距离算法）
func calculateSimilarity(s1, s2 string) float64 {
	if s1 == s2 {
		return 1.0
	}

	len1, len2 := utf8.RuneCountInString(s1), utf8.RuneCountInString(s2)
	if len1 == 0 || len2 == 0 {
		return 0.0
	}

	// 转换为rune切片以正确处理中文字符
	runes1 := []rune(s1)
	runes2 := []rune(s2)

	// 创建编辑距离矩阵
	dp := make([][]int, len1+1)
	for i := range dp {
		dp[i] = make([]int, len2+1)
	}

	// 初始化矩阵
	for i := 0; i <= len1; i++ {
		dp[i][0] = i
	}
	for j := 0; j <= len2; j++ {
		dp[0][j] = j
	}

	// 计算编辑距离
	for i := 1; i <= len1; i++ {
		for j := 1; j <= len2; j++ {
			if runes1[i-1] == runes2[j-1] {
				dp[i][j] = dp[i-1][j-1]
			} else {
				dp[i][j] = min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1]) + 1
			}
		}
	}

	// 计算相似度（1 - 编辑距离/最大长度）
	maxLen := max(len1, len2)
	similarity := 1.0 - float64(dp[len1][len2])/float64(maxLen)
	return similarity
}

// min 返回三个整数中的最小值
func min(a, b, c int) int {
	if a <= b && a <= c {
		return a
	}
	if b <= c {
		return b
	}
	return c
}

// max 返回两个整数中的最大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// findBestMatchOrgan 在器官名称库中查找最佳匹配的器官名称
func (o *OCRService) findBestMatchOrgan(ocrOrganName string) (string, float64) {
	if o.organDB == nil || len(o.organDB.Organs) == 0 {
		return ocrOrganName, 0.0
	}

	// 清理输入文本
	cleanOCRName := strings.TrimSpace(ocrOrganName)
	if cleanOCRName == "" {
		return ocrOrganName, 0.0
	}

	bestMatch := ocrOrganName
	bestSimilarity := 0.0
	minSimilarityThreshold := 0.6 // 最小相似度阈值

	// 遍历所有器官名称，找到最佳匹配
	for _, organ := range o.organDB.Organs {
		// 计算完整匹配相似度
		similarity := calculateSimilarity(cleanOCRName, organ.Name)

		// 检查完全包含匹配（OCR结果是否包含在标准名称中）
		if strings.Contains(organ.Name, cleanOCRName) && len(cleanOCRName) >= 2 {
			containSimilarity := float64(len(cleanOCRName)) / float64(len(organ.Name))
			// 对于包含匹配，给予更高的权重
			containSimilarity = containSimilarity*0.8 + 0.2
			if containSimilarity > similarity {
				similarity = containSimilarity
			}
		}

		// 检查反向包含匹配（标准名称是否包含在OCR结果中）
		if strings.Contains(cleanOCRName, organ.Name) && len(organ.Name) >= 2 {
			containSimilarity := float64(len(organ.Name)) / float64(len(cleanOCRName))
			// 对于反向包含匹配，给予适当的权重
			containSimilarity = containSimilarity*0.7 + 0.3
			if containSimilarity > similarity {
				similarity = containSimilarity
			}
		}

		// 检查关键词匹配（对于常见器官名称）
		keywordSimilarity := calculateKeywordSimilarity(cleanOCRName, organ.Name)
		if keywordSimilarity > similarity {
			similarity = keywordSimilarity
		}

		if similarity > bestSimilarity && similarity >= minSimilarityThreshold {
			bestSimilarity = similarity
			bestMatch = organ.Name
		}
	}

	return bestMatch, bestSimilarity
}

// calculateKeywordSimilarity 计算关键词相似度
func calculateKeywordSimilarity(ocrName, standardName string) float64 {
	// 定义常见器官关键词映射
	keywordMap := map[string][]string{
		"心脏":  {"心脏", "心房", "心室", "心瓣"},
		"大脑":  {"大脑", "脑", "脑部", "脑动脉", "脑静脉", "脑室"},
		"脊柱":  {"脊柱", "脊椎", "颈椎", "胸椎", "腰椎"},
		"膝关节": {"膝关节", "膝"},
		"肝":   {"肝", "肝脏"},
		"肺":   {"肺", "右肺", "左肺"},
		"肾":   {"肾", "右肾", "左肾", "肾脏"},
		"胃":   {"胃", "胃壁", "胃前壁", "胃后壁"},
	}

	// 检查OCR名称是否匹配任何关键词
	for keyword, variants := range keywordMap {
		for _, variant := range variants {
			if strings.Contains(ocrName, variant) {
				// 检查标准名称是否包含相关关键词
				if strings.Contains(standardName, keyword) || strings.Contains(standardName, variant) {
					return 0.8 // 关键词匹配给予较高相似度
				}
			}
		}
	}

	return 0.0
}

// TestOrganMatching 测试器官名称匹配功能（用于调试）
func (o *OCRService) TestOrganMatching(testNames []string) {
	if o.organDB == nil {
		fmt.Println("器官名称库未加载")
		return
	}

	fmt.Printf("器官名称库已加载，共 %d 个条目\n", len(o.organDB.Organs))
	fmt.Println("\n=== 器官名称匹配测试 ===")

	for _, testName := range testNames {
		bestMatch, similarity := o.findBestMatchOrgan(testName)
		fmt.Printf("测试: '%s' -> '%s' (相似度: %.2f)\n", testName, bestMatch, similarity)
	}
}

// Close 关闭OCR服务
func (o *OCRService) Close() {
	// 对于API方式的OCR服务，无需特殊的关闭操作
	// 这里可以添加清理资源的代码，如果有的话
}

// extractDataFromTableResponse 从表格识别API响应中提取键值对数据和HTML内容
// func extractDataFromTableResponse(responseBody []byte) (map[string]string, string, error) {
// 	var apiResp APIResponse
// 	if err := json.Unmarshal(responseBody, &apiResp); err != nil {
// 		return nil, "", fmt.Errorf("无法解析JSON响应: %w", err)
// 	}

// 	extractedData := make(map[string]string)
// 	var htmlContent string

// 	if results, ok := apiResp["results"]; ok {
// 		if resultsList, ok := results.([]interface{}); ok {
// 			for _, item := range resultsList {
// 				if itemMap, ok := item.(map[string]interface{}); ok {
// 					if textData, ok := itemMap["text_data"].([]interface{}); ok {
// 						var currentNumber string
// 						for i, textItem := range textData {
// 							if textMap, ok := textItem.(map[string]interface{}); ok {
// 								if recTexts, ok := textMap["rec_texts"].([]interface{}); ok && len(recTexts) > 0 {
// 									text := fmt.Sprintf("%v", recTexts[0])
// 									if _, errConv := strconv.ParseFloat(text, 64); errConv == nil { // 是数字
// 										currentNumber = text
// 										// 尝试获取配对的文本
// 										if i+1 < len(textData) {
// 											if nextTextItem, okNext := textData[i+1].(map[string]interface{}); okNext {
// 												if nextRecTexts, okNextRec := nextTextItem["rec_texts"].([]interface{}); okNextRec && len(nextRecTexts) > 0 {
// 													pairedText := fmt.Sprintf("%v", nextRecTexts[0])
// 													// 简单检查配对文本是否也像数字，如果是，则可能不是期望的配对
// 													if _, errPair := strconv.ParseFloat(pairedText, 64); errPair != nil {
// 														extractedData[currentNumber] = pairedText
// 														currentNumber = "" // 重置
// 														i++                // 跳过已配对的文本项
// 														continue           // 继续下一个循环迭代
// 													}
// 												}
// 											}
// 										}
// 										// 如果数字没有有效的配对文本，重置currentNumber
// 										currentNumber = ""
// 									} else { // 不是数字
// 										// 如果 currentNumber 不为空，说明它没有找到配对，这里可以处理这种情况
// 										if currentNumber != "" {
// 											// extractedData[currentNumber] = "" // 例如，记录为空字符串
// 											currentNumber = "" // 重置
// 										}
// 									}
// 								}
// 							}
// 						}
// 					}
// 				}
// 			}
// 		}
// 	}

// 	// 提取HTML内容
// 	if html, ok := apiResp["html"]; ok {
// 		if htmlStr, okStr := html.(string); okStr {
// 			htmlContent = htmlStr
// 		}
// 	}
// 	if htmlContent == "" { // 如果顶层没有html，尝试从data字段下查找
// 		if data, okData := apiResp["data"]; okData {
// 			if tableData, okTable := data.(map[string]interface{}); okTable {
// 				if html, okHtml := tableData["html"]; okHtml {
// 					if htmlStr, okHtmlStr := html.(string); okHtmlStr {
// 						htmlContent = htmlStr
// 					}
// 				}
// 			}
// 		}
// 	}

// 	return extractedData, htmlContent, nil
// }

// extractTextFromOCRResponse 从OCR API响应中提取所有识别的文本并拼接成一个字符串
// // 该函数兼容多种API响应格式，按顺序尝试解析：
// // 1. 最新格式: `data.result[].prunedResult.rec_texts`
// // 2. 较新格式: `prunedResult.rec_texts`
// // 3. 旧格式: `data.ocr_result` (按行拼接)
// // 4. 更旧的格式: `results[].text_data[].rec_texts`
// // 如果API调用成功但未识别到文本 (errorCode == 0)，则返回空字符串，不报错。
// func extractTextFromOCRResponse(responseBody []byte) (string, error) {
// 	var apiResp APIResponse
// 	if err := json.Unmarshal(responseBody, &apiResp); err != nil {
// 		return "", fmt.Errorf("无法解析JSON响应: %w", err)
// 	}

// 	var allTextBuilder strings.Builder

// 	// 检查最新的响应格式 `data.result`
// 	if data, ok := apiResp["data"].(map[string]interface{}); ok {
// 		if result, ok := data["result"].([]interface{}); ok {
// 			for _, item := range result {
// 				if itemMap, ok := item.(map[string]interface{}); ok {
// 					if prunedResult, ok := itemMap["prunedResult"].(map[string]interface{}); ok {
// 						if recTexts, ok := prunedResult["rec_texts"].([]interface{}); ok {
// 							for _, text := range recTexts {
// 								if textStr, ok := text.(string); ok && textStr != "" {
// 									allTextBuilder.WriteString(textStr)
// 									allTextBuilder.WriteString(" ")
// 								}
// 							}
// 							return strings.TrimSpace(allTextBuilder.String()), nil
// 						}
// 					}
// 				}
// 			}
// 		}
// 	}

// 	// 检查响应格式 `prunedResult.rec_texts`
// 	if prunedResult, ok := apiResp["prunedResult"].(map[string]interface{}); ok {
// 		if recTexts, ok := prunedResult["rec_texts"].([]interface{}); ok {
// 			for _, text := range recTexts {
// 				if textStr, ok := text.(string); ok && textStr != "" {
// 					allTextBuilder.WriteString(textStr)
// 					allTextBuilder.WriteString(" ")
// 				}
// 			}
// 			return strings.TrimSpace(allTextBuilder.String()), nil
// 		}
// 	}

// 	// 检查旧的响应格式 `data.ocr_result`
// 	if data, ok := apiResp["data"].(map[string]interface{}); ok {
// 		if ocrResult, ok := data["ocr_result"].([]interface{}); ok {
// 			for _, item := range ocrResult {
// 				if line, ok := item.([]interface{}); ok && len(line) > 1 {
// 					if text, ok := line[1].(string); ok {
// 						allTextBuilder.WriteString(text)
// 						allTextBuilder.WriteString("\n") // 使用换行符分隔每行文本
// 					}
// 				}
// 			}
// 			return strings.TrimSpace(allTextBuilder.String()), nil
// 		}
// 	}

// 	// 兼容更旧的响应格式 `results`
// 	if results, ok := apiResp["results"]; ok {
// 		if resultsList, ok := results.([]interface{}); ok {
// 			for _, item := range resultsList {
// 				if itemMap, ok := item.(map[string]interface{}); ok {
// 					if textData, ok := itemMap["text_data"].([]interface{}); ok {
// 						for _, textItem := range textData {
// 							if textMap, ok := textItem.(map[string]interface{}); ok {
// 								if recTexts, ok := textMap["rec_texts"].([]interface{}); ok && len(recTexts) > 0 {
// 									text := fmt.Sprintf("%v", recTexts[0])
// 									allTextBuilder.WriteString(text)
// 									allTextBuilder.WriteString(" ")
// 								}
// 							}
// 						}
// 					}
// 				}
// 			}
// 			return strings.TrimSpace(allTextBuilder.String()), nil
// 		}
// 	}

// 	// 如果没有找到文本数据，检查是否有错误信息
// 	if errorCode, ok := apiResp["errorCode"]; ok {
// 		if code, ok := errorCode.(float64); ok && code == 0 {
// 			// API调用成功但没有识别到文本，返回空字符串而不是错误
// 			return "", nil
// 		}
// 	}

// 	return "", fmt.Errorf("响应中未找到有效的文本数据(prunedResult.rec_texts, data.ocr_result 或 results)")
// }
