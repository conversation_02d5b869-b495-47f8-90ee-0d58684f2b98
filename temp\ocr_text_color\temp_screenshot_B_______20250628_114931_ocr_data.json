{"logId": "4ca6172a-09d3-497a-b9f4-5e03c94eb3e1", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 14, "label": "algorithm", "score": 0.6832137107849121, "coordinate": [12.31842041015625, 6.115364074707031, 624.716796875, 1716]}, {"cls_id": 2, "label": "text", "score": 0.5606260895729065, "coordinate": [12.31842041015625, 6.115364074707031, 624.716796875, 1716]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[15, 0], [419, 0], [419, 21], [15, 21]], [[63, 23], [279, 23], [279, 48], [63, 48]], [[63, 52], [347, 50], [347, 75], [63, 77]], [[63, 79], [349, 75], [349, 100], [63, 104]], [[61, 104], [417, 100], [417, 130], [61, 134]], [[61, 131], [461, 127], [462, 157], [61, 161]], [[61, 159], [282, 155], [283, 184], [61, 188]], [[61, 184], [262, 182], [262, 213], [61, 215]], [[66, 218], [161, 218], [161, 238], [66, 238]], [[153, 214], [617, 214], [617, 238], [153, 238]], [[66, 245], [159, 245], [159, 265], [66, 265]], [[161, 240], [484, 238], [484, 263], [161, 265]], [[66, 272], [166, 272], [166, 291], [66, 291]], [[159, 268], [371, 268], [371, 291], [159, 291]], [[63, 295], [358, 291], [358, 316], [63, 320]], [[63, 322], [504, 318], [504, 343], [63, 347]], [[63, 349], [358, 345], [358, 372], [63, 375]], [[63, 374], [236, 370], [237, 400], [63, 404]], [[63, 404], [280, 400], [281, 425], [63, 429]], [[63, 431], [380, 427], [380, 450], [63, 454]], [[62, 460], [164, 455], [165, 481], [64, 485]], [[151, 456], [426, 454], [427, 479], [152, 481]], [[63, 483], [161, 483], [161, 508], [63, 508]], [[161, 481], [414, 481], [414, 506], [161, 506]], [[65, 511], [161, 511], [161, 536], [65, 536]], [[159, 509], [391, 509], [391, 533], [159, 533]], [[41, 536], [161, 540], [160, 565], [40, 561]], [[159, 538], [393, 538], [393, 561], [159, 561]], [[66, 568], [161, 568], [161, 588], [66, 588]], [[159, 565], [378, 565], [378, 588], [159, 588]], [[65, 593], [161, 593], [161, 618], [65, 618]], [[164, 592], [415, 592], [415, 617], [164, 617]], [[65, 620], [161, 620], [161, 645], [65, 645]], [[162, 618], [305, 618], [305, 644], [162, 644]], [[65, 647], [168, 647], [168, 672], [65, 672]], [[159, 645], [223, 645], [223, 672], [159, 672]], [[59, 670], [304, 668], [305, 699], [59, 701]], [[39, 697], [238, 693], [238, 726], [39, 730]], [[39, 724], [238, 720], [238, 752], [39, 756]], [[61, 751], [513, 749], [513, 779], [61, 781]], [[63, 781], [393, 779], [393, 804], [63, 806]], [[61, 806], [304, 802], [305, 833], [61, 837]], [[65, 838], [162, 838], [162, 858], [65, 858]], [[153, 835], [216, 835], [216, 860], [153, 860]], [[65, 862], [484, 862], [484, 887], [65, 887]], [[63, 887], [218, 887], [218, 917], [63, 917]], [[61, 914], [268, 912], [268, 942], [61, 944]], [[63, 938], [218, 938], [218, 971], [63, 971]], [[61, 969], [291, 965], [292, 994], [61, 998]], [[63, 994], [288, 992], [288, 1022], [63, 1024]], [[65, 1024], [369, 1024], [369, 1049], [65, 1049]], [[61, 1050], [217, 1045], [218, 1076], [61, 1080]], [[61, 1076], [217, 1072], [218, 1103], [61, 1107]], [[65, 1105], [303, 1105], [303, 1130], [65, 1130]], [[61, 1130], [358, 1128], [358, 1158], [61, 1160]], [[63, 1157], [353, 1157], [353, 1182], [63, 1182]], [[63, 1185], [354, 1180], [355, 1208], [63, 1214]], [[61, 1210], [402, 1208], [403, 1239], [61, 1241]], [[63, 1241], [402, 1237], [403, 1262], [63, 1266]], [[61, 1264], [334, 1262], [334, 1292], [61, 1294]], [[63, 1291], [270, 1291], [270, 1321], [63, 1321]], [[61, 1319], [269, 1315], [270, 1346], [61, 1350]], [[65, 1348], [550, 1348], [550, 1373], [65, 1373]], [[65, 1375], [310, 1375], [310, 1400], [65, 1400]], [[65, 1401], [310, 1401], [310, 1426], [65, 1426]], [[61, 1427], [290, 1425], [290, 1455], [61, 1457]], [[61, 1453], [290, 1451], [290, 1482], [61, 1484]], [[65, 1484], [168, 1484], [168, 1509], [65, 1509]], [[161, 1482], [349, 1482], [349, 1507], [161, 1507]], [[63, 1510], [437, 1509], [438, 1534], [63, 1536]], [[61, 1534], [328, 1532], [329, 1562], [61, 1564]], [[63, 1564], [415, 1562], [415, 1587], [63, 1589]], [[63, 1591], [393, 1589], [393, 1614], [63, 1616]], [[63, 1618], [393, 1616], [393, 1641], [63, 1643]], [[65, 1644], [303, 1644], [303, 1670], [65, 1670]], [[63, 1673], [301, 1668], [301, 1693], [63, 1698]], [[63, 1698], [282, 1694], [283, 1712], [63, 1716]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["□Session2025/4/2809:22-2025/4/2810:00", "2025/4/28头部纵剖面", "2025/4/28左侧头部中间部分", "2025/4/28正面的头部横截面", "2025/4/28头部大脑导管水平横截面", "2025/4/28头部的第四脑室的水平横截面", "2025/4/28脖子横截面", "2025/4/28矢状开胸", "2025/4/28", "腹主动脉上升部分的冠状水平横截面，正视图", "2025/4/28", "腔静脉冠状水平横截面，前视图", "2025/4/28", "肩关节的水平横截面", "2025/4/28第2胸椎水平横截面", "2025/4/28开胸水平在第4THORACAL椎体水平", "2025/4/28第6胸椎水平横截面", "2025/4/28 横隔膜", "2025/4/28腹膜后脏器", "2025/4/28腹部第1腰椎水平截面", "2025/4/28", "经腹在第2腰椎水平横截面", "2025/4/28", "肚脐位置横截面躯干水平", "2025/4/28", "骼骨翼水平腹腔纵切面", "2025/4/28", "男性小骨盆器官，右侧", "2025/4/28", "男性器官小骨盆：左侧", "2025/4/28", "骨盆腔前列腺水平横截面", "2025/4/28", "骨架；前视图", "2025/4/28", "牙;左", "2025/4/28食管；前视图", "2025/4/28胃后壁", " 2025/4/28 胃前壁", "2025/4/28消化系统—胰腺;十二指肠;正面图", "2025/4/28十二指肠和胰腺的动脉", "2025/4/28胰腺，前视图", "2025/4/28", "肠道", "2025/4/28肠系膜上动脉和门静脉分支机构", "2025/4/28直肠", "2025/4/28肝；后视图", "2025/4/28胆囊", "2025/4/28鼻窦;右视图", "2025/4/28鼻窦;左视图", "2025/4/28喉和气管的冠状断面", "2025/4/28 右肺", "2025/4/28 左肺", "2025/4/28气管和支气管", "2025/4/28右肾纵切面；后视图", "2025/4/28右肾纵切面：正面图", "2025/4/28左肾纵切面;后视图", "2025/4/28左肾纵向剖面图;正面图", "2025/4/28肾脏KIDNEYVESSELS容器", "2025/4/28男性膀胱;后视图", "2025/4/28睾丸；右侧", "2025/4/28睾丸;左侧", "2025/4/28主要血管MAINVASSELSOF THE TRUNK", "2025/4/28头颈动脉：左侧", "2025/4/28头颈动脉：右侧", "2025/4/28心脏;从前面", "2025/4/28心脏;从背后", "2025/4/28", "大脑动脉，上视图", "2025/4/28脑动脉，右半球的横向视图", "2025/4/28脑动脉，仰视图", "2025/4/28脑动脉，侧面图，左半球", "2025/4/28动脉大脑内侧面，右侧", "2025/4/28动脉大脑内侧面，左侧", "2025/4/28心脏前壁血管", "2025/4/28心脏后壁血管", "2025/4/28心脏纠截面"], "rec_scores": [0.9656650424003601, 0.9578500390052795, 0.9947769045829773, 0.9956860542297363, 0.9975556135177612, 0.9970661401748657, 0.998321533203125, 0.9937530159950256, 0.9957683086395264, 0.9979565739631653, 0.995711624622345, 0.9969978332519531, 0.9986845850944519, 0.9969404935836792, 0.98961341381073, 0.9873136281967163, 0.9942203164100647, 0.9599244594573975, 0.993804395198822, 0.9939095377922058, 0.9996092915534973, 0.9932283759117126, 0.9987925291061401, 0.985616147518158, 0.9993054866790771, 0.9905222654342651, 0.9993667602539062, 0.9914287328720093, 0.996608316898346, 0.939300537109375, 0.9994557499885559, 0.9967489242553711, 0.9994557499885559, 0.9887111783027649, 0.9996055960655212, 0.9071564674377441, 0.9949058890342712, 0.9986452460289001, 0.9351099133491516, 0.9603033661842346, 0.9941598176956177, 0.9948923587799072, 0.9969542026519775, 0.999660313129425, 0.9974318742752075, 0.9991353750228882, 0.959775447845459, 0.9975433945655823, 0.9661554098129272, 0.9695147275924683, 0.9960877895355225, 0.9659412503242493, 0.9606584906578064, 0.9937313199043274, 0.9656350612640381, 0.9773619174957275, 0.9678938388824463, 0.9619200825691223, 0.9950758218765259, 0.9627500176429749, 0.9158013463020325, 0.9412598609924316, 0.9653321504592896, 0.9570012092590332, 0.95395827293396, 0.9608933329582214, 0.9620069265365601, 0.9992579221725464, 0.9996452331542969, 0.9949716925621033, 0.9936927556991577, 0.9952557682991028, 0.9950435757637024, 0.9946686625480652, 0.9910562634468079, 0.9943500757217407, 0.9744523167610168], "rec_polys": [[[15, 0], [419, 0], [419, 21], [15, 21]], [[63, 23], [279, 23], [279, 48], [63, 48]], [[63, 52], [347, 50], [347, 75], [63, 77]], [[63, 79], [349, 75], [349, 100], [63, 104]], [[61, 104], [417, 100], [417, 130], [61, 134]], [[61, 131], [461, 127], [462, 157], [61, 161]], [[61, 159], [282, 155], [283, 184], [61, 188]], [[61, 184], [262, 182], [262, 213], [61, 215]], [[66, 218], [161, 218], [161, 238], [66, 238]], [[153, 214], [617, 214], [617, 238], [153, 238]], [[66, 245], [159, 245], [159, 265], [66, 265]], [[161, 240], [484, 238], [484, 263], [161, 265]], [[66, 272], [166, 272], [166, 291], [66, 291]], [[159, 268], [371, 268], [371, 291], [159, 291]], [[63, 295], [358, 291], [358, 316], [63, 320]], [[63, 322], [504, 318], [504, 343], [63, 347]], [[63, 349], [358, 345], [358, 372], [63, 375]], [[63, 374], [236, 370], [237, 400], [63, 404]], [[63, 404], [280, 400], [281, 425], [63, 429]], [[63, 431], [380, 427], [380, 450], [63, 454]], [[62, 460], [164, 455], [165, 481], [64, 485]], [[151, 456], [426, 454], [427, 479], [152, 481]], [[63, 483], [161, 483], [161, 508], [63, 508]], [[161, 481], [414, 481], [414, 506], [161, 506]], [[65, 511], [161, 511], [161, 536], [65, 536]], [[159, 509], [391, 509], [391, 533], [159, 533]], [[41, 536], [161, 540], [160, 565], [40, 561]], [[159, 538], [393, 538], [393, 561], [159, 561]], [[66, 568], [161, 568], [161, 588], [66, 588]], [[159, 565], [378, 565], [378, 588], [159, 588]], [[65, 593], [161, 593], [161, 618], [65, 618]], [[164, 592], [415, 592], [415, 617], [164, 617]], [[65, 620], [161, 620], [161, 645], [65, 645]], [[162, 618], [305, 618], [305, 644], [162, 644]], [[65, 647], [168, 647], [168, 672], [65, 672]], [[159, 645], [223, 645], [223, 672], [159, 672]], [[59, 670], [304, 668], [305, 699], [59, 701]], [[39, 697], [238, 693], [238, 726], [39, 730]], [[39, 724], [238, 720], [238, 752], [39, 756]], [[61, 751], [513, 749], [513, 779], [61, 781]], [[63, 781], [393, 779], [393, 804], [63, 806]], [[61, 806], [304, 802], [305, 833], [61, 837]], [[65, 838], [162, 838], [162, 858], [65, 858]], [[153, 835], [216, 835], [216, 860], [153, 860]], [[65, 862], [484, 862], [484, 887], [65, 887]], [[63, 887], [218, 887], [218, 917], [63, 917]], [[61, 914], [268, 912], [268, 942], [61, 944]], [[63, 938], [218, 938], [218, 971], [63, 971]], [[61, 969], [291, 965], [292, 994], [61, 998]], [[63, 994], [288, 992], [288, 1022], [63, 1024]], [[65, 1024], [369, 1024], [369, 1049], [65, 1049]], [[61, 1050], [217, 1045], [218, 1076], [61, 1080]], [[61, 1076], [217, 1072], [218, 1103], [61, 1107]], [[65, 1105], [303, 1105], [303, 1130], [65, 1130]], [[61, 1130], [358, 1128], [358, 1158], [61, 1160]], [[63, 1157], [353, 1157], [353, 1182], [63, 1182]], [[63, 1185], [354, 1180], [355, 1208], [63, 1214]], [[61, 1210], [402, 1208], [403, 1239], [61, 1241]], [[63, 1241], [402, 1237], [403, 1262], [63, 1266]], [[61, 1264], [334, 1262], [334, 1292], [61, 1294]], [[63, 1291], [270, 1291], [270, 1321], [63, 1321]], [[61, 1319], [269, 1315], [270, 1346], [61, 1350]], [[65, 1348], [550, 1348], [550, 1373], [65, 1373]], [[65, 1375], [310, 1375], [310, 1400], [65, 1400]], [[65, 1401], [310, 1401], [310, 1426], [65, 1426]], [[61, 1427], [290, 1425], [290, 1455], [61, 1457]], [[61, 1453], [290, 1451], [290, 1482], [61, 1484]], [[65, 1484], [168, 1484], [168, 1509], [65, 1509]], [[161, 1482], [349, 1482], [349, 1507], [161, 1507]], [[63, 1510], [437, 1509], [438, 1534], [63, 1536]], [[61, 1534], [328, 1532], [329, 1562], [61, 1564]], [[63, 1564], [415, 1562], [415, 1587], [63, 1589]], [[63, 1591], [393, 1589], [393, 1614], [63, 1616]], [[63, 1618], [393, 1616], [393, 1641], [63, 1643]], [[65, 1644], [303, 1644], [303, 1670], [65, 1670]], [[63, 1673], [301, 1668], [301, 1693], [63, 1698]], [[63, 1698], [282, 1694], [283, 1712], [63, 1716]]], "rec_boxes": [[15, 0, 419, 21], [63, 23, 279, 48], [63, 50, 347, 77], [63, 75, 349, 104], [61, 100, 417, 134], [61, 127, 462, 161], [61, 155, 283, 188], [61, 182, 262, 215], [66, 218, 161, 238], [153, 214, 617, 238], [66, 245, 159, 265], [161, 238, 484, 265], [66, 272, 166, 291], [159, 268, 371, 291], [63, 291, 358, 320], [63, 318, 504, 347], [63, 345, 358, 375], [63, 370, 237, 404], [63, 400, 281, 429], [63, 427, 380, 454], [62, 455, 165, 485], [151, 454, 427, 481], [63, 483, 161, 508], [161, 481, 414, 506], [65, 511, 161, 536], [159, 509, 391, 533], [40, 536, 161, 565], [159, 538, 393, 561], [66, 568, 161, 588], [159, 565, 378, 588], [65, 593, 161, 618], [164, 592, 415, 617], [65, 620, 161, 645], [162, 618, 305, 644], [65, 647, 168, 672], [159, 645, 223, 672], [59, 668, 305, 701], [39, 693, 238, 730], [39, 720, 238, 756], [61, 749, 513, 781], [63, 779, 393, 806], [61, 802, 305, 837], [65, 838, 162, 858], [153, 835, 216, 860], [65, 862, 484, 887], [63, 887, 218, 917], [61, 912, 268, 944], [63, 938, 218, 971], [61, 965, 292, 998], [63, 992, 288, 1024], [65, 1024, 369, 1049], [61, 1045, 218, 1080], [61, 1072, 218, 1107], [65, 1105, 303, 1130], [61, 1128, 358, 1160], [63, 1157, 353, 1182], [63, 1180, 355, 1214], [61, 1208, 403, 1241], [63, 1237, 403, 1266], [61, 1262, 334, 1294], [63, 1291, 270, 1321], [61, 1315, 270, 1350], [65, 1348, 550, 1373], [65, 1375, 310, 1400], [65, 1401, 310, 1426], [61, 1425, 290, 1457], [61, 1451, 290, 1484], [65, 1484, 168, 1509], [161, 1482, 349, 1507], [63, 1509, 438, 1536], [61, 1532, 329, 1564], [63, 1562, 415, 1589], [63, 1589, 393, 1616], [63, 1616, 393, 1643], [65, 1644, 303, 1670], [63, 1668, 301, 1698], [63, 1694, 283, 1716]]}, "table_res_list": []}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//4ca6172a-09d3-497a-b9f4-5e03c94eb3e1/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-28T03%3A49%3A52Z%2F-1%2F%2F66970af989ab574065d7b3289395c2e721c2a8993d8751c400f20e15293a6325", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//4ca6172a-09d3-497a-b9f4-5e03c94eb3e1/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-28T03%3A49%3A52Z%2F-1%2F%2Fbd8ded3033a19afb645138bb6ee93e6af1c9e8f90e1ffb29bd083c37d9339206"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//4ca6172a-09d3-497a-b9f4-5e03c94eb3e1/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-28T03%3A49%3A52Z%2F-1%2F%2F26bd5bce0b8e93534800ddb8a54eec85bb80535912958ecb4051fe865c09b8f7"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}