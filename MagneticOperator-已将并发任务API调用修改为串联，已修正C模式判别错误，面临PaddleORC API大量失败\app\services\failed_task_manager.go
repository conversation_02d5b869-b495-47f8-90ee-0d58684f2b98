package services

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"MagneticOperator/app/utils"
)

// FailedTask 失败任务结构
type FailedTask struct {
	ID           string    `json:"id"`            // 任务唯一ID
	UserName     string    `json:"user_name"`     // 用户名
	Mode         string    `json:"mode"`          // 模式 (B02/C03)
	ImagePath    string    `json:"image_path"`    // 图片路径
	FailedTime   time.Time `json:"failed_time"`   // 失败时间
	RetryCount   int       `json:"retry_count"`   // 重试次数
	MaxRetries   int       `json:"max_retries"`   // 最大重试次数
	NextRetryAt  time.Time `json:"next_retry_at"` // 下次重试时间
	ErrorMessage string    `json:"error_message"` // 错误信息
	Status       string    `json:"status"`        // 状态: pending, retrying, failed, completed
}

// FailedTaskManager 失败任务管理器
type FailedTaskManager struct {
	mu           sync.RWMutex
	tasks        map[string]*FailedTask
	storageFile  string
	retryTicker  *time.Ticker
	stopChan     chan bool
	ocrService   OCRInterface
	screenshot   *ScreenshotService
	app          AppInterface // 用于回调App的方法
}

// NewFailedTaskManager 创建失败任务管理器
func NewFailedTaskManager(ocrService OCRInterface, screenshotService *ScreenshotService, app AppInterface) *FailedTaskManager {
	return &FailedTaskManager{
		tasks:       make(map[string]*FailedTask),
		storageFile: "failed_tasks.json",
		stopChan:    make(chan bool),
		ocrService:  ocrService,
		screenshot:  screenshotService,
		app:         app,
	}
}

// Start 启动失败任务管理器
func (ftm *FailedTaskManager) Start() error {
	// 加载已保存的失败任务
	if err := ftm.loadFailedTasks(); err != nil {
		utils.LogError("加载失败任务失败", "", err)
	}

	// 启动定时重试检查器（每分钟检查一次）
	ftm.retryTicker = time.NewTicker(1 * time.Minute)
	go ftm.retryLoop()

	utils.LogInfo("失败任务管理器启动成功")
	return nil
}

// Stop 停止失败任务管理器
func (ftm *FailedTaskManager) Stop() {
	if ftm.retryTicker != nil {
		ftm.retryTicker.Stop()
	}
	ftm.stopChan <- true
	
	// 保存当前状态
	ftm.saveFailedTasks()
	utils.LogInfo("失败任务管理器已停止")
}

// AddFailedTask 添加失败任务
func (ftm *FailedTaskManager) AddFailedTask(userName, mode, imagePath, errorMessage string) {
	ftm.mu.Lock()
	defer ftm.mu.Unlock()

	taskID := fmt.Sprintf("%s_%s_%d", userName, mode, time.Now().Unix())
	nextRetryAt := time.Now().Add(5 * time.Minute) // 5分钟后重试

	task := &FailedTask{
		ID:           taskID,
		UserName:     userName,
		Mode:         mode,
		ImagePath:    imagePath,
		FailedTime:   time.Now(),
		RetryCount:   0,
		MaxRetries:   3, // 最多重试3次
		NextRetryAt:  nextRetryAt,
		ErrorMessage: errorMessage,
		Status:       "pending",
	}

	ftm.tasks[taskID] = task
	ftm.saveFailedTasks()

	utils.LogInfo(fmt.Sprintf("添加失败任务: %s, 下次重试时间: %s", taskID, nextRetryAt.Format("2006-01-02 15:04:05")))
}

// retryLoop 重试循环
func (ftm *FailedTaskManager) retryLoop() {
	for {
		select {
		case <-ftm.retryTicker.C:
			ftm.checkAndRetryTasks()
		case <-ftm.stopChan:
			return
		}
	}
}

// checkAndRetryTasks 检查并重试任务
func (ftm *FailedTaskManager) checkAndRetryTasks() {
	ftm.mu.Lock()
	defer ftm.mu.Unlock()

	now := time.Now()
	for taskID, task := range ftm.tasks {
		if task.Status == "pending" && now.After(task.NextRetryAt) {
			go ftm.retryTask(taskID)
		}
	}
}

// retryTask 重试单个任务
func (ftm *FailedTaskManager) retryTask(taskID string) {
	ftm.mu.Lock()
	task, exists := ftm.tasks[taskID]
	if !exists {
		ftm.mu.Unlock()
		return
	}

	// 更新状态为重试中
	task.Status = "retrying"
	task.RetryCount++
	ftm.mu.Unlock()

	utils.LogInfo(fmt.Sprintf("开始重试任务: %s (第%d次重试)", taskID, task.RetryCount))

	// 执行OCR重试
	ocrResult, err := ftm.app.ProcessImageWithOCR(task.ImagePath)
	if err != nil {
		// 重试失败
		ftm.handleRetryFailure(taskID, err.Error())
	} else {
		// 重试成功
		ftm.handleRetrySuccess(taskID, ocrResult)
	}
}

// handleRetryFailure 处理重试失败
func (ftm *FailedTaskManager) handleRetryFailure(taskID, errorMessage string) {
	ftm.mu.Lock()
	defer ftm.mu.Unlock()

	task := ftm.tasks[taskID]
	if task.RetryCount >= task.MaxRetries {
		// 达到最大重试次数，标记为最终失败
		task.Status = "failed"
		utils.LogError(fmt.Sprintf("任务最终失败: %s, 已重试%d次", taskID, task.RetryCount), task.UserName, fmt.Errorf(errorMessage))
	} else {
		// 计算下次重试时间（递增延迟：5分钟、10分钟、15分钟）
		delayMinutes := 5 * task.RetryCount
		task.NextRetryAt = time.Now().Add(time.Duration(delayMinutes) * time.Minute)
		task.Status = "pending"
		task.ErrorMessage = errorMessage
		utils.LogInfo(fmt.Sprintf("任务重试失败: %s, 下次重试时间: %s", taskID, task.NextRetryAt.Format("2006-01-02 15:04:05")))
	}

	ftm.saveFailedTasks()
}

// handleRetrySuccess 处理重试成功
func (ftm *FailedTaskManager) handleRetrySuccess(taskID string, ocrResult *OCRResult) {
	ftm.mu.Lock()
	defer ftm.mu.Unlock()

	task := ftm.tasks[taskID]
	task.Status = "completed"

	utils.LogInfo(fmt.Sprintf("任务重试成功: %s, 用户: %s", taskID, task.UserName))

	// 从失败任务列表中移除
	delete(ftm.tasks, taskID)
	ftm.saveFailedTasks()
}

// loadFailedTasks 加载失败任务
func (ftm *FailedTaskManager) loadFailedTasks() error {
	if _, err := os.Stat(ftm.storageFile); os.IsNotExist(err) {
		return nil // 文件不存在，正常情况
	}

	data, err := os.ReadFile(ftm.storageFile)
	if err != nil {
		return fmt.Errorf("读取失败任务文件失败: %v", err)
	}

	var tasks map[string]*FailedTask
	if err := json.Unmarshal(data, &tasks); err != nil {
		return fmt.Errorf("解析失败任务文件失败: %v", err)
	}

	ftm.mu.Lock()
	ftm.tasks = tasks
	ftm.mu.Unlock()

	utils.LogInfo(fmt.Sprintf("加载了%d个失败任务", len(tasks)))
	return nil
}

// saveFailedTasks 保存失败任务
func (ftm *FailedTaskManager) saveFailedTasks() error {
	ftm.mu.RLock()
	defer ftm.mu.RUnlock()

	data, err := json.MarshalIndent(ftm.tasks, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化失败任务失败: %v", err)
	}

	// 确保目录存在
	dir := filepath.Dir(ftm.storageFile)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	if err := os.WriteFile(ftm.storageFile, data, 0644); err != nil {
		return fmt.Errorf("保存失败任务文件失败: %v", err)
	}

	return nil
}

// GetFailedTasks 获取所有失败任务
func (ftm *FailedTaskManager) GetFailedTasks() map[string]*FailedTask {
	ftm.mu.RLock()
	defer ftm.mu.RUnlock()

	// 返回副本以避免并发问题
	tasks := make(map[string]*FailedTask)
	for k, v := range ftm.tasks {
		tasks[k] = v
	}
	return tasks
}

// GetTaskCount 获取任务数量统计
func (ftm *FailedTaskManager) GetTaskCount() map[string]int {
	ftm.mu.RLock()
	defer ftm.mu.RUnlock()

	counts := map[string]int{
		"pending":   0,
		"retrying":  0,
		"failed":    0,
		"completed": 0,
	}

	for _, task := range ftm.tasks {
		counts[task.Status]++
	}

	return counts
}

// ClearCompletedTasks 清理已完成的任务
func (ftm *FailedTaskManager) ClearCompletedTasks() {
	ftm.mu.Lock()
	defer ftm.mu.Unlock()

	for taskID, task := range ftm.tasks {
		if task.Status == "completed" {
			delete(ftm.tasks, taskID)
		}
	}

	ftm.saveFailedTasks()
	utils.LogInfo("已清理完成的任务")
}