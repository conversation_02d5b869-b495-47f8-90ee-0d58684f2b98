{"level":"INFO","timestamp":"2025-07-03T09:47:09.717+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-03T09:47:09.717+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-03T09:47:09.717+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-03T09:47:09.717+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:09.717+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.043+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.043+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.043+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":44972}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.045+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.045+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.045+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.045+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.045+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.405+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.405+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.405+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.405+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-03T09:47:10.406+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.675+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.675+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.675+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.675+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.675+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.675+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.675+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.675+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":20800}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.681+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.681+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.681+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.681+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.681+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.742+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.759+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.759+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.759+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-03T09:47:15.759+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.031+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.032+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.032+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.032+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.032+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.032+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.032+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.032+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":47496}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.232+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.232+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.232+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.232+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.233+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.609+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.610+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.610+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.610+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.611+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.611+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.616+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.617+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.617+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.617+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.617+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.618+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.618+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.618+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.618+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.618+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.618+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.618+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.618+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.619+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.619+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.619+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.619+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.619+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.619+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.619+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.619+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.619+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.619+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.620+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.620+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.620+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.620+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.620+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.620+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.620+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.620+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.621+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.621+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.621+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.621+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.621+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.621+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.621+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.621+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.621+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.621+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.621+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.621+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.622+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.622+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.622+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.622+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.622+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.622+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.622+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.622+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.622+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.622+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.622+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.622+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.623+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.623+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.623+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.623+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.623+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.623+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.623+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.623+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.623+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.624+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.624+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.624+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:19.624+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:20.123+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-03T09:47:20.193+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-03T09:47:20.203+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-03T09:47:20.203+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-03T09:47:20.203+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-03T09:47:20.212+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-03T09:47:20.216+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-03T09:47:20.217+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-03T09:47:20.223+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.429+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.429+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.429+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.429+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.429+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.429+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.429+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.429+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.429+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.530+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.530+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.747+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.747+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.747+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.747+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.747+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.747+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.747+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:21.748+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-03T09:47:23.296+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:47:23.296+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:47:23.296+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:47:24.117+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:47:24.134+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-03T09:47:24.137+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:47:26.529+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:47:27.961+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:47:31.166+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:47:31.166+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:47:44.247+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:47:44.247+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:48:01.310+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:48:01.638+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751507281638992000"}
{"level":"INFO","timestamp":"2025-07-03T09:48:01.638+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_21653100_20250703_094801.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751507281638992000"}
{"level":"INFO","timestamp":"2025-07-03T09:48:01.638+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751507281638992000","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-03T09:48:01.638+0800","caller":"utils/logger.go:94","msg":"创建轮次数据","轮次数":1,"operationID":"test-11200_B_1751507281638992000"}
{"level":"INFO","timestamp":"2025-07-03T09:48:01.639+0800","caller":"utils/logger.go:94","msg":"设置器官名称","器官":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751507281638992000"}
{"level":"INFO","timestamp":"2025-07-03T09:48:01.642+0800","caller":"utils/logger.go:94","msg":"开始计算已完成轮次数","operationID":"test-11200_B_1751507281638992000"}
{"level":"INFO","timestamp":"2025-07-03T09:48:01.642+0800","caller":"utils/logger.go:94","msg":"用户数据更新","userName":"test-11200","mode":"B","organName":"腹部第1腰椎水平截面","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751507281638992000"}
{"level":"INFO","timestamp":"2025-07-03T09:48:01.642+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751507281638992000"}
{"level":"INFO","timestamp":"2025-07-03T09:48:27.227+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:48:27.509+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751507307509687900"}
{"level":"INFO","timestamp":"2025-07-03T09:48:27.510+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_28522500_20250703_094827.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751507307509687900"}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.236+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.236+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.236+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.236+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.236+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.237+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.237+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.237+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":51028}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.255+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.256+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.256+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.256+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.256+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.289+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.289+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.289+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.289+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-03T09:49:49.289+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.013+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.013+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.013+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.013+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.013+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.014+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-03T09:47:19.032+0800"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.014+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"47496"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.014+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":47496}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.014+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":47496}
{"level":"WARN","timestamp":"2025-07-03T09:49:51.014+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":47496,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.014+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":47496}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.014+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.014+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.015+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":48460}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.127+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.128+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.128+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.128+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.128+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.521+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.522+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.522+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.523+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.523+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.523+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.529+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.530+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.530+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.530+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.530+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.530+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.531+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.531+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.531+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.531+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.531+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.531+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.531+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.544+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.545+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.545+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.545+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.545+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.545+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.545+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.545+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.545+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.545+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.546+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.547+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.548+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.548+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.548+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.548+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.548+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.548+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.548+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.548+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.548+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.548+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.549+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.549+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.549+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.549+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.690+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.759+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.788+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.788+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.788+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.801+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.887+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.887+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-03T09:49:51.899+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.133+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.133+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.133+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.133+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.133+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.133+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.133+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.133+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.133+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.442+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.442+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.442+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.681+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.681+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.900+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.900+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.901+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.901+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.902+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.902+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.902+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:55.902+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-03T09:49:57.058+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:49:57.059+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-03T09:49:57.067+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:49:59.354+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:50:00.052+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T09:54:51.549+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T09:59:51.549+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T10:04:51.549+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T10:09:51.549+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T10:14:51.549+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T10:19:51.549+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T10:24:51.549+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T10:29:51.549+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T10:34:51.549+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T10:36:12.056+0800","caller":"utils/logger.go:94","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:12.056+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:12.057+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:12.057+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-03T10:36:12.058+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T10:36:12.058+0800","caller":"utils/logger.go:94","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.589+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.589+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.589+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.589+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.589+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.589+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.589+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.589+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":50964}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.608+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.608+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.608+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.621+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.621+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.629+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.629+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.629+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.629+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-03T10:36:28.629+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.539+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.539+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.539+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.539+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.539+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.539+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.540+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.540+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":44052}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.562+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.562+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.562+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.562+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.562+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.570+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.570+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.570+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.570+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-03T10:36:32.570+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.171+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.171+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.172+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.172+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.172+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.172+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.172+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.172+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":43688}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.282+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.283+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.283+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.283+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.283+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.713+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.714+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.715+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.715+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.715+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.715+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.722+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.725+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.725+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.725+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.725+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.726+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.726+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.726+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.726+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.726+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.726+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.726+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.726+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.726+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.726+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.726+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.727+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.727+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.727+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.727+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.727+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.727+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.727+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.727+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.727+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.727+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.727+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.730+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.730+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.730+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.730+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.730+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.730+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.730+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.730+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.730+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.730+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.731+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.731+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.732+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.732+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:34.732+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.244+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.324+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.334+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.334+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.335+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.345+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.351+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.352+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.357+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.562+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.562+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.562+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.562+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.562+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.562+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.562+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.562+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:35.562+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-03T10:36:36.608+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:36:36.608+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:36:36.608+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:36:37.358+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-03T10:36:37.358+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-03T10:36:41.403+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:36:41.404+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-03T10:36:41.411+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:36:42.076+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-03T10:36:42.076+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-03T10:36:42.076+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:42.076+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-03T10:36:42.077+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:42.077+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-03T10:36:42.077+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:42.077+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-03T10:36:48.542+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:36:49.277+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:39:44.374+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:39:44.374+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:39:49.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:39:49.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:40:03.510+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:40:03.510+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:40:06.494+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:40:06.494+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:40:18.593+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:40:18.884+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751510418884868300"}
{"level":"INFO","timestamp":"2025-07-03T10:40:18.885+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_27163400_20250703_104018.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751510418884868300"}
{"level":"INFO","timestamp":"2025-07-03T10:40:18.885+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751510418884868300","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-03T10:40:18.885+0800","caller":"utils/logger.go:94","msg":"创建轮次数据","轮次数":1,"operationID":"test-11200_B_1751510418884868300"}
{"level":"INFO","timestamp":"2025-07-03T10:40:18.885+0800","caller":"utils/logger.go:94","msg":"设置器官名称","器官":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751510418884868300"}
{"level":"INFO","timestamp":"2025-07-03T10:40:18.885+0800","caller":"utils/logger.go:94","msg":"开始计算已完成轮次数","operationID":"test-11200_B_1751510418884868300"}
{"level":"INFO","timestamp":"2025-07-03T10:40:18.885+0800","caller":"utils/logger.go:94","msg":"用户数据更新","userName":"test-11200","mode":"B","organName":"腹部第1腰椎水平截面","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751510418884868300"}
{"level":"INFO","timestamp":"2025-07-03T10:40:18.885+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751510418884868300"}
{"level":"INFO","timestamp":"2025-07-03T10:40:46.467+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:40:46.744+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751510446744092000"}
{"level":"INFO","timestamp":"2025-07-03T10:40:46.744+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_92373200_20250703_104046.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751510446744092000"}
{"level":"INFO","timestamp":"2025-07-03T10:41:34.731+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T10:41:53.014+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:41:53.369+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751510513369186100"}
{"level":"INFO","timestamp":"2025-07-03T10:41:53.369+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_72547000_20250703_104153.png","organName":"胃后壁","operationID":"test-11200_B_1751510513369186100"}
{"level":"INFO","timestamp":"2025-07-03T10:42:48.042+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T10:42:48.692+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751510568692954900"}
{"level":"INFO","timestamp":"2025-07-03T10:42:48.692+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_21532300_20250703_104248.png","organName":"胃后壁","operationID":"test-11200_B_1751510568692954900"}
{"level":"INFO","timestamp":"2025-07-03T10:46:34.731+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T10:51:34.731+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T10:56:34.731+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.335+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.356+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.356+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.356+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.356+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.356+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.356+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.357+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":21940}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.377+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.378+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.378+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.378+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.378+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.386+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.386+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.386+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.386+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-03T13:29:24.386+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.434+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.434+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.434+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.434+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.434+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.434+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.434+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.434+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":18684}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.456+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.457+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.457+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.457+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.457+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.464+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.464+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.464+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.464+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-03T13:29:31.465+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.643+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.643+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.643+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.643+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.643+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.643+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-03T10:36:34.172+0800"}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.643+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"43688"}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.644+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":43688}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.644+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":43688}
{"level":"WARN","timestamp":"2025-07-03T13:29:33.644+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":43688,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.644+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":43688}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.644+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.644+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.644+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":7336}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.686+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.686+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.686+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.686+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:33.686+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.183+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.184+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.184+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.184+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.184+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.185+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.191+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.193+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.193+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.193+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.193+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.193+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.193+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.193+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.194+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.194+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.194+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.194+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.194+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.194+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.194+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.194+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.194+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.194+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.194+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.195+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.195+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.195+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.195+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.195+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.195+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.195+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.195+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.195+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.195+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.195+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.195+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.196+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.197+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.198+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.198+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.198+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.198+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.198+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.198+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.198+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.198+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.199+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.199+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.847+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.910+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.934+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.934+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.934+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-03T13:29:34.946+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-03T13:29:35.002+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-03T13:29:35.003+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-03T13:29:35.017+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T13:29:44.734+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T13:29:44.734+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T13:29:44.734+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T13:29:44.843+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-03T13:29:44.843+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-03T13:29:44.843+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-03T13:29:44.844+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-03T13:29:44.844+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:44.844+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:44.844+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-03T13:29:44.844+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-03T13:29:44.844+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"ERROR","timestamp":"2025-07-03T13:30:03.845+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"网络连接测试","patient":"","error":"连接失败: https://www.baidu.com, 错误: 请求失败，已重试3次: error when dialing *************:443: dialing to the given TCP address timed out","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).checkNetworkConnectivityOnStartup\n\tF:/myHbuilderAPP/MagneticOperator/app.go:2991\nmain.(*App).checkOCREnvironmentOnStartup\n\tF:/myHbuilderAPP/MagneticOperator/app.go:2964\nmain.(*App).startup\n\tF:/myHbuilderAPP/MagneticOperator/app.go:162\ngithub.com/wailsapp/wails/v2/internal/frontend/desktop/windows.(*Frontend).Run.func3\n\tC:/Users/<USER>/go/pkg/mod/github.com/wailsapp/wails/v2@v2.10.1/internal/frontend/desktop/windows/frontend.go:209"}
{"level":"ERROR","timestamp":"2025-07-03T13:30:03.845+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"OCR环境检测","patient":"","error":"网络连接检测失败","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).checkOCREnvironmentOnStartup\n\tF:/myHbuilderAPP/MagneticOperator/app.go:2965\nmain.(*App).startup\n\tF:/myHbuilderAPP/MagneticOperator/app.go:162\ngithub.com/wailsapp/wails/v2/internal/frontend/desktop/windows.(*Frontend).Run.func3\n\tC:/Users/<USER>/go/pkg/mod/github.com/wailsapp/wails/v2@v2.10.1/internal/frontend/desktop/windows/frontend.go:209"}
{"level":"INFO","timestamp":"2025-07-03T13:30:03.846+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-03T13:30:03.846+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-03T13:30:03.846+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-03T13:30:03.846+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-03T13:30:03.846+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-03T13:30:03.846+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-03T13:30:46.773+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T13:30:46.779+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-03T13:30:46.782+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T13:30:53.712+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T13:30:55.150+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-03T13:34:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T13:39:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T13:44:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T13:49:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T13:54:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T13:59:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T14:04:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T14:09:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T14:14:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T14:19:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T14:24:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T14:29:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T14:34:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T14:39:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T14:44:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T14:49:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T14:54:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T14:59:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T15:04:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T15:09:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T15:14:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-03T15:19:34.198+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
