# 并发截图OCR处理系统使用指南

## 概述

本系统基于 `github.com/panjf2000/ants` 协程池实现了高效的并发截图OCR处理功能，主要解决了10轮共20次截图OCR API调用的性能问题。通过并发处理，用户无需等待前一轮的OCR处理完成即可进行下一轮截图操作，大大提升了用户体验。

## 系统架构

### 核心组件

1. **ConcurrentOCRProcessor** (`concurrent_ocr_processor.go`)
   - 基于 `ants` 协程池的并发OCR处理器
   - 支持任务队列、限流控制、统计监控
   - 提供结果回调机制

2. **ScreenshotRoundManager** (`screenshot_round_manager.go`)
   - 轮次管理器，负责管理10轮截图的生命周期
   - 跟踪每轮的状态、进度和结果
   - 提供非阻塞的截图提交接口

3. **IntegratedScreenshotService** (`integrated_screenshot_service.go`)
   - 集成服务，整合了轮次管理器和OCR处理器
   - 提供统一的API接口
   - 兼容原有的截图服务

### 数据结构

```go
// 截图任务
type ScreenshotTask struct {
    ID           string    // 任务唯一标识
    ImagePath    string    // 图片路径
    UserName     string    // 用户名
    RoundNumber  int       // 轮次编号 (1-10)
    ScreenNumber int       // 截图编号 (1-2)
    Timestamp    time.Time // 时间戳
    Mode         string    // 截图模式
}

// OCR任务结果
type OCRTaskResult struct {
    Task      *ScreenshotTask // 原始任务
    Result    *OCRResult      // OCR识别结果
    Error     error           // 错误信息
    Duration  time.Duration   // 处理耗时
    Timestamp time.Time       // 完成时间
}

// 截图轮次
type ScreenshotRound struct {
    RoundNumber   int                // 轮次编号 (1-10)
    UserName      string             // 用户名
    Status        RoundStatus        // 轮次状态
    Screenshots   []*ScreenshotTask  // 截图任务列表（每轮2个）
    Results       []*OCRTaskResult   // OCR结果列表
    StartTime     time.Time          // 开始时间
    CompletedTime *time.Time         // 完成时间
    Errors        []string           // 错误信息
}
```

## 使用方法

### 1. 基本使用流程

```go
package main

import (
    "fmt"
    "log"
    "time"
    "MagneticOperator/app/services"
)

func main() {
    // 1. 创建配置服务
    configService := services.NewConfigService()
    
    // 2. 创建集成截图服务
    screenshotService := services.NewIntegratedScreenshotService(configService)
    
    // 3. 初始化服务
    if err := screenshotService.Initialize(); err != nil {
        log.Fatalf("初始化失败: %v", err)
    }
    defer screenshotService.Shutdown()
    
    // 4. 设置最大轮次数
    screenshotService.SetMaxRounds(10)
    
    // 5. 执行10轮截图
    userName := "张三"
    for round := 1; round <= 10; round++ {
        // 开始新轮次
        roundInfo, err := screenshotService.StartNewRound(userName)
        if err != nil {
            fmt.Printf("开始第%d轮失败: %v\n", round, err)
            continue
        }
        
        // 每轮进行2次截图
        modes := []string{"B01", "B02"}
        for _, mode := range modes {
            // 非阻塞截图（立即返回，OCR在后台处理）
            if err := screenshotService.TakeScreenshot(round, mode); err != nil {
                fmt.Printf("截图失败: %v\n", err)
            }
        }
        
        // 用户可以立即进行下一轮操作，无需等待OCR完成
    }
    
    // 6. 监控进度（可选）
    progress := screenshotService.GetOverallProgress()
    fmt.Printf("整体进度: %+v\n", progress)
}
```

### 2. 进度监控

```go
// 实时监控处理进度
func monitorProgress(service *services.IntegratedScreenshotService) {
    ticker := time.NewTicker(2 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            progress := service.GetOverallProgress()
            
            currentRound := progress["current_round"].(int)
            completedRounds := progress["completed_rounds"].(int)
            totalScreenshots := progress["total_screenshots"].(int)
            completedScreenshots := progress["completed_screenshots"].(int)
            
            fmt.Printf("轮次进度: %d/%d, 截图进度: %d/%d\n",
                completedRounds, currentRound,
                completedScreenshots, totalScreenshots)
                
            // 检查是否完成
            if totalScreenshots > 0 && completedScreenshots == totalScreenshots {
                fmt.Println("所有处理完成！")
                return
            }
        }
    }
}
```

### 3. 获取结果

```go
// 获取指定轮次的结果
func getRoundResults(service *services.IntegratedScreenshotService, roundNumber int) {
    results, err := service.GetRoundResults(roundNumber)
    if err != nil {
        fmt.Printf("获取轮次%d结果失败: %v\n", roundNumber, err)
        return
    }
    
    fmt.Printf("轮次%d结果:\n", roundNumber)
    for i, result := range results {
        if result.Error != nil {
            fmt.Printf("  截图%d: 失败 - %v\n", i+1, result.Error)
        } else {
            fmt.Printf("  截图%d: 成功 - 器官: %s, 耗时: %v\n",
                i+1, result.Result.OrganName, result.Duration)
        }
    }
}

// 等待特定轮次完成
func waitForRound(service *services.IntegratedScreenshotService, roundNumber int) {
    round, err := service.WaitForRoundCompletion(roundNumber, 30*time.Second)
    if err != nil {
        fmt.Printf("等待轮次%d完成失败: %v\n", roundNumber, err)
        return
    }
    
    fmt.Printf("轮次%d已完成，状态: %v\n", roundNumber, round.Status)
}
```

## 配置参数

### OCR处理器配置

```go
// 创建自定义配置的OCR处理器
processor := services.NewConcurrentOCRProcessor(
    ocrService,
    5,   // 工作协程数（建议3-8个）
    100, // 任务队列大小
)

// 配置限流
processor.SetRateLimit(10, time.Second) // 每秒最多10个请求
```

### 轮次管理器配置

```go
// 设置最大轮次数
screenshotService.SetMaxRounds(10)

// 添加回调函数
screenshotService.AddProgressCallback(func(round *ScreenshotRound, progress float64) {
    fmt.Printf("轮次%d进度: %.1f%%\n", round.RoundNumber, progress*100)
})

screenshotService.AddCompletedCallback(func(round *ScreenshotRound) {
    fmt.Printf("轮次%d已完成\n", round.RoundNumber)
})
```

## 性能优势

### 传统同步方式
```
轮次1: 截图1 -> OCR1(20-30s) -> 截图2 -> OCR2(20-30s) -> 完成
轮次2: 等待轮次1完成 -> 截图1 -> OCR1(20-30s) -> ...
总耗时: 10轮 × 2截图 × 25秒 = 500秒 (约8.3分钟)
```

### 新的并发方式
```
轮次1: 截图1 -> 提交OCR1 -> 截图2 -> 提交OCR2 -> 立即进入轮次2
轮次2: 截图1 -> 提交OCR1 -> 截图2 -> 提交OCR2 -> 立即进入轮次3
...
所有OCR在后台并发处理
用户操作耗时: 10轮 × 2截图 × 1秒 = 20秒
```

**性能提升**: 用户等待时间从8.3分钟减少到20秒，提升约25倍！

## 错误处理

### 1. 网络错误重试

系统内置了智能重试机制：
- 4xx客户端错误：不重试
- 5xx服务器错误：自动重试（最多3次）
- 网络超时：自动重试
- 指数退避延迟：避免服务器过载

### 2. 错误监控

```go
// 获取错误统计
stats := screenshotService.GetOCRProcessorStats()
fmt.Printf("失败率: %.1f%%\n", float64(stats.TotalFailed)/float64(stats.TotalSubmitted)*100)

// 获取轮次错误
round, _ := screenshotService.GetRound(1)
for _, err := range round.Errors {
    fmt.Printf("错误: %s\n", err)
}
```

### 3. 故障恢复

```go
// 检查服务状态
if !screenshotService.IsRunning() {
    // 重新初始化服务
    if err := screenshotService.Initialize(); err != nil {
        log.Printf("服务恢复失败: %v", err)
    }
}
```

## 最佳实践

### 1. 资源管理

- **协程数量**: 建议设置为3-8个，避免过多协程导致资源竞争
- **队列大小**: 建议设置为50-200，根据内存情况调整
- **及时关闭**: 程序结束时调用 `Shutdown()` 释放资源

### 2. 错误处理

- **监控失败率**: 如果失败率超过10%，检查网络和API配置
- **日志记录**: 启用详细日志以便问题排查
- **优雅降级**: 在高负载时减少并发数

### 3. 性能优化

- **批量处理**: 尽量批量提交任务，减少调用开销
- **缓存结果**: 对相同图片的OCR结果进行缓存
- **压缩图片**: 在不影响识别精度的前提下压缩图片大小

## 故障排查

### 常见问题

1. **OCR处理缓慢**
   - 检查网络连接
   - 减少并发协程数
   - 检查API服务器负载

2. **内存使用过高**
   - 减少队列大小
   - 及时清理完成的任务
   - 检查图片大小

3. **任务丢失**
   - 检查错误日志
   - 确认服务正常运行
   - 验证任务提交逻辑

### 调试模式

```go
// 启用详细日志
processor.SetDebugMode(true)

// 获取详细统计
stats := processor.GetDetailedStats()
fmt.Printf("详细统计: %+v\n", stats)
```

## 示例代码

完整的使用示例请参考 `examples/concurrent_screenshot_example.go` 文件。

## 注意事项

1. **API限制**: 确保OCR API服务支持并发请求
2. **网络稳定**: 并发处理对网络稳定性要求较高
3. **资源监控**: 监控CPU和内存使用情况
4. **错误恢复**: 实现适当的错误恢复机制
5. **数据一致性**: 确保并发处理不影响数据一致性

## 更新日志

- **v1.0.0**: 初始版本，支持基本的并发OCR处理
- **v1.1.0**: 添加轮次管理和进度监控
- **v1.2.0**: 优化错误处理和重试机制
- **v1.3.0**: 添加统计监控和性能优化