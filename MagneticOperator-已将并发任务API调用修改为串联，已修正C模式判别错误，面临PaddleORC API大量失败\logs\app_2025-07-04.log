{"level":"INFO","timestamp":"2025-07-04T00:52:46.142+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.142+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.142+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.142+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.142+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.143+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.143+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.143+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":1872}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.146+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.146+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.146+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.146+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.146+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.154+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.154+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.154+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.154+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T00:52:46.154+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.733+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.733+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.733+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.733+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.733+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.733+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.733+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.733+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":16536}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.744+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.744+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.745+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.745+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.745+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.755+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.755+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.755+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.755+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T00:52:53.755+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.373+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.374+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.374+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.374+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.374+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.374+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":4,"modified":"2025-07-03T13:29:33.645+0800"}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.374+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"7336"}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.374+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":7336}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.374+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":7336}
{"level":"WARN","timestamp":"2025-07-04T00:52:58.374+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":7336,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.375+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":7336}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.375+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.375+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.375+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":11600}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.454+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.454+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.454+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.454+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:58.454+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.675+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.675+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.676+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.676+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.676+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.676+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.681+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.682+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.683+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.683+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.683+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.683+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.683+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.683+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.683+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.684+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.684+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.684+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.684+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.684+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.684+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.684+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.684+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.684+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.684+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.684+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.685+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.685+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.685+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.685+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.685+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.685+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.685+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.686+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.687+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.688+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.688+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.688+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.688+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.688+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.688+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.688+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.688+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.688+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.689+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.689+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.689+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.689+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T00:52:59.689+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.367+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.368+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.368+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.368+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.368+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.368+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.368+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.368+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.368+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.387+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.448+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.449+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.454+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.461+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.461+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.461+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.470+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.474+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.474+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.480+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.598+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.598+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.598+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.598+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.598+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.598+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.599+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T00:53:00.599+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-04T00:53:01.179+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T00:53:01.179+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T00:53:01.179+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T00:53:01.874+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T00:53:01.875+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T00:53:01.879+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T00:53:02.157+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T00:53:02.372+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T00:57:59.689+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T01:02:59.689+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T01:03:38.575+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T01:03:38.583+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:03:38.583+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:03:38.583+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T01:03:38.586+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:03:39.282+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:03:39.282+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:03:39.282+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:03:39.900+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:03:39.901+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T01:03:39.903+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:03:40.076+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:03:40.292+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:04:09.217+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T01:04:09.219+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:04:09.219+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:04:09.219+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T01:04:09.221+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:04:09.472+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:04:09.472+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:04:09.472+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:04:09.695+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:04:09.695+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T01:04:09.697+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:04:09.928+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:04:10.114+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:07:59.689+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T01:12:07.204+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:12:07.205+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:12:12.582+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:12:12.582+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:12:37.366+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:12:37.641+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751562757641767000"}
{"level":"INFO","timestamp":"2025-07-04T01:12:37.641+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_44685100_20250704_011237.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751562757641767000"}
{"level":"INFO","timestamp":"2025-07-04T01:12:37.642+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751562757641767000","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-04T01:12:37.642+0800","caller":"utils/logger.go:94","msg":"创建轮次数据","轮次数":1,"operationID":"test-11200_B_1751562757641767000"}
{"level":"INFO","timestamp":"2025-07-04T01:12:37.642+0800","caller":"utils/logger.go:94","msg":"设置器官名称","器官":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751562757641767000"}
{"level":"INFO","timestamp":"2025-07-04T01:12:37.642+0800","caller":"utils/logger.go:94","msg":"开始计算已完成轮次数","operationID":"test-11200_B_1751562757641767000"}
{"level":"INFO","timestamp":"2025-07-04T01:12:37.642+0800","caller":"utils/logger.go:94","msg":"用户数据更新","userName":"test-11200","mode":"B","organName":"腹部第1腰椎水平截面","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751562757641767000"}
{"level":"INFO","timestamp":"2025-07-04T01:12:37.642+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751562757641767000"}
{"level":"INFO","timestamp":"2025-07-04T01:12:40.773+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:12:40.773+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:12:44.023+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:12:44.023+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:12:58.578+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:12:58.578+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:12:59.689+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T01:13:01.724+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:13:01.724+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:13:04.514+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:13:04.736+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751562784736450600"}
{"level":"INFO","timestamp":"2025-07-04T01:13:04.736+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_36747700_20250704_011304.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751562784736450600"}
{"level":"INFO","timestamp":"2025-07-04T01:13:17.028+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:13:17.028+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:13:20.208+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:13:20.209+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:13:32.186+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:13:32.356+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751562812356988400"}
{"level":"INFO","timestamp":"2025-07-04T01:13:32.357+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_12876800_20250704_011332.png","organName":"胃后壁","operationID":"test-11200_B_1751562812356988400"}
{"level":"INFO","timestamp":"2025-07-04T01:17:59.689+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.716+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.717+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.717+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.717+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.717+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.717+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.717+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.717+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":19084}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.722+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.722+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.722+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.722+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.722+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.775+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.775+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.775+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.775+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.776+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.860+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.861+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.863+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.872+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.872+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.872+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.872+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.872+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.872+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.872+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.873+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.873+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.875+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.876+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:24.876+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:25.527+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:25.527+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:25.527+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:25.536+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:25.536+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:25.537+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:25.538+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:25.538+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:25.538+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.139+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.140+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.140+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.155+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.155+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.158+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.165+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.165+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.167+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.389+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.435+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.435+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.586+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.616+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:26.651+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.666+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.666+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.666+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.667+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.667+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.667+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-04T00:52:58.375+0800"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.667+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"11600"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.667+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":11600}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.667+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":11600}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.667+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.667+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 11600\" /NH"}
{"level":"WARN","timestamp":"2025-07-04T01:18:27.692+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.692+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":11600}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.692+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.692+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.693+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":2832}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.729+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.730+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.730+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.730+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:27.730+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.155+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.156+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.158+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.158+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.158+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.159+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.167+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.168+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.169+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.169+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.169+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.169+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.169+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.169+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.169+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.170+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.170+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.170+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.170+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.170+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.170+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.170+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.170+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.170+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.171+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.171+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.171+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.171+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.171+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.171+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.172+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.172+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.172+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.171+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.172+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.172+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.172+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.172+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.172+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.172+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.172+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.173+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.173+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.173+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.173+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.173+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.173+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.173+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.173+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.173+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.173+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.173+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.174+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.174+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.174+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.174+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.174+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.174+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.174+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.175+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.175+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.175+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.175+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.175+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.175+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.175+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.175+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.176+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.176+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.176+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.176+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.177+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.177+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.177+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.377+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.450+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.472+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.472+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.472+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.484+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.553+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.553+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.553+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.770+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.770+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.770+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.898+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.898+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.898+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.898+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.898+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.899+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.899+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.899+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.899+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.973+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-04T01:18:28.973+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.012+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.012+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.018+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.060+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.060+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.060+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.060+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.061+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.061+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.061+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.061+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.236+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:18:29.431+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:23:28.176+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T01:25:45.879+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-04T01:25:45.915+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T01:25:45.915+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-04T01:25:45.915+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-04T01:25:45.915+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-04T01:25:45.915+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-04T01:25:45.916+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-04T01:25:45.941+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T01:25:45.941+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T01:25:45.941+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:25:45.942+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T01:25:45.942+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.705+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.705+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.705+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.705+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.705+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.705+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.705+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.706+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":21212}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.716+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.716+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.739+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.739+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.739+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.746+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.746+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.746+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.746+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T01:26:09.747+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.581+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.582+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.582+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.582+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.582+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.582+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.582+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.582+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":15080}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.587+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.587+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.587+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.587+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.587+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.595+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.595+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.596+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.596+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T01:26:16.596+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.125+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.126+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.126+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.126+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.126+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.127+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.127+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.127+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":14404}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.337+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.343+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.343+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.343+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.343+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.777+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.778+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.779+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.779+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.779+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.779+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.787+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.788+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.789+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.789+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.789+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.789+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.789+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.789+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.790+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.791+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.791+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.791+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.792+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.792+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.792+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.792+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.792+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.792+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.792+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.792+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.792+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.793+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.793+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.793+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.793+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.793+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.793+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.793+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.793+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.793+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.793+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.793+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.793+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.794+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.794+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.794+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.794+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.794+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.794+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.794+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.794+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.794+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.794+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.795+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.795+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.795+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.795+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.795+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.795+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.795+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.795+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.795+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.795+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.795+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.795+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.796+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.796+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.796+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.796+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.796+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.797+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.797+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.797+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.797+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.798+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.798+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.798+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:19.798+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.411+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.480+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.480+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.480+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.481+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.481+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.481+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.481+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.481+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.481+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.499+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.516+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.517+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.517+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.532+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.539+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.539+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.547+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.569+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.569+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.782+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.782+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.783+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.783+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.783+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.783+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.783+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:20.783+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:26:21.168+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:26:21.168+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:26:21.168+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:26:21.809+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:26:21.809+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T01:26:21.813+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:26:22.050+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:26:22.282+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:26:33.887+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:26:33.887+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:26:39.150+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:26:39.150+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:27:02.456+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:27:02.639+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751563622639242100"}
{"level":"INFO","timestamp":"2025-07-04T01:27:02.639+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_74995700_20250704_012702.png","organName":"矢状开胸","operationID":"test-11200_B_1751563622639242100"}
{"level":"INFO","timestamp":"2025-07-04T01:27:02.639+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751563622639242100","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-04T01:27:02.639+0800","caller":"utils/logger.go:94","msg":"创建轮次数据","轮次数":1,"operationID":"test-11200_B_1751563622639242100"}
{"level":"INFO","timestamp":"2025-07-04T01:27:02.639+0800","caller":"utils/logger.go:94","msg":"设置器官名称","器官":"矢状开胸","operationID":"test-11200_B_1751563622639242100"}
{"level":"INFO","timestamp":"2025-07-04T01:27:02.639+0800","caller":"utils/logger.go:94","msg":"开始计算已完成轮次数","operationID":"test-11200_B_1751563622639242100"}
{"level":"INFO","timestamp":"2025-07-04T01:27:02.639+0800","caller":"utils/logger.go:94","msg":"用户数据更新","userName":"test-11200","mode":"B","organName":"矢状开胸","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751563622639242100"}
{"level":"INFO","timestamp":"2025-07-04T01:27:02.640+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751563622639242100"}
{"level":"INFO","timestamp":"2025-07-04T01:27:28.562+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:27:28.778+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751563648778176000"}
{"level":"INFO","timestamp":"2025-07-04T01:27:28.778+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_32574600_20250704_012728.png","organName":"矢状开胸","operationID":"test-11200_B_1751563648778176000"}
{"level":"INFO","timestamp":"2025-07-04T01:29:46.027+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-04T01:29:46.050+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T01:29:46.050+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-04T01:29:46.050+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-04T01:29:46.050+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-04T01:29:46.066+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-04T01:29:46.066+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-04T01:29:46.081+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T01:29:46.081+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T01:29:46.082+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:29:46.082+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T01:29:46.082+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.765+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.765+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.765+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.765+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.765+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.765+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.765+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.765+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":18340}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.767+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.767+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.767+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.767+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.767+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.775+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.775+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.775+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.775+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T01:30:46.775+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.211+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.212+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.212+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.212+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.212+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.212+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.212+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.212+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":19136}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.233+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.234+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.234+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.234+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.234+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.242+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.242+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.242+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.242+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T01:30:53.242+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.500+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.500+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.500+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.500+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.501+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.501+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.501+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.501+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":20160}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.662+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.662+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.662+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.662+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.662+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.974+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.975+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.976+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.976+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.976+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.976+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.982+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.984+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.984+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.984+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.984+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.984+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.984+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.984+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.985+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.985+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.985+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.985+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.985+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.985+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.985+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.985+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.985+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.985+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.985+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.986+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.986+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.986+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.986+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.986+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.986+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.986+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.986+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.986+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.986+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.986+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.986+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.987+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.987+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.987+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.987+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.987+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.987+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.987+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.987+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.987+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.987+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.987+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.988+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.988+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.988+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.988+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.988+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.988+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.988+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.989+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.989+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.989+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.989+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.989+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.989+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.989+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.989+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.989+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.989+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.990+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.990+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.990+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.990+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.990+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.991+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.991+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.991+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:55.991+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.564+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.630+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.637+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.637+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.638+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.644+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.646+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.646+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.697+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.698+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.698+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.698+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.698+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.699+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.699+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.699+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.699+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.748+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.854+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-04T01:30:56.854+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-04T01:30:57.038+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-04T01:30:57.038+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-04T01:30:57.038+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:57.038+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T01:30:57.038+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:57.039+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-04T01:30:57.039+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:57.039+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-04T01:30:57.352+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:30:57.352+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:30:57.352+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:30:58.044+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:30:58.045+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T01:30:58.051+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:30:58.288+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:30:58.510+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:31:05.163+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:31:05.163+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:31:10.521+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:31:10.521+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:31:22.919+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:31:22.919+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:31:27.653+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:31:27.653+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:31:33.811+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:31:34.050+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751563894050863400"}
{"level":"INFO","timestamp":"2025-07-04T01:31:34.051+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_66222100_20250704_013133.png","organName":"矢状开胸","operationID":"test-11200_B_1751563894050863400"}
{"level":"INFO","timestamp":"2025-07-04T01:31:34.051+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751563894050863400","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-04T01:31:34.051+0800","caller":"utils/logger.go:94","msg":"创建轮次数据","轮次数":1,"operationID":"test-11200_B_1751563894050863400"}
{"level":"INFO","timestamp":"2025-07-04T01:31:34.051+0800","caller":"utils/logger.go:94","msg":"设置器官名称","器官":"矢状开胸","operationID":"test-11200_B_1751563894050863400"}
{"level":"INFO","timestamp":"2025-07-04T01:31:34.054+0800","caller":"utils/logger.go:94","msg":"开始计算已完成轮次数","operationID":"test-11200_B_1751563894050863400"}
{"level":"INFO","timestamp":"2025-07-04T01:31:34.054+0800","caller":"utils/logger.go:94","msg":"用户数据更新","userName":"test-11200","mode":"B","organName":"矢状开胸","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751563894050863400"}
{"level":"INFO","timestamp":"2025-07-04T01:31:34.054+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751563894050863400"}
{"level":"INFO","timestamp":"2025-07-04T01:32:00.241+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:32:00.496+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751563920496956800"}
{"level":"INFO","timestamp":"2025-07-04T01:32:00.496+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_49100000_20250704_013159.png","organName":"矢状开胸","operationID":"test-11200_B_1751563920496956800"}
{"level":"INFO","timestamp":"2025-07-04T01:33:21.923+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:33:22.183+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751564002183622800"}
{"level":"INFO","timestamp":"2025-07-04T01:33:22.184+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_56766100_20250704_013321.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751564002183622800"}
{"level":"INFO","timestamp":"2025-07-04T01:34:15.280+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:34:15.878+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751564055878929100"}
{"level":"INFO","timestamp":"2025-07-04T01:34:15.878+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_82069800_20250704_013415.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751564055878929100"}
{"level":"INFO","timestamp":"2025-07-04T01:35:55.990+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T01:36:53.629+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T01:36:53.635+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:36:53.635+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T01:36:53.635+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T01:36:53.641+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:36:54.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:36:54.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:36:54.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:36:54.996+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:36:54.996+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T01:36:54.999+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:36:55.215+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:36:55.401+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-03","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T01:37:55.925+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-04T01:37:55.950+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T01:37:55.950+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-04T01:37:55.950+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-04T01:37:55.950+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-04T01:37:55.950+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-04T01:37:55.950+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-04T01:37:55.971+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T01:37:55.971+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T01:37:55.971+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T01:37:55.972+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T01:37:55.972+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.195+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.195+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.195+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.195+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.195+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.196+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.196+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.196+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":10368}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.201+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.201+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.201+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.201+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.201+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.209+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.209+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.209+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.209+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T13:21:57.209+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.851+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.852+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.852+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.852+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.852+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.852+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.852+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.852+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":30512}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.855+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.855+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.855+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.855+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.855+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.882+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.882+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.882+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.882+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T13:22:10.882+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.669+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.669+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.669+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.669+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.670+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.670+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.670+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.670+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":29860}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.713+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.713+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.713+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.713+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:20.713+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.952+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.952+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.953+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.953+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.953+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.953+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.959+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.961+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.961+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.961+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.961+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.962+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.962+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.962+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.962+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.962+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.962+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.962+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.962+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.962+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.963+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.963+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.963+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.963+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.963+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.963+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.963+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.963+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.963+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.964+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.964+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.964+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.964+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.964+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.964+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.965+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.965+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.965+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.965+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.966+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.966+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.966+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.966+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.966+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.966+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.966+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.966+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.966+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.966+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.967+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.967+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.967+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.967+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.967+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.967+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.967+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.967+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.967+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.967+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.967+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.967+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.968+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.968+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.968+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.968+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.968+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.968+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.968+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.968+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.968+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.969+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.969+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.969+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:21.969+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.675+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.675+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.676+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.676+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.676+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.676+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.676+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.676+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.676+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.747+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.753+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.753+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.825+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.837+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.837+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.837+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.858+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.866+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.867+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-04T13:22:22.872+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:22:23.044+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-04T13:22:23.044+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-04T13:22:23.045+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:23.045+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T13:22:23.045+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:23.045+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-04T13:22:23.045+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:23.045+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-04T13:22:23.583+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:22:23.583+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:22:23.583+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:22:24.226+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:22:24.226+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T13:22:24.231+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:22:24.458+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:22:24.649+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:25:29.867+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:25:29.867+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:25:34.567+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:25:34.567+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:26:02.150+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:26:02.425+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751606762425548200"}
{"level":"INFO","timestamp":"2025-07-04T13:26:02.425+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_56761000_20250704_132602.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751606762425548200"}
{"level":"INFO","timestamp":"2025-07-04T13:26:02.425+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751606762425548200","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-04T13:26:02.426+0800","caller":"utils/logger.go:94","msg":"创建轮次数据","轮次数":1,"operationID":"test-11200_B_1751606762425548200"}
{"level":"INFO","timestamp":"2025-07-04T13:26:02.426+0800","caller":"utils/logger.go:94","msg":"设置器官名称","器官":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751606762425548200"}
{"level":"INFO","timestamp":"2025-07-04T13:26:02.426+0800","caller":"utils/logger.go:94","msg":"开始计算已完成轮次数","operationID":"test-11200_B_1751606762425548200"}
{"level":"INFO","timestamp":"2025-07-04T13:26:02.426+0800","caller":"utils/logger.go:94","msg":"用户数据更新","userName":"test-11200","mode":"B","organName":"腹部第1腰椎水平截面","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751606762425548200"}
{"level":"INFO","timestamp":"2025-07-04T13:26:02.427+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751606762425548200"}
{"level":"INFO","timestamp":"2025-07-04T13:26:28.933+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:26:29.171+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751606789171337800"}
{"level":"INFO","timestamp":"2025-07-04T13:26:29.171+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_14068600_20250704_132628.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751606789171337800"}
{"level":"INFO","timestamp":"2025-07-04T13:27:21.969+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.021+0800","caller":"utils/logger.go:94","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.021+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.021+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.021+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.022+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.022+0800","caller":"utils/logger.go:94","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.100+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.100+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.100+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.100+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.100+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.100+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.100+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.101+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":30556}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.109+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.109+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.109+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.109+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.109+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.122+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.122+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.122+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.122+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T13:31:32.122+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.743+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.744+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.744+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.744+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.744+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.744+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.744+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.744+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":29548}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.748+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.748+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.749+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.749+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.749+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.757+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.757+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.757+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.757+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T13:33:50.757+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.992+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.992+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.992+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.992+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.992+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.992+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.992+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.993+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":25496}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.995+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.995+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.995+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.995+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:33:58.995+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T13:33:59.005+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T13:33:59.005+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T13:33:59.005+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:33:59.005+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T13:33:59.005+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.558+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.558+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.558+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.558+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.558+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.559+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.559+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.559+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":30452}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.645+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.645+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.646+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.646+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:02.646+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.085+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.086+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.087+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.087+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.087+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.087+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.093+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.096+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.096+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.096+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.096+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.096+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.096+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.097+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.097+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.097+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.097+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.097+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.097+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.097+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.097+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.098+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.098+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.098+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.098+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.098+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.098+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.098+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.098+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.098+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.099+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.098+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.099+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.099+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.099+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.099+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.099+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.099+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.099+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.099+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.099+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.100+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.100+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.100+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.100+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.100+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.100+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.101+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.101+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.101+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.101+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.101+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.101+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.101+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.101+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.101+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.101+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.102+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.102+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.102+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.102+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.102+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.102+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.102+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.102+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.103+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.103+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.103+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.103+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.103+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.104+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.104+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.104+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.105+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.691+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.755+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.765+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.765+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.766+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.776+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.782+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.783+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.790+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.796+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.796+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.796+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.796+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.796+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.796+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.797+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.797+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.797+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.898+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-04T13:34:03.898+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-04T13:34:04.037+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-04T13:34:04.037+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-04T13:34:04.037+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:04.037+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T13:34:04.037+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:04.037+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-04T13:34:04.038+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:04.038+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-04T13:34:04.467+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:34:04.467+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:34:04.467+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:34:05.139+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:34:05.139+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T13:34:05.145+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:34:05.389+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:34:05.613+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T13:39:03.104+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T13:44:03.103+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T13:49:03.103+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T13:54:03.103+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T13:59:03.103+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T14:03:10.414+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:03:10.414+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:03:17.928+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:03:17.928+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:03:40.459+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:03:40.680+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751609020680809000"}
{"level":"INFO","timestamp":"2025-07-04T14:03:40.680+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_82562300_20250704_140340.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751609020680809000"}
{"level":"INFO","timestamp":"2025-07-04T14:03:40.680+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751609020680809000","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-04T14:03:40.680+0800","caller":"utils/logger.go:94","msg":"创建轮次数据","轮次数":1,"operationID":"test-11200_B_1751609020680809000"}
{"level":"INFO","timestamp":"2025-07-04T14:03:40.681+0800","caller":"utils/logger.go:94","msg":"设置器官名称","器官":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751609020680809000"}
{"level":"INFO","timestamp":"2025-07-04T14:03:40.683+0800","caller":"utils/logger.go:94","msg":"开始计算已完成轮次数","operationID":"test-11200_B_1751609020680809000"}
{"level":"INFO","timestamp":"2025-07-04T14:03:40.683+0800","caller":"utils/logger.go:94","msg":"用户数据更新","userName":"test-11200","mode":"B","organName":"腹部第1腰椎水平截面","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751609020680809000"}
{"level":"INFO","timestamp":"2025-07-04T14:03:40.683+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751609020680809000"}
{"level":"INFO","timestamp":"2025-07-04T14:04:03.104+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T14:04:07.362+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:04:07.599+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751609047599760500"}
{"level":"INFO","timestamp":"2025-07-04T14:04:07.600+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_77602800_20250704_140407.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751609047599760500"}
{"level":"INFO","timestamp":"2025-07-04T14:06:13.220+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-04T14:06:13.247+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T14:06:13.247+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-04T14:06:13.247+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-04T14:06:13.247+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-04T14:06:13.247+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-04T14:06:13.247+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-04T14:06:13.273+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T14:06:13.273+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T14:06:13.273+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T14:06:13.273+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T14:06:13.274+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.829+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.829+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.829+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.829+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.829+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.830+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.830+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.830+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":28072}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.851+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.851+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.851+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.851+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.851+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.859+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.859+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.859+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.859+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T14:17:04.860+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.812+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.812+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.812+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.812+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.812+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.812+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.812+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.813+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":25760}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.834+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.834+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.834+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.834+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.834+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.860+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.860+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.860+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.860+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T14:17:13.860+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.351+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.353+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.353+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.353+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.353+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.353+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.353+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.353+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":11032}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.383+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.384+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.384+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.384+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.384+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.910+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.910+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.912+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.912+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.912+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.912+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.919+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.923+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.923+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.923+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.923+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.924+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.924+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.924+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.924+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.924+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.924+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.924+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.924+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.924+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.925+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.925+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.925+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.925+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.925+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.925+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.925+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.925+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.926+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.928+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.930+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.928+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.930+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.933+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.934+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.934+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.933+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.934+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.934+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.935+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.936+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.936+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.937+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.937+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.936+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.937+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.938+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.937+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.938+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.938+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.938+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.938+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.938+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.938+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.938+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.939+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.939+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.939+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.940+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.940+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.940+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:19.940+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.571+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.643+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.652+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.653+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.653+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.662+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.662+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.662+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.663+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.663+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.663+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.663+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.663+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.663+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.665+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.672+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.673+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.688+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.902+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-04T14:17:20.902+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-04T14:17:21.092+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-04T14:17:21.092+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-04T14:17:21.093+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:21.093+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T14:17:21.093+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:21.093+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-04T14:17:21.093+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:21.093+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-04T14:17:21.458+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:17:21.458+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:17:21.458+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:17:22.370+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:17:22.371+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T14:17:22.375+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:17:22.552+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:17:22.916+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T14:22:19.940+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T14:27:19.940+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T14:32:19.939+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T14:37:19.939+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T14:42:01.635+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-04T14:42:01.661+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T14:42:01.661+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-04T14:42:01.661+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-04T14:42:01.661+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-04T14:42:01.661+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-04T14:42:01.662+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-04T14:42:01.682+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T14:42:01.682+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T14:42:01.682+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T14:42:01.683+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T14:42:01.683+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.267+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.267+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.267+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.267+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.267+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.267+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.267+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.267+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":29364}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.272+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.272+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.272+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.272+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.272+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.280+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.280+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.280+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.280+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T15:52:14.280+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.048+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.048+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.048+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.048+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.048+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.048+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.048+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.049+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":12852}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.053+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.053+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.053+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.053+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.053+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.062+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.062+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.062+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.062+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T15:52:22.062+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.345+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.345+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.346+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.346+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.346+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.346+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.346+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.346+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":31500}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.422+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.423+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.423+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.423+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.423+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.907+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.908+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.908+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.909+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.909+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.909+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.916+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.918+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.918+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.918+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.918+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.919+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.919+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.919+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.919+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.919+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.919+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.919+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.919+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.919+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.920+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.920+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.920+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.920+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.920+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.920+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.920+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.920+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.920+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.921+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.922+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.922+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.922+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.922+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.922+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.922+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.922+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.922+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.922+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.922+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.922+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.922+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.923+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.923+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.923+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.923+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.923+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.923+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.923+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.923+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.923+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.923+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.923+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.923+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.924+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.924+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.924+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.924+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.924+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.925+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:25.925+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.544+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.562+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.562+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.562+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.562+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.562+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.562+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.562+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.562+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.563+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.607+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.614+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.614+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.615+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.625+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.628+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.628+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.640+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.641+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-04T15:52:26.641+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-04T15:52:27.478+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:52:27.478+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:52:27.478+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.249+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.250+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.250+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.250+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.250+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.250+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.250+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.250+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.323+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.323+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.328+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.583+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:52:28.811+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:52:56.752+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:52:56.752+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:53:04.490+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:53:04.490+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:53:25.971+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:53:26.225+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751615606225783800"}
{"level":"INFO","timestamp":"2025-07-04T15:53:26.226+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_48840200_20250704_155325.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751615606225783800"}
{"level":"INFO","timestamp":"2025-07-04T15:53:26.226+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751615606225783800","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-04T15:53:26.226+0800","caller":"utils/logger.go:94","msg":"创建轮次数据","轮次数":1,"operationID":"test-11200_B_1751615606225783800"}
{"level":"INFO","timestamp":"2025-07-04T15:53:26.226+0800","caller":"utils/logger.go:94","msg":"设置器官名称","器官":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751615606225783800"}
{"level":"INFO","timestamp":"2025-07-04T15:53:26.228+0800","caller":"utils/logger.go:94","msg":"用户数据更新","userName":"test-11200","mode":"B","organName":"腹部第1腰椎水平截面","totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751615606225783800"}
{"level":"INFO","timestamp":"2025-07-04T15:53:26.228+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751615606225783800"}
{"level":"INFO","timestamp":"2025-07-04T15:53:52.598+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:53:52.804+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751615632804283200"}
{"level":"INFO","timestamp":"2025-07-04T15:53:52.804+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_12052000_20250704_155352.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751615632804283200"}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.714+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.714+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.714+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.714+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.714+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.714+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.714+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.715+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":10764}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.719+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.719+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.719+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.719+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.719+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.746+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.746+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.746+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.746+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T15:56:10.746+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.997+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.998+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.998+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.998+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.998+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.998+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-04T15:52:25.346+0800"}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.998+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"31500"}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.998+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":31500}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.998+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":31500}
{"level":"WARN","timestamp":"2025-07-04T15:56:14.998+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":31500,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.999+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":31500}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.999+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.999+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:14.999+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":320}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.029+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.030+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.030+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.030+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.030+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.585+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.586+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.587+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.587+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.587+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.587+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.592+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.595+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.595+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.595+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.595+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.595+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.595+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.595+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.596+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.596+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.596+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.596+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.596+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.596+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.596+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.596+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.596+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.596+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.596+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.597+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.597+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.597+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.597+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.597+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.597+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.597+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.597+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.597+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.597+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.598+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.598+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.598+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.600+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.599+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.600+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.600+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.600+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.600+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.600+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.600+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.600+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.600+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.600+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.600+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.600+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.601+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.601+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.601+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.601+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.601+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.601+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.601+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.602+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.602+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.602+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.602+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.858+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.928+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.937+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.937+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.937+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.945+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.953+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.953+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-04T15:56:15.962+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.233+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.233+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.233+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.233+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.233+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.233+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.234+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.234+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.234+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.342+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.343+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.557+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.557+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.557+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.677+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.677+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.678+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.678+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.678+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.678+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.678+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:16.678+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:56:17.151+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:56:17.151+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T15:56:17.156+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:56:17.382+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:56:17.580+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:56:31.173+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:31.220+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T15:56:31.220+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-04T15:56:31.220+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-04T15:56:31.220+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-04T15:56:31.220+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-04T15:56:31.220+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-04T15:56:31.241+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T15:56:31.241+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T15:56:31.241+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:56:31.241+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T15:56:31.241+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.581+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.581+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.581+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.581+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.581+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.581+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.581+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.581+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":17212}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.603+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.603+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.603+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.603+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.603+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.611+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.611+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.611+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.611+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T15:57:23.611+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.148+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.149+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.149+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.149+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.149+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.149+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.149+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.149+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":9504}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.167+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.168+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.168+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.168+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.168+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.176+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.176+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.176+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.176+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T15:57:32.177+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.511+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.511+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.511+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.511+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.511+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.512+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.512+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.512+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":28416}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.598+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.598+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.598+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.598+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:36.598+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.015+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.016+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.016+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.017+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.017+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.017+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.023+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.025+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.025+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.025+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.025+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.026+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.026+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.026+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.028+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.030+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.030+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.030+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.030+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.031+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.031+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.031+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.031+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.031+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.031+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.031+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.031+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.032+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.032+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.032+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.032+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.032+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.032+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.032+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.032+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.033+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.033+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.033+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.033+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.033+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.033+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.034+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.034+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.034+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.034+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.034+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.034+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.035+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.035+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.035+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.035+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.035+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.035+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.035+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.035+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.035+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.035+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.035+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.035+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.036+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.036+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.036+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.036+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.036+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.036+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.036+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.036+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.036+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.036+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.036+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.037+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.037+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.037+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.037+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.625+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.693+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.695+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.696+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.696+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.696+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.696+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.696+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.696+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.696+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.696+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.703+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.703+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.703+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.714+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.720+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.721+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.730+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.776+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.776+0800","caller":"utils/logger.go:94","msg":"正在测试OCR API连接: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.929+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.929+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:57:37.929+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.116+0800","caller":"utils/logger.go:94","msg":"OCR API连接测试成功: https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition, 状态码: 403"}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.116+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.117+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.117+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.117+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.117+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.117+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.117+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.172+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.172+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.177+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.374+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T15:57:38.582+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-04","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T16:00:20.040+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T16:00:20.040+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T16:00:50.196+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-04T16:00:50.883+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751616050883360700"}
{"level":"INFO","timestamp":"2025-07-04T16:00:50.883+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\test-11200_BTN_B_90505100_20250704_160049.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751616050883360700"}
{"level":"INFO","timestamp":"2025-07-04T16:00:50.883+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751616050883360700","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-04T16:00:50.883+0800","caller":"utils/logger.go:94","msg":"创建轮次数据","轮次数":1,"operationID":"test-11200_B_1751616050883360700"}
{"level":"INFO","timestamp":"2025-07-04T16:00:50.883+0800","caller":"utils/logger.go:94","msg":"设置器官名称","器官":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751616050883360700"}
{"level":"INFO","timestamp":"2025-07-04T16:00:50.884+0800","caller":"utils/logger.go:94","msg":"用户数据更新","userName":"test-11200","mode":"B","organName":"腹部第1腰椎水平截面","totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751616050883360700"}
{"level":"INFO","timestamp":"2025-07-04T16:00:50.884+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751616050883360700"}
{"level":"INFO","timestamp":"2025-07-04T16:02:37.037+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T16:07:37.037+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T16:12:37.037+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T16:17:37.037+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-04T16:20:03.019+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-04T16:20:03.044+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-04T16:20:03.044+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-04T16:20:03.044+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-04T16:20:03.044+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-04T16:20:03.044+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-04T16:20:03.044+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-04T16:20:03.061+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T16:20:03.062+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T16:20:03.062+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T16:20:03.062+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T16:20:03.062+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.947+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.947+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.947+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.947+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.947+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.947+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.947+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.948+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":8124}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.952+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.952+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.952+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.952+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.952+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.960+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.960+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.960+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.960+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-04T16:20:05.960+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
