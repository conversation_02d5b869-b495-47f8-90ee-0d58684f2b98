package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"MagneticOperator/app/models"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

// ViperConfigManager 基于 Viper 的配置管理器
type ViperConfigManager struct {
	mu           sync.RWMutex
	viper        *viper.Viper
	configDir    string
	environment  string
	watchers     []func(*models.AppConfig)
	stopWatching chan bool
}

// NewViperConfigManager 创建新的 Viper 配置管理器
func NewViperConfigManager(configDir string, environment string) *ViperConfigManager {
	v := viper.New()

	// 设置配置文件名和路径
	v.SetConfigName("app_config")
	v.SetConfigType("json")
	v.AddConfigPath(configDir)

	// 设置环境变量前缀
	v.SetEnvPrefix("MAGNETIC_OPERATOR")
	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 设置默认值
	setDefaults(v)

	return &ViperConfigManager{
		viper:        v,
		configDir:    configDir,
		environment:  environment,
		watchers:     make([]func(*models.AppConfig), 0),
		stopWatching: make(chan bool),
	}
}

// setDefaults 设置默认配置值
func setDefaults(v *viper.Viper) {
	// 小程序信息默认值
	v.SetDefault("mp_app_info.appid", "")
	v.SetDefault("mp_app_info.target_page", "pages/p_scan/p_scan")

	// 站点信息默认值
	v.SetDefault("site_info.site_id", "")
	v.SetDefault("site_info.site_name", "")
	v.SetDefault("site_info.site_type", "社区医院")
	v.SetDefault("site_info.parent_org", "")

	// 裁剪设置默认值
	v.SetDefault("crop_settings.top_percent", 0.155)
	v.SetDefault("crop_settings.bottom_percent", 0.051)
	v.SetDefault("crop_settings.left_percent", 0.05)
	v.SetDefault("crop_settings.right_percent", 0.75)
	v.SetDefault("crop_settings.always_on_top", false)

	// 普通窗口设置默认值
	v.SetDefault("normal_windows_setting.top_percent", 0.520)
	v.SetDefault("normal_windows_setting.bottom_percent", 1.080)
	v.SetDefault("normal_windows_setting.left_percent", 1.150)
	v.SetDefault("normal_windows_setting.right_percent", 1.350)
	v.SetDefault("normal_windows_setting.always_on_top", true)

	// 扩展裁剪设置默认值
	v.SetDefault("expanded_crop_settings.top_percent", 0.0)
	v.SetDefault("expanded_crop_settings.bottom_percent", 0.0)
	v.SetDefault("expanded_crop_settings.left_percent", 0.0)
	v.SetDefault("expanded_crop_settings.right_percent", 0.0)
	v.SetDefault("expanded_crop_settings.always_on_top", false)

	// 系统通知默认值
	v.SetDefault("use_system_notification", true)

	// 颜色检测默认值
	v.SetDefault("color_detection.debug_mode", false)
	v.SetDefault("color_detection.save_debug_files", false)

	// 环境配置默认值
	v.SetDefault("environment_config.environment", "development")
	v.SetDefault("environment_config.debug", true)
}

// LoadConfig 加载配置
func (vcm *ViperConfigManager) LoadConfig() (*models.AppConfig, error) {
	vcm.mu.Lock()
	defer vcm.mu.Unlock()

	// 读取配置文件
	if err := vcm.viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// 配置文件不存在，使用默认值
			fmt.Printf("配置文件不存在，使用默认配置: %v\n", err)
		} else {
			// 配置文件存在但读取失败
			return nil, fmt.Errorf("读取配置文件失败: %w", err)
		}
	}

	// 加载环境特定配置
	if vcm.environment != "" {
		envConfigPath := filepath.Join(vcm.configDir, vcm.environment+".json")
		if _, err := os.Stat(envConfigPath); err == nil {
			envViper := viper.New()
			envViper.SetConfigFile(envConfigPath)
			if err := envViper.ReadInConfig(); err == nil {
				// 合并环境配置
				vcm.viper.MergeConfigMap(envViper.AllSettings())
			}
		}
	}

	// 将配置解析到结构体
	var config models.AppConfig
	if err := vcm.viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	return &config, nil
}

// SaveConfig 保存配置
func (vcm *ViperConfigManager) SaveConfig(config *models.AppConfig) error {
	vcm.mu.Lock()
	defer vcm.mu.Unlock()

	// 将结构体转换为 map
	configMap := make(map[string]interface{})
	if err := vcm.viper.Unmarshal(&configMap); err != nil {
		return fmt.Errorf("转换配置失败: %w", err)
	}

	// 更新配置值
	vcm.updateConfigMap(configMap, config)

	// 写入配置文件
	configPath := filepath.Join(vcm.configDir, "app_config.json")
	if err := vcm.viper.WriteConfigAs(configPath); err != nil {
		return fmt.Errorf("保存配置文件失败: %w", err)
	}

	// 通知观察者
	for _, watcher := range vcm.watchers {
		go watcher(config)
	}

	return nil
}

// updateConfigMap 更新配置映射
func (vcm *ViperConfigManager) updateConfigMap(configMap map[string]interface{}, config *models.AppConfig) {
	// 这里可以根据需要实现具体的更新逻辑
	// 由于 Viper 的 Unmarshal 会自动处理，这里主要是为了扩展性
}

// GetString 获取字符串配置值
func (vcm *ViperConfigManager) GetString(key string) string {
	vcm.mu.RLock()
	defer vcm.mu.RUnlock()
	return vcm.viper.GetString(key)
}

// GetBool 获取布尔配置值
func (vcm *ViperConfigManager) GetBool(key string) bool {
	vcm.mu.RLock()
	defer vcm.mu.RUnlock()
	return vcm.viper.GetBool(key)
}

// GetFloat64 获取浮点数配置值
func (vcm *ViperConfigManager) GetFloat64(key string) float64 {
	vcm.mu.RLock()
	defer vcm.mu.RUnlock()
	return vcm.viper.GetFloat64(key)
}

// GetInt 获取整数配置值
func (vcm *ViperConfigManager) GetInt(key string) int {
	vcm.mu.RLock()
	defer vcm.mu.RUnlock()
	return vcm.viper.GetInt(key)
}

// Set 设置配置值
func (vcm *ViperConfigManager) Set(key string, value interface{}) {
	vcm.mu.Lock()
	defer vcm.mu.Unlock()
	vcm.viper.Set(key, value)
}

// AddWatcher 添加配置变化观察者
func (vcm *ViperConfigManager) AddWatcher(watcher func(*models.AppConfig)) {
	vcm.mu.Lock()
	defer vcm.mu.Unlock()
	vcm.watchers = append(vcm.watchers, watcher)
}

// StartWatching 开始监听配置文件变化
func (vcm *ViperConfigManager) StartWatching() {
	vcm.viper.WatchConfig()
	vcm.viper.OnConfigChange(func(e fsnotify.Event) {
		fmt.Printf("配置文件发生变化: %s\n", e.Name)

		// 重新加载配置
		if config, err := vcm.LoadConfig(); err == nil {
			// 通知所有观察者
			for _, watcher := range vcm.watchers {
				go watcher(config)
			}
		} else {
			fmt.Printf("重新加载配置失败: %v\n", err)
		}
	})
}

// StopWatching 停止监听配置文件变化
func (vcm *ViperConfigManager) StopWatching() {
	select {
	case vcm.stopWatching <- true:
	default:
	}
}

// GetConfigDir 获取配置目录
func (vcm *ViperConfigManager) GetConfigDir() string {
	return vcm.configDir
}

// GetEnvironment 获取当前环境
func (vcm *ViperConfigManager) GetEnvironment() string {
	return vcm.environment
}

// IsDebugMode 检查是否为调试模式
func (vcm *ViperConfigManager) IsDebugMode() bool {
	return vcm.GetBool("environment_config.debug") || vcm.GetBool("color_detection.debug_mode")
}

// GetAPIKey 获取 API 密钥
func (vcm *ViperConfigManager) GetAPIKey(service, key string) string {
	return vcm.GetString(fmt.Sprintf("api_keys.%s.%s", service, key))
}

// SetAPIKey 设置 API 密钥
func (vcm *ViperConfigManager) SetAPIKey(service, key, value string) {
	vcm.Set(fmt.Sprintf("api_keys.%s.%s", service, key), value)
}
