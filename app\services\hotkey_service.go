package services

import (
	"fmt"
	"syscall"
	"time"

	"MagneticOperator/app/utils"
)

var (
	user32               = syscall.NewLazyDLL("user32.dll")
	procGetAsyncKeyState = user32.NewProc("GetAsyncKeyState")
	VK_CONTROL           = 0x11
	VK_SHIFT             = 0x10
	VK_A                 = 0x41
	VK_B                 = 0x42
	VK_C                 = 0x43
)

// AppInterface 定义应用接口
type AppInterface interface {
	ProcessScreenshotWorkflow(mode string) (string, error)
	// UploadLatestScreenshot() (string, error) // 已注释，功能由ProcessScreenshotWorkflow替代

	// 任务管理器方法
	SubmitScreenshotTask(taskType TaskType, mode, description string)

	// Wails通知方法
	ShowWailsNotification(notificationType, title, message string, duration int) string
	ShowSuccessNotification(title, message string, duration int) string
	ShowErrorNotification(title, message string, duration int) string
	ShowWarningNotification(title, message string, duration int) string
	ShowInfoNotification(title, message string, duration int) string
	ShowProgressNotification(title, message string, progress int, duration int) string
	UpdateProgressNotification(id string, progress int, message string)
	ShowOCRProcessNotification(title, message string, progress int)
}

// HotkeyService 快捷键服务
type HotkeyService struct {
	app           AppInterface // 引用主应用接口
	isRunning     bool
	stopChan      chan bool
	lastOperation time.Time
	cooldown      time.Duration
}

// NewHotkeyService 创建快捷键服务
func NewHotkeyService(app AppInterface) *HotkeyService {
	return &HotkeyService{
		app:           app,
		isRunning:     false,
		stopChan:      make(chan bool),
		lastOperation: time.Now().Add(-1 * time.Second),
		cooldown:      500 * time.Millisecond,
	}
}

// getAsyncKeyState 检查按键状态
func getAsyncKeyState(vKey int) bool {
	ret, _, _ := procGetAsyncKeyState.Call(uintptr(vKey))
	return ret&0x8000 != 0
}

// StartHotkeyListener 启动快捷键监听
func (hs *HotkeyService) StartHotkeyListener() {
	if hs.isRunning {
		return
	}

	hs.isRunning = true
	utils.LogOperation("启动快捷键监听", "", "")

	go func() {
		for {
			select {
			case <-hs.stopChan:
				return
			default:
				hs.checkHotkeys()
				time.Sleep(50 * time.Millisecond)
			}
		}
	}()
}

// StopHotkeyListener 停止快捷键监听
func (hs *HotkeyService) StopHotkeyListener() {
	if !hs.isRunning {
		return
	}

	hs.isRunning = false
	hs.stopChan <- true
	utils.LogOperation("停止快捷键监听", "", "")
}

// checkHotkeys 检查快捷键
func (hs *HotkeyService) checkHotkeys() {
	// 检查组合键是否按下
	ctrlPressed := getAsyncKeyState(VK_CONTROL)
	shiftPressed := getAsyncKeyState(VK_SHIFT)
	aPressed := getAsyncKeyState(VK_A)
	bPressed := getAsyncKeyState(VK_B)
	cPressed := getAsyncKeyState(VK_C)
	// uPressed := getAsyncKeyState(VK_U) // 已注释，U键功能已移除

	// 检查截图快捷键和模式选择
	if ctrlPressed && shiftPressed {
		// 检查是否在冷却时间内
		if time.Since(hs.lastOperation) < hs.cooldown {
			return
		}

		// if uPressed {
		// 	// 上传最新截图 - 已注释，功能已由A、B、C快捷键的ProcessScreenshotWorkflow替代
		// 	hs.handleUploadLatest()
		// 	return
		// }

		var mode string
		if aPressed {
			mode = "A"
		} else if bPressed {
			mode = "B"
		} else if cPressed {
			mode = "C"
		}

		if mode != "" {
			hs.handleScreenshot(mode)
		}
	}
}

// handleScreenshot 处理截图
func (hs *HotkeyService) handleScreenshot(mode string) {
	hs.lastOperation = time.Now()

	utils.LogOperation(fmt.Sprintf("快捷键截图-模式%s", mode), "", "")

	// 使用任务管理器处理截图任务，实现并发处理
	fmt.Printf("=== 快捷键触发截图任务 ===\n")
	fmt.Printf("模式: %s\n", mode)
	utils.LogInfo(fmt.Sprintf("快捷键触发截图任务 - 模式: %s", mode))

	// 根据模式确定任务类型
	var taskType TaskType
	var description string
	switch mode {
	case "A":
		taskType = TaskTypeScreenshotA
		description = "快捷键A模式截图"
	case "B":
		taskType = TaskTypeScreenshotB
		description = "快捷键B模式截图"
	case "C":
		taskType = TaskTypeScreenshotC
		description = "快捷键C模式截图"
	default:
		fmt.Printf("未知的截图模式: %s\n", mode)
		utils.LogError("未知的截图模式", "", fmt.Errorf("mode: %s", mode))
		return
	}

	// 提交任务到任务管理器进行并发处理
	hs.app.SubmitScreenshotTask(taskType, mode, description)

	fmt.Printf("截图任务已提交到任务管理器: %s\n", description)
	utils.LogInfo(fmt.Sprintf("截图任务已提交到任务管理器: %s", description))
}

// handleUploadLatest 处理上传最新截图
// 注释原因：A、B、C三个截图快捷键都通过ProcessScreenshotWorkflow函数处理，此函数已不再需要
// func (hs *HotkeyService) handleUploadLatest() {
// 	hs.lastOperation = time.Now()
// 	userName := "医生或健康专家"

// 	utils.LogOperation("快捷键上传最新截图", userName, "")

// 	// 获取pic目录下最新的PNG文件
// 	files, err := filepath.Glob("pic/*.png")
// 	if err != nil {
// 		utils.LogError("获取文件列表失败", userName, err)
// 		fmt.Printf("获取文件列表失败: %v\n", err)
// 		return
// 	}

// 	if len(files) == 0 {
// 		fmt.Println("没有找到可上传的图片文件")
// 		return
// 	}

// 	// 按修改时间排序，获取最新的文件
// 	latestFile := files[0]
// 	latestTime, err := os.Stat(latestFile)
// 	if err != nil {
// 		utils.LogError("获取文件信息失败", userName, err)
// 		fmt.Printf("获取文件信息失败: %v\n", err)
// 		return
// 	}

// 	for _, file := range files[1:] {
// 		fileInfo, err := os.Stat(file)
// 		if err != nil {
// 			continue
// 		}
// 		if fileInfo.ModTime().After(latestTime.ModTime()) {
// 			latestFile = file
// 			latestTime = fileInfo
// 		}
// 	}

// 	fmt.Printf("正在上传最新截图: %s\n", latestFile)

// 	// 调用应用的上传方法
// 	picURL, err := hs.app.UploadLatestScreenshot()
// 	if err != nil {
// 		utils.LogError("上传失败", userName, err)
// 		fmt.Printf("上传失败: %v\n", err)
// 		return
// 	}

// 	fmt.Printf("========================================\n")
// 	fmt.Printf("上传成功! 文件URL: \n%s\n", picURL)
// 	fmt.Printf("========================================\n")
// }
