{"level":"INFO","timestamp":"2025-06-30T09:26:12.585+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.585+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.585+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.585+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.585+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.586+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":5,"modified":"2025-06-29T23:59:14.485+0800"}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.586+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"17912"}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.586+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":17912}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.586+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":17912}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.586+0800","caller":"utils/logger.go:96","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.586+0800","caller":"utils/logger.go:96","msg":"执行命令","command":"tasklist /FI \"PID eq 17912\" /NH"}
{"level":"WARN","timestamp":"2025-06-30T09:26:12.611+0800","caller":"utils/logger.go:103","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.611+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":17912}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.611+0800","caller":"utils/logger.go:96","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.611+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.611+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":22804}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.671+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T09:26:12.671+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.816+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.816+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.820+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.822+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.822+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.822+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.822+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.822+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.822+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.822+0800","caller":"utils/logger.go:96","msg":"任务管理器启动","maxConcurrent":3}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.822+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.822+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.822+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.822+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T09:26:13.822+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-30T09:26:14.389+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T09:26:14.447+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-30T09:26:14.450+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T09:26:14.450+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T09:26:14.450+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T09:26:14.453+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-30T09:26:14.454+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-30T09:26:14.454+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-30T09:26:14.480+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:26:17.026+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-30T09:26:17.026+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T09:26:17.026+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-30T09:26:17.026+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-30T09:26:17.401+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:26:17.401+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:26:17.401+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:26:20.141+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:26:20.141+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-30T09:26:20.147+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:26:22.640+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:26:25.034+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:27:21.646+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T09:27:21.646+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:27:24.378+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-30T09:27:24.378+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:27:26.786+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:27:26.786+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:27:27.916+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:27:30.393+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:31:13.822+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.124+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.127+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.129+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.135+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.135+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.135+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.135+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.135+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.135+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.135+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.135+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.135+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.140+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.140+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:35:59.140+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.398+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.398+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.398+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.398+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.398+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.398+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":5,"modified":"2025-06-30T09:26:12.611+0800"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.398+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"22804"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.398+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":22804}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.398+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":22804}
{"level":"WARN","timestamp":"2025-06-30T09:36:01.398+0800","caller":"utils/logger.go:103","msg":"查找进程失败","pid":22804,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.399+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":22804}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.399+0800","caller":"utils/logger.go:96","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.399+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.399+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":22740}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.557+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.557+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.974+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.974+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.981+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.983+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.983+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.983+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.983+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.983+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.983+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.983+0800","caller":"utils/logger.go:96","msg":"任务管理器启动","maxConcurrent":3}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.983+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.983+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.983+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.984+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T09:36:01.984+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-30T09:36:02.146+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T09:36:02.205+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-30T09:36:02.209+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T09:36:02.209+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T09:36:02.209+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T09:36:02.210+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-30T09:36:02.211+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-30T09:36:02.211+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-30T09:36:02.221+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:36:04.861+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-30T09:36:04.861+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T09:36:04.862+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-30T09:36:04.862+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-30T09:36:04.996+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:36:04.996+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:36:04.996+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:36:07.833+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:36:07.833+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-30T09:36:07.841+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:36:10.093+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:36:12.531+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T09:38:39.279+0800","caller":"utils/logger.go:96","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-06-30T09:38:39.279+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T09:38:39.279+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T09:38:39.279+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T09:38:39.279+0800","caller":"utils/logger.go:96","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-06-30T10:03:36.670+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T10:03:36.670+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T10:03:36.670+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T10:03:36.670+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T10:03:36.670+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:03:36.671+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T10:03:36.671+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:03:36.671+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":9616}
{"level":"INFO","timestamp":"2025-06-30T10:03:36.806+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T10:03:36.807+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.238+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.239+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.245+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.248+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.248+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.248+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.248+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.248+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.248+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.248+0800","caller":"utils/logger.go:96","msg":"任务管理器启动","maxConcurrent":3}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.248+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.248+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.248+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.248+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.248+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.755+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.813+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.816+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.816+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.816+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.819+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.820+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.820+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-30T10:03:37.827+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:03:40.510+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-30T10:03:40.510+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T10:03:40.511+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-30T10:03:40.511+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-30T10:03:40.772+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:03:40.772+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:03:40.772+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:03:44.077+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:03:44.077+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-30T10:03:44.083+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:03:46.413+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:03:48.840+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:03:55.105+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T10:03:55.105+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:03:57.973+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-30T10:03:57.973+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:04:00.363+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:04:00.364+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:04:00.764+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:04:03.301+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:04:55.957+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T10:04:55.957+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:04:58.738+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-30T10:04:58.738+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:05:01.084+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:05:01.084+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:05:01.457+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:05:03.877+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:08:37.248+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.241+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":4,"modified":"2025-06-30T10:03:36.671+0800"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"9616"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":9616}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":9616}
{"level":"WARN","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:103","msg":"查找进程失败","pid":9616,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":9616}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:96","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.242+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":7872}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.428+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.428+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.789+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.789+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.794+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.796+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.796+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.796+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.796+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.796+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.796+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.796+0800","caller":"utils/logger.go:96","msg":"任务管理器启动","maxConcurrent":3}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.796+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.796+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.796+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.796+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.796+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-30T10:11:38.979+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T10:11:39.040+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-30T10:11:39.044+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T10:11:39.044+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T10:11:39.044+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T10:11:39.046+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-30T10:11:39.047+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-30T10:11:39.047+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-30T10:11:39.053+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:11:41.924+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:11:41.924+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:11:41.925+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:11:41.954+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-30T10:11:41.954+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T10:11:41.954+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-30T10:11:41.954+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-30T10:11:44.727+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:11:44.727+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-30T10:11:44.734+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:11:47.204+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:11:50.610+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:16:38.797+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-06-30T10:21:38.797+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-06-30T10:26:38.797+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-06-30T10:31:11.920+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T10:31:11.924+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-30T10:31:11.925+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-30T10:31:11.925+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-30T10:31:11.933+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T10:31:11.933+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T10:31:11.933+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T10:31:11.943+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:33:55.501+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-06-30T10:33:59.192+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:33:59.192+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:33:59.192+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:33:59.800+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:33:59.801+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-30T10:33:59.805+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:34:00.032+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:34:00.255+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:36:38.797+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-06-30T10:37:48.010+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-30T10:37:48.058+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T10:37:48.058+0800","caller":"utils/logger.go:96","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-06-30T10:37:48.058+0800","caller":"utils/logger.go:96","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-06-30T10:37:48.058+0800","caller":"utils/logger.go:96","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-06-30T10:37:48.058+0800","caller":"utils/logger.go:96","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-06-30T10:37:48.080+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:37:48.080+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T10:37:48.080+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:44:50.693+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T10:44:50.693+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T10:44:50.693+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T10:44:50.693+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T10:44:50.693+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T10:44:50.693+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T10:44:50.693+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:44:50.693+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":5640}
{"level":"INFO","timestamp":"2025-06-30T10:44:50.744+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T10:44:50.745+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.174+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.174+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.178+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.180+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.180+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.180+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.180+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.180+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.180+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.180+0800","caller":"utils/logger.go:96","msg":"任务管理器启动","maxConcurrent":3}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.180+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.180+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.180+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.181+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.181+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.181+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.721+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.782+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.785+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.785+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.785+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.787+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.788+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.788+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-30T10:44:51.798+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:44:54.317+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-30T10:44:54.317+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T10:44:54.317+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-30T10:44:54.317+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-30T10:44:54.848+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:44:54.848+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:44:54.848+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:44:58.253+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:44:58.253+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-30T10:44:58.259+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:45:00.820+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:45:03.206+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:46:40.814+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T10:46:40.814+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:46:43.671+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-30T10:46:43.671+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:46:46.145+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:46:46.145+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:46:46.637+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:46:49.021+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T10:49:51.181+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-06-30T10:54:21.284+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-30T10:54:21.319+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T10:54:21.319+0800","caller":"utils/logger.go:96","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-06-30T10:54:21.319+0800","caller":"utils/logger.go:96","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-06-30T10:54:21.319+0800","caller":"utils/logger.go:96","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-06-30T10:54:21.319+0800","caller":"utils/logger.go:96","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-06-30T10:54:21.334+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T10:54:21.334+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T10:54:21.334+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.306+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.306+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.306+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.306+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.306+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.306+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.306+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.306+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":5592}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.352+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.352+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.352+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.352+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.352+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.836+0800","caller":"utils/logger.go:96","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.836+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.836+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.836+0800","caller":"utils/logger.go:96","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.836+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.843+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.844+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.844+0800","caller":"utils/logger.go:96","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.844+0800","caller":"utils/logger.go:96","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.844+0800","caller":"utils/logger.go:96","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.844+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.844+0800","caller":"utils/logger.go:96","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.844+0800","caller":"utils/logger.go:96","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.844+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.844+0800","caller":"utils/logger.go:96","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.844+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:23.845+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:24.013+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T11:20:24.072+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-30T11:20:24.078+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T11:20:24.078+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T11:20:24.078+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T11:20:24.091+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-30T11:20:24.095+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-30T11:20:24.095+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-30T11:20:24.102+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.253+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.253+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.253+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.275+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.275+0800","caller":"utils/logger.go:96","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.275+0800","caller":"utils/logger.go:96","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.275+0800","caller":"utils/logger.go:96","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.275+0800","caller":"utils/logger.go:96","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.275+0800","caller":"utils/logger.go:96","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.275+0800","caller":"utils/logger.go:96","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.275+0800","caller":"utils/logger.go:96","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.275+0800","caller":"utils/logger.go:96","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.275+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.275+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:27.275+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-30T11:20:32.291+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:20:32.291+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-30T11:20:32.299+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:20:34.988+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:20:37.639+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.294+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.294+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.294+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.294+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.294+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.294+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":4,"modified":"2025-06-30T11:20:23.306+0800"}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.294+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"5592"}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.294+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":5592}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.294+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":5592}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.294+0800","caller":"utils/logger.go:96","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.294+0800","caller":"utils/logger.go:96","msg":"执行命令","command":"tasklist /FI \"PID eq 5592\" /NH"}
{"level":"WARN","timestamp":"2025-06-30T11:21:53.976+0800","caller":"utils/logger.go:103","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.976+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":5592}
{"level":"WARN","timestamp":"2025-06-30T11:21:53.976+0800","caller":"utils/logger.go:103","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
{"level":"INFO","timestamp":"2025-06-30T11:21:53.978+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"ERROR","timestamp":"2025-06-30T11:21:53.978+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-30T11:21:54.079+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":5592}
{"level":"INFO","timestamp":"2025-06-30T11:21:54.079+0800","caller":"utils/logger.go:96","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-06-30T11:21:54.079+0800","caller":"utils/logger.go:96","msg":"执行命令","command":"tasklist /FI \"PID eq 5592\" /NH"}
{"level":"WARN","timestamp":"2025-06-30T11:21:54.749+0800","caller":"utils/logger.go:103","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"WARN","timestamp":"2025-06-30T11:21:54.749+0800","caller":"utils/logger.go:103","msg":"单实例检查失败，程序退出"}
{"level":"INFO","timestamp":"2025-06-30T11:22:02.251+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:02.290+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:22:02.290+0800","caller":"utils/logger.go:96","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-06-30T11:22:02.290+0800","caller":"utils/logger.go:96","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-06-30T11:22:02.290+0800","caller":"utils/logger.go:96","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-06-30T11:22:02.290+0800","caller":"utils/logger.go:96","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-06-30T11:22:02.311+0800","caller":"utils/logger.go:96","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-06-30T11:22:02.311+0800","caller":"utils/logger.go:96","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-06-30T11:22:02.311+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:02.311+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T11:22:02.311+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.265+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.265+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.265+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.265+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.265+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.265+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.265+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.266+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":29124}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.332+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.333+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.333+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.333+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.333+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.775+0800","caller":"utils/logger.go:96","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.776+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.776+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.776+0800","caller":"utils/logger.go:96","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.776+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.783+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.787+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.787+0800","caller":"utils/logger.go:96","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.788+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.789+0800","caller":"utils/logger.go:96","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.789+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.789+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:44.789+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:45.320+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T11:22:45.384+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-30T11:22:45.393+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T11:22:45.393+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T11:22:45.393+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T11:22:45.403+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-30T11:22:45.408+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-30T11:22:45.408+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-30T11:22:45.411+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.147+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.147+0800","caller":"utils/logger.go:96","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.148+0800","caller":"utils/logger.go:96","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.148+0800","caller":"utils/logger.go:96","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.148+0800","caller":"utils/logger.go:96","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.148+0800","caller":"utils/logger.go:96","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.148+0800","caller":"utils/logger.go:96","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.148+0800","caller":"utils/logger.go:96","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.148+0800","caller":"utils/logger.go:96","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.148+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.148+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.148+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.499+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.499+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:22:48.499+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:22:52.250+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:22:52.250+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-30T11:22:52.257+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:22:55.007+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:22:57.511+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:24:10.190+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:24:10.190+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:24:12.868+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-30T11:24:12.868+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:24:15.423+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:24:15.423+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:24:15.830+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:24:18.161+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:27:44.789+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-06-30T11:29:25.848+0800","caller":"utils/logger.go:96","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-06-30T11:29:25.848+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:29:25.848+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T11:29:25.848+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-30T11:29:25.848+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:29:25.848+0800","caller":"utils/logger.go:96","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.799+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.799+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.799+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.799+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.799+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.799+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.799+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.799+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":25680}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.827+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.827+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.827+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.827+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:05.827+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.239+0800","caller":"utils/logger.go:96","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.240+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.240+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.240+0800","caller":"utils/logger.go:96","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.240+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.246+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.249+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.249+0800","caller":"utils/logger.go:96","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.249+0800","caller":"utils/logger.go:96","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.249+0800","caller":"utils/logger.go:96","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.250+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.251+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.251+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.802+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.866+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.874+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.875+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.875+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.884+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.890+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.890+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-30T11:32:06.893+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.429+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.429+0800","caller":"utils/logger.go:96","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.429+0800","caller":"utils/logger.go:96","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.429+0800","caller":"utils/logger.go:96","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.429+0800","caller":"utils/logger.go:96","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.429+0800","caller":"utils/logger.go:96","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.429+0800","caller":"utils/logger.go:96","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.429+0800","caller":"utils/logger.go:96","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.429+0800","caller":"utils/logger.go:96","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.429+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.429+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.429+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.571+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.571+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:09.571+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:12.719+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:12.719+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-30T11:32:12.725+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:15.383+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:17.800+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:30.262+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:32:30.262+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-06-30T11:32:30.262+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-06-30T11:32:30.262+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-06-30T11:32:30.262+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:32.699+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-06-30T11:32:32.699+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-06-30T11:32:32.699+0800","caller":"utils/logger.go:96","msg":"任务创建成功","taskID":"screenshot_B_1751254352699581100_暂无候检者","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-06-30T11:32:32.699+0800","caller":"utils/logger.go:96","msg":"任务提交成功","taskID":"screenshot_B_1751254352699581100_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","priority":2}
{"level":"INFO","timestamp":"2025-06-30T11:32:32.699+0800","caller":"utils/logger.go:96","msg":"开始处理任务","workerID":1,"taskID":"screenshot_B_1751254352699581100_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-06-30T11:32:32.700+0800","caller":"utils/logger.go:96","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751254352699581100_暂无候检者, User: 暂无候检者, Round: 0"}
{"level":"INFO","timestamp":"2025-06-30T11:32:32.700+0800","caller":"utils/logger.go:96","msg":"开始处理任务","taskID":"screenshot_B_1751254352699581100_暂无候检者","taskType":"screenshot_B","workerID":1}
{"level":"INFO","timestamp":"2025-06-30T11:32:32.700+0800","caller":"utils/logger.go:96","msg":"执行任务处理器","taskID":"screenshot_B_1751254352699581100_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-06-30T11:32:32.700+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"执行截图任务-B","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:32:32.700+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-06-30T11:32:32.700+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:35.017+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-30T11:32:35.017+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:37.460+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:37.460+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:37.890+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:40.187+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:40.801+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:32:40.801+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-06-30T11:32:40.801+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-06-30T11:32:40.802+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-06-30T11:32:40.802+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:43.267+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-06-30T11:32:43.267+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-06-30T11:32:43.267+0800","caller":"utils/logger.go:96","msg":"任务创建成功","taskID":"screenshot_C_1751254363267327500_暂无候检者","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-06-30T11:32:43.267+0800","caller":"utils/logger.go:96","msg":"任务提交成功","taskID":"screenshot_C_1751254363267327500_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","priority":2}
{"level":"INFO","timestamp":"2025-06-30T11:32:43.267+0800","caller":"utils/logger.go:96","msg":"开始处理任务","workerID":2,"taskID":"screenshot_C_1751254363267327500_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-06-30T11:32:43.267+0800","caller":"utils/logger.go:96","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751254363267327500_暂无候检者, User: 暂无候检者, Round: 0"}
{"level":"INFO","timestamp":"2025-06-30T11:32:43.267+0800","caller":"utils/logger.go:96","msg":"开始处理任务","taskID":"screenshot_C_1751254363267327500_暂无候检者","taskType":"screenshot_C","workerID":2}
{"level":"INFO","timestamp":"2025-06-30T11:32:43.267+0800","caller":"utils/logger.go:96","msg":"执行任务处理器","taskID":"screenshot_C_1751254363267327500_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-06-30T11:32:43.267+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-06-30T11:32:43.267+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"执行截图任务-C","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:32:43.267+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:45.586+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-30T11:32:45.586+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:50.211+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:50.211+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:50.574+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:32:52.855+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:33:06.108+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:33:06.108+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-06-30T11:33:06.108+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-06-30T11:33:06.108+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-06-30T11:33:06.108+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:33:09.192+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-06-30T11:33:09.192+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-06-30T11:33:09.192+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"暂无候检者","error":"task screenshot_B for user 暂无候检者 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:384\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-06-30T11:33:09.193+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-06-30T11:33:13.693+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"截图任务完成-B","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:33:13.693+0800","caller":"utils/logger.go:96","msg":"任务执行成功","taskID":"screenshot_B_1751254352699581100_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-06-30T11:33:13.693+0800","caller":"utils/logger.go:96","msg":"任务执行完成","taskID":"screenshot_B_1751254352699581100_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","duration":40.9940514,"workerID":1}
{"level":"INFO","timestamp":"2025-06-30T11:33:13.693+0800","caller":"utils/logger.go:96","msg":"任务执行成功 - TaskID: screenshot_B_1751254352699581100_暂无候检者, Type: screenshot_B, Worker: 1, Duration: 40.9940514s"}
{"level":"INFO","timestamp":"2025-06-30T11:33:13.693+0800","caller":"utils/logger.go:96","msg":"发送任务完成事件","taskID":"screenshot_B_1751254352699581100_暂无候检者","status":"completed"}
{"level":"INFO","timestamp":"2025-06-30T11:33:13.693+0800","caller":"utils/logger.go:96","msg":"收到任务完成通知 - 任务ID: screenshot_B_1751254352699581100_暂无候检者, 状态: completed"}
{"level":"INFO","timestamp":"2025-06-30T11:33:13.694+0800","caller":"utils/logger.go:96","msg":"任务执行成功，准备发送成功通知"}
{"level":"INFO","timestamp":"2025-06-30T11:33:13.694+0800","caller":"utils/logger.go:96","msg":"准备发送Toast通知 - 类型: success, 标题: 任务完成, 消息: 截图任务已完成"}
{"level":"INFO","timestamp":"2025-06-30T11:33:13.694+0800","caller":"utils/logger.go:96","msg":"Toast通知已发送到前端: 任务完成 - 截图任务已完成"}
{"level":"INFO","timestamp":"2025-06-30T11:33:16.529+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:33:16.529+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-06-30T11:33:16.529+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-06-30T11:33:16.529+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-06-30T11:33:16.529+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:33:19.212+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-06-30T11:33:19.212+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-06-30T11:33:19.212+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"暂无候检者","error":"task screenshot_C for user 暂无候检者 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:384\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-06-30T11:33:19.212+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-06-30T11:33:28.299+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"截图任务完成-C","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:33:28.300+0800","caller":"utils/logger.go:96","msg":"任务执行成功","taskID":"screenshot_C_1751254363267327500_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-06-30T11:33:28.300+0800","caller":"utils/logger.go:96","msg":"任务执行完成","taskID":"screenshot_C_1751254363267327500_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","duration":45.0327922,"workerID":2}
{"level":"INFO","timestamp":"2025-06-30T11:33:28.300+0800","caller":"utils/logger.go:96","msg":"任务执行成功 - TaskID: screenshot_C_1751254363267327500_暂无候检者, Type: screenshot_C, Worker: 2, Duration: 45.0327922s"}
{"level":"INFO","timestamp":"2025-06-30T11:33:28.300+0800","caller":"utils/logger.go:96","msg":"发送任务完成事件","taskID":"screenshot_C_1751254363267327500_暂无候检者","status":"completed"}
{"level":"INFO","timestamp":"2025-06-30T11:33:28.300+0800","caller":"utils/logger.go:96","msg":"收到任务完成通知 - 任务ID: screenshot_C_1751254363267327500_暂无候检者, 状态: completed"}
{"level":"INFO","timestamp":"2025-06-30T11:33:28.300+0800","caller":"utils/logger.go:96","msg":"任务执行成功，准备发送成功通知"}
{"level":"INFO","timestamp":"2025-06-30T11:33:28.300+0800","caller":"utils/logger.go:96","msg":"准备发送Toast通知 - 类型: success, 标题: 任务完成, 消息: 截图任务已完成"}
{"level":"INFO","timestamp":"2025-06-30T11:33:28.300+0800","caller":"utils/logger.go:96","msg":"Toast通知已发送到前端: 任务完成 - 截图任务已完成"}
{"level":"INFO","timestamp":"2025-06-30T11:35:03.733+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:35:03.733+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-06-30T11:35:03.733+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-06-30T11:35:03.733+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-06-30T11:35:03.733+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:06.405+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-06-30T11:35:06.405+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-06-30T11:35:06.405+0800","caller":"utils/logger.go:96","msg":"任务创建成功","taskID":"screenshot_B_1751254506405661400_暂无候检者","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-06-30T11:35:06.405+0800","caller":"utils/logger.go:96","msg":"任务提交成功","taskID":"screenshot_B_1751254506405661400_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","priority":2}
{"level":"INFO","timestamp":"2025-06-30T11:35:06.405+0800","caller":"utils/logger.go:96","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751254506405661400_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-06-30T11:35:06.405+0800","caller":"utils/logger.go:96","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751254506405661400_暂无候检者, User: 暂无候检者, Round: 0"}
{"level":"INFO","timestamp":"2025-06-30T11:35:06.405+0800","caller":"utils/logger.go:96","msg":"开始处理任务","taskID":"screenshot_B_1751254506405661400_暂无候检者","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-06-30T11:35:06.406+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-06-30T11:35:06.406+0800","caller":"utils/logger.go:96","msg":"执行任务处理器","taskID":"screenshot_B_1751254506405661400_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-06-30T11:35:06.406+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"执行截图任务-B","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:35:06.406+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:09.177+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-30T11:35:09.177+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:10.632+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:35:10.632+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-06-30T11:35:10.632+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-06-30T11:35:10.632+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-06-30T11:35:10.632+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:11.625+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:11.625+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:12.033+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:12.969+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-06-30T11:35:12.969+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-06-30T11:35:12.969+0800","caller":"utils/logger.go:96","msg":"任务创建成功","taskID":"screenshot_C_1751254512969410800_暂无候检者","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-06-30T11:35:12.969+0800","caller":"utils/logger.go:96","msg":"任务提交成功","taskID":"screenshot_C_1751254512969410800_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","priority":2}
{"level":"INFO","timestamp":"2025-06-30T11:35:12.969+0800","caller":"utils/logger.go:96","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751254512969410800_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-06-30T11:35:12.969+0800","caller":"utils/logger.go:96","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751254512969410800_暂无候检者, User: 暂无候检者, Round: 0"}
{"level":"INFO","timestamp":"2025-06-30T11:35:12.969+0800","caller":"utils/logger.go:96","msg":"开始处理任务","taskID":"screenshot_C_1751254512969410800_暂无候检者","taskType":"screenshot_C","workerID":1}
{"level":"INFO","timestamp":"2025-06-30T11:35:12.969+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-06-30T11:35:12.969+0800","caller":"utils/logger.go:96","msg":"执行任务处理器","taskID":"screenshot_C_1751254512969410800_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-06-30T11:35:12.969+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"执行截图任务-C","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:35:12.969+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:14.409+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:15.288+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-30T11:35:15.288+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:17.785+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:17.785+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:18.437+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:20.778+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:27.967+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:35:27.967+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-06-30T11:35:27.967+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-06-30T11:35:27.967+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-06-30T11:35:27.967+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:31.599+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-06-30T11:35:31.599+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-06-30T11:35:31.599+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"暂无候检者","error":"task screenshot_B for user 暂无候检者 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:384\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-06-30T11:35:31.599+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-06-30T11:35:31.801+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:35:31.801+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-06-30T11:35:31.801+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-06-30T11:35:31.801+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-06-30T11:35:31.801+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T11:35:34.628+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-06-30T11:35:34.628+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-06-30T11:35:34.628+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"暂无候检者","error":"task screenshot_C for user 暂无候检者 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:384\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-06-30T11:35:34.629+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-06-30T11:35:47.707+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"截图任务完成-B","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:35:47.707+0800","caller":"utils/logger.go:96","msg":"任务执行成功","taskID":"screenshot_B_1751254506405661400_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-06-30T11:35:47.707+0800","caller":"utils/logger.go:96","msg":"任务执行完成","taskID":"screenshot_B_1751254506405661400_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","duration":41.302097,"workerID":0}
{"level":"INFO","timestamp":"2025-06-30T11:35:47.707+0800","caller":"utils/logger.go:96","msg":"任务执行成功 - TaskID: screenshot_B_1751254506405661400_暂无候检者, Type: screenshot_B, Worker: 0, Duration: 41.302097s"}
{"level":"INFO","timestamp":"2025-06-30T11:35:47.707+0800","caller":"utils/logger.go:96","msg":"发送任务完成事件","taskID":"screenshot_B_1751254506405661400_暂无候检者","status":"completed"}
{"level":"INFO","timestamp":"2025-06-30T11:35:47.707+0800","caller":"utils/logger.go:96","msg":"收到任务完成通知 - 任务ID: screenshot_B_1751254506405661400_暂无候检者, 状态: completed"}
{"level":"INFO","timestamp":"2025-06-30T11:35:47.707+0800","caller":"utils/logger.go:96","msg":"任务执行成功，准备发送成功通知"}
{"level":"INFO","timestamp":"2025-06-30T11:35:47.707+0800","caller":"utils/logger.go:96","msg":"准备发送Toast通知 - 类型: success, 标题: 任务完成, 消息: 截图任务已完成"}
{"level":"INFO","timestamp":"2025-06-30T11:35:47.708+0800","caller":"utils/logger.go:96","msg":"Toast通知已发送到前端: 任务完成 - 截图任务已完成"}
{"level":"INFO","timestamp":"2025-06-30T11:35:53.435+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"截图任务完成-C","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T11:35:53.435+0800","caller":"utils/logger.go:96","msg":"任务执行成功","taskID":"screenshot_C_1751254512969410800_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-06-30T11:35:53.435+0800","caller":"utils/logger.go:96","msg":"任务执行完成","taskID":"screenshot_C_1751254512969410800_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","duration":40.4664586,"workerID":1}
{"level":"INFO","timestamp":"2025-06-30T11:35:53.435+0800","caller":"utils/logger.go:96","msg":"任务执行成功 - TaskID: screenshot_C_1751254512969410800_暂无候检者, Type: screenshot_C, Worker: 1, Duration: 40.4664586s"}
{"level":"INFO","timestamp":"2025-06-30T11:35:53.435+0800","caller":"utils/logger.go:96","msg":"发送任务完成事件","taskID":"screenshot_C_1751254512969410800_暂无候检者","status":"completed"}
{"level":"INFO","timestamp":"2025-06-30T11:35:53.435+0800","caller":"utils/logger.go:96","msg":"收到任务完成通知 - 任务ID: screenshot_C_1751254512969410800_暂无候检者, 状态: completed"}
{"level":"INFO","timestamp":"2025-06-30T11:35:53.436+0800","caller":"utils/logger.go:96","msg":"任务执行成功，准备发送成功通知"}
{"level":"INFO","timestamp":"2025-06-30T11:35:53.436+0800","caller":"utils/logger.go:96","msg":"准备发送Toast通知 - 类型: success, 标题: 任务完成, 消息: 截图任务已完成"}
{"level":"INFO","timestamp":"2025-06-30T11:35:53.436+0800","caller":"utils/logger.go:96","msg":"Toast通知已发送到前端: 任务完成 - 截图任务已完成"}
{"level":"INFO","timestamp":"2025-06-30T11:37:06.250+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":4}
{"level":"INFO","timestamp":"2025-06-30T11:42:06.250+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":4}
{"level":"INFO","timestamp":"2025-06-30T11:47:06.250+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":4}
{"level":"INFO","timestamp":"2025-06-30T11:52:06.250+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":4}
{"level":"INFO","timestamp":"2025-06-30T11:56:23.525+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-30T11:56:23.530+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-30T11:56:23.532+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-30T11:56:23.532+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-30T11:56:23.532+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-30T11:56:23.532+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-30T11:56:23.532+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-30T11:56:23.546+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T12:09:17.501+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":4}
{"level":"INFO","timestamp":"2025-06-30T12:09:21.493+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T12:09:21.495+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T12:09:21.505+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T12:09:22.391+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T12:09:22.392+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-30T12:09:22.688+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T12:09:24.043+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T12:09:24.545+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-30","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T12:12:06.250+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":4}
{"level":"INFO","timestamp":"2025-06-30T12:17:06.250+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":4}
{"level":"INFO","timestamp":"2025-06-30T12:22:06.250+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":4}
{"level":"INFO","timestamp":"2025-06-30T12:27:06.250+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":4}
{"level":"INFO","timestamp":"2025-06-30T12:32:06.250+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":4}
{"level":"INFO","timestamp":"2025-06-30T12:37:06.250+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-06-30T12:37:54.221+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-30T12:37:54.265+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T12:37:54.265+0800","caller":"utils/logger.go:96","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-06-30T12:37:54.265+0800","caller":"utils/logger.go:96","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-06-30T12:37:54.265+0800","caller":"utils/logger.go:96","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-06-30T12:37:54.265+0800","caller":"utils/logger.go:96","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-06-30T12:37:54.289+0800","caller":"utils/logger.go:96","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-06-30T12:37:54.289+0800","caller":"utils/logger.go:96","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-06-30T12:37:54.289+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-30T12:37:54.289+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-30T12:37:54.289+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
