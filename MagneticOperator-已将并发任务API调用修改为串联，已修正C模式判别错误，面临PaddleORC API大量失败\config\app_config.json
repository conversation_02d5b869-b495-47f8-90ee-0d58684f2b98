{"mp_app_info": {"appid": "wxd70aa4b433ef1843", "target_page": "pages/p_scan/p_scan"}, "site_info": {"site_id": "YL-BJ-TZ-001", "site_name": "北京市通州区潞城镇潞城社区卫生服务中心", "site_type": "社区医院", "parent_org": "", "location": {"province": "", "city": "", "district": "", "address": "北京市通州区潞城镇潞城社区卫生服务中心"}, "contact": {"manager": "李医生", "phone": "13800138002"}}, "crop_settings": {"top_percent": 0.155, "bottom_percent": 0.051, "left_percent": 0.05, "right_percent": 0.75}, "normal_windows_setting": {"top_percent": 0.52, "bottom_percent": 1.08, "left_percent": 1.15, "right_percent": 1.35, "always_on_top": true}, "api_keys": {"ocr": {"api_url": "https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition", "table_api_url": "", "token": "4d377e625b3f2d11d1fe52b86616237bb2fefb06"}, "coze": {"token": "pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0", "workflow_id_post_pic": "7496900622433812531", "workflow_id_post_registration": "7501566019660939279", "workflow_id_user_info": "7501680491335614516", "space_id": "7331689003143544832", "app_id": "7496871719090077733"}, "cloud_function": {"registrations_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice", "screenshot_records_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/screenshot-records/createOrUpdateScreenshotRecord", "siteInfoByDeviceMAC_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC", "mark_patient_completed_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/markPatientCompleted"}}, "concurrency": {"max_workers": 1, "rate_limit": 1, "rate_burst": 1, "serial_mode": true, "queue_monitoring": {"warning_threshold": 0.8, "alert_threshold": 0.95}, "retry": {"max_attempts": 3, "initial_delay_ms": 2000, "max_delay_ms": 15000, "backoff_multiplier": 2.0}}, "device_info": {"mac_address": "00:15:5d:ed:4a:58", "device_name": ""}, "user_info": {"name": "", "birth": "", "id_number": ""}, "use_system_notification": true, "color_detection": {"debug_mode": false, "save_debug_files": false}}