/**
 * 异步操作错误处理装饰器和工具函数
 */

import errorHandler from './errorHandler.js'

/**
 * 异步操作错误处理装饰器
 * @param {string} context 错误上下文
 * @param {Object} options 处理选项
 */
export function withErrorHandling(context = '', options = {}) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function(...args) {
      try {
        const result = await originalMethod.apply(this, args)
        return result
      } catch (error) {
        const errorInfo = errorHandler.handleError(error, `${context}.${propertyKey}`, options)
        
        // 如果设置了重试，尝试重试
        if (options.retry && errorInfo.isRetryable) {
          return await errorHandler.retry(
            () => originalMethod.apply(this, args),
            options.retry
          )
        }
        
        throw errorInfo
      }
    }
    
    return descriptor
  }
}

/**
 * 简化的错误处理函数
 * @param {Function} fn 要执行的异步函数
 * @param {string} context 错误上下文
 * @param {Object} options 处理选项
 */
export async function safeAsync(fn, context = '', options = {}) {
  try {
    return await fn()
  } catch (error) {
    const errorInfo = errorHandler.handleError(error, context, options)
    
    if (options.retry && errorInfo.isRetryable) {
      return await errorHandler.retry(fn, options.retry)
    }
    
    if (options.throwError !== false) {
      throw errorInfo
    }
    
    return options.defaultValue
  }
}

/**
 * 创建带错误处理的异步函数包装器
 * @param {Function} fn 原始函数
 * @param {string} context 错误上下文
 * @param {Object} options 处理选项
 */
export function createSafeAsyncWrapper(fn, context, options = {}) {
  return async (...args) => {
    return await safeAsync(
      () => fn.apply(this, args),
      context,
      options
    )
  }
}

/**
 * 批量处理异步操作
 * @param {Array} operations 操作数组，每个元素包含 {fn, context, options}
 * @param {Object} batchOptions 批量处理选项
 */
export async function safeBatch(operations, batchOptions = {}) {
  const {
    concurrent = false,
    stopOnError = false,
    maxConcurrency = 5
  } = batchOptions

  const results = []
  const errors = []

  if (concurrent) {
    // 并发执行
    const chunks = []
    for (let i = 0; i < operations.length; i += maxConcurrency) {
      chunks.push(operations.slice(i, i + maxConcurrency))
    }

    for (const chunk of chunks) {
      const promises = chunk.map(async (operation, index) => {
        try {
          const result = await safeAsync(
            operation.fn,
            operation.context || `batch-${index}`,
            operation.options || {}
          )
          return { success: true, result, index: i + index }
        } catch (error) {
          return { success: false, error, index: i + index }
        }
      })

      const chunkResults = await Promise.allSettled(promises)
      
      for (const promiseResult of chunkResults) {
        if (promiseResult.status === 'fulfilled') {
          const { success, result, error, index } = promiseResult.value
          if (success) {
            results[index] = result
          } else {
            errors[index] = error
            if (stopOnError) {
              throw error
            }
          }
        } else {
          errors.push(promiseResult.reason)
          if (stopOnError) {
            throw promiseResult.reason
          }
        }
      }
    }
  } else {
    // 顺序执行
    for (let i = 0; i < operations.length; i++) {
      const operation = operations[i]
      try {
        const result = await safeAsync(
          operation.fn,
          operation.context || `batch-${i}`,
          operation.options || {}
        )
        results[i] = result
      } catch (error) {
        errors[i] = error
        if (stopOnError) {
          throw error
        }
      }
    }
  }

  return {
    results,
    errors,
    hasErrors: errors.some(e => e !== undefined),
    successCount: results.filter(r => r !== undefined).length,
    errorCount: errors.filter(e => e !== undefined).length
  }
}

/**
 * 创建重试配置
 * @param {Object} options 重试选项
 */
export function createRetryConfig(options = {}) {
  return {
    maxAttempts: options.maxAttempts || 3,
    delay: options.delay || 1000,
    backoff: options.backoff || 1.5,
    onRetry: options.onRetry || null,
    ...options
  }
}

/**
 * 截图操作专用的错误处理包装器
 * @param {Function} fn 截图函数
 * @param {string} context 上下文
 */
export function withScreenshotErrorHandling(fn, context = 'screenshot') {
  return createSafeAsyncWrapper(fn, context, {
    retry: createRetryConfig({
      maxAttempts: 3,
      delay: 500,
      onRetry: (attempt, error) => {
        console.log(`截图操作重试，第${attempt}次尝试:`, error.message)
      }
    }),
    showToUser: true
  })
}

/**
 * OCR操作专用的错误处理包装器
 * @param {Function} fn OCR函数
 * @param {string} context 上下文
 */
export function withOCRErrorHandling(fn, context = 'ocr') {
  return createSafeAsyncWrapper(fn, context, {
    retry: createRetryConfig({
      maxAttempts: 2,
      delay: 1000,
      onRetry: (attempt, error) => {
        console.log(`OCR操作重试，第${attempt}次尝试:`, error.message)
      }
    }),
    showToUser: true
  })
}

/**
 * 网络请求专用的错误处理包装器
 * @param {Function} fn 网络请求函数
 * @param {string} context 上下文
 */
export function withNetworkErrorHandling(fn, context = 'network') {
  return createSafeAsyncWrapper(fn, context, {
    retry: createRetryConfig({
      maxAttempts: 3,
      delay: 1000,
      backoff: 2,
      onRetry: (attempt, error) => {
        console.log(`网络请求重试，第${attempt}次尝试:`, error.message)
      }
    }),
    showToUser: true
  })
}

/**
 * 创建超时包装器
 * @param {Function} fn 要包装的函数
 * @param {number} timeout 超时时间（毫秒）
 * @param {string} timeoutMessage 超时错误消息
 */
export function withTimeout(fn, timeout = 30000, timeoutMessage = '操作超时') {
  return async (...args) => {
    return Promise.race([
      fn.apply(this, args),
      new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error(timeoutMessage))
        }, timeout)
      })
    ])
  }
}

/**
 * 防抖错误处理包装器
 * @param {Function} fn 要包装的函数
 * @param {number} delay 防抖延迟
 */
export function withDebounce(fn, delay = 300) {
  let timeoutId
  
  return async (...args) => {
    return new Promise((resolve, reject) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(async () => {
        try {
          const result = await fn.apply(this, args)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }, delay)
    })
  }
}

/**
 * 节流错误处理包装器
 * @param {Function} fn 要包装的函数
 * @param {number} delay 节流延迟
 */
export function withThrottle(fn, delay = 1000) {
  let lastCall = 0
  let timeoutId
  
  return async (...args) => {
    const now = Date.now()
    
    if (now - lastCall >= delay) {
      lastCall = now
      return await fn.apply(this, args)
    } else {
      return new Promise((resolve, reject) => {
        clearTimeout(timeoutId)
        timeoutId = setTimeout(async () => {
          try {
            lastCall = Date.now()
            const result = await fn.apply(this, args)
            resolve(result)
          } catch (error) {
            reject(error)
          }
        }, delay - (now - lastCall))
      })
    }
  }
}

export default {
  withErrorHandling,
  safeAsync,
  createSafeAsyncWrapper,
  safeBatch,
  createRetryConfig,
  withScreenshotErrorHandling,
  withOCRErrorHandling,
  withNetworkErrorHandling,
  withTimeout,
  withDebounce,
  withThrottle
}