package utils

import (
	"fmt"
	"net/http"
	"runtime/debug"
	"strings"
	"time"
)

// ErrorCode 错误码类型
type ErrorCode int

const (
	// 系统级错误 (1000-1999)
	ErrSystemInternal ErrorCode = 1000
	ErrSystemTimeout  ErrorCode = 1001
	ErrSystemOverload ErrorCode = 1002

	// 业务级错误 (2000-2999)
	ErrBusinessValidation ErrorCode = 2000
	ErrBusinessNotFound   ErrorCode = 2001
	ErrBusinessConflict   ErrorCode = 2002

	// 截图相关错误 (3000-3099)
	ErrScreenshotCapture ErrorCode = 3000
	ErrScreenshotSave    ErrorCode = 3001
	ErrScreenshotProcess ErrorCode = 3002

	// OCR相关错误 (3100-3199)
	ErrOCRProcess    ErrorCode = 3100
	ErrOCRTimeout    ErrorCode = 3101
	ErrOCRInvalidImg ErrorCode = 3102

	// 网络相关错误 (4000-4099)
	ErrNetworkTimeout    ErrorCode = 4000
	ErrNetworkConnection ErrorCode = 4001
	ErrNetworkAPI        ErrorCode = 4002
)

// AppError 应用错误结构
type AppError struct {
	Code       ErrorCode `json:"code"`
	Message    string    `json:"message"`
	Details    string    `json:"details,omitempty"`
	Cause      error     `json:"-"`
	StackTrace string    `json:"stack_trace,omitempty"`
	Timestamp  int64     `json:"timestamp"`
	UserID     string    `json:"user_id,omitempty"`
	Operation  string    `json:"operation,omitempty"`
}

// Error 实现error接口
func (e *AppError) Error() string {
	return fmt.Sprintf("[%d] %s: %s", e.Code, e.Message, e.Details)
}

// NewAppError 创建新的应用错误
func NewAppError(code ErrorCode, message, details string, cause error) *AppError {
	return &AppError{
		Code:      code,
		Message:   message,
		Details:   details,
		Cause:     cause,
		Timestamp: time.Now().Unix(),
	}
}

// WithOperation 添加操作信息
func (e *AppError) WithOperation(operation string) *AppError {
	e.Operation = operation
	return e
}

// WithUser 添加用户信息
func (e *AppError) WithUser(userID string) *AppError {
	e.UserID = userID
	return e
}

// WithStackTrace 添加堆栈信息
func (e *AppError) WithStackTrace() *AppError {
	e.StackTrace = string(debug.Stack())
	return e
}

// IsRetryable 判断错误是否可重试
func (e *AppError) IsRetryable() bool {
	retryableCodes := []ErrorCode{
		ErrSystemTimeout,
		ErrNetworkTimeout,
		ErrNetworkConnection,
		ErrOCRTimeout,
	}

	for _, code := range retryableCodes {
		if e.Code == code {
			return true
		}
	}
	return false
}

// GetHTTPStatus 获取对应的HTTP状态码
func (e *AppError) GetHTTPStatus() int {
	switch e.Code {
	case ErrBusinessNotFound:
		return http.StatusNotFound
	case ErrBusinessValidation:
		return http.StatusBadRequest
	case ErrBusinessConflict:
		return http.StatusConflict
	case ErrSystemTimeout, ErrNetworkTimeout:
		return http.StatusRequestTimeout
	default:
		return http.StatusInternalServerError
	}
}

// GetUserMessage 获取用户友好的错误信息
func (e *AppError) GetUserMessage() string {
	userMessages := map[ErrorCode]string{
		ErrNetworkTimeout:     "网络请求超时，请稍后重试",
		ErrNetworkConnection:  "网络连接失败，请检查网络设置",
		ErrBusinessValidation: "输入的数据格式不正确",
		ErrBusinessNotFound:   "请求的资源不存在",
		ErrBusinessConflict:   "操作冲突，请刷新后重试",
		ErrSystemInternal:     "系统内部错误，请联系技术支持",
		ErrSystemOverload:     "系统繁忙，请稍后重试",
		ErrScreenshotCapture:  "截图失败，请重试",
		ErrScreenshotSave:     "截图保存失败",
		ErrScreenshotProcess:  "截图处理失败",
		ErrOCRProcess:         "OCR识别失败",
		ErrOCRTimeout:         "OCR识别超时",
		ErrOCRInvalidImg:      "图片格式不支持",
	}

	if msg, ok := userMessages[e.Code]; ok {
		return msg
	}
	return e.Message
}

// DetermineErrorCode 根据错误内容确定错误码
func DetermineErrorCode(err error) ErrorCode {
	if err == nil {
		return ErrSystemInternal
	}

	errorMsg := strings.ToLower(err.Error())

	// 网络相关错误
	if contains(errorMsg, "timeout", "deadline") {
		return ErrNetworkTimeout
	}
	if contains(errorMsg, "connection", "network") {
		return ErrNetworkConnection
	}

	// OCR相关错误
	if contains(errorMsg, "ocr", "识别") {
		return ErrOCRProcess
	}

	// 截图相关错误
	if contains(errorMsg, "screenshot", "截图") {
		return ErrScreenshotCapture
	}

	// 默认系统内部错误
	return ErrSystemInternal
}

// contains 检查字符串是否包含任一关键词
func contains(str string, keywords ...string) bool {
	for _, keyword := range keywords {
		if strings.Contains(str, keyword) {
			return true
		}
	}
	return false
}

// WrapError 包装普通错误为AppError
func WrapError(err error, operation string) *AppError {
	if err == nil {
		return nil
	}

	// 如果已经是AppError，直接返回
	if appErr, ok := err.(*AppError); ok {
		return appErr.WithOperation(operation)
	}

	// 包装为AppError
	code := DetermineErrorCode(err)
	return NewAppError(
		code,
		"操作失败",
		err.Error(),
		err,
	).WithOperation(operation)
}

// IsDevelopment 判断是否为开发环境
func IsDevelopment() bool {
	// 这里可以根据实际情况判断环境
	// 例如通过环境变量或构建标签
	return true // 暂时返回true，实际使用时需要根据环境配置
}
