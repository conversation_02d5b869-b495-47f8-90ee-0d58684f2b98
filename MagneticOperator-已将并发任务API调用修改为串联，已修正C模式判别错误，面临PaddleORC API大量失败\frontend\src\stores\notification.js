import { defineStore } from 'pinia'

export const useNotificationStore = defineStore('notification', {
  state: () => ({
    // Toast 通知
    notification: {
      show: false,
      message: '',
      type: 'info', // 'success', 'error', 'warning', 'info'
      duration: 3000
    },
    // 通知历史
    notificationHistory: [],
    // 系统通知设置
    useSystemNotification: true,
    // 通知计数器
    notificationCount: 0
  }),

  getters: {
    // 当前通知状态
    currentNotification: (state) => state.notification,
    
    // 是否有活跃通知
    hasActiveNotification: (state) => state.notification.show,
    
    // 最近的通知历史
    recentNotifications: (state) => {
      return state.notificationHistory.slice(-10).reverse()
    },
    
    // 未读通知数量
    unreadCount: (state) => {
      return state.notificationHistory.filter(n => !n.read).length
    }
  },

  actions: {
    // 显示通知
    showNotification(message, type = 'info', duration = 3000) {
      this.notification = {
        show: true,
        message,
        type,
        duration
      }
      
      // 添加到历史记录
      this.addToHistory(message, type)
      
      // 自动隐藏
      if (duration > 0) {
        setTimeout(() => {
          this.hideNotification()
        }, duration)
      }
      
      return {
        success: true,
        message: `通知已显示: ${message}`
      }
    },

    // 显示成功通知
    showSuccess(message, duration = 3000) {
      return this.showNotification(message, 'success', duration)
    },

    // 显示错误通知
    showError(message, duration = 5000) {
      return this.showNotification(message, 'error', duration)
    },

    // 显示警告通知
    showWarning(message, duration = 4000) {
      return this.showNotification(message, 'warning', duration)
    },

    // 显示信息通知
    showInfo(message, duration = 3000) {
      return this.showNotification(message, 'info', duration)
    },

    // 隐藏通知
    hideNotification() {
      this.notification.show = false
      return { success: true }
    },

    // 添加到历史记录
    addToHistory(message, type) {
      const notification = {
        id: ++this.notificationCount,
        message,
        type,
        timestamp: new Date(),
        read: false
      }
      
      this.notificationHistory.push(notification)
      
      // 限制历史记录数量
      if (this.notificationHistory.length > 100) {
        this.notificationHistory = this.notificationHistory.slice(-50)
      }
    },

    // 标记通知为已读
    markAsRead(notificationId) {
      const notification = this.notificationHistory.find(n => n.id === notificationId)
      if (notification) {
        notification.read = true
        return { success: true }
      }
      return { success: false, message: '通知不存在' }
    },

    // 标记所有通知为已读
    markAllAsRead() {
      this.notificationHistory.forEach(n => {
        n.read = true
      })
      return { success: true, message: '所有通知已标记为已读' }
    },

    // 清空通知历史
    clearHistory() {
      this.notificationHistory = []
      this.notificationCount = 0
      return { success: true, message: '通知历史已清空' }
    },

    // 更新通知模式
    updateNotificationMode(useSystemNotification) {
      this.useSystemNotification = useSystemNotification
      return {
        success: true,
        message: useSystemNotification ? '已切换到系统级通知模式' : '已切换到应用内通知模式'
      }
    },

    // 处理后端通知事件
    handleNotificationShown(data) {
      if (data && data.message) {
        const type = data.type || 'info'
        const duration = data.duration || 3000
        
        this.showNotification(data.message, type, duration)
        
        return {
          success: true,
          message: `处理后端通知: ${data.message}`
        }
      }
      return { success: false, message: '无效的通知数据' }
    },

    // 显示操作状态提示
    showOperationStatus(message, isSuccess = true) {
      const type = isSuccess ? 'success' : 'error'
      const duration = isSuccess ? 2000 : 4000
      
      return this.showNotification(message, type, duration)
    },

    // 显示加载状态
    showLoading(message = '加载中...') {
      return this.showNotification(message, 'info', 0) // 0 表示不自动隐藏
    },

    // 隐藏加载状态
    hideLoading() {
      return this.hideNotification()
    },

    // 显示网络错误
    showNetworkError(error) {
      const message = error?.message || '网络连接失败，请检查网络设置'
      return this.showError(message, 5000)
    },

    // 显示API错误
    showApiError(error, operation = '操作') {
      let message = `${operation}失败`
      
      if (error?.response?.data?.message) {
        message += `: ${error.response.data.message}`
      } else if (error?.message) {
        message += `: ${error.message}`
      }
      
      return this.showError(message, 5000)
    },

    // 显示验证错误
    showValidationError(errors) {
      if (Array.isArray(errors)) {
        const message = errors.join('; ')
        return this.showError(`验证失败: ${message}`, 4000)
      } else if (typeof errors === 'string') {
        return this.showError(`验证失败: ${errors}`, 4000)
      }
      return this.showError('数据验证失败', 4000)
    },

    // 显示权限错误
    showPermissionError(action = '执行此操作') {
      return this.showError(`权限不足，无法${action}`, 4000)
    },

    // 显示配置更新通知
    showConfigUpdate(configType = '配置') {
      return this.showSuccess(`${configType}已更新`, 2000)
    },

    // 显示数据同步通知
    showDataSync(dataType = '数据', isSuccess = true) {
      if (isSuccess) {
        return this.showSuccess(`${dataType}同步成功`, 2000)
      } else {
        return this.showError(`${dataType}同步失败`, 4000)
      }
    }
  }
})