/**
 * 增强的事件管理器
 * 用于优化前后端通信和并发处理的通知机制
 */

class EventManager {
  constructor() {
    this.listeners = new Map()
    this.eventQueue = []
    this.isProcessing = false
    this.retryAttempts = new Map()
    this.maxRetries = 3
    this.retryDelay = 1000
    this.debugMode = true
  }

  /**
   * 初始化事件管理器
   */
  init() {
    this.log('EventManager 初始化')
    this.setupWailsEventListeners()
    this.startEventProcessor()
  }

  /**
   * 设置Wails事件监听器
   */
  setupWailsEventListeners() {
    if (!window.runtime || !window.runtime.EventsOn) {
      this.error('Wails runtime 不可用')
      return
    }

    // 监听并发截图进度事件
    window.runtime.EventsOn('concurrent-screenshot-progress', (data) => {
      this.handleConcurrentProgress(data)
    })

    // 监听OCR处理进度事件
    window.runtime.EventsOn('ocr-processing-progress', (data) => {
      this.handleOCRProgress(data)
    })

    // 监听任务完成事件
    window.runtime.EventsOn('task-completed', (data) => {
      this.handleTaskCompleted(data)
    })

    // 监听错误事件
    window.runtime.EventsOn('task-error', (data) => {
      this.handleTaskError(data)
    })

    // 监听系统状态更新
    window.runtime.EventsOn('system-status-update', (data) => {
      this.handleSystemStatusUpdate(data)
    })

    this.log('Wails事件监听器设置完成')
  }

  /**
   * 启动事件处理器
   */
  startEventProcessor() {
    setInterval(() => {
      this.processEventQueue()
    }, 100) // 每100ms处理一次事件队列
  }

  /**
   * 处理并发截图进度
   */
  handleConcurrentProgress(data) {
    this.log('收到并发截图进度:', data)
    
    const event = {
      type: 'concurrent-progress',
      data: {
        mode: data.mode || 'B',
        progress: data.progress || 0,
        message: data.message || '正在处理...',
        timestamp: Date.now()
      }
    }
    
    this.queueEvent(event)
  }

  /**
   * 处理OCR处理进度
   */
  handleOCRProgress(data) {
    this.log('收到OCR处理进度:', data)
    
    const event = {
      type: 'ocr-progress',
      data: {
        organName: data.organName || '未知器官',
        confidence: data.confidence || 0,
        progress: data.progress || 0,
        message: data.message || 'OCR处理中...',
        timestamp: Date.now()
      }
    }
    
    this.queueEvent(event)
  }

  /**
   * 处理任务完成事件
   */
  handleTaskCompleted(data) {
    this.log('收到任务完成事件:', data)
    
    const event = {
      type: 'task-completed',
      data: {
        taskType: data.taskType || 'unknown',
        result: data.result || {},
        duration: data.duration || 0,
        message: data.message || '任务完成',
        timestamp: Date.now()
      }
    }
    
    this.queueEvent(event)
  }

  /**
   * 处理任务错误事件
   */
  handleTaskError(data) {
    this.error('收到任务错误事件:', data)
    
    const event = {
      type: 'task-error',
      data: {
        taskType: data.taskType || 'unknown',
        error: data.error || '未知错误',
        message: data.message || '任务执行失败',
        timestamp: Date.now()
      }
    }
    
    this.queueEvent(event)
  }

  /**
   * 处理系统状态更新
   */
  handleSystemStatusUpdate(data) {
    this.log('收到系统状态更新:', data)
    
    const event = {
      type: 'system-status',
      data: {
        status: data.status || 'unknown',
        message: data.message || '系统状态更新',
        details: data.details || {},
        timestamp: Date.now()
      }
    }
    
    this.queueEvent(event)
  }

  /**
   * 将事件加入队列
   */
  queueEvent(event) {
    event.id = this.generateEventId()
    this.eventQueue.push(event)
    this.log(`事件已加入队列: ${event.type} (ID: ${event.id})`)
  }

  /**
   * 处理事件队列
   */
  async processEventQueue() {
    if (this.isProcessing || this.eventQueue.length === 0) {
      return
    }

    this.isProcessing = true

    try {
      while (this.eventQueue.length > 0) {
        const event = this.eventQueue.shift()
        await this.processEvent(event)
      }
    } catch (error) {
      this.error('处理事件队列时发生错误:', error)
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * 处理单个事件
   */
  async processEvent(event) {
    try {
      this.log(`处理事件: ${event.type} (ID: ${event.id})`)
      
      // 触发事件监听器
      const listeners = this.listeners.get(event.type) || []
      
      for (const listener of listeners) {
        try {
          await listener(event.data)
        } catch (error) {
          this.error(`事件监听器执行失败 (${event.type}):`, error)
        }
      }
      
      // 清除重试记录
      this.retryAttempts.delete(event.id)
      
    } catch (error) {
      this.error(`处理事件失败 (${event.type}):`, error)
      await this.retryEvent(event)
    }
  }

  /**
   * 重试事件处理
   */
  async retryEvent(event) {
    const attempts = this.retryAttempts.get(event.id) || 0
    
    if (attempts < this.maxRetries) {
      this.retryAttempts.set(event.id, attempts + 1)
      
      setTimeout(() => {
        this.eventQueue.unshift(event) // 重新加入队列头部
        this.log(`重试事件: ${event.type} (ID: ${event.id}, 尝试: ${attempts + 1}/${this.maxRetries})`)
      }, this.retryDelay * (attempts + 1))
    } else {
      this.error(`事件处理失败，已达到最大重试次数: ${event.type} (ID: ${event.id})`)
      this.retryAttempts.delete(event.id)
    }
  }

  /**
   * 注册事件监听器
   */
  on(eventType, listener) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, [])
    }
    
    this.listeners.get(eventType).push(listener)
    this.log(`注册事件监听器: ${eventType}`)
  }

  /**
   * 移除事件监听器
   */
  off(eventType, listener) {
    const listeners = this.listeners.get(eventType)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
        this.log(`移除事件监听器: ${eventType}`)
      }
    }
  }

  /**
   * 生成事件ID
   */
  generateEventId() {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 日志输出
   */
  log(...args) {
    if (this.debugMode) {
      console.log('[EventManager]', ...args)
    }
  }

  /**
   * 错误输出
   */
  error(...args) {
    console.error('[EventManager]', ...args)
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      queueLength: this.eventQueue.length,
      isProcessing: this.isProcessing,
      listenerCount: Array.from(this.listeners.values()).reduce((sum, listeners) => sum + listeners.length, 0),
      retryingEvents: this.retryAttempts.size
    }
  }
}

// 创建全局实例
const eventManager = new EventManager()

export default eventManager
export { EventManager }