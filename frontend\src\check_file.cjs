const fs = require('fs');

try {
  const content = fs.readFileSync('App.vue', 'utf8');
  console.log('File length:', content.length);
  
  // 检查换行符类型
  const crlfCount = (content.match(/\r\n/g) || []).length;
  const lfCount = (content.match(/(?<!\r)\n/g) || []).length;
  const crCount = (content.match(/\r(?!\n)/g) || []).length;
  
  console.log('CRLF count:', crlfCount);
  console.log('LF only count:', lfCount);
  console.log('CR only count:', crCount);
  
  // 按行分割
  const lines = content.split(/\r?\n/);
  console.log('Total lines:', lines.length);
  
  // 检查最后几行
  console.log('\nLast 10 lines:');
  lines.slice(-10).forEach((line, i) => {
    const lineNum = lines.length - 10 + i + 1;
    console.log(`${lineNum}: ${line}`);
  });
  
  // 检查是否有未闭合的标签
  const templateMatch = content.match(/<template[^>]*>/g);
  const templateCloseMatch = content.match(/<\/template>/g);
  const scriptMatch = content.match(/<script[^>]*>/g);
  const scriptCloseMatch = content.match(/<\/script>/g);
  const styleMatch = content.match(/<style[^>]*>/g);
  const styleCloseMatch = content.match(/<\/style>/g);
  
  console.log('\nTag counts:');
  console.log('template open:', templateMatch ? templateMatch.length : 0);
  console.log('template close:', templateCloseMatch ? templateCloseMatch.length : 0);
  console.log('script open:', scriptMatch ? scriptMatch.length : 0);
  console.log('script close:', scriptCloseMatch ? scriptCloseMatch.length : 0);
  console.log('style open:', styleMatch ? styleMatch.length : 0);
  console.log('style close:', styleCloseMatch ? styleCloseMatch.length : 0);
  console.log('style close:', styleCloseMatch ? styleCloseMatch.length : 0);
  
} catch (error) {
  console.error('Error reading file:', error.message);
}