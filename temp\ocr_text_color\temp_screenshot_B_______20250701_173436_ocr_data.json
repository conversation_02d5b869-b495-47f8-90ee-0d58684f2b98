{"logId": "f146c753-cf93-4cff-bb1e-3c0fa392691e", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 8, "label": "table", "score": 0.9876863956451416, "coordinate": [12.904655456542969, 75.52501678466797, 768, 1710.4400634765625]}, {"cls_id": 9, "label": "table_title", "score": 0.6557138562202454, "coordinate": [19.524375915527344, 27.168771743774414, 520.5422973632812, 62.937538146972656]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[100, 77], [157, 77], [157, 102], [100, 102]], [[192, 77], [377, 77], [377, 102], [192, 102]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[194, 102], [270, 102], [270, 129], [194, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[194, 156], [425, 156], [425, 179], [194, 179]], [[98, 179], [161, 179], [161, 204], [98, 204]], [[192, 181], [319, 177], [320, 202], [193, 206]], [[98, 204], [161, 204], [161, 229], [98, 229]], [[194, 206], [554, 206], [554, 229], [194, 229]], [[98, 231], [161, 231], [161, 256], [98, 256]], [[194, 229], [386, 229], [386, 252], [194, 252]], [[98, 256], [162, 256], [162, 281], [98, 281]], [[194, 259], [310, 259], [310, 279], [194, 279]], [[98, 281], [162, 281], [162, 306], [98, 306]], [[190, 283], [319, 279], [320, 304], [191, 308]], [[98, 306], [162, 306], [162, 332], [98, 332]], [[194, 307], [727, 307], [727, 331], [194, 331]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [491, 336], [491, 354], [196, 354]], [[98, 358], [161, 358], [161, 383], [98, 383]], [[192, 358], [342, 358], [342, 381], [192, 381]], [[98, 383], [162, 383], [162, 408], [98, 408]], [[192, 383], [303, 383], [303, 408], [192, 408]], [[98, 408], [161, 408], [161, 434], [98, 434]], [[192, 409], [415, 409], [415, 433], [192, 433]], [[98, 434], [162, 434], [162, 459], [98, 459]], [[192, 436], [225, 436], [225, 459], [192, 459]], [[98, 459], [155, 459], [155, 484], [98, 484]], [[194, 463], [474, 463], [474, 481], [194, 481]], [[98, 484], [155, 484], [155, 511], [98, 511]], [[194, 486], [443, 486], [443, 509], [194, 509]], [[98, 511], [153, 511], [153, 536], [98, 536]], [[192, 511], [399, 511], [399, 534], [192, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[196, 538], [406, 538], [406, 556], [196, 556]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[194, 563], [436, 563], [436, 586], [194, 586]], [[98, 586], [155, 586], [155, 613], [98, 613]], [[194, 590], [306, 590], [306, 610], [194, 610]], [[98, 611], [155, 611], [155, 638], [98, 638]], [[192, 613], [299, 613], [299, 638], [192, 638]], [[98, 638], [153, 638], [153, 663], [98, 663]], [[192, 638], [223, 638], [223, 663], [192, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[190, 663], [341, 657], [342, 688], [191, 694]], [[98, 688], [157, 688], [157, 715], [98, 715]], [[188, 686], [260, 686], [260, 719], [188, 719]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[196, 719], [639, 719], [639, 736], [196, 736]], [[98, 740], [155, 740], [155, 767], [98, 767]], [[194, 742], [378, 742], [378, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[194, 767], [360, 767], [360, 790], [194, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [476, 792], [476, 815], [192, 815]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[191, 813], [270, 817], [268, 844], [190, 840]], [[98, 842], [157, 842], [157, 867], [98, 867]], [[190, 844], [524, 844], [524, 867], [190, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [351, 869], [351, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[192, 894], [618, 894], [618, 917], [192, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[194, 922], [371, 922], [371, 940], [194, 940]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[192, 946], [606, 946], [606, 969], [192, 969]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[192, 971], [733, 971], [733, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[190, 994], [244, 994], [244, 1022], [190, 1022]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[194, 1022], [504, 1022], [504, 1046], [194, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[192, 1047], [447, 1047], [447, 1071], [192, 1071]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[192, 1072], [297, 1072], [297, 1098], [192, 1098]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [476, 1098], [476, 1121], [192, 1121]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[194, 1124], [443, 1124], [443, 1148], [194, 1148]], [[98, 1148], [157, 1148], [157, 1174], [98, 1174]], [[192, 1149], [689, 1149], [689, 1173], [192, 1173]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[192, 1173], [277, 1173], [277, 1199], [192, 1199]], [[98, 1199], [157, 1199], [157, 1224], [98, 1224]], [[192, 1199], [406, 1199], [406, 1224], [192, 1224]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[192, 1226], [414, 1226], [414, 1249], [192, 1249]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[190, 1249], [364, 1249], [364, 1273], [190, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[192, 1274], [260, 1274], [260, 1301], [192, 1301]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[192, 1303], [417, 1303], [417, 1326], [192, 1326]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[190, 1326], [363, 1324], [364, 1349], [190, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [412, 1353], [412, 1376], [192, 1376]], [[98, 1376], [155, 1376], [155, 1403], [98, 1403]], [[190, 1378], [279, 1378], [279, 1403], [190, 1403]], [[100, 1403], [155, 1403], [155, 1428], [100, 1428]], [[190, 1403], [347, 1403], [347, 1426], [190, 1426]], [[98, 1428], [155, 1428], [155, 1453], [98, 1453]], [[188, 1428], [262, 1428], [262, 1455], [188, 1455]], [[98, 1453], [157, 1453], [157, 1478], [98, 1478]], [[194, 1455], [534, 1455], [534, 1478], [194, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[190, 1478], [362, 1480], [362, 1505], [190, 1503]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[192, 1505], [314, 1505], [314, 1530], [192, 1530]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[192, 1532], [384, 1532], [384, 1555], [192, 1555]], [[98, 1555], [157, 1555], [157, 1582], [98, 1582]], [[188, 1555], [244, 1555], [244, 1584], [188, 1584]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[190, 1582], [262, 1582], [262, 1609], [190, 1609]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[190, 1607], [593, 1607], [593, 1632], [190, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[190, 1632], [260, 1632], [260, 1659], [190, 1659]], [[98, 1657], [157, 1657], [157, 1684], [98, 1684]], [[192, 1659], [474, 1659], [474, 1682], [192, 1682]], [[100, 1684], [157, 1684], [157, 1709], [100, 1709]], [[192, 1686], [430, 1686], [430, 1709], [192, 1709]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["按照标准图谱相似度递减列表：", "0.000", "男性器官小骨盆;侧", "4.243", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.047", "血尿酸SERUMURICACID", "0.067", "血管紧张素Ⅱ*", "0.081", "胆固醇COMMONPLASMA CHOLESTERIN", "0.087", "单核细胞MONOCYTES", "0.088", "血管紧张素I*", "0.096", "免疫球蛋白M*", "0.096", "血浆丰配化脂肪酸NONETHERIZED FATTYACIDSOF PLASMA", "0.104", "C反应蛋白C-REACTIVEPROTEIN", "0.123", "SEROMUCOIDS*", "0.158", "结合珠蛋白*", "0.161", "血红血球ERYTHROCYTES", "0.186", "铜*", "0.117", "PERIPHERICBLOODLEUCOCYTES", "0.118", "尿中蛋白质PROTEININURINE", "0.121", "嗜碱性粒细胞BASOPHILS", "0.127", "血钾PLASMAPOTASSIUM", "0.133", "尿白血球URINELEUCOCYTES", "0.139", "免疫球蛋白G", "0.152", "BETA球蛋白", "0.161", "锂*", "0.051", "肿瘤标志物CA50*", "0.093", "备解素", "0.095", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.101", "血氨SERUMAMMONIA", "0.102", "血尿素BLOODUREA", "0.104", "酸性磷酸酵素ACIDPHOSPHATASE", "0.104", "尿中尿素", "0.104", "游离胆固醇FREEPLASMACHOLESTERIN", "0.105", "血细胞比容，全血*", "0.105", "碱性磷酸酯酵素COMMONALKALINEPHOSPHATASE", "0.107", "尿氨URINEAMMONIA", "0.108", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.108", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.110", "糖苷*", "0.112", "血清淀粉酵素SERUMALPHAAMYLASE", "0.112", "肿瘤标志物MELANOGENE在尿*", "0.112", "胰高血糖素*", "0.112", "血浆磷脂PLASMA PHOSPHOTIDES", "0.115", "血清溶菌酵SERUMLYSOZYME", "0.115", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.115", "肌钙蛋白*", "0.116", "尿磷URINE PHOSPHORUS", "0.117", "血磷BLOOD PHOSPHORUS", "0.118", "红细胞沉降率(ESR)", "0.118", "脂肪酶*", "0.119", "血肌配SERUMCREATININE", "0.119", "维生素E（生育酚)*", "0.120", "尿中尿酸URINEURICACID", "0.121", "醛固酮尿*", "0.121", "RHEUMOFACTOR*", "0.121", "唾液酸*", "0.122", "血浆中性脂肪NEUTRALFATSOFPLASMA", "0.122", "甲状腺素结合球蛋白", "0.122", "甲状腺球蛋白*", "0.122", "17-血浆氧皮质类固醇类", "0.123", "睾酮*", "0.123", "催乳素*", "0.123", "乳酸脱氢酵素COMMONLACTADEHYDROGENASE", "0.124", "胰岛素*", "0.124", "嗜中性粒细胞STABNEUTROPHILS", "0.124", "嗜酸性粒细胞EOSINOPHILES"], "rec_scores": [0.9954331517219543, 0.9991452097892761, 0.9270201921463013, 0.9992626309394836, 0.9987521767616272, 0.9667975902557373, 0.9998911023139954, 0.9951974749565125, 0.9999173879623413, 0.95135498046875, 0.9998822212219238, 0.9771552085876465, 0.9999210238456726, 0.9562473893165588, 0.999927818775177, 0.9654912352561951, 0.9999502301216125, 0.9895482659339905, 0.9999111294746399, 0.9332330822944641, 0.999850869178772, 0.9680902361869812, 0.9998744130134583, 0.9895607829093933, 0.9999011754989624, 0.9578317999839783, 0.9998953938484192, 0.9949594736099243, 0.9999270439147949, 0.7702764272689819, 0.9991239309310913, 0.9963095784187317, 0.9995819926261902, 0.9969702363014221, 0.9994450807571411, 0.9987413883209229, 0.9994102716445923, 0.9795147180557251, 0.9996241331100464, 0.9961475729942322, 0.9995198249816895, 0.9941864013671875, 0.9996626973152161, 0.9950935244560242, 0.9993520975112915, 0.8252460360527039, 0.9996099472045898, 0.9914361834526062, 0.9998332858085632, 0.9968001246452332, 0.9998568296432495, 0.9966492652893066, 0.9995571374893188, 0.9979380369186401, 0.9993680715560913, 0.9966976046562195, 0.9995644688606262, 0.99799644947052, 0.9994826316833496, 0.9995532631874084, 0.9993721842765808, 0.9736791849136353, 0.9993208646774292, 0.9495124816894531, 0.9996339082717896, 0.9867056608200073, 0.9995242357254028, 0.9923023581504822, 0.9995409250259399, 0.9979647397994995, 0.9995819330215454, 0.9958996772766113, 0.9992928504943848, 0.9691548347473145, 0.9992367625236511, 0.9972469210624695, 0.9994252324104309, 0.98753821849823, 0.9994117021560669, 0.9709548354148865, 0.999462902545929, 0.9782581329345703, 0.9991846084594727, 0.9980633854866028, 0.9992122650146484, 0.9957689046859741, 0.9991780519485474, 0.9105976819992065, 0.9994516372680664, 0.9832112789154053, 0.9991371035575867, 0.9772404432296753, 0.9994661211967468, 0.9381177425384521, 0.999498188495636, 0.9570178985595703, 0.9989644885063171, 0.9624761939048767, 0.9992556571960449, 0.9057332277297974, 0.999311625957489, 0.9954226016998291, 0.9995757937431335, 0.9777018427848816, 0.999352753162384, 0.9959729313850403, 0.9993472099304199, 0.9542421102523804, 0.9994784593582153, 0.9841814637184143, 0.9996312856674194, 0.9959399104118347, 0.9995940327644348, 0.9298602342605591, 0.9994674921035767, 0.992068350315094, 0.9997221231460571, 0.8835622668266296, 0.9996641278266907, 0.9697609543800354, 0.9995576739311218, 0.9958463311195374, 0.999275803565979, 0.9551318883895874, 0.999648928642273, 0.9938259124755859, 0.9994627833366394, 0.9977051615715027], "rec_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[100, 77], [157, 77], [157, 102], [100, 102]], [[192, 77], [377, 77], [377, 102], [192, 102]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[194, 102], [270, 102], [270, 129], [194, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[194, 156], [425, 156], [425, 179], [194, 179]], [[98, 179], [161, 179], [161, 204], [98, 204]], [[192, 181], [319, 177], [320, 202], [193, 206]], [[98, 204], [161, 204], [161, 229], [98, 229]], [[194, 206], [554, 206], [554, 229], [194, 229]], [[98, 231], [161, 231], [161, 256], [98, 256]], [[194, 229], [386, 229], [386, 252], [194, 252]], [[98, 256], [162, 256], [162, 281], [98, 281]], [[194, 259], [310, 259], [310, 279], [194, 279]], [[98, 281], [162, 281], [162, 306], [98, 306]], [[190, 283], [319, 279], [320, 304], [191, 308]], [[98, 306], [162, 306], [162, 332], [98, 332]], [[194, 307], [727, 307], [727, 331], [194, 331]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [491, 336], [491, 354], [196, 354]], [[98, 358], [161, 358], [161, 383], [98, 383]], [[192, 358], [342, 358], [342, 381], [192, 381]], [[98, 383], [162, 383], [162, 408], [98, 408]], [[192, 383], [303, 383], [303, 408], [192, 408]], [[98, 408], [161, 408], [161, 434], [98, 434]], [[192, 409], [415, 409], [415, 433], [192, 433]], [[98, 434], [162, 434], [162, 459], [98, 459]], [[192, 436], [225, 436], [225, 459], [192, 459]], [[98, 459], [155, 459], [155, 484], [98, 484]], [[194, 463], [474, 463], [474, 481], [194, 481]], [[98, 484], [155, 484], [155, 511], [98, 511]], [[194, 486], [443, 486], [443, 509], [194, 509]], [[98, 511], [153, 511], [153, 536], [98, 536]], [[192, 511], [399, 511], [399, 534], [192, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[196, 538], [406, 538], [406, 556], [196, 556]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[194, 563], [436, 563], [436, 586], [194, 586]], [[98, 586], [155, 586], [155, 613], [98, 613]], [[194, 590], [306, 590], [306, 610], [194, 610]], [[98, 611], [155, 611], [155, 638], [98, 638]], [[192, 613], [299, 613], [299, 638], [192, 638]], [[98, 638], [153, 638], [153, 663], [98, 663]], [[192, 638], [223, 638], [223, 663], [192, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[190, 663], [341, 657], [342, 688], [191, 694]], [[98, 688], [157, 688], [157, 715], [98, 715]], [[188, 686], [260, 686], [260, 719], [188, 719]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[196, 719], [639, 719], [639, 736], [196, 736]], [[98, 740], [155, 740], [155, 767], [98, 767]], [[194, 742], [378, 742], [378, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[194, 767], [360, 767], [360, 790], [194, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [476, 792], [476, 815], [192, 815]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[191, 813], [270, 817], [268, 844], [190, 840]], [[98, 842], [157, 842], [157, 867], [98, 867]], [[190, 844], [524, 844], [524, 867], [190, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [351, 869], [351, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[192, 894], [618, 894], [618, 917], [192, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[194, 922], [371, 922], [371, 940], [194, 940]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[192, 946], [606, 946], [606, 969], [192, 969]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[192, 971], [733, 971], [733, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[190, 994], [244, 994], [244, 1022], [190, 1022]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[194, 1022], [504, 1022], [504, 1046], [194, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[192, 1047], [447, 1047], [447, 1071], [192, 1071]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[192, 1072], [297, 1072], [297, 1098], [192, 1098]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [476, 1098], [476, 1121], [192, 1121]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[194, 1124], [443, 1124], [443, 1148], [194, 1148]], [[98, 1148], [157, 1148], [157, 1174], [98, 1174]], [[192, 1149], [689, 1149], [689, 1173], [192, 1173]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[192, 1173], [277, 1173], [277, 1199], [192, 1199]], [[98, 1199], [157, 1199], [157, 1224], [98, 1224]], [[192, 1199], [406, 1199], [406, 1224], [192, 1224]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[192, 1226], [414, 1226], [414, 1249], [192, 1249]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[190, 1249], [364, 1249], [364, 1273], [190, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[192, 1274], [260, 1274], [260, 1301], [192, 1301]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[192, 1303], [417, 1303], [417, 1326], [192, 1326]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[190, 1326], [363, 1324], [364, 1349], [190, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [412, 1353], [412, 1376], [192, 1376]], [[98, 1376], [155, 1376], [155, 1403], [98, 1403]], [[190, 1378], [279, 1378], [279, 1403], [190, 1403]], [[100, 1403], [155, 1403], [155, 1428], [100, 1428]], [[190, 1403], [347, 1403], [347, 1426], [190, 1426]], [[98, 1428], [155, 1428], [155, 1453], [98, 1453]], [[188, 1428], [262, 1428], [262, 1455], [188, 1455]], [[98, 1453], [157, 1453], [157, 1478], [98, 1478]], [[194, 1455], [534, 1455], [534, 1478], [194, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[190, 1478], [362, 1480], [362, 1505], [190, 1503]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[192, 1505], [314, 1505], [314, 1530], [192, 1530]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[192, 1532], [384, 1532], [384, 1555], [192, 1555]], [[98, 1555], [157, 1555], [157, 1582], [98, 1582]], [[188, 1555], [244, 1555], [244, 1584], [188, 1584]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[190, 1582], [262, 1582], [262, 1609], [190, 1609]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[190, 1607], [593, 1607], [593, 1632], [190, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[190, 1632], [260, 1632], [260, 1659], [190, 1659]], [[98, 1657], [157, 1657], [157, 1684], [98, 1684]], [[192, 1659], [474, 1659], [474, 1682], [192, 1682]], [[100, 1684], [157, 1684], [157, 1709], [100, 1709]], [[192, 1686], [430, 1686], [430, 1709], [192, 1709]]], "rec_boxes": [[20, 30, 519, 61], [100, 77, 157, 102], [192, 77, 377, 102], [98, 102, 157, 127], [194, 102, 270, 129], [194, 129, 462, 152], [98, 154, 161, 179], [194, 156, 425, 179], [98, 179, 161, 204], [192, 177, 320, 206], [98, 204, 161, 229], [194, 206, 554, 229], [98, 231, 161, 256], [194, 229, 386, 252], [98, 256, 162, 281], [194, 259, 310, 279], [98, 281, 162, 306], [190, 279, 320, 308], [98, 306, 162, 332], [194, 307, 727, 331], [98, 332, 161, 358], [196, 336, 491, 354], [98, 358, 161, 383], [192, 358, 342, 381], [98, 383, 162, 408], [192, 383, 303, 408], [98, 408, 161, 434], [192, 409, 415, 433], [98, 434, 162, 459], [192, 436, 225, 459], [98, 459, 155, 484], [194, 463, 474, 481], [98, 484, 155, 511], [194, 486, 443, 509], [98, 511, 153, 536], [192, 511, 399, 534], [98, 536, 155, 561], [196, 538, 406, 556], [98, 561, 155, 588], [194, 563, 436, 586], [98, 586, 155, 613], [194, 590, 306, 610], [98, 611, 155, 638], [192, 613, 299, 638], [98, 638, 153, 663], [192, 638, 223, 663], [98, 663, 155, 688], [190, 657, 342, 694], [98, 688, 157, 715], [188, 686, 260, 719], [98, 713, 157, 740], [196, 719, 639, 736], [98, 740, 155, 767], [194, 742, 378, 765], [98, 765, 157, 790], [194, 767, 360, 790], [98, 790, 157, 817], [192, 792, 476, 815], [98, 815, 157, 842], [190, 813, 270, 844], [98, 842, 157, 867], [190, 844, 524, 867], [98, 867, 157, 892], [192, 869, 351, 892], [98, 892, 157, 919], [192, 894, 618, 917], [98, 917, 155, 944], [194, 922, 371, 940], [98, 944, 157, 969], [192, 946, 606, 969], [98, 969, 157, 996], [192, 971, 733, 994], [98, 994, 157, 1021], [190, 994, 244, 1022], [98, 1021, 157, 1046], [194, 1022, 504, 1046], [98, 1046, 157, 1071], [192, 1047, 447, 1071], [98, 1071, 157, 1098], [192, 1072, 297, 1098], [98, 1096, 157, 1123], [192, 1098, 476, 1121], [98, 1123, 157, 1148], [194, 1124, 443, 1148], [98, 1148, 157, 1174], [192, 1149, 689, 1173], [98, 1173, 157, 1199], [192, 1173, 277, 1199], [98, 1199, 157, 1224], [192, 1199, 406, 1224], [98, 1224, 157, 1249], [192, 1226, 414, 1249], [98, 1249, 157, 1274], [190, 1249, 364, 1273], [98, 1274, 157, 1301], [192, 1274, 260, 1301], [98, 1300, 157, 1326], [192, 1303, 417, 1326], [98, 1326, 157, 1351], [190, 1324, 364, 1351], [98, 1351, 157, 1376], [192, 1353, 412, 1376], [98, 1376, 155, 1403], [190, 1378, 279, 1403], [100, 1403, 155, 1428], [190, 1403, 347, 1426], [98, 1428, 155, 1453], [188, 1428, 262, 1455], [98, 1453, 157, 1478], [194, 1455, 534, 1478], [98, 1478, 157, 1505], [190, 1478, 362, 1505], [98, 1505, 157, 1530], [192, 1505, 314, 1530], [98, 1530, 157, 1555], [192, 1532, 384, 1555], [98, 1555, 157, 1582], [188, 1555, 244, 1584], [98, 1580, 157, 1607], [190, 1582, 262, 1609], [98, 1607, 157, 1632], [190, 1607, 593, 1632], [100, 1632, 157, 1657], [190, 1632, 260, 1659], [98, 1657, 157, 1684], [192, 1659, 474, 1682], [100, 1684, 157, 1709], [192, 1686, 430, 1709]]}, "table_res_list": [{"cell_box_list": [[98.70755767822266, 76.34599024057388, 170.83861541748047, 103.30792427062988], [194.80342864990234, 76.1965816617012, 768.0, 103.55651473999023], [43.263999938964844, 102.72017860412598, 70.3354606628418, 128.37676239013672], [70.29537200927734, 102.82455825805664, 98.81841278076172, 128.53142166137695], [98.69878387451172, 102.7440242767334, 171.0838394165039, 128.59574508666992], [194.0, 102.0, 270.0, 129.0], [70.26660537719727, 128.56844329833984, 98.84259033203125, 153.88372802734375], [98.62299346923828, 128.45977020263672, 171.16405487060547, 179.47415161132812], [194.29145050048828, 127.84300231933594, 768.0, 154.0475082397461], [70.25923156738281, 153.6587371826172, 98.76348114013672, 179.34001922607422], [193.4059066772461, 153.9556655883789, 768.0, 179.69632720947266], [98.6433334350586, 179.20370483398438, 171.5877456665039, 229.95995330810547], [192.0, 177.0, 320.0, 206.0], [193.19519805908203, 204.91393280029297, 768.0, 229.92520904541016], [98.73741149902344, 229.91902923583984, 171.79007720947266, 255.64879608154297], [193.27155303955078, 229.93665313720703, 768.0, 256.0349655151367], [70.26140594482422, 254.88031768798828, 98.78642272949219, 280.67740631103516], [98.67273712158203, 255.37786102294922, 171.9649429321289, 281.0716018676758], [194.0, 259.0, 310.0, 279.0], [98.62154388427734, 280.7390365600586, 171.95244598388672, 306.6051712036133], [193.0116195678711, 281.19478607177734, 768.0, 307.1140823364258], [43.18193817138672, 305.9232406616211, 70.26360702514648, 331.34912872314453], [70.23452758789062, 305.97916412353516, 98.81768798828125, 331.56346893310547], [98.63217163085938, 306.4502182006836, 172.02100372314453, 332.04927825927734], [194.0, 307.0, 727.0, 331.0], [70.25307083129883, 331.6303024291992, 98.81079864501953, 382.68367767333984], [98.70590209960938, 331.8568649291992, 172.02970123291016, 357.6146774291992], [192.78829193115234, 332.2252731323242, 768.0, 358.39144134521484], [43.22033500671387, 356.77562713623047, 70.33989334106445, 382.65404510498047], [98.60054779052734, 357.3731002807617, 171.9458236694336, 408.5302963256836], [192.89092254638672, 358.2724838256836, 768.0, 383.6037826538086], [70.23387908935547, 382.6436996459961, 98.81481170654297, 433.66060638427734], [192.0, 383.0, 303.0, 408.0], [43.18553924560547, 407.99376678466797, 70.25279235839844, 433.5114974975586], [98.61803436279297, 408.41780853271484, 171.9273452758789, 433.96190643310547], [192.88123321533203, 409.20470428466797, 768.0, 459.73461151123047], [43.178504943847656, 433.73213958740234, 70.34225845336914, 484.71935272216797], [70.2551498413086, 433.76219940185547, 98.81501770019531, 484.74605560302734], [98.70365905761719, 433.7903366088867, 171.99810028076172, 459.52587127685547], [98.63384246826172, 459.2993392944336, 171.94872283935547, 485.14144134521484], [194.0, 463.0, 474.0, 481.0], [70.18933868408203, 484.74044036865234, 98.79373931884766, 510.2643356323242], [98.66394805908203, 484.8550033569336, 172.07462310791016, 510.5902633666992], [192.94519805908203, 485.5427780151367, 768.0, 510.8785934448242], [43.18701171875, 510.1180953979492, 70.220458984375, 535.6582565307617], [70.19259643554688, 510.10814666748047, 98.84191131591797, 535.7816390991211], [98.64021301269531, 510.49730682373047, 172.1170883178711, 536.0534286499023], [192.0, 511.0, 399.0, 534.0], [43.1945858001709, 535.9045028686523, 70.32135391235352, 586.9099655151367], [70.21660614013672, 535.8785018920898, 98.8746566772461, 586.8885726928711], [98.69187927246094, 535.914176940918, 172.15108489990234, 561.5691146850586], [193.12975311279297, 535.9316635131836, 768.0, 562.0325546264648], [98.66207122802734, 561.3285140991211, 172.01702117919922, 587.0763168334961], [194.0, 563.0, 436.0, 586.0], [43.16275215148926, 586.9047470092773, 70.23344039916992, 637.7995529174805], [70.16291427612305, 586.8542709350586, 98.80801391601562, 612.376091003418], [98.67023468017578, 586.8846969604492, 172.06266021728516, 612.5478439331055], [194.0, 590.0, 306.0, 610.0], [70.14422607421875, 612.266960144043, 98.81909942626953, 637.892204284668], [98.64013671875, 612.4804000854492, 172.02921295166016, 638.0864181518555], [192.6917495727539, 612.9214401245117, 768.0, 637.615348815918], [43.20450782775879, 638.0432662963867, 70.3012924194336, 689.0813522338867], [70.19306564331055, 637.9572677612305, 98.86610412597656, 663.4127731323242], [98.69149017333984, 638.0212936401367, 172.05521392822266, 663.6001510620117], [192.98542022705078, 638.200798034668, 768.0, 664.0142135620117], [70.2051887512207, 663.186393737793, 98.78932189941406, 689.1135177612305], [98.62870788574219, 663.3713912963867, 172.00662994384766, 689.1661911010742], [190.0, 657.0, 342.0, 694.0], [43.17689514160156, 689.060173034668, 70.21858596801758, 739.9647750854492], [70.1442985534668, 689.012809753418, 98.78828430175781, 714.5183029174805], [98.66777801513672, 689.0493698120117, 172.01021575927734, 714.749870300293], [193.00106048583984, 689.4143600463867, 768.0, 715.417594909668], [70.14314270019531, 714.380729675293, 98.79961395263672, 740.003532409668], [98.63323211669922, 714.7155685424805, 172.0151138305664, 740.2665328979492], [196.0, 719.0, 639.0, 736.0], [43.21595764160156, 740.2270431518555, 70.3077621459961, 791.2722702026367], [70.19554901123047, 740.0681076049805, 98.83141326904297, 765.4665451049805], [98.63329315185547, 740.3239669799805, 172.04898834228516, 791.2296676635742], [192.94609832763672, 740.3427658081055, 768.0, 766.0503463745117], [70.22139739990234, 765.316276550293, 98.77020263671875, 791.3113327026367], [194.0, 767.0, 360.0, 790.0], [43.19327926635742, 791.2211227416992, 70.2388916015625, 842.1327438354492], [70.19263076782227, 791.1491012573242, 98.7971420288086, 816.7822799682617], [98.68034362792969, 791.137809753418, 172.0327377319336, 816.7730026245117], [192.0, 792.0, 476.0, 815.0], [70.16227722167969, 816.6196212768555, 98.81204986572266, 842.1971969604492], [98.63959503173828, 816.717155456543, 172.0345230102539, 842.1921920776367], [192.91478729248047, 817.2562789916992, 768.0, 842.0978317260742], [43.22119331359863, 842.3557662963867, 70.29653549194336, 867.745964050293], [70.19882202148438, 842.1011276245117, 98.81209564208984, 867.6130294799805], [98.63542175292969, 842.2572555541992, 172.03986358642578, 867.6924362182617], [192.88497161865234, 842.5109176635742, 768.0, 868.471794128418], [43.26774978637695, 867.464225769043, 70.31317520141602, 893.4436569213867], [70.22197723388672, 867.487663269043, 98.77800750732422, 893.448356628418], [98.62236785888672, 867.6195602416992, 171.97708892822266, 893.4176559448242], [192.0, 869.0, 351.0, 892.0], [98.68156433105469, 893.348258972168, 172.0655288696289, 918.9122848510742], [192.9137954711914, 893.7702560424805, 768.0, 919.347526550293], [43.234275817871094, 918.731315612793, 70.19184875488281, 944.2614669799805], [70.17654037475586, 918.7557907104492, 98.82679748535156, 944.3523483276367], [98.66399383544922, 918.8598556518555, 171.99457550048828, 944.3597946166992], [194.0, 922.0, 371.0, 940.0], [43.211483001708984, 944.5549850463867, 70.31815719604492, 995.5829391479492], [70.19441604614258, 944.3000411987305, 98.81953430175781, 969.8385543823242], [98.65727233886719, 944.5007858276367, 172.02045440673828, 969.9283981323242], [192.9899673461914, 944.8176803588867, 768.0, 970.8541793823242], [70.22586059570312, 969.669548034668, 98.78372955322266, 995.588493347168], [98.65786743164062, 969.8933029174805, 171.91246795654297, 995.5892868041992], [193.20331573486328, 970.530143737793, 768.0, 996.0856246948242], [98.68973541259766, 995.491813659668, 171.9924087524414, 1021.152702331543], [190.0, 994.0, 244.0, 1022.0], [43.22831344604492, 1020.9304122924805, 70.1963119506836, 1046.4150924682617], [70.18659210205078, 1020.9240646362305, 98.83551025390625, 1046.4333419799805], [98.66629028320312, 1021.0347213745117, 172.02104949951172, 1046.6110153198242], [193.1395034790039, 1021.2491989135742, 768.0, 1046.699577331543], [43.2054557800293, 1046.7080612182617, 70.33777236938477, 1097.7770919799805], [70.20799255371094, 1046.460075378418, 98.81615447998047, 1071.973014831543], [98.67147064208984, 1046.682243347168, 172.0879135131836, 1071.9921188354492], [192.86193084716797, 1046.8980026245117, 768.0, 1072.7411422729492], [70.244384765625, 1071.7949752807617, 98.77227783203125, 1097.7678756713867], [98.0, 1071.0, 157.0, 1098.0], [192.0, 1072.0, 297.0, 1098.0], [98.68730926513672, 1097.5790328979492, 172.25115203857422, 1123.278190612793], [193.02442169189453, 1098.096794128418, 768.0, 1124.0827560424805], [43.20966148376465, 1123.1516036987305, 70.21953201293945, 1148.6367111206055], [70.2172737121582, 1123.2128829956055, 98.83283996582031, 1148.7092208862305], [98.66045379638672, 1123.4377365112305, 172.23255157470703, 1173.848014831543], [194.0, 1124.0, 443.0, 1148.0], [43.19006538391113, 1148.896598815918, 70.3556022644043, 1199.967155456543], [70.24050903320312, 1148.601676940918, 98.80036926269531, 1174.110954284668], [193.06517791748047, 1149.444694519043, 768.0, 1174.667839050293], [70.27671432495117, 1174.012565612793, 98.74198913574219, 1200.032341003418], [98.6644058227539, 1174.156852722168, 172.11943817138672, 1199.609977722168], [193.19393157958984, 1174.8955001831055, 768.0, 1200.1686935424805], [70.24703216552734, 1199.939079284668, 98.8047103881836, 1276.443473815918], [98.70558166503906, 1199.467887878418, 172.05004119873047, 1225.621452331543], [192.0, 1199.0, 406.0, 1224.0], [43.199995040893555, 1225.338005065918, 70.24081802368164, 1250.799919128418], [98.64322662353516, 1225.7883224487305, 171.99829864501953, 1276.5478439331055], [193.06829071044922, 1226.0703048706055, 768.0, 1250.7661056518555], [43.191104888916016, 1251.098991394043, 70.34431457519531, 1276.530876159668], [193.17188262939453, 1251.3059005737305, 768.0, 1277.110221862793], [70.28824615478516, 1276.3124923706055, 98.77494812011719, 1302.1450119018555], [98.68074798583984, 1276.4665451049805, 171.9394302368164, 1302.116081237793], [192.0, 1274.0, 260.0, 1301.0], [98.69867706298828, 1301.9714279174805, 171.94684600830078, 1327.6667404174805], [193.14925384521484, 1302.456901550293, 768.0, 1328.246208190918], [43.18424606323242, 1327.5537033081055, 70.26559066772461, 1353.0014572143555], [70.25905227661133, 1327.6308517456055, 98.84532928466797, 1353.1293869018555], [98.67301940917969, 1327.7546310424805, 171.96007537841797, 1353.117057800293], [190.0, 1324.0, 364.0, 1351.0], [70.27278518676758, 1353.1184005737305, 98.8368911743164, 1378.651237487793], [98.71980285644531, 1353.2917404174805, 171.97066497802734, 1378.743522644043], [193.23807525634766, 1353.235710144043, 768.0, 1379.3173751831055], [70.2821159362793, 1378.5222091674805, 98.79346466064453, 1404.302604675293], [98.69505310058594, 1378.684196472168, 171.85712432861328, 1404.3874435424805], [190.0, 1378.0, 279.0, 1403.0], [98.73397064208984, 1404.2324142456055, 171.83194732666016, 1429.745964050293], [190.0, 1403.0, 347.0, 1426.0], [43.16407012939453, 1429.6835861206055, 70.3577880859375, 1480.4370040893555], [70.2327766418457, 1429.7224044799805, 98.8642807006836, 1455.1765060424805], [98.68152618408203, 1429.987174987793, 171.81377410888672, 1455.503044128418], [193.19841766357422, 1430.4601974487305, 768.0, 1454.8649826049805], [98.6253890991211, 1455.7065353393555, 171.8254165649414, 1505.965202331543], [193.31702423095703, 1455.680046081543, 768.0, 1480.8547286987305], [70.23625564575195, 1480.7277755737305, 98.8399887084961, 1531.2158126831055], [190.0, 1478.0, 362.0, 1505.0], [43.16770362854004, 1505.611198425293, 70.31095123291016, 1556.357292175293], [98.74723052978516, 1505.9011154174805, 171.86400604248047, 1531.5690841674805], [193.37140655517578, 1506.4445724487305, 768.0, 1531.277214050293], [70.22359466552734, 1531.064079284668, 98.86956024169922, 1581.587760925293], [98.74259185791016, 1531.5832443237305, 171.8539047241211, 1581.9099044799805], [192.0, 1532.0, 384.0, 1555.0], [193.38758087158203, 1556.4518966674805, 768.0, 1607.4448165893555], [98.64231872558594, 1582.014762878418, 171.72054290771484, 1632.9282150268555], [70.19967651367188, 1606.905387878418, 98.83425903320312, 1657.822135925293], [190.0, 1607.0, 593.0, 1632.0], [98.71537017822266, 1632.7053146362305, 171.73554229736328, 1658.703239440918], [190.0, 1632.0, 260.0, 1659.0], [98.0, 1657.0, 157.0, 1684.0], [192.0, 1659.0, 474.0, 1682.0], [100.0, 1684.0, 157.0, 1709.0], [192.0, 1686.0, 430.0, 1709.0]], "pred_html": "<html><body><table><tbody><tr><td>0.000</td><td>男性器官小骨盆;侧</td><td></td></tr><tr><td></td><td></td><td>4.243</td></tr><tr><td></td><td>0.047</td><td>虚拟模式-健康问题发展趋势列表：</td></tr><tr><td></td><td>血尿酸SERUMURICACID</td><td></td></tr><tr><td>0.067 0.081</td><td>血管紧张素Ⅱ*</td><td>胆固醇COMMONPLASMA CHOLESTERIN</td></tr><tr><td>0.087</td><td>单核细胞MONOCYTES</td><td></td></tr><tr><td></td><td>0.088</td><td>血管紧张素I*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.096</td></tr><tr><td></td><td>0.104</td><td>C反应蛋白C-REACTIVEPROTEIN</td></tr><tr><td></td><td>0.123 0.158</td><td>SEROMUCOIDS*</td></tr><tr><td></td><td>0.161</td><td>血红血球ERYTHROCYTES 铜*</td></tr><tr><td></td><td></td><td>0.186</td></tr><tr><td>0.117</td><td>PERIPHERICBLOODLEUCOCYTES</td><td></td></tr><tr><td></td><td>0.118</td><td>尿中蛋白质PROTEININURINE</td></tr><tr><td></td><td></td><td>0.121</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.127</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.139</td></tr><tr><td></td><td>0.152</td><td>BETA球蛋白</td></tr><tr><td></td><td></td><td>0.161</td></tr><tr><td></td><td>0.051</td><td>肿瘤标志物CA50*</td></tr><tr><td></td><td></td><td>0.093</td></tr><tr><td></td><td>0.095</td><td>血清中的氨基酸NITROGENOFAMINOACIDSINSERUM</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.101 0.102</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.104</td></tr><tr><td></td><td>0.104</td><td>尿中尿素</td></tr><tr><td></td><td></td><td>0.104</td></tr><tr><td></td><td></td><td>0.105</td></tr><tr><td>0.105</td><td>碱性磷酸酯酵素COMMONALKALINEPHOSPHATASE</td><td></td></tr><tr><td></td><td></td><td>0.107</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.108</td></tr><tr><td></td><td>0.108</td><td>AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.112</td></tr><tr><td></td><td></td><td>0.112</td></tr><tr><td></td><td>0.112</td><td>胰高血糖素*</td></tr><tr><td>0.112</td><td>血浆磷脂PLASMA PHOSPHOTIDES</td><td></td></tr><tr><td></td><td></td><td>0.115 0.115</td></tr><tr><td></td><td></td><td>ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM</td></tr><tr><td></td><td>0.115</td><td>肌钙蛋白*</td></tr><tr><td></td><td>0.116</td><td>尿磷URINE PHOSPHORUS</td></tr><tr><td></td><td>0.117 0.118</td><td>血磷BLOOD PHOSPHORUS</td></tr><tr><td></td><td>红细胞沉降率(ESR)</td><td></td></tr><tr><td></td><td>0.118</td><td>脂肪酶*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.119</td></tr><tr><td></td><td>0.120</td><td>尿中尿酸URINEURICACID</td></tr><tr><td></td><td>0.121</td><td>醛固酮尿*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.121</td></tr><tr><td></td><td>甲状腺素结合球蛋白</td><td></td></tr><tr><td></td><td>0.122</td><td>甲状腺球蛋白*</td></tr><tr><td></td><td>0.122 0.123</td><td>17-血浆氧皮质类固醇类</td></tr><tr><td>0.123 0.123</td><td></td><td>乳酸脱氢酵素COMMONLACTADEHYDROGENASE</td></tr><tr><td>0.124</td><td>胰岛素*</td><td></td></tr><tr><td>0.124</td><td>嗜中性粒细胞STABNEUTROPHILS</td><td>0.124</td></tr></tbody></table></body></html>", "table_ocr_pred": {"rec_polys": [[[100, 77], [157, 77], [157, 102], [100, 102]], [[192, 77], [377, 77], [377, 102], [192, 102]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[194, 102], [270, 102], [270, 129], [194, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[194, 156], [425, 156], [425, 179], [194, 179]], [[98, 179], [161, 179], [161, 204], [98, 204]], [[192, 181], [319, 177], [320, 202], [193, 206]], [[98, 204], [161, 204], [161, 229], [98, 229]], [[194, 206], [554, 206], [554, 229], [194, 229]], [[98, 231], [161, 231], [161, 256], [98, 256]], [[194, 229], [386, 229], [386, 252], [194, 252]], [[98, 256], [162, 256], [162, 281], [98, 281]], [[194, 259], [310, 259], [310, 279], [194, 279]], [[98, 281], [162, 281], [162, 306], [98, 306]], [[190, 283], [319, 279], [320, 304], [191, 308]], [[98, 306], [162, 306], [162, 332], [98, 332]], [[194, 307], [727, 307], [727, 331], [194, 331]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [491, 336], [491, 354], [196, 354]], [[98, 358], [161, 358], [161, 383], [98, 383]], [[192, 358], [342, 358], [342, 381], [192, 381]], [[98, 383], [162, 383], [162, 408], [98, 408]], [[192, 383], [303, 383], [303, 408], [192, 408]], [[98, 408], [161, 408], [161, 434], [98, 434]], [[192, 409], [415, 409], [415, 433], [192, 433]], [[98, 434], [162, 434], [162, 459], [98, 459]], [[192, 436], [225, 436], [225, 459], [192, 459]], [[98, 459], [155, 459], [155, 484], [98, 484]], [[194, 463], [474, 463], [474, 481], [194, 481]], [[98, 484], [155, 484], [155, 511], [98, 511]], [[194, 486], [443, 486], [443, 509], [194, 509]], [[98, 511], [153, 511], [153, 536], [98, 536]], [[192, 511], [399, 511], [399, 534], [192, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[196, 538], [406, 538], [406, 556], [196, 556]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[194, 563], [436, 563], [436, 586], [194, 586]], [[98, 586], [155, 586], [155, 613], [98, 613]], [[194, 590], [306, 590], [306, 610], [194, 610]], [[98, 611], [155, 611], [155, 638], [98, 638]], [[192, 613], [299, 613], [299, 638], [192, 638]], [[98, 638], [153, 638], [153, 663], [98, 663]], [[192, 638], [223, 638], [223, 663], [192, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[190, 663], [341, 657], [342, 688], [191, 694]], [[98, 688], [157, 688], [157, 715], [98, 715]], [[188, 686], [260, 686], [260, 719], [188, 719]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[196, 719], [639, 719], [639, 736], [196, 736]], [[98, 740], [155, 740], [155, 767], [98, 767]], [[194, 742], [378, 742], [378, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[194, 767], [360, 767], [360, 790], [194, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [476, 792], [476, 815], [192, 815]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[191, 813], [270, 817], [268, 844], [190, 840]], [[98, 842], [157, 842], [157, 867], [98, 867]], [[190, 844], [524, 844], [524, 867], [190, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [351, 869], [351, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[192, 894], [618, 894], [618, 917], [192, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[194, 922], [371, 922], [371, 940], [194, 940]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[192, 946], [606, 946], [606, 969], [192, 969]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[192, 971], [733, 971], [733, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[190, 994], [244, 994], [244, 1022], [190, 1022]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[194, 1022], [504, 1022], [504, 1046], [194, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[192, 1047], [447, 1047], [447, 1071], [192, 1071]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[192, 1072], [297, 1072], [297, 1098], [192, 1098]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [476, 1098], [476, 1121], [192, 1121]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[194, 1124], [443, 1124], [443, 1148], [194, 1148]], [[98, 1148], [157, 1148], [157, 1174], [98, 1174]], [[192, 1149], [689, 1149], [689, 1173], [192, 1173]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[192, 1173], [277, 1173], [277, 1199], [192, 1199]], [[98, 1199], [157, 1199], [157, 1224], [98, 1224]], [[192, 1199], [406, 1199], [406, 1224], [192, 1224]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[192, 1226], [414, 1226], [414, 1249], [192, 1249]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[190, 1249], [364, 1249], [364, 1273], [190, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[192, 1274], [260, 1274], [260, 1301], [192, 1301]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[192, 1303], [417, 1303], [417, 1326], [192, 1326]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[190, 1326], [363, 1324], [364, 1349], [190, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [412, 1353], [412, 1376], [192, 1376]], [[98, 1376], [155, 1376], [155, 1403], [98, 1403]], [[190, 1378], [279, 1378], [279, 1403], [190, 1403]], [[100, 1403], [155, 1403], [155, 1428], [100, 1428]], [[190, 1403], [347, 1403], [347, 1426], [190, 1426]], [[98, 1428], [155, 1428], [155, 1453], [98, 1453]], [[188, 1428], [262, 1428], [262, 1455], [188, 1455]], [[98, 1453], [157, 1453], [157, 1478], [98, 1478]], [[194, 1455], [534, 1455], [534, 1478], [194, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[190, 1478], [362, 1480], [362, 1505], [190, 1503]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[192, 1505], [314, 1505], [314, 1530], [192, 1530]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[192, 1532], [384, 1532], [384, 1555], [192, 1555]], [[98, 1555], [157, 1555], [157, 1582], [98, 1582]], [[188, 1555], [244, 1555], [244, 1584], [188, 1584]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[190, 1582], [262, 1582], [262, 1609], [190, 1609]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[190, 1607], [593, 1607], [593, 1632], [190, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[190, 1632], [260, 1632], [260, 1659], [190, 1659]], [[98, 1657], [157, 1657], [157, 1684], [98, 1684]], [[192, 1659], [474, 1659], [474, 1682], [192, 1682]], [[100, 1684], [157, 1684], [157, 1709], [100, 1709]], [[192, 1686], [430, 1686], [430, 1709], [192, 1709]]], "rec_texts": ["0.000", "男性器官小骨盆;侧", "4.243", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.047", "血尿酸SERUMURICACID", "0.067", "血管紧张素Ⅱ*", "0.081", "胆固醇COMMONPLASMA CHOLESTERIN", "0.087", "单核细胞MONOCYTES", "0.088", "血管紧张素I*", "0.096", "免疫球蛋白M*", "0.096", "血浆丰配化脂肪酸NONETHERIZED FATTYACIDSOF PLASMA", "0.104", "C反应蛋白C-REACTIVEPROTEIN", "0.123", "SEROMUCOIDS*", "0.158", "结合珠蛋白*", "0.161", "血红血球ERYTHROCYTES", "0.186", "铜*", "0.117", "PERIPHERICBLOODLEUCOCYTES", "0.118", "尿中蛋白质PROTEININURINE", "0.121", "嗜碱性粒细胞BASOPHILS", "0.127", "血钾PLASMAPOTASSIUM", "0.133", "尿白血球URINELEUCOCYTES", "0.139", "免疫球蛋白G", "0.152", "BETA球蛋白", "0.161", "锂*", "0.051", "肿瘤标志物CA50*", "0.093", "备解素", "0.095", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.101", "血氨SERUMAMMONIA", "0.102", "血尿素BLOODUREA", "0.104", "酸性磷酸酵素ACIDPHOSPHATASE", "0.104", "尿中尿素", "0.104", "游离胆固醇FREEPLASMACHOLESTERIN", "0.105", "血细胞比容，全血*", "0.105", "碱性磷酸酯酵素COMMONALKALINEPHOSPHATASE", "0.107", "尿氨URINEAMMONIA", "0.108", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.108", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.110", "糖苷*", "0.112", "血清淀粉酵素SERUMALPHAAMYLASE", "0.112", "肿瘤标志物MELANOGENE在尿*", "0.112", "胰高血糖素*", "0.112", "血浆磷脂PLASMA PHOSPHOTIDES", "0.115", "血清溶菌酵SERUMLYSOZYME", "0.115", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.115", "肌钙蛋白*", "0.116", "尿磷URINE PHOSPHORUS", "0.117", "血磷BLOOD PHOSPHORUS", "0.118", "红细胞沉降率(ESR)", "0.118", "脂肪酶*", "0.119", "血肌配SERUMCREATININE", "0.119", "维生素E（生育酚)*", "0.120", "尿中尿酸URINEURICACID", "0.121", "醛固酮尿*", "0.121", "RHEUMOFACTOR*", "0.121", "唾液酸*", "0.122", "血浆中性脂肪NEUTRALFATSOFPLASMA", "0.122", "甲状腺素结合球蛋白", "0.122", "甲状腺球蛋白*", "0.122", "17-血浆氧皮质类固醇类", "0.123", "睾酮*", "0.123", "催乳素*", "0.123", "乳酸脱氢酵素COMMONLACTADEHYDROGENASE", "0.124", "胰岛素*", "0.124", "嗜中性粒细胞STABNEUTROPHILS", "0.124", "嗜酸性粒细胞EOSINOPHILES"], "rec_scores": [0.9991452097892761, 0.9270201921463013, 0.9992626309394836, 0.9987521767616272, 0.9667975902557373, 0.9998911023139954, 0.9951974749565125, 0.9999173879623413, 0.95135498046875, 0.9998822212219238, 0.9771552085876465, 0.9999210238456726, 0.9562473893165588, 0.999927818775177, 0.9654912352561951, 0.9999502301216125, 0.9895482659339905, 0.9999111294746399, 0.9332330822944641, 0.999850869178772, 0.9680902361869812, 0.9998744130134583, 0.9895607829093933, 0.9999011754989624, 0.9578317999839783, 0.9998953938484192, 0.9949594736099243, 0.9999270439147949, 0.7702764272689819, 0.9991239309310913, 0.9963095784187317, 0.9995819926261902, 0.9969702363014221, 0.9994450807571411, 0.9987413883209229, 0.9994102716445923, 0.9795147180557251, 0.9996241331100464, 0.9961475729942322, 0.9995198249816895, 0.9941864013671875, 0.9996626973152161, 0.9950935244560242, 0.9993520975112915, 0.8252460360527039, 0.9996099472045898, 0.9914361834526062, 0.9998332858085632, 0.9968001246452332, 0.9998568296432495, 0.9966492652893066, 0.9995571374893188, 0.9979380369186401, 0.9993680715560913, 0.9966976046562195, 0.9995644688606262, 0.99799644947052, 0.9994826316833496, 0.9995532631874084, 0.9993721842765808, 0.9736791849136353, 0.9993208646774292, 0.9495124816894531, 0.9996339082717896, 0.9867056608200073, 0.9995242357254028, 0.9923023581504822, 0.9995409250259399, 0.9979647397994995, 0.9995819330215454, 0.9958996772766113, 0.9992928504943848, 0.9691548347473145, 0.9992367625236511, 0.9972469210624695, 0.9994252324104309, 0.98753821849823, 0.9994117021560669, 0.9709548354148865, 0.999462902545929, 0.9782581329345703, 0.9991846084594727, 0.9980633854866028, 0.9992122650146484, 0.9957689046859741, 0.9991780519485474, 0.9105976819992065, 0.9994516372680664, 0.9832112789154053, 0.9991371035575867, 0.9772404432296753, 0.9994661211967468, 0.9381177425384521, 0.999498188495636, 0.9570178985595703, 0.9989644885063171, 0.9624761939048767, 0.9992556571960449, 0.9057332277297974, 0.999311625957489, 0.9954226016998291, 0.9995757937431335, 0.9777018427848816, 0.999352753162384, 0.9959729313850403, 0.9993472099304199, 0.9542421102523804, 0.9994784593582153, 0.9841814637184143, 0.9996312856674194, 0.9959399104118347, 0.9995940327644348, 0.9298602342605591, 0.9994674921035767, 0.992068350315094, 0.9997221231460571, 0.8835622668266296, 0.9996641278266907, 0.9697609543800354, 0.9995576739311218, 0.9958463311195374, 0.999275803565979, 0.9551318883895874, 0.999648928642273, 0.9938259124755859, 0.9994627833366394, 0.9977051615715027], "rec_boxes": [[100, 77, 157, 102], [192, 77, 377, 102], [98, 102, 157, 127], [194, 102, 270, 129], [194, 129, 462, 152], [98, 154, 161, 179], [194, 156, 425, 179], [98, 179, 161, 204], [192, 177, 320, 206], [98, 204, 161, 229], [194, 206, 554, 229], [98, 231, 161, 256], [194, 229, 386, 252], [98, 256, 162, 281], [194, 259, 310, 279], [98, 281, 162, 306], [190, 279, 320, 308], [98, 306, 162, 332], [194, 307, 727, 331], [98, 332, 161, 358], [196, 336, 491, 354], [98, 358, 161, 383], [192, 358, 342, 381], [98, 383, 162, 408], [192, 383, 303, 408], [98, 408, 161, 434], [192, 409, 415, 433], [98, 434, 162, 459], [192, 436, 225, 459], [98, 459, 155, 484], [194, 463, 474, 481], [98, 484, 155, 511], [194, 486, 443, 509], [98, 511, 153, 536], [192, 511, 399, 534], [98, 536, 155, 561], [196, 538, 406, 556], [98, 561, 155, 588], [194, 563, 436, 586], [98, 586, 155, 613], [194, 590, 306, 610], [98, 611, 155, 638], [192, 613, 299, 638], [98, 638, 153, 663], [192, 638, 223, 663], [98, 663, 155, 688], [190, 657, 342, 694], [98, 688, 157, 715], [188, 686, 260, 719], [98, 713, 157, 740], [196, 719, 639, 736], [98, 740, 155, 767], [194, 742, 378, 765], [98, 765, 157, 790], [194, 767, 360, 790], [98, 790, 157, 817], [192, 792, 476, 815], [98, 815, 157, 842], [190, 813, 270, 844], [98, 842, 157, 867], [190, 844, 524, 867], [98, 867, 157, 892], [192, 869, 351, 892], [98, 892, 157, 919], [192, 894, 618, 917], [98, 917, 155, 944], [194, 922, 371, 940], [98, 944, 157, 969], [192, 946, 606, 969], [98, 969, 157, 996], [192, 971, 733, 994], [98, 994, 157, 1021], [190, 994, 244, 1022], [98, 1021, 157, 1046], [194, 1022, 504, 1046], [98, 1046, 157, 1071], [192, 1047, 447, 1071], [98, 1071, 157, 1098], [192, 1072, 297, 1098], [98, 1096, 157, 1123], [192, 1098, 476, 1121], [98, 1123, 157, 1148], [194, 1124, 443, 1148], [98, 1148, 157, 1174], [192, 1149, 689, 1173], [98, 1173, 157, 1199], [192, 1173, 277, 1199], [98, 1199, 157, 1224], [192, 1199, 406, 1224], [98, 1224, 157, 1249], [192, 1226, 414, 1249], [98, 1249, 157, 1274], [190, 1249, 364, 1273], [98, 1274, 157, 1301], [192, 1274, 260, 1301], [98, 1300, 157, 1326], [192, 1303, 417, 1326], [98, 1326, 157, 1351], [190, 1324, 364, 1351], [98, 1351, 157, 1376], [192, 1353, 412, 1376], [98, 1376, 155, 1403], [190, 1378, 279, 1403], [100, 1403, 155, 1428], [190, 1403, 347, 1426], [98, 1428, 155, 1453], [188, 1428, 262, 1455], [98, 1453, 157, 1478], [194, 1455, 534, 1478], [98, 1478, 157, 1505], [190, 1478, 362, 1505], [98, 1505, 157, 1530], [192, 1505, 314, 1530], [98, 1530, 157, 1555], [192, 1532, 384, 1555], [98, 1555, 157, 1582], [188, 1555, 244, 1584], [98, 1580, 157, 1607], [190, 1582, 262, 1609], [98, 1607, 157, 1632], [190, 1607, 593, 1632], [100, 1632, 157, 1657], [190, 1632, 260, 1659], [98, 1657, 157, 1684], [192, 1659, 474, 1682], [100, 1684, 157, 1709], [192, 1686, 430, 1709]]}}]}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//f146c753-cf93-4cff-bb1e-3c0fa392691e/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T09%3A35%3A06Z%2F-1%2F%2Fdeeacb94272cb51e2966fdc952c0f11b26d850d6afae0c21856eb069df58131c", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//f146c753-cf93-4cff-bb1e-3c0fa392691e/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T09%3A35%3A06Z%2F-1%2F%2F35140d50d4d87edac2c292a4fc1fc98d2b54170691cb8b7f814266ce7e46b35c", "table_cell_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//f146c753-cf93-4cff-bb1e-3c0fa392691e/table_cell_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T09%3A35%3A06Z%2F-1%2F%2F9f7bbca7aec60ee6d0f77aed5b087caba97e54135fc205b9317f72cf9e57bd4b"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//f146c753-cf93-4cff-bb1e-3c0fa392691e/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T09%3A35%3A06Z%2F-1%2F%2F4f5495266ac5a83e6ab72185e863ad584d0e402fe5435b3db3845c1ab51a8ab5"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}