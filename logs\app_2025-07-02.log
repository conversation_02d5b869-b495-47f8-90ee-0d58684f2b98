{"level":"INFO","timestamp":"2025-07-02T00:21:44.434+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.434+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.434+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.434+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.435+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.435+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.435+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.435+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":44060}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.456+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.457+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.457+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.457+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.457+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.464+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.464+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.464+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.464+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.465+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:21:56.837+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
2025-07-02 00:21:56.8375896 +0800 CST m=+0.020026901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:56.870+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
2025-07-02 00:21:56.8702415 +0800 CST m=+0.052678801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:56.914+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
2025-07-02 00:21:56.9147878 +0800 CST m=+0.097225101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:56.937+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
2025-07-02 00:21:56.9374489 +0800 CST m=+0.119886201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:56.969+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-02 00:21:56.969675 +0800 CST m=+0.152112301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:56.993+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
2025-07-02 00:21:56.993045 +0800 CST m=+0.175482301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.025+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
2025-07-02 00:21:57.0259644 +0800 CST m=+0.208401701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.048+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":37024}
2025-07-02 00:21:57.0486578 +0800 CST m=+0.231095101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.114+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
2025-07-02 00:21:57.114969 +0800 CST m=+0.297406301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.137+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
2025-07-02 00:21:57.1373282 +0800 CST m=+0.319765501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.148+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
2025-07-02 00:21:57.1480351 +0800 CST m=+0.330472401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.159+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
2025-07-02 00:21:57.1596249 +0800 CST m=+0.342062201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.170+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
2025-07-02 00:21:57.1707686 +0800 CST m=+0.353205901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.603+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
2025-07-02 00:21:57.6035376 +0800 CST m=+0.785974901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.615+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
2025-07-02 00:21:57.6153555 +0800 CST m=+0.797792801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.626+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
2025-07-02 00:21:57.6269563 +0800 CST m=+0.809393601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.637+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-02 00:21:57.6373542 +0800 CST m=+0.819791501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.648+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
2025-07-02 00:21:57.6486255 +0800 CST m=+0.831062801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.659+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-02 00:21:57.6598562 +0800 CST m=+0.842293501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.676+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
2025-07-02 00:21:57.6763555 +0800 CST m=+0.858792801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.682+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
2025-07-02 00:21:57.6825695 +0800 CST m=+0.865006801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.693+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
2025-07-02 00:21:57.6933005 +0800 CST m=+0.875737801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.704+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
2025-07-02 00:21:57.7045004 +0800 CST m=+0.886937701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.715+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
2025-07-02 00:21:57.7153921 +0800 CST m=+0.897829401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.726+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
2025-07-02 00:21:57.7264369 +0800 CST m=+0.908874201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.737+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
2025-07-02 00:21:57.7376869 +0800 CST m=+0.920124201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.749+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
2025-07-02 00:21:57.749177 +0800 CST m=+0.931614301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.749+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
2025-07-02 00:21:57.7496801 +0800 CST m=+0.932117401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.759+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
2025-07-02 00:21:57.7599736 +0800 CST m=+0.942410901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.782+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
2025-07-02 00:21:57.7822414 +0800 CST m=+0.964678701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.793+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
2025-07-02 00:21:57.7937833 +0800 CST m=+0.976220601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.804+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
2025-07-02 00:21:57.8046263 +0800 CST m=+0.987063601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.805+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T00:21:57.813+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
2025-07-02 00:21:57.8051367 +0800 CST m=+0.987574001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.815+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
2025-07-02 00:21:57.8138964 +0800 CST m=+0.996333701 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:57.8159827 +0800 CST m=+0.998420001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.838+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
2025-07-02 00:21:57.8381869 +0800 CST m=+1.020624201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.838+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T00:21:57.838+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
2025-07-02 00:21:57.8381869 +0800 CST m=+1.020624201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.849+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
2025-07-02 00:21:57.838752 +0800 CST m=+1.021189301 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:57.849603 +0800 CST m=+1.032040301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.860+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
2025-07-02 00:21:57.8604276 +0800 CST m=+1.042864901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.873+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T00:21:57.874+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:57.8734346 +0800 CST m=+1.055871901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.882+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
2025-07-02 00:21:57.8749766 +0800 CST m=+1.057413901 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:57.8829065 +0800 CST m=+1.065343801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.915+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
2025-07-02 00:21:57.9153059 +0800 CST m=+1.097743201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.938+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
2025-07-02 00:21:57.9385905 +0800 CST m=+1.121027801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.994+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
2025-07-02 00:21:57.9941785 +0800 CST m=+1.176615801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.026+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
2025-07-02 00:21:58.0262489 +0800 CST m=+1.208686201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.049+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
2025-07-02 00:21:58.0494128 +0800 CST m=+1.231850101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.082+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
2025-07-02 00:21:58.0822193 +0800 CST m=+1.264656601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.105+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T00:21:58.105+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
2025-07-02 00:21:58.1053647 +0800 CST m=+1.287802001 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:58.1053647 +0800 CST m=+1.287802001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.137+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T00:21:58.137+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
2025-07-02 00:21:58.1377703 +0800 CST m=+1.320207601 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:58.1377703 +0800 CST m=+1.320207601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.193+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T00:21:58.193+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
2025-07-02 00:21:58.1933415 +0800 CST m=+1.375778801 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:58.1933415 +0800 CST m=+1.375778801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.248+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
2025-07-02 00:21:58.2489545 +0800 CST m=+1.431391801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.304+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
2025-07-02 00:21:58.3045671 +0800 CST m=+1.487004401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.327+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
2025-07-02 00:21:58.3278146 +0800 CST m=+1.510251901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.360+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
2025-07-02 00:21:58.3601826 +0800 CST m=+1.542619901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.383+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
2025-07-02 00:21:58.3834573 +0800 CST m=+1.565894601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:58.5486619 +0800 CST m=+1.731099201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T00:21:58.549+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:58.5486619 +0800 CST m=+1.731099201 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:58.5491862 +0800 CST m=+1.731623501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.072+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
2025-07-02 00:21:59.0723332 +0800 CST m=+2.254770501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.105+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
2025-07-02 00:21:59.1050938 +0800 CST m=+2.287531101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.127+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
2025-07-02 00:21:59.1279616 +0800 CST m=+2.310398901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.160+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
2025-07-02 00:21:59.1600927 +0800 CST m=+2.342530001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.183+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
2025-07-02 00:21:59.1835792 +0800 CST m=+2.366016501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.215+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
2025-07-02 00:21:59.2157957 +0800 CST m=+2.398233001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.239+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
2025-07-02 00:21:59.2391406 +0800 CST m=+2.421577901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.271+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
2025-07-02 00:21:59.2719283 +0800 CST m=+2.454365601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.287+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:59.2879161 +0800 CST m=+2.470353401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.294+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
2025-07-02 00:21:59.2944157 +0800 CST m=+2.476853001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.327+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
2025-07-02 00:21:59.3275028 +0800 CST m=+2.509940101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.350+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
2025-07-02 00:21:59.3503385 +0800 CST m=+2.532775801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.384+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:59.3841352 +0800 CST m=+2.566572501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.405+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
2025-07-02 00:21:59.4059852 +0800 CST m=+2.588422501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.461+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
2025-07-02 00:21:59.4615824 +0800 CST m=+2.644019701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.494+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
2025-07-02 00:21:59.4943371 +0800 CST m=+2.676774401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.517+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
2025-07-02 00:21:59.5171102 +0800 CST m=+2.699547501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.652+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:59.6529508 +0800 CST m=+2.835388101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.916+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:59.9164308 +0800 CST m=+3.098868101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:26:58.249+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
2025-07-02 00:26:58.2496583 +0800 CST m=+301.432095601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:31:58.249+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
2025-07-02 00:31:58.2494008 +0800 CST m=+601.431838101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.480+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
2025-07-02 00:32:09.4801039 +0800 CST m=+612.662541201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
2025-07-02 00:32:09.5486354 +0800 CST m=+612.731072701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.569+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
2025-07-02 00:32:09.5697538 +0800 CST m=+612.752191101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.601+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
2025-07-02 00:32:09.6015138 +0800 CST m=+612.783951101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.625+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
2025-07-02 00:32:09.6253578 +0800 CST m=+612.807795101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.657+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
2025-07-02 00:32:09.657126 +0800 CST m=+612.839563301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.704+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
2025-07-02 00:32:09.7042779 +0800 CST m=+612.886715201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.734+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
2025-07-02 00:32:09.7347434 +0800 CST m=+612.917180701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.758+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
2025-07-02 00:32:09.758856 +0800 CST m=+612.941293301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:30.725+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":58268}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.747+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.748+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.748+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.748+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.748+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.756+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.756+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.756+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.756+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.756+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.551+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":52328}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.571+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.572+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.572+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.572+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.572+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.580+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.580+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.580+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.580+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.580+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.476+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-02T00:21:57.081+0800"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"37024"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":37024}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":37024}
{"level":"WARN","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":37024,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":37024}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.478+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.478+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":53472}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.528+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.528+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.528+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.528+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.528+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.985+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.986+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.987+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.987+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.987+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.987+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.994+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.000+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.000+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.000+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.000+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.571+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.634+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.643+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.643+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.643+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.658+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.662+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.662+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T00:32:56.635+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T00:32:56.635+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T00:32:56.635+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.125+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.125+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.125+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.125+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.125+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.125+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.216+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.264+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.264+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.264+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.264+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.264+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.287+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.287+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.287+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.287+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.288+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.827+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":20792}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.828+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.828+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.829+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.829+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.829+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.836+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.836+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.836+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.836+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.836+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
