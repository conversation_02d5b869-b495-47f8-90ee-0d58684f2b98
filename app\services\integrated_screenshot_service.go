package services

import (
	"fmt"
	"sync"
	"time"
)

// IntegratedScreenshotService 集成截图服务，协调并发截图和OCR处理
type IntegratedScreenshotService struct {
	roundService      *RoundService
	screenshotService *ConcurrentScreenshotService
	roundManager      *ScreenshotRoundManager
	mu                sync.RWMutex
	initialized       bool
	isRunning         bool
}

// NewIntegratedScreenshotService 创建新的集成截图服务
func NewIntegratedScreenshotService(roundService *RoundService, screenshotService *ConcurrentScreenshotService, roundManager *ScreenshotRoundManager) *IntegratedScreenshotService {
	return &IntegratedScreenshotService{
		roundService:      roundService,
		screenshotService: screenshotService,
		roundManager:      roundManager,
	}
}

// Initialize 初始化服务
func (iss *IntegratedScreenshotService) Initialize() error {
	iss.mu.Lock()
	defer iss.mu.Unlock()

	if iss.initialized {
		return fmt.Errorf("服务已初始化")
	}

	iss.initialized = true
	iss.isRunning = true
	fmt.Println("[集成服务] 集成截图服务已初始化")
	return nil
}

// Shutdown 关闭服务
func (iss *IntegratedScreenshotService) Shutdown() error {
	iss.mu.Lock()
	defer iss.mu.Unlock()

	if !iss.initialized {
		return fmt.Errorf("服务未初始化")
	}

	iss.initialized = false
	iss.isRunning = false
	fmt.Println("[集成服务] 集成截图服务已关闭")
	return nil
}

// StartNewRound 开始新一轮的并发截图
func (iss *IntegratedScreenshotService) StartNewRound(userName string) (*ScreenshotRound, error) {
	iss.mu.Lock()
	defer iss.mu.Unlock()

	if !iss.initialized {
		return nil, fmt.Errorf("服务尚未初始化")
	}

	round, err := iss.roundService.StartNewRound(userName)
	if err != nil {
		return nil, err
	}

	iss.isRunning = true
	return round, nil
}

// TakeScreenshot 在指定轮次中执行截图
func (iss *IntegratedScreenshotService) TakeScreenshot(roundNumber int, mode string) error {
	iss.mu.RLock()
	if !iss.isRunning {
		iss.mu.RUnlock()
		return fmt.Errorf("服务当前未运行，无法截图")
	}
	iss.mu.RUnlock()

	return iss.screenshotService.TakeScreenshot(roundNumber, mode)
}

// TakeScreenshotLegacy 传统截图方法（兼容性）
// 保持与原有代码的兼容性，但不使用并发处理
func (iss *IntegratedScreenshotService) TakeScreenshotLegacy(mode string, userName string) (string, error) {
	// ConcurrentScreenshotService.TakeScreenshot returns error, not (string, error)
	// We need to use the underlying screenshot service for legacy compatibility
	if iss.screenshotService.screenshotService != nil {
		return iss.screenshotService.screenshotService.TakeScreenshot(mode, userName)
	}
	return "", fmt.Errorf("screenshot service not available")
}

// TakeScreenshotWithOptimizedNaming 使用优化命名的截图方法（兼容性）
func (iss *IntegratedScreenshotService) TakeScreenshotWithOptimizedNaming(
	mode string, userName string, organName string, currentUser interface{}, currentRound int) (string, error) {
	// ConcurrentScreenshotService doesn't have TakeScreenshotWithOptimizedNaming
	// We need to use the underlying screenshot service
	if iss.screenshotService.screenshotService != nil {
		return iss.screenshotService.screenshotService.TakeScreenshotWithOptimizedNaming(mode, userName, organName, currentUser, currentRound)
	}
	return "", fmt.Errorf("screenshot service not available")
}

// GetRound 获取指定轮次的信息
func (iss *IntegratedScreenshotService) GetRound(roundNumber int) (*ScreenshotRound, bool) {
	return iss.roundService.GetRound(roundNumber)
}

// GetCurrentRoundNumber 获取当前轮次号
func (iss *IntegratedScreenshotService) GetCurrentRoundNumber() int {
	return iss.roundService.GetCurrentRoundNumber()
}

// SyncRoundNumber 同步轮次号到内部管理器
func (iss *IntegratedScreenshotService) SyncRoundNumber(roundNumber int) error {
	iss.mu.Lock()
	defer iss.mu.Unlock()

	if !iss.initialized {
		return fmt.Errorf("服务未初始化")
	}

	if iss.roundManager == nil {
		return fmt.Errorf("轮次管理器未初始化")
	}

	// 同步轮次号到截图轮次管理器
	iss.roundManager.mu.Lock()
	oldRound := iss.roundManager.currentRound
	iss.roundManager.currentRound = roundNumber
	iss.roundManager.mu.Unlock()

	fmt.Printf("[集成服务] 轮次号同步: %d -> %d\n", oldRound, roundNumber)
	return nil
}

// GetAllRounds 获取所有轮次的信息
func (iss *IntegratedScreenshotService) GetAllRounds() map[int]*ScreenshotRound {
	return iss.roundService.GetAllRounds()
}

// GetTotalRounds 获取总轮次数
func (iss *IntegratedScreenshotService) GetTotalRounds() int {
	return len(iss.roundService.GetAllRounds())
}

// GetCompletedRoundsCount 获取已完成的轮次数
func (iss *IntegratedScreenshotService) GetCompletedRoundsCount() int {
	completedRounds := 0
	for _, round := range iss.roundService.GetAllRounds() {
		if round.Status == RoundCompleted {
			completedRounds++
		}
	}
	return completedRounds
}

// GetOverallProgress 获取整体进度
func (iss *IntegratedScreenshotService) GetOverallProgress() map[string]interface{} {
	iss.mu.RLock()
	initialized := iss.initialized
	iss.mu.RUnlock()

	if !initialized {
		return map[string]interface{}{
			"error": "服务未初始化",
		}
	}

	// 获取OCR处理器的统计信息
	ocrStats := iss.screenshotService.GetOCRProcessorStats()

	// 计算成功率
	totalProcessed := ocrStats.TotalCompleted + ocrStats.TotalFailed
	successRate := 0.0
	if totalProcessed > 0 {
		successRate = float64(ocrStats.TotalCompleted) / float64(totalProcessed)
	}

	// 获取所有轮次
	allRounds := iss.roundService.GetAllRounds()
	totalRounds := len(allRounds)
	completedRounds := 0
	for _, round := range allRounds {
		if round.Status == RoundCompleted {
			completedRounds++
		}
	}

	// 合并进度信息
	return map[string]interface{}{
		"total_rounds":     totalRounds,
		"completed_rounds": completedRounds,
		"ocr_stats": map[string]interface{}{
			"total_submitted":  ocrStats.TotalSubmitted,
			"total_completed":  ocrStats.TotalCompleted,
			"total_failed":     ocrStats.TotalFailed,
			"success_rate":     successRate,
			"average_duration": ocrStats.AverageTime,
		},
	}
}

// GetOCRProcessorStats 获取OCR处理器统计信息
func (iss *IntegratedScreenshotService) GetOCRProcessorStats() *ProcessorStats {
	return iss.screenshotService.GetOCRProcessorStats()
}

// IsRunning 检查服务是否运行中
func (iss *IntegratedScreenshotService) IsRunning() bool {
	iss.mu.RLock()
	defer iss.mu.RUnlock()
	return iss.initialized && iss.isRunning
}

// SetMaxRounds 设置最大轮次数
func (iss *IntegratedScreenshotService) SetMaxRounds(maxRounds int) {
	iss.mu.RLock()
	initialized := iss.initialized
	iss.mu.RUnlock()

	if initialized {
		// RoundService doesn't have SetMaxRounds method
		// We can use the roundManager directly
		if iss.roundManager != nil {
			iss.roundManager.SetMaxRounds(maxRounds)
		}
	}
}

// setupCallbacks 设置回调函数
func (iss *IntegratedScreenshotService) setupCallbacks() {
	// 注册轮次进度回调
	iss.roundManager.AddProgressCallback(func(round *ScreenshotRound, progress float64) {
		fmt.Printf("[集成服务] 轮次%d进度更新: %.1f%% (%d/%d)\n",
			round.RoundNumber, progress*100, len(round.Results), len(round.Screenshots))
	})

	// 注册轮次完成回调
	iss.roundManager.AddCompletedCallback(func(round *ScreenshotRound) {
		round.RLock()
		status := round.Status
		errorCount := len(round.Errors)
		successCount := 0
		for _, result := range round.Results {
			if result.Error == nil {
				successCount++
			}
		}
		duration := time.Since(round.StartTime)
		round.RUnlock()

		if status == RoundCompleted {
			fmt.Printf("[集成服务] 轮次%d已完成 - 成功: %d, 错误: %d, 耗时: %v\n",
				round.RoundNumber, successCount, errorCount, duration)
		} else {
			fmt.Printf("[集成服务] 轮次%d处理失败 - 成功: %d, 错误: %d, 耗时: %v\n",
				round.RoundNumber, successCount, errorCount, duration)
		}

		// 这里可以添加更多的完成后处理逻辑
		// 例如：保存结果到数据库、发送通知等
		iss.handleRoundCompleted(round)
	})
}

// handleRoundCompleted 处理轮次完成事件
func (iss *IntegratedScreenshotService) handleRoundCompleted(round *ScreenshotRound) {
	// 这里可以添加轮次完成后的处理逻辑
	// 例如：
	// 1. 保存结果到数据库
	// 2. 生成报告
	// 3. 发送通知
	// 4. 清理临时文件

	round.RLock()
	defer round.RUnlock()

	// 示例：打印详细的结果信息
	fmt.Printf("[集成服务] 轮次%d详细结果:\n", round.RoundNumber)
	for i, result := range round.Results {
		if result.Error != nil {
			fmt.Printf("  截图%d: 失败 - %v\n", i+1, result.Error)
		} else {
			fmt.Printf("  截图%d: 成功 - 器官: %s, 耗时: %v\n",
				i+1, result.Result.OrganName, result.Duration)
		}
	}

	// TODO: 在这里添加具体的业务逻辑
}

// WaitForRoundCompletion 等待指定轮次完成
func (iss *IntegratedScreenshotService) WaitForRoundCompletion(roundNumber int, timeout time.Duration) (*ScreenshotRound, error) {
	return iss.roundService.WaitForRoundCompletion(roundNumber, timeout)
}

// GetRoundResults 获取指定轮次的所有OCR结果
func (iss *IntegratedScreenshotService) GetRoundResults(roundNumber int) ([]*OCRTaskResult, error) {
	round, exists := iss.GetRound(roundNumber)
	if !exists {
		return nil, fmt.Errorf("轮次 %d 不存在", roundNumber)
	}

	round.mu.RLock()
	defer round.mu.RUnlock()

	// 创建结果副本
	results := make([]*OCRTaskResult, len(round.Results))
	copy(results, round.Results)

	return results, nil
}

// SetConcurrentScreenshotService 设置并发截图服务
func (iss *IntegratedScreenshotService) SetConcurrentScreenshotService(service *ConcurrentScreenshotService) {
	iss.mu.Lock()
	defer iss.mu.Unlock()
	iss.screenshotService = service
}
