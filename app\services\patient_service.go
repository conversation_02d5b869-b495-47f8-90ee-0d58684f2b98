package services

import (
	"fmt"
	"strconv"
	"time"

	"MagneticOperator/app/models"

	"github.com/mozillazg/go-pinyin"
)

// PatientService 患者服务
type PatientService struct {
	configService             *ConfigService
	apiService                *APIService
	patientList               []models.Patient
	currentRegistrationNumber int
}

// NewPatientService 创建新的患者服务
func NewPatientService(configService *ConfigService, apiService *APIService) *PatientService {
	return &PatientService{
		configService:             configService,
		apiService:                apiService,
		patientList:               make([]models.Patient, 0),
		currentRegistrationNumber: 1,
	}
}

// AddPatient 添加患者
func (ps *PatientService) AddPatient(name string) error {
	if name == "" {
		return fmt.Errorf("患者姓名不能为空")
	}

	// 生成挂号码
	fullCode, shortCode := ps.generateRegistrationNumber()

	// 创建患者信息
	patient := models.Patient{
		Name:             name,
		RegistrationCode: shortCode,
		FullCode:         fullCode,
		RegisterTime:     time.Now().Format("2006-01-02 15:04:05"),
	}

	// 添加到列表
	ps.patientList = append(ps.patientList, patient)

	// // 调用扣子API推送挂号信息
	// // TODO: Update this section to correctly call CallCozeWorkflow with all required CozeUserInput parameters.
	// // Example of how you might gather the necessary data and call the updated function:
	// /*
	// if ps.apiService != nil {
	// 	// Placeholder: You need to obtain actual user data (ID, Gender, BirthDate, RegistrationID)
	// 	// This might come from the 'patient' object if it's extended, or another source.
	// 	userInput := models.CozeUserInput{
	// 		UserID:         "some_user_id",       // Replace with actual user ID
	// 		Gender:         1,                    // Replace with actual gender (e.g., 1 for male, 2 for female)
	// 		BirthDate:      "1990-01-01",         // Replace with actual birth date in YYYY-MM-DD format
	// 		RegistrationID: patient.FullCode,     // Or another relevant ID for registration
	// 	}
	// 	// Assuming picURL, picName, and reportID are available or generated here
	// 	picURL := "http://example.com/image.png" // Placeholder
	// 	picName := "image.png"                   // Placeholder
	// 	reportID := "report123"                 // Placeholder

	// 	if err := ps.apiService.CallCozeWorkflow(picURL, picName, reportID, userInput); err != nil {
	// 		return fmt.Errorf("推送挂号信息失败: %v", err)
	// 	}
	// }
	// */

	return nil
}

// GetPatientList 获取患者列表
func (ps *PatientService) GetPatientList() []models.Patient {
	return ps.patientList
}

// GetCurrentRegistrationNumber 获取当前挂号序号
func (ps *PatientService) GetCurrentRegistrationNumber() int {
	return ps.currentRegistrationNumber
}

// generateRegistrationNumber 生成挂号码
func (ps *PatientService) generateRegistrationNumber() (string, string) {
	config := ps.configService.GetConfig()
	if config == nil {
		return "", ""
	}

	// 获取当前日期
	now := time.Now()
	dateStr := now.Format("20060102")

	// 生成完整挂号码：网点ID-日期-序号
	fullCode := fmt.Sprintf("%s-%s-%03d", config.SiteInfo.SiteID, dateStr, ps.currentRegistrationNumber)

	// 生成短挂号码：序号（3位数字）
	shortCode := fmt.Sprintf("%03d", ps.currentRegistrationNumber)

	// 递增序号
	ps.currentRegistrationNumber++

	return fullCode, shortCode
}

// ConvertToPinyin 将中文姓名转换为拼音
func (ps *PatientService) ConvertToPinyin(name string) string {
	pinyinArgs := pinyin.NewArgs()
	pys := pinyin.Pinyin(name, pinyinArgs)
	var result string
	for _, py := range pys {
		if len(py) > 0 {
			result += py[0]
		}
	}
	return result
}

// RemovePatient 移除患者
func (ps *PatientService) RemovePatient(index int) error {
	if index < 0 || index >= len(ps.patientList) {
		return fmt.Errorf("无效的患者索引")
	}

	// 移除患者
	ps.patientList = append(ps.patientList[:index], ps.patientList[index+1:]...)
	return nil
}

// ClearPatientList 清空患者列表
func (ps *PatientService) ClearPatientList() {
	ps.patientList = make([]models.Patient, 0)
	ps.currentRegistrationNumber = 1
}

// GetPatientByRegistrationCode 根据挂号码获取患者
func (ps *PatientService) GetPatientByRegistrationCode(code string) (*models.Patient, error) {
	for _, patient := range ps.patientList {
		if patient.RegistrationCode == code || patient.FullCode == code {
			return &patient, nil
		}
	}
	return nil, fmt.Errorf("未找到挂号码为 %s 的患者", code)
}

// UpdatePatient 更新患者信息
func (ps *PatientService) UpdatePatient(index int, name string) error {
	if index < 0 || index >= len(ps.patientList) {
		return fmt.Errorf("无效的患者索引")
	}

	if name == "" {
		return fmt.Errorf("患者姓名不能为空")
	}

	ps.patientList[index].Name = name
	return nil
}

// GetTodayPatientCount 获取今日患者数量
func (ps *PatientService) GetTodayPatientCount() int {
	return len(ps.patientList)
}

// ExportPatientList 导出患者列表（可用于数据持久化）
func (ps *PatientService) ExportPatientList() map[string]interface{} {
	return map[string]interface{}{
		"patients":                    ps.patientList,
		"current_registration_number": ps.currentRegistrationNumber,
		"export_time":                 time.Now().Format("2006-01-02 15:04:05"),
	}
}

// ImportPatientList 导入患者列表（可用于数据恢复）
func (ps *PatientService) ImportPatientList(data map[string]interface{}) error {
	if patients, ok := data["patients"].([]models.Patient); ok {
		ps.patientList = patients
	}

	if currentNum, ok := data["current_registration_number"].(int); ok {
		ps.currentRegistrationNumber = currentNum
	} else if currentNumFloat, ok := data["current_registration_number"].(float64); ok {
		ps.currentRegistrationNumber = int(currentNumFloat)
	} else if currentNumStr, ok := data["current_registration_number"].(string); ok {
		if num, err := strconv.Atoi(currentNumStr); err == nil {
			ps.currentRegistrationNumber = num
		}
	}

	return nil
}
