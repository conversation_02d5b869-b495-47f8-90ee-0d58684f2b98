<template>
  <div class="crop-settings-panel">
    <div class="panel-header">
      <h3>裁剪设置</h3>
      <button @click="toggleEdit" class="edit-btn">
        {{ isEditing ? '保存' : '编辑' }}
      </button>
    </div>
    
    <div class="panel-content">
      <div class="preview-area">
        <div class="crop-preview">
          <div class="screen-area">
            <div 
              class="crop-area" 
              :style="cropAreaStyle"
            >
              <span class="crop-label">裁剪区域</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="settings-form">
        <div class="form-row">
          <div class="form-group">
            <label>上边距 (%):</label>
            <input 
              v-if="isEditing" 
              v-model.number="editForm.CropSettings.TopPercent" 
              type="number" 
              step="0.001"
              min="0"
              max="1"
              @input="updatePreview"
            />
            <span v-else>{{ formatPercent(config?.CropSettings?.TopPercent) }}</span>
          </div>
          
          <div class="form-group">
            <label>下边距 (%):</label>
            <input 
              v-if="isEditing" 
              v-model.number="editForm.CropSettings.BottomPercent" 
              type="number" 
              step="0.001"
              min="0"
              max="1"
              @input="updatePreview"
            />
            <span v-else>{{ formatPercent(config?.CropSettings?.BottomPercent) }}</span>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label>左边距 (%):</label>
            <input 
              v-if="isEditing" 
              v-model.number="editForm.CropSettings.LeftPercent" 
              type="number" 
              step="0.001"
              min="0"
              max="1"
              @input="updatePreview"
            />
            <span v-else>{{ formatPercent(config?.CropSettings?.LeftPercent) }}</span>
          </div>
          
          <div class="form-group">
            <label>右边距 (%):</label>
            <input 
              v-if="isEditing" 
              v-model.number="editForm.CropSettings.RightPercent" 
              type="number" 
              step="0.001"
              min="0"
              max="1"
              @input="updatePreview"
            />
            <span v-else>{{ formatPercent(config?.CropSettings?.RightPercent) }}</span>
          </div>
        </div>
        
        <div class="form-group full-width">
          <label>图像增强:</label>
          <div class="checkbox-group">
            <label class="checkbox-label">
              <input 
                v-if="isEditing" 
                v-model="editForm.CropSettings.EnableContrast" 
                type="checkbox"
              />
              <span v-else class="checkbox-display">
                {{ config?.CropSettings?.EnableContrast ? '✓' : '✗' }}
              </span>
              对比度增强
            </label>
            
            <label class="checkbox-label">
              <input 
                v-if="isEditing" 
                v-model="editForm.CropSettings.EnableSharpen" 
                type="checkbox"
              />
              <span v-else class="checkbox-display">
                {{ config?.CropSettings?.EnableSharpen ? '✓' : '✗' }}
              </span>
              锐化处理
            </label>
            
            <label class="checkbox-label">
              <input 
                v-if="isEditing" 
                v-model="editForm.CropSettings.EnableSmooth" 
                type="checkbox"
              />
              <span v-else class="checkbox-display">
                {{ config?.CropSettings?.EnableSmooth ? '✓' : '✗' }}
              </span>
              平滑处理
            </label>
          </div>
        </div>
        
        <div class="preset-buttons" v-if="isEditing">
          <button @click="applyPreset('default')" class="preset-btn">默认设置</button>
          <button @click="applyPreset('wide')" class="preset-btn">宽屏模式</button>
          <button @click="applyPreset('narrow')" class="preset-btn">窄屏模式</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CropSettingsPanel',
  props: {
    config: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      isEditing: false,
      editForm: {
        CropSettings: {
          TopPercent: 0.153,
          BottomPercent: 0.051,
          LeftPercent: 0.0482,
          RightPercent: 0.30,
          EnableContrast: true,
          EnableSharpen: false,
          EnableSmooth: false
        }
      },
      presets: {
        default: {
          TopPercent: 0.153,
          BottomPercent: 0.051,
          LeftPercent: 0.0482,
          RightPercent: 0.30
        },
        wide: {
          TopPercent: 0.1,
          BottomPercent: 0.1,
          LeftPercent: 0.05,
          RightPercent: 0.05
        },
        narrow: {
          TopPercent: 0.2,
          BottomPercent: 0.1,
          LeftPercent: 0.1,
          RightPercent: 0.4
        }
      }
    }
  },
  computed: {
    cropAreaStyle() {
      const settings = this.isEditing ? this.editForm.CropSettings : (this.config?.CropSettings || {})
      
      const top = (settings.TopPercent || 0) * 100
      const bottom = (settings.BottomPercent || 0) * 100
      const left = (settings.LeftPercent || 0) * 100
      const right = (settings.RightPercent || 0) * 100
      
      return {
        top: `${top}%`,
        bottom: `${bottom}%`,
        left: `${left}%`,
        right: `${right}%`
      }
    }
  },
  watch: {
    config: {
      handler(newConfig) {
        if (newConfig && newConfig.CropSettings) {
          this.editForm.CropSettings = { ...newConfig.CropSettings }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    toggleEdit() {
      if (this.isEditing) {
        // 保存
        this.saveChanges()
      } else {
        // 开始编辑
        if (this.config && this.config.CropSettings) {
          this.editForm.CropSettings = { ...this.config.CropSettings }
        }
      }
      this.isEditing = !this.isEditing
    },
    
    saveChanges() {
      // 验证数值范围
      const settings = this.editForm.CropSettings
      
      if (settings.TopPercent < 0 || settings.TopPercent > 1 ||
          settings.BottomPercent < 0 || settings.BottomPercent > 1 ||
          settings.LeftPercent < 0 || settings.LeftPercent > 1 ||
          settings.RightPercent < 0 || settings.RightPercent > 1) {
        this.showNotification('参数错误', '裁剪参数必须在0-1之间')
        return
      }
      
      if (settings.TopPercent + settings.BottomPercent >= 1 ||
          settings.LeftPercent + settings.RightPercent >= 1) {
        this.showNotification('参数错误', '裁剪参数设置不合理，请检查边距设置')
        return
      }
      
      // 发送更新事件
      this.$emit('update-crop', this.editForm)
    },
    
    updatePreview() {
      // 实时更新预览
      this.$forceUpdate()
    },
    
    applyPreset(presetName) {
      if (this.presets[presetName]) {
        Object.assign(this.editForm.CropSettings, this.presets[presetName])
        this.updatePreview()
      }
    },
    
    formatPercent(value) {
      if (typeof value !== 'number') return '0.00%'
      return (value * 100).toFixed(2) + '%'
    },
    
    showNotification(title, message) {
      // 调用后端的置顶信息窗口通知
      if (window.go && window.go.main && window.go.main.App) {
        window.go.main.App.ShowWailsNotification('error', title, message, 5000)
      } else {
        // 备用方案：控制台输出
        console.error(`${title}: ${message}`)
      }
    }
  }
}
</script>

<style scoped>
.crop-settings-panel {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  width: 100%;
  box-sizing: border-box;
}

.panel-header {
  background: #f8f9fa;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.edit-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.edit-btn:hover {
  background: #2980b9;
}

.panel-content {
  padding: 16px;
}

.preview-area {
  margin-bottom: 20px;
}

.crop-preview {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  background: #f8f9fa;
}

.screen-area {
  width: 100%;
  height: 120px;
  background: linear-gradient(45deg, #e0e0e0 25%, transparent 25%), 
              linear-gradient(-45deg, #e0e0e0 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #e0e0e0 75%), 
              linear-gradient(-45deg, transparent 75%, #e0e0e0 75%);
  background-size: 10px 10px;
  background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
  position: relative;
  border: 2px solid #666;
}

.crop-area {
  position: absolute;
  background: rgba(52, 152, 219, 0.3);
  border: 2px dashed #3498db;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.crop-label {
  background: rgba(52, 152, 219, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  gap: 12px;
}

.form-group {
  flex: 1;
}

.form-group.full-width {
  width: 100%;
  box-sizing: border-box;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.form-group input[type="number"] {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  box-sizing: border-box;
  max-width: 100%;
}

.form-group input[type="number"]:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group span {
  display: block;
  padding: 6px 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.checkbox-display {
  width: 16px;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

.preset-buttons {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.preset-btn {
  flex: 1;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.preset-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}
</style>