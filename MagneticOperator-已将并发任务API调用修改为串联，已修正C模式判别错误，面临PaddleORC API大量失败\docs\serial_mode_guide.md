# 串行OCR处理模式使用指南

## 概述

为了解决单台设备并发OCR API请求导致的调用失败问题，系统现在支持串行处理模式。该模式确保OCR请求按顺序执行，避免API服务器过载。

## 问题背景

- **并发问题**: 单台设备20个并发OCR请求导致API调用失败
- **多设备影响**: 多台设备同时发出并发请求会加剧问题
- **时间匹配**: 健康检测需要10-15分钟，20次串行OCR请求约14分钟，时间完美匹配

## 解决方案

### 串行处理策略

1. **顺序执行**: 每次只处理一个OCR请求
2. **用户队列**: 支持多用户排队，当前用户完成后处理下一用户
3. **时间优化**: 单次OCR请求30-40秒，20次约14分钟

### 配置说明

在 `config/app_config.json` 中的相关配置：

```json
{
  "concurrency": {
    "serial_mode": true,        // 启用串行模式
    "max_workers": 1,          // 工作线程数设为1
    "rate_limit": 1,           // 速率限制设为1
    "rate_burst": 1            // 速率突发设为1
  }
}
```

## 使用方法

### 1. 启用串行模式

修改配置文件 `config/app_config.json`：

```json
"serial_mode": true
```

### 2. 重启应用

重启应用程序以加载新配置。

### 3. 验证模式

应用启动时会显示：
```
[系统] 串行模式已启用 - 单台设备20次截图将按顺序处理，预计耗时约14分钟
```

## 工作流程

### 单用户处理流程

1. 用户提交20个截图任务
2. 任务进入串行队列
3. 按顺序逐个处理OCR请求
4. 每个请求完成后再处理下一个
5. 所有任务完成后释放队列

### 多用户队列管理

1. **用户A** 提交任务 → 立即开始处理
2. **用户B** 提交任务 → 进入等待队列
3. **用户C** 提交任务 → 进入等待队列
4. 用户A完成 → 自动切换到用户B
5. 用户B完成 → 自动切换到用户C

## 性能对比

| 模式 | 并发数 | 成功率 | 单用户耗时 | 适用场景 |
|------|--------|--------|------------|----------|
| 并发模式 | 20 | 低 | 快速但失败多 | 理想网络环境 |
| 串行模式 | 1 | 高 | 14分钟 | 生产环境推荐 |

## 监控和调试

### 查看处理状态

系统会输出详细的处理日志：

```
[串行OCR] 用户 张三 的任务已提交到队列
[串行OCR] 开始处理用户 张三 的任务
[串行OCR] 用户 张三 的所有任务已完成
[串行OCR] 切换到下一个用户: 李四，队列剩余: 1
```

### 错误处理

- **重试机制**: 失败的请求会自动重试
- **指数退避**: 重试间隔逐渐增加
- **最大重试**: 避免无限重试

## 注意事项

1. **时间规划**: 确保健康检测时间足够覆盖OCR处理时间
2. **用户体验**: 向用户说明处理时间较长的原因
3. **监控**: 定期检查处理日志确保系统正常运行
4. **网络环境**: 在网络条件改善后可考虑切换回并发模式

## 故障排除

### 常见问题

1. **配置未生效**: 确保重启应用程序
2. **处理缓慢**: 检查网络连接和API响应时间
3. **队列阻塞**: 查看日志确认是否有任务卡住

### 切换回并发模式

如果网络环境改善，可以切换回并发模式：

```json
"serial_mode": false
```

然后重启应用程序。