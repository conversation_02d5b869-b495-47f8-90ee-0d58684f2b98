const fs = require('fs');

try {
  const content = fs.readFileSync('f:/myHbuilderAPP/MagneticOperator/frontend/src/App.vue', 'utf8');
  const styleStart = content.indexOf('<style>');
  const styleEnd = content.indexOf('</style>');
  
  if (styleStart !== -1 && styleEnd !== -1) {
    const styleContent = content.substring(styleStart + 7, styleEnd);
    const lines = styleContent.split('\n');
    console.log('Style section lines:');
    lines.forEach((line, index) => {
      if (index < 30) {
        console.log(`${index + 1}: ${line}`);
      }
    });
    
    // 检查大括号匹配
    let openBraces = 0;
    let closeBraces = 0;
    for (let char of styleContent) {
      if (char === '{') openBraces++;
      if (char === '}') closeBraces++;
    }
    console.log(`\nOpen braces: ${openBraces}`);
    console.log(`Close braces: ${closeBraces}`);
  } else {
    console.log('Style section not found');
  }
} catch (error) {
  console.error('Error:', error.message);
}