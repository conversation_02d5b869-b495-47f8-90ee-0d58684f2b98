package utils

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"time"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts   int           `json:"max_attempts"`
	BaseDelay     time.Duration `json:"base_delay"`
	MaxDelay      time.Duration `json:"max_delay"`
	BackoffFactor float64       `json:"backoff_factor"`
	Jitter        bool          `json:"jitter"`
}

// DefaultRetryConfig 默认重试配置
var DefaultRetryConfig = RetryConfig{
	MaxAttempts:   3,
	BaseDelay:     100 * time.Millisecond,
	MaxDelay:      5 * time.Second,
	BackoffFactor: 2.0,
	Jitter:        true,
}

// ScreenshotRetryConfig 截图操作重试配置
var ScreenshotRetryConfig = RetryConfig{
	MaxAttempts:   3,
	BaseDelay:     200 * time.Millisecond,
	MaxDelay:      2 * time.Second,
	BackoffFactor: 1.5,
	Jitter:        true,
}

// OCRRetryConfig OCR操作重试配置
var OCRRetryConfig = RetryConfig{
	MaxAttempts:   2,
	BaseDelay:     500 * time.Millisecond,
	MaxDelay:      3 * time.Second,
	BackoffFactor: 2.0,
	Jitter:        false,
}

// RetryableFunc 可重试的函数类型
type RetryableFunc func() error

// RetryableFuncWithResult 可重试的带返回值函数类型
type RetryableFuncWithResult func() (interface{}, error)

// OnRetryCallback 重试回调函数类型
type OnRetryCallback func(attempt int, err error)

// RetryWithConfig 使用配置进行重试
func RetryWithConfig(ctx context.Context, config RetryConfig, fn RetryableFunc) error {
	return RetryWithConfigAndCallback(ctx, config, fn, nil)
}

// RetryWithConfigAndCallback 使用配置和回调进行重试
func RetryWithConfigAndCallback(ctx context.Context, config RetryConfig, fn RetryableFunc, onRetry OnRetryCallback) error {
	var lastErr error

	for attempt := 1; attempt <= config.MaxAttempts; attempt++ {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 执行函数
		err := fn()
		if err == nil {
			return nil
		}

		lastErr = err

		// 检查是否可重试
		if !isRetryableError(err) {
			return err
		}

		// 最后一次尝试，不再等待
		if attempt == config.MaxAttempts {
			break
		}

		// 调用重试回调
		if onRetry != nil {
			onRetry(attempt, err)
		}

		// 计算延迟时间
		delay := calculateDelay(config, attempt-1)

		// 等待重试
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// 继续重试
		}
	}

	return fmt.Errorf("重试%d次后仍然失败: %w", config.MaxAttempts, lastErr)
}

// RetryWithResult 带返回值的重试
func RetryWithResult(ctx context.Context, config RetryConfig, fn RetryableFuncWithResult) (interface{}, error) {
	return RetryWithResultAndCallback(ctx, config, fn, nil)
}

// RetryWithResultAndCallback 带返回值和回调的重试
func RetryWithResultAndCallback(ctx context.Context, config RetryConfig, fn RetryableFuncWithResult, onRetry OnRetryCallback) (interface{}, error) {
	var lastErr error

	for attempt := 1; attempt <= config.MaxAttempts; attempt++ {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		// 执行函数
		result, err := fn()
		if err == nil {
			return result, nil
		}

		lastErr = err

		// 检查是否可重试
		if !isRetryableError(err) {
			return nil, err
		}

		// 最后一次尝试，不再等待
		if attempt == config.MaxAttempts {
			break
		}

		// 调用重试回调
		if onRetry != nil {
			onRetry(attempt, err)
		}

		// 计算延迟时间
		delay := calculateDelay(config, attempt-1)

		// 等待重试
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(delay):
			// 继续重试
		}
	}

	return nil, fmt.Errorf("重试%d次后仍然失败: %w", config.MaxAttempts, lastErr)
}

// calculateDelay 计算延迟时间
func calculateDelay(config RetryConfig, attempt int) time.Duration {
	delay := time.Duration(float64(config.BaseDelay) * math.Pow(config.BackoffFactor, float64(attempt)))

	if delay > config.MaxDelay {
		delay = config.MaxDelay
	}

	// 添加抖动
	if config.Jitter {
		jitter := time.Duration(rand.Float64() * float64(delay) * 0.1)
		delay += jitter
	}

	return delay
}

// isRetryableError 判断错误是否可重试
func isRetryableError(err error) bool {
	if appErr, ok := err.(*AppError); ok {
		return appErr.IsRetryable()
	}

	// 对于非AppError，根据错误内容判断
	errorMsg := err.Error()
	retryableKeywords := []string{
		"timeout",
		"deadline",
		"connection",
		"network",
		"temporary",
		"retry",
	}

	for _, keyword := range retryableKeywords {
		if contains(errorMsg, keyword) {
			return true
		}
	}

	return false
}

// Retry 使用默认配置重试
func Retry(ctx context.Context, fn RetryableFunc) error {
	return RetryWithConfig(ctx, DefaultRetryConfig, fn)
}

// RetryScreenshot 截图操作重试
func RetryScreenshot(ctx context.Context, fn RetryableFunc) error {
	return RetryWithConfig(ctx, ScreenshotRetryConfig, fn)
}

// RetryOCR OCR操作重试
func RetryOCR(ctx context.Context, fn RetryableFunc) error {
	return RetryWithConfig(ctx, OCRRetryConfig, fn)
}

// RetryWithCallback 使用默认配置和回调重试
func RetryWithCallback(ctx context.Context, fn RetryableFunc, onRetry OnRetryCallback) error {
	return RetryWithConfigAndCallback(ctx, DefaultRetryConfig, fn, onRetry)
}

// CreateRetryContext 创建带超时的重试上下文
func CreateRetryContext(timeout time.Duration) (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), timeout)
}

// CreateRetryContextWithParent 基于父上下文创建带超时的重试上下文
func CreateRetryContextWithParent(parent context.Context, timeout time.Duration) (context.Context, context.CancelFunc) {
	return context.WithTimeout(parent, timeout)
}
