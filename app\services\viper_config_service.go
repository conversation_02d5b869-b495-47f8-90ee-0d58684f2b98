package services

import (
	"fmt"
	"sync"

	"MagneticOperator/app/models"
	"MagneticOperator/app/utils"
)

// ViperConfigService 基于Viper的配置服务
type ViperConfigService struct {
	mu           sync.RWMutex
	viperConfig  *utils.ViperConfig
	configDir    string
	environment  string
	currentConfig *models.AppConfig
}

// NewViperConfigService 创建新的Viper配置服务
func NewViperConfigService(configDir, environment string) *ViperConfigService {
	return &ViperConfigService{
		viperConfig: utils.NewViperConfig(configDir, environment),
		configDir:   configDir,
		environment: environment,
	}
}

// LoadConfig 加载配置
func (vcs *ViperConfigService) LoadConfig() (*models.AppConfig, error) {
	vcs.mu.Lock()
	defer vcs.mu.Unlock()
	
	config, err := vcs.viperConfig.LoadConfig()
	if err != nil {
		return nil, err
	}
	
	vcs.currentConfig = config
	return config, nil
}

// SaveConfig 保存配置
func (vcs *ViperConfigService) SaveConfig(config *models.AppConfig) error {
	vcs.mu.Lock()
	defer vcs.mu.Unlock()
	
	// 更新Viper配置
	vcs.updateViperFromConfig(config)
	
	// 保存到文件
	if err := vcs.viperConfig.SaveConfig(vcs.configDir); err != nil {
		return err
	}
	
	vcs.currentConfig = config
	return nil
}

// GetConfig 获取当前配置
func (vcs *ViperConfigService) GetConfig() *models.AppConfig {
	vcs.mu.RLock()
	defer vcs.mu.RUnlock()
	return vcs.currentConfig
}

// updateViperFromConfig 从配置结构体更新Viper
func (vcs *ViperConfigService) updateViperFromConfig(config *models.AppConfig) {
	// 小程序信息
	vcs.viperConfig.Set("mp_app_info.appid", config.MpAppInfo.AppID)
	vcs.viperConfig.Set("mp_app_info.target_page", config.MpAppInfo.TargetPage)
	
	// 站点信息
	vcs.viperConfig.Set("site_info.site_id", config.SiteInfo.SiteID)
	vcs.viperConfig.Set("site_info.site_name", config.SiteInfo.SiteName)
	vcs.viperConfig.Set("site_info.site_type", config.SiteInfo.SiteType)
	
	// 裁剪设置
	vcs.viperConfig.Set("crop_settings.top_percent", config.CropSettings.TopPercent)
	vcs.viperConfig.Set("crop_settings.bottom_percent", config.CropSettings.BottomPercent)
	vcs.viperConfig.Set("crop_settings.left_percent", config.CropSettings.LeftPercent)
	vcs.viperConfig.Set("crop_settings.right_percent", config.CropSettings.RightPercent)
	
	// API密钥
	vcs.viperConfig.Set("api_keys.ocr.api_url", config.APIKeys.OCR.APIURL)
	vcs.viperConfig.Set("api_keys.ocr.token", config.APIKeys.OCR.Token)
	vcs.viperConfig.Set("api_keys.coze.token", config.APIKeys.Coze.Token)
	
	// 系统通知
	vcs.viperConfig.Set("use_system_notification", config.UseSystemNotification)
	
	// 颜色检测
	vcs.viperConfig.Set("color_detection.debug_mode", config.ColorDetection.DebugMode)
	vcs.viperConfig.Set("color_detection.save_debug_files", config.ColorDetection.SaveDebugFiles)
}

// GetAPIKey 获取API密钥
func (vcs *ViperConfigService) GetAPIKey(service, key string) string {
	return vcs.viperConfig.GetString(fmt.Sprintf("api_keys.%s.%s", service, key))
}

// SetAPIKey 设置API密钥
func (vcs *ViperConfigService) SetAPIKey(service, key, value string) {
	vcs.viperConfig.Set(fmt.Sprintf("api_keys.%s.%s", service, key), value)
}

// IsDebugMode 检查是否为调试模式
func (vcs *ViperConfigService) IsDebugMode() bool {
	return vcs.viperConfig.GetBool("color_detection.debug_mode")
}

// GetCropSettings 获取裁剪设置
func (vcs *ViperConfigService) GetCropSettings() models.CropSettings {
	return models.CropSettings{
		TopPercent:    vcs.viperConfig.GetFloat64("crop_settings.top_percent"),
		BottomPercent: vcs.viperConfig.GetFloat64("crop_settings.bottom_percent"),
		LeftPercent:   vcs.viperConfig.GetFloat64("crop_settings.left_percent"),
		RightPercent:  vcs.viperConfig.GetFloat64("crop_settings.right_percent"),
		AlwaysOnTop:   vcs.viperConfig.GetBool("crop_settings.always_on_top"),
	}
}