# 火山引擎官方SDK集成指南

## 概述

本文档介绍了如何在MagneticOperator项目中集成和使用火山引擎官方SDK来替代之前的自定义OCR实现。新的实现解决了之前遇到的404错误问题，提供了更稳定和标准化的API调用方式。

## 主要改进

### 1. 添加官方SDK依赖

在 `go.mod` 中添加了火山引擎官方SDK：
```go
github.com/volcengine/volc-sdk-golang latest
```

### 2. 新增SDK客户端实现

创建了 `volcengine_ocr_sdk_client.go` 文件，实现了基于官方SDK的OCR客户端：

- **VolcEngineSDKOCRClient**: 使用官方SDK的火山引擎OCR客户端
- **ProcessImage**: 使用官方SDK处理图片的核心方法
- **parseSDKResponse**: 解析SDK响应的辅助方法

### 3. 增强OCR提供者

修改了 `volcengine_ocr_provider.go`，支持在官方SDK和自定义实现之间切换：

- 添加了 `sdkClient` 字段和 `useSDK` 标志
- 提供了 `SetUseSDK()` 和 `IsUsingSDK()` 方法来控制使用哪种实现
- 默认使用官方SDK实现

## 使用方法

### 1. 配置凭证

确保在配置文件中正确设置火山引擎OCR的凭证：

```json
{
  "api_keys": {
    "VolcEngine_ocr": {
      "api_url": "https://visual.volcengineapi.com",
      "AccessKeyID": "your_access_key_id",
      "SecretAccessKey": "your_secret_access_key"
    }
  }
}
```

### 2. 使用官方SDK（推荐）

默认情况下，系统会使用官方SDK实现：

```go
// 创建OCR提供者（默认使用官方SDK）
provider := services.NewVolcEngineOCRProvider(config, organDB)

// 处理图片
result, err := provider.ProcessImage(ctx, imagePath)
```

### 3. 切换到自定义实现

如果需要使用之前的自定义实现：

```go
// 切换到自定义客户端
provider.SetUseSDK(false)

// 检查当前使用的实现
if provider.IsUsingSDK() {
    fmt.Println("使用官方SDK")
} else {
    fmt.Println("使用自定义实现")
}
```

## API调用详情

### 官方SDK调用流程

1. **凭证配置**: 使用 `visual.DefaultInstance.Client.SetAccessKey()` 和 `SetSecretKey()` 设置凭证
2. **请求构建**: 使用 `url.Values` 构建请求参数，包含base64编码的图片数据
3. **API调用**: 调用 `visual.DefaultInstance.OCRApi(form, "OCRNormal")` 进行通用OCR识别
4. **响应解析**: 解析返回的结构化数据，提取文本和置信度信息

### 支持的OCR类型

当前实现使用 `"OCRNormal"` 进行通用OCR识别，官方SDK还支持其他类型：

- `"OCRNormal"`: 通用OCR
- `"MultiLanguageOCR"`: 多语种OCR
- `"BankCard"`: 银行卡识别
- `"IDCard"`: 身份证识别
- 等等...

## 错误处理

新的实现提供了更好的错误处理：

1. **配置验证**: 在处理前验证AccessKeyID和SecretAccessKey
2. **状态码检查**: 检查API返回的HTTP状态码
3. **响应验证**: 验证响应结构和业务状态码
4. **详细日志**: 提供详细的调试日志信息

## 测试

项目包含了测试程序 `test_volcengine_sdk.go`，可以用来验证SDK集成：

```bash
# 运行测试
go run test_volcengine_sdk.go
```

测试程序会：
- 验证配置
- 测试客户端切换功能
- 显示当前使用的实现类型

## 故障排除

### 常见问题

1. **404错误**: 使用官方SDK后应该不再出现404错误
2. **认证失败**: 检查AccessKeyID和SecretAccessKey是否正确
3. **网络问题**: 确保网络连接正常，可以访问火山引擎API

### 调试建议

1. 启用详细日志查看API调用过程
2. 使用测试程序验证基本功能
3. 如果官方SDK有问题，可以临时切换回自定义实现

## 性能优化

1. **连接复用**: 官方SDK内部处理连接复用
2. **错误重试**: 可以在上层实现重试逻辑
3. **并发控制**: 配合现有的并发控制机制使用

## 未来改进

1. 支持更多OCR类型（如表格识别、多语种等）
2. 添加区域配置支持
3. 实现更细粒度的错误处理
4. 添加性能监控和统计

## 总结

通过集成火山引擎官方SDK，我们解决了之前遇到的API调用问题，提供了更稳定和标准化的OCR服务。新的实现保持了向后兼容性，同时提供了更好的错误处理和调试能力。