package middleware

import (
	"MagneticOperator/app/utils"
	"context"
	"fmt"
	"runtime/debug"

	"go.uber.org/zap"
)

// ErrorHandler 错误处理中间件
type ErrorHandler struct {
	logger *zap.Logger
}

// NewErrorHandler 创建错误处理中间件
func NewErrorHandler(logger *zap.Logger) *ErrorHandler {
	return &ErrorHandler{logger: logger}
}

// HandleError 处理错误
func (eh *ErrorHandler) HandleError(ctx context.Context, err error, operation string) *utils.AppError {
	if err == nil {
		return nil
	}

	// 如果已经是AppError，直接返回
	if appErr, ok := err.(*utils.AppError); ok {
		eh.logError(appErr)
		return appErr
	}

	// 包装为AppError
	appErr := eh.wrapError(err, operation)
	eh.logError(appErr)
	return appErr
}

// wrapError 包装普通错误为AppError
func (eh *ErrorHandler) wrapError(err error, operation string) *utils.AppError {
	// 根据错误类型判断错误码
	code := utils.DetermineErrorCode(err)

	appErr := utils.NewAppError(
		code,
		"操作失败",
		err.Error(),
		err,
	).WithOperation(operation)

	// 添加堆栈信息（仅在开发环境）
	if utils.IsDevelopment() {
		appErr.StackTrace = string(debug.Stack())
	}

	return appErr
}

// logError 记录错误日志
func (eh *ErrorHandler) logError(appErr *utils.AppError) {
	fields := []zap.Field{
		zap.Int("error_code", int(appErr.Code)),
		zap.String("message", appErr.Message),
		zap.String("details", appErr.Details),
		zap.String("operation", appErr.Operation),
		zap.String("user_id", appErr.UserID),
		zap.Int64("timestamp", appErr.Timestamp),
	}

	if appErr.Cause != nil {
		fields = append(fields, zap.String("cause", appErr.Cause.Error()))
	}

	if appErr.StackTrace != "" {
		fields = append(fields, zap.String("stack_trace", appErr.StackTrace))
	}

	eh.logger.Error("应用错误", fields...)
}

// RecoverPanic 恢复panic并转换为错误
func (eh *ErrorHandler) RecoverPanic(operation string) *utils.AppError {
	if r := recover(); r != nil {
		var err error
		switch v := r.(type) {
		case error:
			err = v
		case string:
			err = fmt.Errorf(v)
		default:
			err = fmt.Errorf("panic: %v", v)
		}

		appErr := utils.NewAppError(
			utils.ErrSystemInternal,
			"系统异常",
			err.Error(),
			err,
		).WithOperation(operation).WithStackTrace()

		eh.logError(appErr)
		return appErr
	}
	return nil
}

// SafeExecute 安全执行函数，自动处理panic和错误
func (eh *ErrorHandler) SafeExecute(operation string, fn func() error) *utils.AppError {
	defer func() {
		if appErr := eh.RecoverPanic(operation); appErr != nil {
			// panic已经被处理和记录
		}
	}()

	err := fn()
	if err != nil {
		return eh.HandleError(context.Background(), err, operation)
	}

	return nil
}

// SafeExecuteWithResult 安全执行函数并返回结果
func (eh *ErrorHandler) SafeExecuteWithResult(operation string, fn func() (interface{}, error)) (interface{}, *utils.AppError) {
	var result interface{}
	var err error

	defer func() {
		if appErr := eh.RecoverPanic(operation); appErr != nil {
			result = nil
			err = appErr
		}
	}()

	result, err = fn()
	if err != nil {
		if appErr, ok := err.(*utils.AppError); ok {
			return nil, appErr
		}
		return nil, eh.HandleError(context.Background(), err, operation)
	}

	return result, nil
}
