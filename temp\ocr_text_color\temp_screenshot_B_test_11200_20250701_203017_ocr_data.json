{"logId": "e9803d6e-1b0e-4e68-bf72-545c8238a502", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 8, "label": "table", "score": 0.986011803150177, "coordinate": [13.65435791015625, 75.009521484375, 767.8768310546875, 1712.9893798828125]}, {"cls_id": 9, "label": "table_title", "score": 0.638416051864624, "coordinate": [19.57994842529297, 27.210477828979492, 520.6580810546875, 63.0865364074707]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.515218198299408, "coordinate": [19.57994842529297, 27.210477828979492, 520.6580810546875, 63.0865364074707]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 79], [384, 79], [384, 102], [192, 102]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[194, 156], [495, 156], [495, 179], [194, 179]], [[98, 179], [161, 179], [161, 204], [98, 204]], [[194, 181], [423, 181], [423, 204], [194, 204]], [[98, 204], [161, 204], [161, 229], [98, 229]], [[192, 204], [266, 204], [266, 231], [192, 231]], [[98, 229], [162, 229], [162, 256], [98, 256]], [[192, 231], [319, 227], [320, 254], [193, 258]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [556, 257], [556, 281], [192, 281]], [[98, 281], [161, 281], [161, 306], [98, 306]], [[196, 284], [310, 284], [310, 304], [196, 304]], [[98, 306], [161, 306], [161, 332], [98, 332]], [[194, 307], [727, 307], [727, 331], [194, 331]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [428, 336], [428, 354], [196, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[194, 359], [412, 359], [412, 383], [194, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[194, 384], [393, 384], [393, 408], [194, 408]], [[98, 408], [155, 408], [155, 434], [98, 434]], [[194, 409], [443, 409], [443, 433], [194, 433]], [[98, 434], [155, 434], [155, 459], [98, 459]], [[192, 434], [474, 434], [474, 458], [192, 458]], [[98, 458], [155, 458], [155, 484], [98, 484]], [[194, 463], [399, 463], [399, 481], [194, 481]], [[98, 484], [155, 484], [155, 509], [98, 509]], [[194, 486], [404, 486], [404, 509], [194, 509]], [[98, 511], [155, 511], [155, 536], [98, 536]], [[194, 511], [567, 511], [567, 534], [194, 534]], [[98, 536], [153, 536], [153, 561], [98, 561]], [[192, 536], [310, 536], [310, 561], [192, 561]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[192, 561], [310, 561], [310, 586], [192, 586]], [[98, 586], [155, 586], [155, 611], [98, 611]], [[194, 588], [438, 588], [438, 611], [194, 611]], [[98, 611], [155, 611], [155, 638], [98, 638]], [[192, 613], [377, 613], [377, 636], [192, 636]], [[98, 636], [155, 636], [155, 663], [98, 663]], [[192, 640], [408, 640], [408, 663], [192, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[192, 663], [301, 663], [301, 688], [192, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[188, 686], [227, 686], [227, 717], [188, 717]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[190, 713], [262, 713], [262, 740], [190, 740]], [[98, 740], [157, 740], [157, 765], [98, 765]], [[192, 740], [689, 740], [689, 763], [192, 763]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[192, 765], [325, 765], [325, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [294, 792], [294, 817], [192, 817]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[190, 815], [244, 815], [244, 844], [190, 844]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[190, 842], [364, 842], [364, 867], [190, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [449, 869], [449, 892], [192, 892]], [[98, 892], [155, 892], [155, 919], [98, 919]], [[192, 894], [449, 894], [449, 917], [192, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[194, 919], [441, 919], [441, 944], [194, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [349, 946], [349, 969], [194, 969]], [[98, 969], [155, 969], [155, 996], [98, 996]], [[192, 971], [524, 971], [524, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [332, 996], [332, 1021], [192, 1021]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[194, 1022], [508, 1022], [508, 1046], [194, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[194, 1047], [606, 1047], [606, 1071], [194, 1071]], [[98, 1071], [157, 1071], [157, 1096], [98, 1096]], [[192, 1072], [733, 1072], [733, 1096], [192, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [347, 1098], [347, 1121], [192, 1121]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[192, 1123], [367, 1123], [367, 1146], [192, 1146]], [[98, 1148], [155, 1148], [155, 1173], [98, 1173]], [[192, 1149], [504, 1149], [504, 1173], [192, 1173]], [[98, 1173], [157, 1173], [157, 1198], [98, 1198]], [[194, 1173], [412, 1173], [412, 1196], [194, 1196]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [456, 1198], [456, 1223], [190, 1225]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[190, 1224], [260, 1224], [260, 1251], [190, 1251]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1251], [430, 1251], [430, 1274], [192, 1274]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[194, 1276], [482, 1276], [482, 1300], [194, 1300]], [[98, 1301], [157, 1301], [157, 1326], [98, 1326]], [[196, 1305], [639, 1305], [639, 1323], [196, 1323]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[194, 1328], [460, 1328], [460, 1351], [194, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [476, 1353], [476, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1378], [500, 1378], [500, 1401], [192, 1401]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [428, 1403], [428, 1426], [192, 1426]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[192, 1428], [327, 1428], [327, 1453], [192, 1453]], [[98, 1453], [155, 1453], [155, 1478], [98, 1478]], [[192, 1455], [356, 1455], [356, 1478], [192, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[192, 1480], [314, 1480], [314, 1505], [192, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[190, 1503], [242, 1503], [242, 1532], [190, 1532]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[190, 1530], [332, 1530], [332, 1555], [190, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[188, 1553], [264, 1553], [264, 1586], [188, 1586]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[191, 1578], [370, 1584], [369, 1611], [190, 1605]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1607], [401, 1607], [401, 1632], [192, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[192, 1634], [375, 1634], [375, 1657], [192, 1657]], [[98, 1657], [157, 1657], [157, 1684], [98, 1684]], [[192, 1659], [478, 1659], [478, 1682], [192, 1682]], [[100, 1682], [155, 1682], [155, 1709], [100, 1709]], [[192, 1684], [286, 1684], [286, 1712], [192, 1712]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["按照标准图谱相似度递减列表：", "0.000", "厦部第1腰椎水平截面", "4.479", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.054", "C反应蛋白C-REACTIVEPROTEIN", "0.072", "血尿酸SERUMURICACID", "0.074", "脂肪酶*", "0.075", "血管紧张素Ⅱ*", "0.087", "胆固醇COMMONPLASMA CHOLESTERIN", "0.099", "血管紧张素I", "0.101", "血浆丰配化脂肪酸NONETHERIZED FATTYACIDSOF PLASMA", "0.161", "血钾PLASMAPOTASSIUM", "0.095", "血清蛋白SERUM ALBUMEN", "0.097", "血红蛋白HAEMOGLOBIN", "0.103", "尿中蛋白质PROTEININURINE", "0.105", "PERIPHERIC BLOOD LEUCOCYTES", "0.107", "嗜碱性粒细胞BASOPHILS", "0.107", "血红血球ERYTHROCYTES", "0.114", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.131", "免疫球蛋白G*", "0.132", "免疫球蛋白M*", "0.133", "尿白血球URINELEUCOCYTES", "0.135", "单核细胞MONOCYTES", "0.143", "血清蛋白SERUMPROTEIN", "0.147", "BETA球蛋白", "0.201", "锂*", "0.092", "胆汁酸*", "0.093", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.099", "ALPHA2球蛋白*", "0.102", "胰高血糖素", "0.104", "糖苷*", "0.105", "红细胞沉降率(ESR)", "0.106", "血清补体SERUMCOMPLEMENT", "0.107", "肿瘤标志物MELANOGENE在尿*", "0.107", "血清溶菌酵SERUMLYSOZYME", "0.109", "血细胞比容，全血", "0.111", "游离胆固醇FREEPLASMACHOLESTERIN", "0.113", "糖基化血红蛋白*", "0.114", "血清铜蓝蛋白SERUMCERULOPLASMIN", "0.115", "肌酸磷酸酵素COMMON CREATINPHOSPHOKINASE", "0.115", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.116", "RHEUMOFACTOR*", "0.116", "肿瘤标志物胸苷激酶", "0.117", "血清淀粉酵素SERUMALPHAAMYLASE", "0.117", "尿肌配URINECREATININE", "0.117", "伽马球蛋白GAMMA-GLOBULINS", "0.117", "铁蛋白*", "0.118", "嗜酸性粒细胞EOSINOPHILES", "0.118", "促肾上腺皮质激素CORTICOTROPIN", "0.118", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.118", "尿中肾上腺素URINEADRENALIN", "0.118", "嗜中性粒细胞STABNEUTROPHILS", "0.119", "生长激素SOMATOTROPICHORMONE", "0.119", "血组织胺BLOODHISTAMINE", "0.119", "ALPHA1球蛋白*", "0.120", "血糖BLOOD SUGAR", "0.120", "甲状腺球蛋白*", "0.120", "肾素*", "0.120", "抗链球菌溶血素*", "0.121", "催乳素*", "0.121", "ALPHA1-抗胰蛋白酶*", "0.123", "维生素B1（THIAMINE）", "0.126", "DELTA氨基乙酰丙酸*", "0.126", "血浆磷脂PLASMA PHOSPHOTIDES", "0.127", "维生素B6*"], "rec_scores": [0.9954331517219543, 0.9992889165878296, 0.9235565066337585, 0.9989500045776367, 0.9983981251716614, 0.9667975902557373, 0.9999244809150696, 0.9883997440338135, 0.9999293088912964, 0.9981278777122498, 0.9998855590820312, 0.9802283644676208, 0.9999462366104126, 0.9428358674049377, 0.999926745891571, 0.9795368313789368, 0.9999297857284546, 0.9789831638336182, 0.9998544454574585, 0.9332330822944641, 0.9999052286148071, 0.9812120795249939, 0.999720573425293, 0.9809539914131165, 0.9997221231460571, 0.9985331296920776, 0.9996230006217957, 0.9969345927238464, 0.9995471239089966, 0.9688208699226379, 0.999531626701355, 0.9848082661628723, 0.9993850588798523, 0.9961680173873901, 0.9993475675582886, 0.9929898977279663, 0.9995128512382507, 0.9875523447990417, 0.999610424041748, 0.9918586611747742, 0.9995530843734741, 0.9923917055130005, 0.9996044039726257, 0.9973767995834351, 0.9995918273925781, 0.9954931735992432, 0.9992172122001648, 0.992581307888031, 0.9994300603866577, 0.9606835842132568, 0.9998161196708679, 0.969925045967102, 0.9997072219848633, 0.9949657917022705, 0.9996426701545715, 0.9461562037467957, 0.9996402859687805, 0.9972966909408569, 0.9994826316833496, 0.973040759563446, 0.9995371103286743, 0.9220654964447021, 0.9993749856948853, 0.9985002279281616, 0.9995378255844116, 0.993736982345581, 0.9995242357254028, 0.9920037388801575, 0.9994146227836609, 0.9945255517959595, 0.9985436201095581, 0.9800680875778198, 0.9995222091674805, 0.9608697891235352, 0.9990524053573608, 0.9947469830513, 0.9994711875915527, 0.9837502241134644, 0.9992499351501465, 0.9949914813041687, 0.9994398951530457, 0.9896174669265747, 0.9991324543952942, 0.9894270300865173, 0.9991629719734192, 0.9988448023796082, 0.9991371035575867, 0.9620406031608582, 0.9991861581802368, 0.9962862133979797, 0.9991371035575867, 0.908932089805603, 0.9994661211967468, 0.997796356678009, 0.999498188495636, 0.9984483122825623, 0.9994878768920898, 0.9974063634872437, 0.9993160963058472, 0.9933616518974304, 0.9994661211967468, 0.9978678822517395, 0.9994169473648071, 0.9967248439788818, 0.9988118410110474, 0.9917929172515869, 0.9987851977348328, 0.9760702848434448, 0.9992280006408691, 0.9667790532112122, 0.99952632188797, 0.9408621191978455, 0.9994872212409973, 0.8973817825317383, 0.9993170499801636, 0.9718778133392334, 0.9995825886726379, 0.9844926595687866, 0.9996083974838257, 0.9883496165275574, 0.9995576739311218, 0.9556136727333069, 0.9991583824157715, 0.9539235234260559, 0.9996776580810547, 0.9760136008262634, 0.9992958903312683, 0.9817430377006531], "rec_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 79], [384, 79], [384, 102], [192, 102]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[194, 156], [495, 156], [495, 179], [194, 179]], [[98, 179], [161, 179], [161, 204], [98, 204]], [[194, 181], [423, 181], [423, 204], [194, 204]], [[98, 204], [161, 204], [161, 229], [98, 229]], [[192, 204], [266, 204], [266, 231], [192, 231]], [[98, 229], [162, 229], [162, 256], [98, 256]], [[192, 231], [319, 227], [320, 254], [193, 258]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [556, 257], [556, 281], [192, 281]], [[98, 281], [161, 281], [161, 306], [98, 306]], [[196, 284], [310, 284], [310, 304], [196, 304]], [[98, 306], [161, 306], [161, 332], [98, 332]], [[194, 307], [727, 307], [727, 331], [194, 331]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [428, 336], [428, 354], [196, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[194, 359], [412, 359], [412, 383], [194, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[194, 384], [393, 384], [393, 408], [194, 408]], [[98, 408], [155, 408], [155, 434], [98, 434]], [[194, 409], [443, 409], [443, 433], [194, 433]], [[98, 434], [155, 434], [155, 459], [98, 459]], [[192, 434], [474, 434], [474, 458], [192, 458]], [[98, 458], [155, 458], [155, 484], [98, 484]], [[194, 463], [399, 463], [399, 481], [194, 481]], [[98, 484], [155, 484], [155, 509], [98, 509]], [[194, 486], [404, 486], [404, 509], [194, 509]], [[98, 511], [155, 511], [155, 536], [98, 536]], [[194, 511], [567, 511], [567, 534], [194, 534]], [[98, 536], [153, 536], [153, 561], [98, 561]], [[192, 536], [310, 536], [310, 561], [192, 561]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[192, 561], [310, 561], [310, 586], [192, 586]], [[98, 586], [155, 586], [155, 611], [98, 611]], [[194, 588], [438, 588], [438, 611], [194, 611]], [[98, 611], [155, 611], [155, 638], [98, 638]], [[192, 613], [377, 613], [377, 636], [192, 636]], [[98, 636], [155, 636], [155, 663], [98, 663]], [[192, 640], [408, 640], [408, 663], [192, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[192, 663], [301, 663], [301, 688], [192, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[188, 686], [227, 686], [227, 717], [188, 717]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[190, 713], [262, 713], [262, 740], [190, 740]], [[98, 740], [157, 740], [157, 765], [98, 765]], [[192, 740], [689, 740], [689, 763], [192, 763]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[192, 765], [325, 765], [325, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [294, 792], [294, 817], [192, 817]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[190, 815], [244, 815], [244, 844], [190, 844]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[190, 842], [364, 842], [364, 867], [190, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [449, 869], [449, 892], [192, 892]], [[98, 892], [155, 892], [155, 919], [98, 919]], [[192, 894], [449, 894], [449, 917], [192, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[194, 919], [441, 919], [441, 944], [194, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [349, 946], [349, 969], [194, 969]], [[98, 969], [155, 969], [155, 996], [98, 996]], [[192, 971], [524, 971], [524, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [332, 996], [332, 1021], [192, 1021]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[194, 1022], [508, 1022], [508, 1046], [194, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[194, 1047], [606, 1047], [606, 1071], [194, 1071]], [[98, 1071], [157, 1071], [157, 1096], [98, 1096]], [[192, 1072], [733, 1072], [733, 1096], [192, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [347, 1098], [347, 1121], [192, 1121]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[192, 1123], [367, 1123], [367, 1146], [192, 1146]], [[98, 1148], [155, 1148], [155, 1173], [98, 1173]], [[192, 1149], [504, 1149], [504, 1173], [192, 1173]], [[98, 1173], [157, 1173], [157, 1198], [98, 1198]], [[194, 1173], [412, 1173], [412, 1196], [194, 1196]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [456, 1198], [456, 1223], [190, 1225]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[190, 1224], [260, 1224], [260, 1251], [190, 1251]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1251], [430, 1251], [430, 1274], [192, 1274]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[194, 1276], [482, 1276], [482, 1300], [194, 1300]], [[98, 1301], [157, 1301], [157, 1326], [98, 1326]], [[196, 1305], [639, 1305], [639, 1323], [196, 1323]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[194, 1328], [460, 1328], [460, 1351], [194, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [476, 1353], [476, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1378], [500, 1378], [500, 1401], [192, 1401]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [428, 1403], [428, 1426], [192, 1426]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[192, 1428], [327, 1428], [327, 1453], [192, 1453]], [[98, 1453], [155, 1453], [155, 1478], [98, 1478]], [[192, 1455], [356, 1455], [356, 1478], [192, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[192, 1480], [314, 1480], [314, 1505], [192, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[190, 1503], [242, 1503], [242, 1532], [190, 1532]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[190, 1530], [332, 1530], [332, 1555], [190, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[188, 1553], [264, 1553], [264, 1586], [188, 1586]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[191, 1578], [370, 1584], [369, 1611], [190, 1605]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1607], [401, 1607], [401, 1632], [192, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[192, 1634], [375, 1634], [375, 1657], [192, 1657]], [[98, 1657], [157, 1657], [157, 1684], [98, 1684]], [[192, 1659], [478, 1659], [478, 1682], [192, 1682]], [[100, 1682], [155, 1682], [155, 1709], [100, 1709]], [[192, 1684], [286, 1684], [286, 1712], [192, 1712]]], "rec_boxes": [[20, 30, 519, 61], [98, 77, 157, 102], [192, 79, 384, 102], [98, 102, 157, 127], [192, 102, 270, 129], [194, 129, 462, 152], [98, 154, 161, 179], [194, 156, 495, 179], [98, 179, 161, 204], [194, 181, 423, 204], [98, 204, 161, 229], [192, 204, 266, 231], [98, 229, 162, 256], [192, 227, 320, 258], [98, 256, 161, 281], [192, 257, 556, 281], [98, 281, 161, 306], [196, 284, 310, 304], [98, 306, 161, 332], [194, 307, 727, 331], [98, 332, 161, 358], [196, 336, 428, 354], [98, 358, 157, 383], [194, 359, 412, 383], [98, 383, 155, 409], [194, 384, 393, 408], [98, 408, 155, 434], [194, 409, 443, 433], [98, 434, 155, 459], [192, 434, 474, 458], [98, 458, 155, 484], [194, 463, 399, 481], [98, 484, 155, 509], [194, 486, 404, 509], [98, 511, 155, 536], [194, 511, 567, 534], [98, 536, 153, 561], [192, 536, 310, 561], [98, 561, 155, 588], [192, 561, 310, 586], [98, 586, 155, 611], [194, 588, 438, 611], [98, 611, 155, 638], [192, 613, 377, 636], [98, 636, 155, 663], [192, 640, 408, 663], [98, 663, 155, 688], [192, 663, 301, 688], [98, 688, 155, 715], [188, 686, 227, 717], [98, 713, 157, 740], [190, 713, 262, 740], [98, 740, 157, 765], [192, 740, 689, 763], [98, 765, 157, 790], [192, 765, 325, 790], [98, 790, 157, 817], [192, 792, 294, 817], [98, 815, 157, 842], [190, 815, 244, 844], [98, 840, 157, 867], [190, 842, 364, 867], [98, 867, 157, 892], [192, 869, 449, 892], [98, 892, 155, 919], [192, 894, 449, 917], [98, 917, 155, 944], [194, 919, 441, 944], [98, 944, 157, 969], [194, 946, 349, 969], [98, 969, 155, 996], [192, 971, 524, 994], [98, 994, 157, 1021], [192, 996, 332, 1021], [98, 1021, 157, 1046], [194, 1022, 508, 1046], [98, 1046, 157, 1071], [194, 1047, 606, 1071], [98, 1071, 157, 1096], [192, 1072, 733, 1096], [98, 1096, 157, 1123], [192, 1098, 347, 1121], [98, 1123, 157, 1148], [192, 1123, 367, 1146], [98, 1148, 155, 1173], [192, 1149, 504, 1173], [98, 1173, 157, 1198], [194, 1173, 412, 1196], [98, 1198, 157, 1224], [190, 1198, 456, 1225], [98, 1224, 157, 1249], [190, 1224, 260, 1251], [98, 1249, 157, 1274], [192, 1251, 430, 1274], [98, 1274, 157, 1301], [194, 1276, 482, 1300], [98, 1301, 157, 1326], [196, 1305, 639, 1323], [98, 1326, 157, 1351], [194, 1328, 460, 1351], [98, 1351, 157, 1376], [192, 1353, 476, 1376], [98, 1376, 157, 1403], [192, 1378, 500, 1401], [100, 1403, 157, 1428], [192, 1403, 428, 1426], [100, 1428, 157, 1453], [192, 1428, 327, 1453], [98, 1453, 155, 1478], [192, 1455, 356, 1478], [98, 1478, 157, 1505], [192, 1480, 314, 1505], [98, 1505, 157, 1530], [190, 1503, 242, 1532], [98, 1530, 157, 1555], [190, 1530, 332, 1555], [98, 1555, 155, 1582], [188, 1553, 264, 1586], [98, 1580, 157, 1607], [190, 1578, 370, 1611], [98, 1607, 157, 1632], [192, 1607, 401, 1632], [100, 1632, 157, 1657], [192, 1634, 375, 1657], [98, 1657, 157, 1684], [192, 1659, 478, 1682], [100, 1682, 155, 1709], [192, 1684, 286, 1712]]}, "table_res_list": [{"cell_box_list": [[98.25765991210938, 75.90400493144989, 170.03582763671875, 102.62752532958984], [193.7397003173828, 75.51187813282013, 766.9332885742188, 102.86422157287598], [69.8473129272461, 101.98740768432617, 98.44967651367188, 127.66159439086914], [98.28245544433594, 102.07539939880371, 170.43402099609375, 153.0579071044922], [192.0, 102.0, 270.0, 129.0], [69.82081604003906, 127.6369514465332, 98.39423370361328, 178.39932250976562], [192.79518127441406, 127.2534408569336, 767.47900390625, 153.33403778076172], [98.20491027832031, 152.76759338378906, 170.52743530273438, 178.63796997070312], [192.14617919921875, 153.1868133544922, 767.4614868164062, 179.11400604248047], [98.191650390625, 178.32927703857422, 170.7962188720703, 229.23487854003906], [194.0, 181.0, 423.0, 204.0], [69.77837753295898, 203.33587646484375, 98.3182373046875, 228.8051300048828], [191.24249267578125, 204.32559204101562, 767.4464721679688, 255.02639770507812], [69.84612274169922, 228.9859161376953, 98.34776306152344, 254.57113647460938], [98.25994110107422, 229.21798706054688, 171.03863525390625, 255.0334014892578], [69.79928207397461, 254.28233337402344, 98.300537109375, 280.1845703125], [98.15457916259766, 254.76109313964844, 171.4764862060547, 305.9014129638672], [192.0, 257.0, 556.0, 281.0], [69.77275085449219, 280.10218811035156, 98.28079223632812, 331.1850280761719], [191.6807098388672, 280.3489227294922, 767.2910766601562, 306.3469543457031], [42.65671920776367, 305.4696807861328, 69.79419708251953, 331.0081329345703], [98.1239013671875, 305.8050079345703, 171.64044189453125, 331.3743896484375], [194.0, 307.0, 727.0, 331.0], [42.707990646362305, 331.28497314453125, 69.8810806274414, 356.7345886230469], [69.84626388549805, 331.2534484863281, 98.32610321044922, 356.7516174316406], [98.21520233154297, 331.2501525878906, 171.75714111328125, 356.9497985839844], [191.89459228515625, 331.4220886230469, 767.403564453125, 357.5111999511719], [42.68790245056152, 356.5068359375, 69.86788177490234, 382.4368591308594], [69.7859115600586, 356.4977111816406, 98.27021026611328, 382.3810119628906], [98.17732238769531, 356.7485046386719, 171.6254425048828, 382.5317687988281], [192.28976440429688, 357.41375732421875, 767.5435180664062, 382.8314208984375], [42.61596488952637, 382.46728515625, 69.79937362670898, 433.3270568847656], [69.77896118164062, 382.35333251953125, 98.2794418334961, 407.8818054199219], [98.18827819824219, 382.3326110839844, 171.7279510498047, 407.9839172363281], [194.0, 384.0, 393.0, 408.0], [69.75857162475586, 407.768310546875, 98.26992797851562, 433.3818664550781], [98.1126708984375, 407.9465026855469, 171.643798828125, 433.4854431152344], [192.16171264648438, 408.25457763671875, 767.4838256835938, 433.3521423339844], [42.76026153564453, 433.54833984375, 69.867919921875, 458.9856262207031], [69.83885955810547, 433.4611511230469, 98.33432006835938, 459.0016174316406], [98.20219421386719, 433.4534606933594, 171.5751953125, 459.1363220214844], [192.2950439453125, 433.657958984375, 767.5050048828125, 459.5025329589844], [42.7413215637207, 458.7888488769531, 69.85730743408203, 484.6932373046875], [69.7861099243164, 458.7890930175781, 98.24735260009766, 484.6234130859375], [98.18419647216797, 458.93267822265625, 171.42083740234375, 484.75469970703125], [194.0, 463.0, 399.0, 481.0], [42.66171073913574, 484.71551513671875, 69.785888671875, 535.6541442871094], [69.73755264282227, 484.6756591796875, 98.27747344970703, 535.6732788085938], [98.17192840576172, 484.595947265625, 171.53392028808594, 510.2483215332031], [192.6575469970703, 484.9194641113281, 767.6296997070312, 510.47308349609375], [98.1081314086914, 510.1920166015625, 171.50254821777344, 535.8020629882812], [194.0, 511.0, 567.0, 534.0], [42.785287857055664, 535.8968200683594, 69.88537216186523, 586.9833984375], [69.83062744140625, 535.7423095703125, 98.34453582763672, 561.1695861816406], [98.20782470703125, 535.8790588378906, 171.42881774902344, 561.1342163085938], [192.5222930908203, 535.9556274414062, 767.5820922851562, 561.83447265625], [69.78604125976562, 561.0213012695312, 98.262939453125, 586.9490356445312], [98.16793823242188, 561.0863342285156, 171.27911376953125, 586.8980712890625], [192.45875549316406, 561.9507446289062, 767.428466796875, 587.4995727539062], [42.69280433654785, 587.0432739257812, 69.81756210327148, 612.49853515625], [69.77252960205078, 586.8970947265625, 98.27201843261719, 612.4920654296875], [98.24625396728516, 586.9003601074219, 171.4285430908203, 612.3511352539062], [194.0, 588.0, 438.0, 611.0], [42.759450912475586, 612.34912109375, 69.796142578125, 637.92529296875], [69.7529296875, 612.3320922851562, 98.28326416015625, 637.9827880859375], [98.15625762939453, 612.523193359375, 171.42601013183594, 663.3259887695312], [192.3702850341797, 612.9910888671875, 767.41162109375, 637.927001953125], [42.76237487792969, 638.1571044921875, 69.88799285888672, 689.2570190429688], [69.83733749389648, 637.8545532226562, 98.32935333251953, 663.2838745117188], [192.50408935546875, 638.2068481445312, 767.3976440429688, 664.226318359375], [69.80436706542969, 663.3109130859375, 98.24491882324219, 689.3917236328125], [98.14774322509766, 663.4759521484375, 171.31654357910156, 689.0166015625], [192.0, 663.0, 301.0, 688.0], [69.77595138549805, 689.3292846679688, 98.31060791015625, 765.7310791015625], [98.22412872314453, 689.0580444335938, 171.40859985351562, 715.0202026367188], [192.4799346923828, 689.5841064453125, 767.6185302734375, 715.2474365234375], [42.719120025634766, 714.6480102539062, 69.82036590576172, 740.3023681640625], [98.15290069580078, 714.9811401367188, 171.32308959960938, 765.6918334960938], [190.0, 713.0, 262.0, 740.0], [42.73400688171387, 740.4920043945312, 69.91244506835938, 791.6049194335938], [192.72804260253906, 740.8302612304688, 767.6543579101562, 765.9489135742188], [69.81132507324219, 765.6827392578125, 98.27595520019531, 791.5684814453125], [98.20096588134766, 765.6119384765625, 171.20767211914062, 791.7222900390625], [192.7270965576172, 766.41015625, 767.6543579101562, 791.671875], [98.25199127197266, 791.481201171875, 171.35205078125, 817.2516479492188], [192.0, 792.0, 294.0, 817.0], [42.68809700012207, 817.012451171875, 69.84536361694336, 842.6343994140625], [69.7911376953125, 817.1165771484375, 98.33329010009766, 868.0924682617188], [98.21416473388672, 817.1600952148438, 171.32489013671875, 842.4605712890625], [192.62298583984375, 817.6817626953125, 767.3139038085938, 842.4003295898438], [98.21916198730469, 842.6091918945312, 171.34584045410156, 868.1710205078125], [192.8060302734375, 842.841064453125, 767.5228271484375, 868.4264526367188], [69.77471542358398, 867.947509765625, 98.28645324707031, 893.6652221679688], [98.20569610595703, 868.0982666015625, 171.4108428955078, 893.7072143554688], [192.0, 869.0, 449.0, 892.0], [98.2810287475586, 893.7158203125, 171.55137634277344, 919.1427001953125], [192.8702850341797, 893.93017578125, 767.2833862304688, 919.4571533203125], [69.72727966308594, 918.9215698242188, 98.31056213378906, 969.1207275390625], [98.0, 917.0, 155.0, 944.0], [194.0, 919.0, 441.0, 944.0], [98.10440063476562, 944.7637329101562, 171.57733154296875, 969.6820678710938], [192.67794799804688, 944.1660766601562, 767.2189331054688, 969.74462890625], [98.0, 969.0, 155.0, 996.0], [192.65145874023438, 969.8673706054688, 767.11279296875, 996.1444702148438], [69.71177291870117, 994.6442260742188, 98.24259185791016, 1045.2086181640625], [98.13185119628906, 994.1710205078125, 171.5695343017578, 1020.0205688476562], [192.0, 996.0, 332.0, 1021.0], [98.18811798095703, 1020.1097412109375, 171.51303100585938, 1045.809814453125], [192.81661987304688, 1019.8805541992188, 767.3912963867188, 1045.952392578125], [69.69837188720703, 1045.1044921875, 98.17686462402344, 1070.488037109375], [98.11196899414062, 1045.6116943359375, 171.5028533935547, 1070.9456787109375], [194.0, 1047.0, 606.0, 1071.0], [98.0, 1071.0, 157.0, 1096.0], [192.82174682617188, 1071.1460571289062, 767.5794067382812, 1096.8565063476562], [69.67752456665039, 1095.8617553710938, 98.21524810791016, 1121.58642578125], [98.11595916748047, 1096.165771484375, 171.52407836914062, 1121.9056396484375], [192.72158813476562, 1097.6480102539062, 767.6543579101562, 1121.74072265625], [69.69750595092773, 1121.434814453125, 98.21939086914062, 1147.164306640625], [98.16949462890625, 1121.5938720703125, 171.3745880126953, 1147.5113525390625], [192.6587677001953, 1122.470703125, 767.5625610351562, 1172.8565673828125], [69.69931030273438, 1147.021728515625, 98.16571044921875, 1172.5479736328125], [98.08781433105469, 1147.4239501953125, 171.45362854003906, 1198.2620849609375], [69.7109375, 1172.53515625, 98.21857452392578, 1198.0291748046875], [192.6493682861328, 1173.12109375, 767.493896484375, 1198.95703125], [42.541534423828125, 1198.0799560546875, 69.75146484375, 1223.8834228515625], [69.68135833740234, 1198.0206298828125, 98.20515441894531, 1223.82958984375], [98.10672760009766, 1198.1806640625, 171.481689453125, 1223.97802734375], [190.0, 1198.0, 456.0, 1225.0], [42.54481506347656, 1223.737060546875, 69.6955451965332, 1249.366943359375], [69.66705703735352, 1223.67919921875, 98.19416809082031, 1249.369384765625], [98.19287109375, 1223.772705078125, 171.43283081054688, 1249.7130126953125], [192.74330139160156, 1224.4327392578125, 767.5075073242188, 1250.426513671875], [69.68075180053711, 1249.23681640625, 98.15010833740234, 1274.7989501953125], [98.1044921875, 1249.571044921875, 171.45703125, 1300.539306640625], [192.0, 1251.0, 430.0, 1274.0], [69.69289779663086, 1274.8328857421875, 98.21038055419922, 1300.3529052734375], [192.6460418701172, 1275.375, 767.6151123046875, 1301.1251220703125], [42.538625717163086, 1300.3741455078125, 69.72919464111328, 1326.1873779296875], [69.6698989868164, 1300.3243408203125, 98.20387268066406, 1326.125244140625], [98.10596466064453, 1300.4483642578125, 171.52337646484375, 1326.2528076171875], [196.0, 1305.0, 639.0, 1323.0], [42.55552864074707, 1326.070068359375, 69.7010726928711, 1351.6739501953125], [69.66437149047852, 1326.0052490234375, 98.18466186523438, 1351.64111328125], [98.14655303955078, 1326.0367431640625, 171.50331115722656, 1351.915771484375], [194.0, 1328.0, 460.0, 1351.0], [42.55950355529785, 1351.5841064453125, 69.71515655517578, 1377.135498046875], [69.69100952148438, 1351.5570068359375, 98.1617431640625, 1377.148193359375], [98.101806640625, 1351.8045654296875, 171.49085998535156, 1402.8094482421875], [192.4971923828125, 1352.2119140625, 767.5164184570312, 1377.3331298828125], [69.70223617553711, 1377.19970703125, 98.21627807617188, 1402.673828125], [192.6198272705078, 1377.71337890625, 767.4627075195312, 1403.29345703125], [42.54152297973633, 1402.6961669921875, 69.74774932861328, 1428.4990234375], [69.67369842529297, 1402.642578125, 98.20420837402344, 1428.438232421875], [98.09996795654297, 1402.712646484375, 171.55372619628906, 1428.5206298828125], [192.0, 1403.0, 428.0, 1426.0], [42.53532791137695, 1428.3687744140625, 69.70676803588867, 1453.9642333984375], [69.66207504272461, 1428.310791015625, 98.19207000732422, 1453.945556640625], [98.17196655273438, 1428.295654296875, 171.58448791503906, 1454.189697265625], [192.82061767578125, 1428.6473388671875, 767.5638427734375, 1454.6224365234375], [42.53052520751953, 1453.87939453125, 69.70919036865234, 1479.3681640625], [69.677001953125, 1453.8553466796875, 98.13970947265625, 1479.4197998046875], [98.09745025634766, 1454.0595703125, 171.5652313232422, 1479.6976318359375], [192.0, 1455.0, 356.0, 1478.0], [69.68229293823242, 1479.5142822265625, 98.18975067138672, 1505.01513671875], [98.0530014038086, 1479.654052734375, 171.5281219482422, 1530.8101806640625], [192.74916076660156, 1479.8074951171875, 767.5409545898438, 1505.724609375], [42.50004768371582, 1505.002685546875, 69.73970031738281, 1530.788818359375], [69.65521621704102, 1504.96923828125, 98.17254638671875, 1530.7352294921875], [192.77642822265625, 1505.8553466796875, 767.4847412109375, 1531.122802734375], [42.52929496765137, 1530.682861328125, 69.72847747802734, 1581.6160888671875], [69.66463088989258, 1530.6265869140625, 98.16468048095703, 1556.212646484375], [98.1529541015625, 1530.62060546875, 171.5604705810547, 1556.39892578125], [193.01321411132812, 1531.2423095703125, 767.5458984375, 1557.2327880859375], [69.67924880981445, 1556.14404296875, 98.13858032226562, 1581.656494140625], [98.06002807617188, 1556.3424072265625, 171.5248260498047, 1581.7900390625], [188.0, 1553.0, 264.0, 1586.0], [69.70048904418945, 1581.7850341796875, 98.1951675415039, 1607.252685546875], [98.13471984863281, 1581.7933349609375, 171.475830078125, 1607.332275390625], [192.78025817871094, 1582.007568359375, 767.4725341796875, 1607.650146484375], [42.507577896118164, 1607.2269287109375, 69.74774932861328, 1633.06640625], [69.65713119506836, 1607.21875, 98.18309020996094, 1633.0234375], [98.0611801147461, 1607.1771240234375, 171.47613525390625, 1633.0150146484375], [192.61831665039062, 1607.7059326171875, 767.1832885742188, 1632.98193359375], [42.5658073425293, 1632.8824462890625, 69.72994232177734, 1658.4794921875], [69.65081024169922, 1632.8123779296875, 98.18333435058594, 1658.396240234375], [98.08848571777344, 1632.7708740234375, 171.5268096923828, 1683.7723388671875], [192.79583740234375, 1633.06494140625, 767.063232421875, 1683.543701171875], [100.0, 1682.0, 155.0, 1709.0], [192.0, 1684.0, 286.0, 1712.0]], "pred_html": "<html><body><table><tbody><tr><td>0.000</td><td>厦部第1腰椎水平截面</td><td></td></tr><tr><td></td><td>4.479</td><td>优化配置</td></tr><tr><td></td><td>虚拟模式-健康问题发展趋势列表：</td><td>0.054</td></tr><tr><td>0.072 0.074</td><td>血尿酸SERUMURICACID</td><td></td></tr><tr><td></td><td>脂肪酶* 血管紧张素Ⅱ*</td><td></td></tr><tr><td></td><td>0.087 0.099</td><td>胆固醇COMMONPLASMA CHOLESTERIN</td></tr><tr><td></td><td>血管紧张素I</td><td></td></tr><tr><td></td><td>0.101</td><td>血浆丰配化脂肪酸NONETHERIZED FATTYACIDSOF PLASMA</td></tr><tr><td></td><td></td><td>0.161</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.095</td></tr><tr><td></td><td></td><td>0.097</td></tr><tr><td></td><td>0.103</td><td>尿中蛋白质PROTEININURINE</td></tr><tr><td></td><td></td><td>0.105</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.107</td></tr><tr><td></td><td></td><td>0.107</td></tr><tr><td>0.114</td><td>分段的中性粒细胞SEGMENTEDNEUTROPHILS</td><td></td></tr><tr><td></td><td></td><td>0.131</td></tr><tr><td></td><td>0.132</td><td>免疫球蛋白M*</td></tr><tr><td></td><td></td><td>0.133</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.135 0.143</td></tr><tr><td></td><td></td><td>血清蛋白SERUMPROTEIN</td></tr><tr><td></td><td>0.147</td><td>BETA球蛋白</td></tr><tr><td></td><td>0.201</td><td>锂*</td></tr><tr><td></td><td>0.092 0.093</td><td>胆汁酸*</td></tr><tr><td></td><td>0.099</td><td>ALPHA2球蛋白*</td></tr><tr><td>0.102</td><td>胰高血糖素</td><td></td></tr><tr><td></td><td></td><td>0.104</td></tr><tr><td>0.105</td><td>红细胞沉降率(ESR)</td><td></td></tr><tr><td></td><td>0.106</td><td>血清补体SERUMCOMPLEMENT</td></tr><tr><td>0.107</td><td>肿瘤标志物MELANOGENE在尿*</td><td></td></tr><tr><td></td><td>0.107</td><td>血清溶菌酵SERUMLYSOZYME</td></tr><tr><td>0.111</td><td>游离胆固醇FREEPLASMACHOLESTERIN</td><td></td></tr><tr><td></td><td>0.113</td><td>糖基化血红蛋白*</td></tr><tr><td>0.114</td><td>血清铜蓝蛋白SERUMCERULOPLASMIN</td><td></td></tr><tr><td></td><td>0.115</td><td>肌酸磷酸酵素COMMON CREATINPHOSPHOKINASE</td></tr><tr><td></td><td>0.116</td><td>RHEUMOFACTOR*</td></tr><tr><td></td><td>0.116</td><td>肿瘤标志物胸苷激酶 血清淀粉酵素SERUMALPHAAMYLASE</td></tr><tr><td></td><td>0.117 0.117</td><td></td></tr><tr><td></td><td>尿肌配URINECREATININE</td><td></td></tr><tr><td></td><td></td><td>0.117</td></tr><tr><td></td><td></td><td>0.117</td></tr><tr><td></td><td>0.118 0.118</td><td>嗜酸性粒细胞EOSINOPHILES</td></tr><tr><td></td><td>促肾上腺皮质激素CORTICOTROPIN</td><td></td></tr><tr><td></td><td></td><td>0.118</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.118</td></tr><tr><td></td><td></td><td>0.118 0.119</td></tr><tr><td></td><td>生长激素SOMATOTROPICHORMONE</td><td></td></tr><tr><td></td><td></td><td>0.119</td></tr><tr><td></td><td></td><td>0.119</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.120</td></tr><tr><td></td><td>0.120 0.120</td><td>甲状腺球蛋白*</td></tr><tr><td></td><td></td><td>肾素*</td></tr><tr><td></td><td></td><td>0.120</td></tr><tr><td></td><td>0.121</td><td>催乳素*</td></tr><tr><td></td><td>0.121</td><td>ALPHA1-抗胰蛋白酶*</td></tr><tr><td></td><td></td><td>0.123</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.126 0.126</td></tr></tbody></table></body></html>", "table_ocr_pred": {"rec_polys": [[[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 79], [384, 79], [384, 102], [192, 102]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[194, 156], [495, 156], [495, 179], [194, 179]], [[98, 179], [161, 179], [161, 204], [98, 204]], [[194, 181], [423, 181], [423, 204], [194, 204]], [[98, 204], [161, 204], [161, 229], [98, 229]], [[192, 204], [266, 204], [266, 231], [192, 231]], [[98, 229], [162, 229], [162, 256], [98, 256]], [[192, 231], [319, 227], [320, 254], [193, 258]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [556, 257], [556, 281], [192, 281]], [[98, 281], [161, 281], [161, 306], [98, 306]], [[196, 284], [310, 284], [310, 304], [196, 304]], [[98, 306], [161, 306], [161, 332], [98, 332]], [[194, 307], [727, 307], [727, 331], [194, 331]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [428, 336], [428, 354], [196, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[194, 359], [412, 359], [412, 383], [194, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[194, 384], [393, 384], [393, 408], [194, 408]], [[98, 408], [155, 408], [155, 434], [98, 434]], [[194, 409], [443, 409], [443, 433], [194, 433]], [[98, 434], [155, 434], [155, 459], [98, 459]], [[192, 434], [474, 434], [474, 458], [192, 458]], [[98, 458], [155, 458], [155, 484], [98, 484]], [[194, 463], [399, 463], [399, 481], [194, 481]], [[98, 484], [155, 484], [155, 509], [98, 509]], [[194, 486], [404, 486], [404, 509], [194, 509]], [[98, 511], [155, 511], [155, 536], [98, 536]], [[194, 511], [567, 511], [567, 534], [194, 534]], [[98, 536], [153, 536], [153, 561], [98, 561]], [[192, 536], [310, 536], [310, 561], [192, 561]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[192, 561], [310, 561], [310, 586], [192, 586]], [[98, 586], [155, 586], [155, 611], [98, 611]], [[194, 588], [438, 588], [438, 611], [194, 611]], [[98, 611], [155, 611], [155, 638], [98, 638]], [[192, 613], [377, 613], [377, 636], [192, 636]], [[98, 636], [155, 636], [155, 663], [98, 663]], [[192, 640], [408, 640], [408, 663], [192, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[192, 663], [301, 663], [301, 688], [192, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[188, 686], [227, 686], [227, 717], [188, 717]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[190, 713], [262, 713], [262, 740], [190, 740]], [[98, 740], [157, 740], [157, 765], [98, 765]], [[192, 740], [689, 740], [689, 763], [192, 763]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[192, 765], [325, 765], [325, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [294, 792], [294, 817], [192, 817]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[190, 815], [244, 815], [244, 844], [190, 844]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[190, 842], [364, 842], [364, 867], [190, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [449, 869], [449, 892], [192, 892]], [[98, 892], [155, 892], [155, 919], [98, 919]], [[192, 894], [449, 894], [449, 917], [192, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[194, 919], [441, 919], [441, 944], [194, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [349, 946], [349, 969], [194, 969]], [[98, 969], [155, 969], [155, 996], [98, 996]], [[192, 971], [524, 971], [524, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [332, 996], [332, 1021], [192, 1021]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[194, 1022], [508, 1022], [508, 1046], [194, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[194, 1047], [606, 1047], [606, 1071], [194, 1071]], [[98, 1071], [157, 1071], [157, 1096], [98, 1096]], [[192, 1072], [733, 1072], [733, 1096], [192, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [347, 1098], [347, 1121], [192, 1121]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[192, 1123], [367, 1123], [367, 1146], [192, 1146]], [[98, 1148], [155, 1148], [155, 1173], [98, 1173]], [[192, 1149], [504, 1149], [504, 1173], [192, 1173]], [[98, 1173], [157, 1173], [157, 1198], [98, 1198]], [[194, 1173], [412, 1173], [412, 1196], [194, 1196]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [456, 1198], [456, 1223], [190, 1225]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[190, 1224], [260, 1224], [260, 1251], [190, 1251]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1251], [430, 1251], [430, 1274], [192, 1274]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[194, 1276], [482, 1276], [482, 1300], [194, 1300]], [[98, 1301], [157, 1301], [157, 1326], [98, 1326]], [[196, 1305], [639, 1305], [639, 1323], [196, 1323]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[194, 1328], [460, 1328], [460, 1351], [194, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [476, 1353], [476, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1378], [500, 1378], [500, 1401], [192, 1401]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [428, 1403], [428, 1426], [192, 1426]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[192, 1428], [327, 1428], [327, 1453], [192, 1453]], [[98, 1453], [155, 1453], [155, 1478], [98, 1478]], [[192, 1455], [356, 1455], [356, 1478], [192, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[192, 1480], [314, 1480], [314, 1505], [192, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[190, 1503], [242, 1503], [242, 1532], [190, 1532]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[190, 1530], [332, 1530], [332, 1555], [190, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[188, 1553], [264, 1553], [264, 1586], [188, 1586]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[191, 1578], [370, 1584], [369, 1611], [190, 1605]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1607], [401, 1607], [401, 1632], [192, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[192, 1634], [375, 1634], [375, 1657], [192, 1657]], [[98, 1657], [157, 1657], [157, 1684], [98, 1684]], [[192, 1659], [478, 1659], [478, 1682], [192, 1682]], [[100, 1682], [155, 1682], [155, 1709], [100, 1709]], [[192, 1684], [286, 1684], [286, 1712], [192, 1712]]], "rec_texts": ["0.000", "厦部第1腰椎水平截面", "4.479", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.054", "C反应蛋白C-REACTIVEPROTEIN", "0.072", "血尿酸SERUMURICACID", "0.074", "脂肪酶*", "0.075", "血管紧张素Ⅱ*", "0.087", "胆固醇COMMONPLASMA CHOLESTERIN", "0.099", "血管紧张素I", "0.101", "血浆丰配化脂肪酸NONETHERIZED FATTYACIDSOF PLASMA", "0.161", "血钾PLASMAPOTASSIUM", "0.095", "血清蛋白SERUM ALBUMEN", "0.097", "血红蛋白HAEMOGLOBIN", "0.103", "尿中蛋白质PROTEININURINE", "0.105", "PERIPHERIC BLOOD LEUCOCYTES", "0.107", "嗜碱性粒细胞BASOPHILS", "0.107", "血红血球ERYTHROCYTES", "0.114", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.131", "免疫球蛋白G*", "0.132", "免疫球蛋白M*", "0.133", "尿白血球URINELEUCOCYTES", "0.135", "单核细胞MONOCYTES", "0.143", "血清蛋白SERUMPROTEIN", "0.147", "BETA球蛋白", "0.201", "锂*", "0.092", "胆汁酸*", "0.093", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.099", "ALPHA2球蛋白*", "0.102", "胰高血糖素", "0.104", "糖苷*", "0.105", "红细胞沉降率(ESR)", "0.106", "血清补体SERUMCOMPLEMENT", "0.107", "肿瘤标志物MELANOGENE在尿*", "0.107", "血清溶菌酵SERUMLYSOZYME", "0.109", "血细胞比容，全血", "0.111", "游离胆固醇FREEPLASMACHOLESTERIN", "0.113", "糖基化血红蛋白*", "0.114", "血清铜蓝蛋白SERUMCERULOPLASMIN", "0.115", "肌酸磷酸酵素COMMON CREATINPHOSPHOKINASE", "0.115", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.116", "RHEUMOFACTOR*", "0.116", "肿瘤标志物胸苷激酶", "0.117", "血清淀粉酵素SERUMALPHAAMYLASE", "0.117", "尿肌配URINECREATININE", "0.117", "伽马球蛋白GAMMA-GLOBULINS", "0.117", "铁蛋白*", "0.118", "嗜酸性粒细胞EOSINOPHILES", "0.118", "促肾上腺皮质激素CORTICOTROPIN", "0.118", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.118", "尿中肾上腺素URINEADRENALIN", "0.118", "嗜中性粒细胞STABNEUTROPHILS", "0.119", "生长激素SOMATOTROPICHORMONE", "0.119", "血组织胺BLOODHISTAMINE", "0.119", "ALPHA1球蛋白*", "0.120", "血糖BLOOD SUGAR", "0.120", "甲状腺球蛋白*", "0.120", "肾素*", "0.120", "抗链球菌溶血素*", "0.121", "催乳素*", "0.121", "ALPHA1-抗胰蛋白酶*", "0.123", "维生素B1（THIAMINE）", "0.126", "DELTA氨基乙酰丙酸*", "0.126", "血浆磷脂PLASMA PHOSPHOTIDES", "0.127", "维生素B6*"], "rec_scores": [0.9992889165878296, 0.9235565066337585, 0.9989500045776367, 0.9983981251716614, 0.9667975902557373, 0.9999244809150696, 0.9883997440338135, 0.9999293088912964, 0.9981278777122498, 0.9998855590820312, 0.9802283644676208, 0.9999462366104126, 0.9428358674049377, 0.999926745891571, 0.9795368313789368, 0.9999297857284546, 0.9789831638336182, 0.9998544454574585, 0.9332330822944641, 0.9999052286148071, 0.9812120795249939, 0.999720573425293, 0.9809539914131165, 0.9997221231460571, 0.9985331296920776, 0.9996230006217957, 0.9969345927238464, 0.9995471239089966, 0.9688208699226379, 0.999531626701355, 0.9848082661628723, 0.9993850588798523, 0.9961680173873901, 0.9993475675582886, 0.9929898977279663, 0.9995128512382507, 0.9875523447990417, 0.999610424041748, 0.9918586611747742, 0.9995530843734741, 0.9923917055130005, 0.9996044039726257, 0.9973767995834351, 0.9995918273925781, 0.9954931735992432, 0.9992172122001648, 0.992581307888031, 0.9994300603866577, 0.9606835842132568, 0.9998161196708679, 0.969925045967102, 0.9997072219848633, 0.9949657917022705, 0.9996426701545715, 0.9461562037467957, 0.9996402859687805, 0.9972966909408569, 0.9994826316833496, 0.973040759563446, 0.9995371103286743, 0.9220654964447021, 0.9993749856948853, 0.9985002279281616, 0.9995378255844116, 0.993736982345581, 0.9995242357254028, 0.9920037388801575, 0.9994146227836609, 0.9945255517959595, 0.9985436201095581, 0.9800680875778198, 0.9995222091674805, 0.9608697891235352, 0.9990524053573608, 0.9947469830513, 0.9994711875915527, 0.9837502241134644, 0.9992499351501465, 0.9949914813041687, 0.9994398951530457, 0.9896174669265747, 0.9991324543952942, 0.9894270300865173, 0.9991629719734192, 0.9988448023796082, 0.9991371035575867, 0.9620406031608582, 0.9991861581802368, 0.9962862133979797, 0.9991371035575867, 0.908932089805603, 0.9994661211967468, 0.997796356678009, 0.999498188495636, 0.9984483122825623, 0.9994878768920898, 0.9974063634872437, 0.9993160963058472, 0.9933616518974304, 0.9994661211967468, 0.9978678822517395, 0.9994169473648071, 0.9967248439788818, 0.9988118410110474, 0.9917929172515869, 0.9987851977348328, 0.9760702848434448, 0.9992280006408691, 0.9667790532112122, 0.99952632188797, 0.9408621191978455, 0.9994872212409973, 0.8973817825317383, 0.9993170499801636, 0.9718778133392334, 0.9995825886726379, 0.9844926595687866, 0.9996083974838257, 0.9883496165275574, 0.9995576739311218, 0.9556136727333069, 0.9991583824157715, 0.9539235234260559, 0.9996776580810547, 0.9760136008262634, 0.9992958903312683, 0.9817430377006531], "rec_boxes": [[98, 77, 157, 102], [192, 79, 384, 102], [98, 102, 157, 127], [192, 102, 270, 129], [194, 129, 462, 152], [98, 154, 161, 179], [194, 156, 495, 179], [98, 179, 161, 204], [194, 181, 423, 204], [98, 204, 161, 229], [192, 204, 266, 231], [98, 229, 162, 256], [192, 227, 320, 258], [98, 256, 161, 281], [192, 257, 556, 281], [98, 281, 161, 306], [196, 284, 310, 304], [98, 306, 161, 332], [194, 307, 727, 331], [98, 332, 161, 358], [196, 336, 428, 354], [98, 358, 157, 383], [194, 359, 412, 383], [98, 383, 155, 409], [194, 384, 393, 408], [98, 408, 155, 434], [194, 409, 443, 433], [98, 434, 155, 459], [192, 434, 474, 458], [98, 458, 155, 484], [194, 463, 399, 481], [98, 484, 155, 509], [194, 486, 404, 509], [98, 511, 155, 536], [194, 511, 567, 534], [98, 536, 153, 561], [192, 536, 310, 561], [98, 561, 155, 588], [192, 561, 310, 586], [98, 586, 155, 611], [194, 588, 438, 611], [98, 611, 155, 638], [192, 613, 377, 636], [98, 636, 155, 663], [192, 640, 408, 663], [98, 663, 155, 688], [192, 663, 301, 688], [98, 688, 155, 715], [188, 686, 227, 717], [98, 713, 157, 740], [190, 713, 262, 740], [98, 740, 157, 765], [192, 740, 689, 763], [98, 765, 157, 790], [192, 765, 325, 790], [98, 790, 157, 817], [192, 792, 294, 817], [98, 815, 157, 842], [190, 815, 244, 844], [98, 840, 157, 867], [190, 842, 364, 867], [98, 867, 157, 892], [192, 869, 449, 892], [98, 892, 155, 919], [192, 894, 449, 917], [98, 917, 155, 944], [194, 919, 441, 944], [98, 944, 157, 969], [194, 946, 349, 969], [98, 969, 155, 996], [192, 971, 524, 994], [98, 994, 157, 1021], [192, 996, 332, 1021], [98, 1021, 157, 1046], [194, 1022, 508, 1046], [98, 1046, 157, 1071], [194, 1047, 606, 1071], [98, 1071, 157, 1096], [192, 1072, 733, 1096], [98, 1096, 157, 1123], [192, 1098, 347, 1121], [98, 1123, 157, 1148], [192, 1123, 367, 1146], [98, 1148, 155, 1173], [192, 1149, 504, 1173], [98, 1173, 157, 1198], [194, 1173, 412, 1196], [98, 1198, 157, 1224], [190, 1198, 456, 1225], [98, 1224, 157, 1249], [190, 1224, 260, 1251], [98, 1249, 157, 1274], [192, 1251, 430, 1274], [98, 1274, 157, 1301], [194, 1276, 482, 1300], [98, 1301, 157, 1326], [196, 1305, 639, 1323], [98, 1326, 157, 1351], [194, 1328, 460, 1351], [98, 1351, 157, 1376], [192, 1353, 476, 1376], [98, 1376, 157, 1403], [192, 1378, 500, 1401], [100, 1403, 157, 1428], [192, 1403, 428, 1426], [100, 1428, 157, 1453], [192, 1428, 327, 1453], [98, 1453, 155, 1478], [192, 1455, 356, 1478], [98, 1478, 157, 1505], [192, 1480, 314, 1505], [98, 1505, 157, 1530], [190, 1503, 242, 1532], [98, 1530, 157, 1555], [190, 1530, 332, 1555], [98, 1555, 155, 1582], [188, 1553, 264, 1586], [98, 1580, 157, 1607], [190, 1578, 370, 1611], [98, 1607, 157, 1632], [192, 1607, 401, 1632], [100, 1632, 157, 1657], [192, 1634, 375, 1657], [98, 1657, 157, 1684], [192, 1659, 478, 1682], [100, 1682, 155, 1709], [192, 1684, 286, 1712]]}}]}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//e9803d6e-1b0e-4e68-bf72-545c8238a502/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T12%3A30%3A48Z%2F-1%2F%2Fc0714fd086a8fb75ee3c015978aa7760b2fcd412eaa5a212232debfcad890e88", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//e9803d6e-1b0e-4e68-bf72-545c8238a502/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T12%3A30%3A48Z%2F-1%2F%2Feda1eafea0dd1d7205fdbda4cfaa3a6ebcadba444450752d4e2f3ad9503c73f6", "table_cell_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//e9803d6e-1b0e-4e68-bf72-545c8238a502/table_cell_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T12%3A30%3A48Z%2F-1%2F%2F6287db6f79179e87f53405ec3add47e74a0a319cd0734a855a0f1c97ffc653b0"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//e9803d6e-1b0e-4e68-bf72-545c8238a502/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T12%3A30%3A48Z%2F-1%2F%2F11ea3a9e1b6f63afef48256d2f56c29cbb45c917fbe18323c694c8c074fe4055"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}