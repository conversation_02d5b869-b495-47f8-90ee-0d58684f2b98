<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文本颜色展示</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 28px;
        }
        .compact-stats {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .compact-stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .color-square {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            flex-shrink: 0;
        }
        .color-square.red {
            background: #ff6b6b;
        }
        .color-square.orange {
            background: #ffa726;
        }
        .color-square.green {
            background: #4caf50;
        }
        .color-square.blue {
            background: #42a5f5;
        }
        .stat-text {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
        }
        .search-box:focus {
            border-color: #667eea;
        }
        .view-toggle {
            display: flex;
            background: #f0f0f0;
            border-radius: 25px;
            overflow: hidden;
        }
        .color-filter {
            display: flex;
            background: #f0f0f0;
            border-radius: 25px;
            overflow: hidden;
            margin-left: 10px;
        }
        .filter-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }
        .filter-btn.active {
            background: #667eea;
            color: white;
        }
        .filter-btn.red.active {
            background: #ff6b6b;
        }
        .filter-btn.orange.active {
            background: #ffa726;
        }
        .filter-btn.green.active {
            background: #4caf50;
        }
        .filter-btn.blue.active {
            background: #42a5f5;
        }
        .view-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }
        .view-btn.active {
            background: #667eea;
            color: white;
        }
        .text-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .text-list {
            margin-top: 20px;
        }
        .text-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s;
            cursor: pointer;
            position: relative;
        }
        .text-item.red {
            border-left: 4px solid #ff6b6b;
            background: linear-gradient(to right, rgba(255, 107, 107, 0.05), white);
        }
        .text-item.orange {
            border-left: 4px solid #ffa726;
            background: linear-gradient(to right, rgba(255, 167, 38, 0.05), white);
        }
        .text-item.green {
            border-left: 4px solid #4caf50;
            background: linear-gradient(to right, rgba(76, 175, 80, 0.05), white);
        }
        .text-item.blue {
            border-left: 4px solid #42a5f5;
            background: linear-gradient(to right, rgba(66, 165, 245, 0.05), white);
        }
        .color-category {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }
        .color-category.red {
            background: #ff6b6b;
        }
        .color-category.orange {
            background: #ffa726;
        }
        .color-category.green {
            background: #4caf50;
        }
        .color-category.blue {
            background: #42a5f5;
        }
        .color-category.normal {
            background: #95a5a6;
        }
        .text-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .text-content {
            font-size: 16px;
            margin-bottom: 10px;
            line-height: 1.4;
            word-break: break-all;
        }
        .color-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        .color-preview {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid #ddd;
            flex-shrink: 0;
        }
        .color-values {
            font-size: 12px;
            color: #666;
        }
        .text-index {
            font-size: 12px;
            color: #999;
            text-align: right;
        }
        .list-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.3s;
        }
        .list-item:hover {
            background-color: #f9f9f9;
        }
        .list-item:last-child {
            border-bottom: none;
        }
        .list-index {
            width: 50px;
            font-size: 14px;
            color: #999;
            text-align: center;
        }
        .list-color {
            width: 40px;
            height: 20px;
            border-radius: 4px;
            margin: 0 15px;
            border: 1px solid #ddd;
        }
        .list-text {
            flex: 1;
            font-size: 14px;
            line-height: 1.4;
        }
        .list-color-info {
            font-size: 12px;
            color: #666;
            min-width: 120px;
            text-align: right;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .error {
            text-align: center;
            padding: 50px;
            color: #e74c3c;
            background: #fdf2f2;
            border-radius: 8px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            .text-grid {
                grid-template-columns: 1fr;
            }
            .stats {
                gap: 15px;
            }
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            .search-box {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>文字颜色分析结果</h1>
                <div class="organ-info" id="organInfo" style="margin: 10px 0; padding: 10px; background: #f0f8ff; border-radius: 5px; display: none;">
                    <strong>当前检测器官:</strong> <span id="targetOrgan">未识别</span>
                </div>
                <div class="stats-panel" id="statsPanel">
                    <div class="compact-stats">
                        <div class="compact-stat-item">
                            <span class="stat-text">总数: {{ textData.length }}</span>
                        </div>
                        <div class="compact-stat-item">
                            <div class="color-square red"></div>
                            <span class="stat-text">红色: {{ colorStats.red }} ({{ Math.round(colorStats.red / textData.length * 100) }}%)</span>
                        </div>
                        <div class="compact-stat-item">
                            <div class="color-square orange"></div>
                            <span class="stat-text">橘色: {{ colorStats.orange }} ({{ Math.round(colorStats.orange / textData.length * 100) }}%)</span>
                        </div>
                        <div class="compact-stat-item">
                            <div class="color-square green"></div>
                            <span class="stat-text">绿色: {{ colorStats.green }} ({{ Math.round(colorStats.green / textData.length * 100) }}%)</span>
                        </div>
                        <div class="compact-stat-item">
                            <div class="color-square blue"></div>
                            <span class="stat-text">蓝色: {{ colorStats.blue }} ({{ Math.round(colorStats.blue / textData.length * 100) }}%)</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="controls">
                <input 
                    v-model="searchText" 
                    class="search-box" 
                    placeholder="搜索文本内容..."
                    @input="filterData"
                >
                <div class="view-toggle">
                    <button 
                        class="view-btn" 
                        :class="{ active: viewMode === 'grid' }"
                        @click="viewMode = 'grid'"
                    >
                        网格视图
                    </button>
                    <button 
                        class="view-btn" 
                        :class="{ active: viewMode === 'list' }"
                        @click="viewMode = 'list'"
                    >
                        列表视图
                    </button>
                </div>
                <div class="color-filter">
                    <button 
                        class="filter-btn" 
                        :class="{ active: colorFilter === 'all' }"
                        @click="setColorFilter('all')"
                    >
                        全部
                    </button>
                    <button 
                        class="filter-btn red" 
                        :class="{ active: colorFilter === 'red' }"
                        @click="setColorFilter('red')"
                    >
                        红色
                    </button>
                    <button 
                        class="filter-btn orange" 
                        :class="{ active: colorFilter === 'orange' }"
                        @click="setColorFilter('orange')"
                    >
                        橘色
                    </button>
                    <button 
                        class="filter-btn green" 
                        :class="{ active: colorFilter === 'green' }"
                        @click="setColorFilter('green')"
                    >
                        绿色
                    </button>
                    <button 
                        class="filter-btn blue" 
                        :class="{ active: colorFilter === 'blue' }"
                        @click="setColorFilter('blue')"
                    >
                        蓝色
                    </button>
                </div>
            </div>

            <div v-if="loading" class="loading">
                <p>正在加载数据...</p>
            </div>

            <div v-else-if="error" class="error">
                <h3>加载失败</h3>
                <p>{{ error }}</p>
                <button @click="loadData" style="margin-top: 10px; padding: 8px 16px; background: #e74c3c; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    重新加载
                </button>
            </div>

            <div v-else>
                <!-- 网格视图 -->
                <div v-if="viewMode === 'grid'" class="text-grid">
                    <div 
                        v-for="item in filteredData" 
                        :key="$index" 
                        class="text-item"
                        :class="item.colorCategory"
                        @click="selectItem(item)"
                    >
                        <div class="color-category" :class="item.colorCategory">
                            {{ getCategoryLabel(item.colorCategory) }}
                        </div>
                        <div class="text-content" :style="{ color: item.color.hex }">
                            {{ item.text }}
                        </div>
                        <div class="color-info">
                            <div 
                                class="color-preview" 
                                :style="{ backgroundColor: item.color.hex }"
                            ></div>
                            <div class="color-values">
                                <div>RGB: {{ item.color.rgb.join(', ') }}</div>
                                <div>HEX: {{ item.color.hex }}</div>
                                <div>分类: {{ getCategoryLabel(item.colorCategory) }}</div>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- 列表视图 -->
                <div v-else class="text-list">
                    <div 
                        v-for="(item, $index) in filteredData" 
                        :key="$index" 
                        class="list-item"
                        :class="item.colorCategory"
                        @click="selectItem(item)"
                    >

                        <div 
                            class="list-color" 
                            :style="{ backgroundColor: item.color.hex }"
                        ></div>
                        <div class="list-text" :style="{ color: item.color.hex }">
                            {{ item.text }}
                        </div>
                        <div class="list-color-info">
                            {{ item.color.hex }}<br>
                            <small>{{ getCategoryLabel(item.colorCategory) }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    textData: [],
                    filteredData: [],
                    searchText: '',
                    viewMode: 'grid',
                    colorFilter: 'all',
                    loading: true,
                    error: null
                }
            },
            computed: {
                uniqueColors() {
                    const colors = new Set();
                    this.textData.forEach(item => {
                        colors.add(item.color.hex);
                    });
                    return Array.from(colors);
                },
                colorStats() {
                    const stats = { red: 0, orange: 0, green: 0, blue: 0, normal: 0 };
                    this.textData.forEach(item => {
                        stats[item.colorCategory] = (stats[item.colorCategory] || 0) + 1;
                    });
                    return stats;
                }
            },
            methods: {
                async loadData() {
                    this.loading = true;
                    this.error = null;
                    
                    try {
                        const response = await fetch('output/text_with_color.json');
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        const data = await response.json();
                        
                        console.log('加载的数据:', data);
                        
                        // 显示目标器官信息
                        if (data.target_organ) {
                            document.getElementById('targetOrgan').textContent = data.target_organ;
                            document.getElementById('organInfo').style.display = 'block';
                        }
                        
                        // 转换数据格式为数字-文字键值对
                        if (data.detailed_results) {
                            this.textData = data.detailed_results.map(item => {
                                // 构建显示文本：数字 -> 文字元素
                                const displayText = `${item.numeric_value} → ${item.text_elements.join(', ')}`;
                                
                                return {
                                    text: displayText,
                                    numeric_value: item.numeric_value,
                                    text_elements: item.text_elements,
                                    color: {
                                        hex: this.rgbToHex(item.numeric_rgb),
                                        rgb: item.numeric_rgb
                                    },
                                    coordinates: item.numeric_coordinates,
                                    value: item.float_value,
                                    text_colors: item.text_colors,
                                    colorCategory: this.categorizeColor(item.numeric_rgb)
                                };
                            });
                        } else if (data.analysis_results) {
                            // 兼容旧格式
                            this.textData = data.analysis_results.map(item => {
                                const displayText = item.text_elements ? 
                                    `${item.numeric_value} → ${item.text_elements.join(', ')}` : 
                                    item.numeric_value;
                                
                                return {
                                    text: displayText,
                                    numeric_value: item.numeric_value,
                                    text_elements: item.text_elements || [],
                                    color: {
                                        hex: this.rgbToHex(item.numeric_rgb || item.rgb_color),
                                        rgb: item.numeric_rgb || item.rgb_color
                                    },
                                    coordinates: item.numeric_coordinates || item.coordinates,
                                    value: item.float_value,
                                    colorCategory: this.categorizeColor(item.numeric_rgb || item.rgb_color)
                                };
                            });
                        }
                        
                        this.filteredData = this.textData;
                        this.loading = false;
                    } catch (err) {
                        this.error = `加载数据失败: ${err.message}`;
                        this.loading = false;
                        console.error('Error loading data:', err);
                    }
                },
                filterData() {
                    let filtered = this.textData;
                    
                    // 文本搜索过滤
                    if (this.searchText.trim()) {
                        const searchLower = this.searchText.toLowerCase();
                        filtered = filtered.filter(item => 
                            item.text.toLowerCase().includes(searchLower) ||
                            item.color.hex.toLowerCase().includes(searchLower) ||
                            item.color.rgb.join(',').includes(searchLower)
                        );
                    }
                    
                    // 颜色分类过滤
                    if (this.colorFilter !== 'all') {
                        filtered = filtered.filter(item => item.colorCategory === this.colorFilter);
                    }
                    
                    this.filteredData = filtered;
                },
                categorizeColor(rgb) {
                    const [r, g, b] = rgb;
                    
                    // 红色判断：基于实际测量范围，扩展以包含更多红色样本
                    // 包含 RGB(245,136,129), RGB(241,138,135), RGB(239,130,135) 等红色样本
                    if ((r >= 235 && g <= 160 && b <= 140 && r > g && r > b) ||
                        (r >= 240 && g <= 180 && b <= 150 && r >= g + 50)) {
                        return 'red';
                    }
                    
                    // 橘色判断：基于实际测量范围 RGB: (235,146,53) 到 RGB: (255,231,200)
// 扩展蓝色通道范围以包含更多橘色样本
                    if ((235 <= r && r <= 255) && (130 <= g && g <= 231) && (53 <= b && b <= 200) &&
                        (r >= 200) && (g >= 100)) {
                        return 'orange';
                    }
                    
                    // 绿色判断：基于实际测量范围 RGB: (70,150,70) 到 RGB: (240,250,240)
                    // 绿色通道占主导，红蓝通道相对较低
                    if ((70 <= r && r <= 240) && (150 <= g && g <= 250) && (70 <= b && b <= 240) &&
                        (g >= r) && (g >= b) && (g >= r + 20)) {
                        return 'green';
                    }
                    
                    // 蓝色判断：基于实际测量范围 RGB: (21,21,170) 到 RGB: (110,166,255)
                    if ((21 <= r && r <= 110) && (21 <= g && g <= 166) && (170 <= b && b <= 255) &&
                        (b >= r) && (b >= g)) {
                        return 'blue';
                    }
                    
                    // 白色判断
                    if (r >= 240 && g >= 240 && b >= 240) {
                        return 'normal';
                    }
                    
                    // 黑色判断
                    if (r <= 50 && g <= 50 && b <= 50) {
                        return 'normal';
                    }
                    
                    // 其他颜色默认为蓝色（正常值）
                    return 'blue';
                },
                setColorFilter(filter) {
                    this.colorFilter = filter;
                    this.filterData();
                },
                getCategoryLabel(category) {
                    const labels = {
                        red: '红色',
                        orange: '橘黄',
                        green: '绿色',
                        blue: '蓝色',
                        normal: '其他'
                    };
                    return labels[category] || '未知';
                },
                rgbToHex(rgb) {
                    const [r, g, b] = rgb;
                    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
                },
                selectItem(item) {
                    // 可以在这里添加选中项的处理逻辑
                    console.log('Selected item:', item);
                    // 例如：显示详细信息、复制颜色值等
                    navigator.clipboard.writeText(item.color.hex).then(() => {
                        // 可以添加一个提示消息
                        console.log('颜色值已复制到剪贴板:', item.color.hex);
                    }).catch(err => {
                        console.log('复制失败:', err);
                    });
                }
            },
            mounted() {
                this.loadData();
            }
        }).mount('#app');
    </script>
</body>
</html>