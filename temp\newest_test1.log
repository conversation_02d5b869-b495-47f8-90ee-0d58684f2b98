PS F:\myHbuilderAPP\MagneticOperator> wails dev
Wails CLI v2.10.1

Executing: go mod tidy
  • Generating bindings: 2025/07/01 23:57:46 KnownStructs: models.APIKeys       models.AppConfig        models.BiochemicalAnalysis      models.CloudFunctionConfig  models.ColorDetectionConfig     models.ConfigUserInfo   models.Contact  models.CozeConfig       models.CropSettingsmodels.CurrentUserCheckingInfo   models.DeviceInfo       models.Location models.MpAppInfo        models.OCRConfig        models.PathologyAnalysis    models.Patient  models.Registration     models.RoundData        models.SiteInfo models.UserInfo models.UserMedicalHistory       services.OCRResult  services.TaskResult
Not found: time.Time
Wails应用正常退出

Done.
  • Installing frontend dependencies: Done.
  • Compiling frontend: Done.

> frontend@0.0.0 dev
> vite


  VITE v3.2.11  ready in 345 ms

Vite Server URL: http://localhost:5173/
  ➜  Local:   http://localhost:5173/
Running frontend DevWatcher command: 'npm run dev'
  ➜  Network: use --host to expose
Building application for development...
  • Generating bindings: 2025/07/01 23:57:52 KnownStructs: models.APIKeys       models.AppConfig        models.BiochemicalAnalysis      models.CloudFunctionConfig  models.ColorDetectionConfig     models.ConfigUserInfo   models.Contact  models.CozeConfig       models.CropSettingsmodels.CurrentUserCheckingInfo   models.DeviceInfo       models.Location models.MpAppInfo        models.OCRConfig        models.PathologyAnalysis    models.Patient  models.Registration     models.RoundData        models.SiteInfo models.UserInfo models.UserMedicalHistory       services.OCRResult  services.TaskResult
Not found: time.Time
Wails应用正常退出

Done.
  • Generating application assets: Done.
  • Compiling application: Done.
 INFO  Wails is now using the new Go WebView2Loader. If you encounter any issues with it, please report them to https://github.com/wailsapp/wails/issues/2004. You could also use the old legacy loader with `-tags native_webview2loader`, but keep in mind this will be deprecated in the near future.

Using DevServer URL: http://localhost:34115
Using Frontend DevServer URL: http://localhost:5173/
Using reload debounce setting of 100 milliseconds
{"level":"INFO","timestamp":"2025-07-01T23:57:53.277+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":49496}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.314+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.314+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.314+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.314+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.314+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
INF | Serving assets from frontend DevServer URL: http://localhost:5173/
DEB | WebView2 Runtime Version '137.0.3296.93' installed. Minimum version required: 94.0.992.31.
DEB | [DevWebServer] Serving DevServer at http://localhost:34115
Watching (sub)/directory: F:\myHbuilderAPP\MagneticOperator
2025/07/01 23:57:53 [WebView2] Environment created successfully
=== 应用开始启动 ===
{"level":"INFO","timestamp":"2025-07-01T23:57:53.750+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
日志系统初始化成功
{"level":"INFO","timestamp":"2025-07-01T23:57:53.751+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
消息配置系统初始化成功
{"level":"INFO","timestamp":"2025-07-01T23:57:53.751+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
开始初始化服务...
{"level":"INFO","timestamp":"2025-07-01T23:57:53.751+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
调用 initServices()...
{"level":"INFO","timestamp":"2025-07-01T23:57:53.751+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.752+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.757+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
[集成服务] 集成截图服务已初始化
{"level":"INFO","timestamp":"2025-07-01T23:57:53.760+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
开始初始化任务管理器...
{"level":"INFO","timestamp":"2025-07-01T23:57:53.760+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
创建事件发射器...
设置App通知器...
创建任务管理器配置...
创建任务管理器...
创建任务处理器...
{"level":"INFO","timestamp":"2025-07-01T23:57:53.760+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.760+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.760+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
注册任务处理器...
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}    
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
启动任务管理器...
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
TRA | No listeners for event 'task-manager-started'
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
任务管理器启动成功！
{"level":"INFO","timestamp":"2025-07-01T23:57:53.763+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
任务管理器初始化完成
{"level":"INFO","timestamp":"2025-07-01T23:57:53.763+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.763+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
2025/07/01 23:57:53 ————————————————————————
2025/07/01 23:57:53 MAC地址: 00:15:5d:ed:4a:58
2025/07/01 23:57:53 Getting site info for device MAC: 00:15:5d:ed:4a:58
2025/07/01 23:57:53 [getSiteInfoByDeviceMAC] HTTP请求参数: {"mac_address":"00:15:5d:ed:4a:58"}
2025/07/01 23:57:53 -------------------------
DEB | [AssetHandler] Handling request '/' (file='.')
DEB | [AssetHandler] File '.' not found, serving '/' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/'
DEB | [AssetHandler] Handling request '/@vite/client' (file='@vite/client')
DEB | [AssetHandler] Handling request '/src/main.js' (file='src/main.js')
DEB | [AssetHandler] File '@vite/client' not found, serving '/@vite/client' by AssetHandler
DEB | [AssetHandler] File 'src/main.js' not found, serving '/src/main.js' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/@vite/client'
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/main.js'
DEB | [AssetHandler] Handling request '/node_modules/.vite/deps/vue.js' (file='node_modules/.vite/deps/vue.js')
DEB | [AssetHandler] File 'node_modules/.vite/deps/vue.js' not found, serving '/node_modules/.vite/deps/vue.js' by AssetHandler
DEB | [AssetHandler] Handling request '/node_modules/.vite/deps/pinia.js' (file='node_modules/.vite/deps/pinia.js')
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/node_modules/.vite/deps/vue.js?v=e3161484'
DEB | [AssetHandler] File 'node_modules/.vite/deps/pinia.js' not found, serving '/node_modules/.vite/deps/pinia.js' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/node_modules/.vite/deps/pinia.js?v=e3161484'
DEB | [AssetHandler] Handling request '/src/App.vue' (file='src/App.vue')
DEB | [AssetHandler] File 'src/App.vue' not found, serving '/src/App.vue' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/App.vue'
DEB | [AssetHandler] Handling request '/src/style.css' (file='src/style.css')
DEB | [AssetHandler] File 'src/style.css' not found, serving '/src/style.css' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/style.css'
DEB | [AssetHandler] Handling request '/node_modules/vite/dist/client/env.mjs' (file='node_modules/vite/dist/client/env.mjs')
DEB | [AssetHandler] File 'node_modules/vite/dist/client/env.mjs' not found, serving '/node_modules/vite/dist/client/env.mjs' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/node_modules/vite/dist/client/env.mjs'
2025/07/01 23:57:54 从[getSiteInfoByDeviceMAC] 响应respBody: {"errCode":"0","errMsg":"success","site_info":{"_id":"6840fe07716846a510dd0cb6","device_no":"00155ded4a58","mac_address":"00:15:5d:ed:4a:58","manufacturer":"河北光媚磁康科技发展有限公司","organization_id":[{"_id":"6840fe47ee43e346e32b3ee2","address":"北京市通州区潞城镇潞城社区卫生服务中心","site_id":"YL-BJ-TZ-001","site_name":"北京市通州区潞城镇潞城社区卫生服务中心","site_type":"社区医院","contact_phone":"13800138002","contact_person":"李医生"}]}}
2025/07/01 23:57:54 -------------------------
2025/07/01 23:57:54 ————————————————————————
2025/07/01 23:57:54 调用API获取站点信息，siteInfo: map[_id:6840fe07716846a510dd0cb6 device_no:00155ded4a58 mac_address:00:15:5d:ed:4a:58 manufacturer:河北光媚磁康科技发展有限公司 organization_id:[map[_id:6840fe47ee43e346e32b3ee2 address:北京市通州区潞城镇潞城社区卫生服务中心 contact_person:李医生 contact_phone:13800138002 site_id:YL-BJ-TZ-001 site_name:北京市通州区潞城镇潞城社区卫生服务中心 site_type:社区医院]]]     
2025/07/01 23:57:54 站点信息有变化，将更新缓存
TRA | No listeners for event 'siteInfoUpdated'
{"level":"INFO","timestamp":"2025-07-01T23:57:54.053+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
站点信息无变化: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)
initServices() 完成
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
创建并发截图服务...
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
并发截图服务创建完成
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.055+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.055+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.055+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
=== 应用启动成功 ===
{"level":"INFO","timestamp":"2025-07-01T23:57:54.055+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
DEB | [AssetHandler] Handling request '/node_modules/.vite/deps/chunk-YXLAR2RR.js' (file='node_modules/.vite/deps/chunk-YXLAR2RR.js')
DEB | [AssetHandler] File 'node_modules/.vite/deps/chunk-YXLAR2RR.js' not found, serving '/node_modules/.vite/deps/chunk-YXLAR2RR.js' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/node_modules/.vite/deps/chunk-YXLAR2RR.js?v=e3161484'
DEB | [AssetHandler] Handling request '/wailsjs/go/main/App.js' (file='wailsjs/go/main/App.js')
DEB | [AssetHandler] File 'wailsjs/go/main/App.js' not found, serving '/wailsjs/go/main/App.js' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/wailsjs/go/main/App.js'
DEB | [AssetHandler] Handling request '/src/stores/patient.js' (file='src/stores/patient.js')
DEB | [AssetHandler] Handling request '/src/stores/device.js' (file='src/stores/device.js')
DEB | [AssetHandler] File 'src/stores/patient.js' not found, serving '/src/stores/patient.js' by AssetHandler
DEB | [AssetHandler] File 'src/stores/device.js' not found, serving '/src/stores/device.js' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/stores/patient.js'
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/stores/device.js'
DEB | [AssetHandler] Handling request '/src/stores/notification.js' (file='src/stores/notification.js')
DEB | [AssetHandler] File 'src/stores/notification.js' not found, serving '/src/stores/notification.js' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/stores/notification.js'
DEB | [AssetHandler] Handling request '/src/components/ToastNotification.vue' (file='src/components/ToastNotification.vue')
DEB | [AssetHandler] File 'src/components/ToastNotification.vue' not found, serving '/src/components/ToastNotification.vue' by AssetHandler 
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/components/ToastNotification.vue'
DEB | [AssetHandler] Handling request '/src/components/ErrorBoundary.vue' (file='src/components/ErrorBoundary.vue')
DEB | [AssetHandler] File 'src/components/ErrorBoundary.vue' not found, serving '/src/components/ErrorBoundary.vue' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/components/ErrorBoundary.vue'
DEB | [AssetHandler] Handling request '/src/config/toastConfig.js' (file='src/config/toastConfig.js')
DEB | [AssetHandler] File 'src/config/toastConfig.js' not found, serving '/src/config/toastConfig.js' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/config/toastConfig.js'
DEB | [AssetHandler] Handling request '/src/utils/toastManager.js' (file='src/utils/toastManager.js')
DEB | [AssetHandler] File 'src/utils/toastManager.js' not found, serving '/src/utils/toastManager.js' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/utils/toastManager.js'
DEB | [AssetHandler] Handling request '/src/utils/eventManager.js' (file='src/utils/eventManager.js')
DEB | [AssetHandler] File 'src/utils/eventManager.js' not found, serving '/src/utils/eventManager.js' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/utils/eventManager.js'
DEB | [AssetHandler] Handling request '/src/utils/notificationManager.js' (file='src/utils/notificationManager.js')
DEB | [AssetHandler] File 'src/utils/notificationManager.js' not found, serving '/src/utils/notificationManager.js' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/utils/notificationManager.js'
DEB | [AssetHandler] Handling request '/src/utils/errorHandler.js' (file='src/utils/errorHandler.js')
DEB | [AssetHandler] File 'src/utils/errorHandler.js' not found, serving '/src/utils/errorHandler.js' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/utils/errorHandler.js'
DEB | [AssetHandler] Handling request '/src/utils/errorMonitor.js' (file='src/utils/errorMonitor.js')
DEB | [AssetHandler] File 'src/utils/errorMonitor.js' not found, serving '/src/utils/errorMonitor.js' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/utils/errorMonitor.js'
DEB | [AssetHandler] Handling request '/src/utils/asyncErrorHandler.js' (file='src/utils/asyncErrorHandler.js')
DEB | [AssetHandler] File 'src/utils/asyncErrorHandler.js' not found, serving '/src/utils/asyncErrorHandler.js' by AssetHandler
DEB | [AssetHandler] Handling request '/src/App.vue' (file='src/App.vue')
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/utils/asyncErrorHandler.js'
DEB | [AssetHandler] File 'src/App.vue' not found, serving '/src/App.vue' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/App.vue?vue&type=style&index=0&lang.css'
DEB | [AssetHandler] Handling request '/@id/__x00__plugin-vue:export-helper' (file='@id/__x00__plugin-vue:export-helper')
DEB | [AssetHandler] File '@id/__x00__plugin-vue:export-helper' not found, serving '/@id/__x00__plugin-vue:export-helper' by AssetHandler   
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/@id/__x00__plugin-vue:export-helper'
DEB | [AssetHandler] Handling request '/src/composables/useWailsRuntime.js' (file='src/composables/useWailsRuntime.js')
DEB | [AssetHandler] File 'src/composables/useWailsRuntime.js' not found, serving '/src/composables/useWailsRuntime.js' by AssetHandler     
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/composables/useWailsRuntime.js'
DEB | [AssetHandler] Handling request '/src/components/ToastIcon.vue' (file='src/components/ToastIcon.vue')
DEB | [AssetHandler] File 'src/components/ToastIcon.vue' not found, serving '/src/components/ToastIcon.vue' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/components/ToastIcon.vue'
DEB | [AssetHandler] Handling request '/src/components/ToastNotification.vue' (file='src/components/ToastNotification.vue')
DEB | [AssetHandler] File 'src/components/ToastNotification.vue' not found, serving '/src/components/ToastNotification.vue' by AssetHandler 
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/components/ToastNotification.vue?vue&type=style&index=0&scoped=0b083ac5&lang.css'
DEB | [AssetHandler] Handling request '/src/components/ErrorBoundary.vue' (file='src/components/ErrorBoundary.vue')
DEB | [AssetHandler] File 'src/components/ErrorBoundary.vue' not found, serving '/src/components/ErrorBoundary.vue' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/components/ErrorBoundary.vue?vue&type=style&index=0&scoped=0455a002&lang.css'
DEB | [AssetHandler] Handling request '/src/components/ToastIcon.vue' (file='src/components/ToastIcon.vue')
DEB | [AssetHandler] File 'src/components/ToastIcon.vue' not found, serving '/src/components/ToastIcon.vue' by AssetHandler
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/src/components/ToastIcon.vue?vue&type=style&index=0&scoped=f6b792fa&lang.css'   
{"level":"INFO","timestamp":"2025-07-01T23:57:54.312+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞
城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
TRA | json call result data: {"result":{"mp_app_info":{"appid":"wxd70aa4b433ef1843","target_page":"pages/p_scan/p_scan"},"site_info":{"site_id":"YL-BJ-TZ-001","site_name":"北京市通州区潞城镇潞城社区卫生服务中心","site_type":"社区医院","parent_org":"","location":{"province":"","city":"","district":"","address":"北京市通州区潞城镇潞城社区卫生服务中心"},"contact":{"manager":"李医生","phone":"13800138002"}},"crop_settings":{"top_percent":0.155,"bottom_percent":0.051,"left_percent":0.05,"right_percent":0.75,"always_on_top":false},"normal_windows_setting":{"top_percent":0.52,"bottom_percent":1.08,"left_percent":1.15,"right_percent":1.35,"always_on_top":true},"expanded_crop_settings":{"top_percent":0,"bottom_percent":0,"left_percent":0,"right_percent":0,"always_on_top":false},"api_keys":{"ocr":{"api_url":"https://kdu4x8t9m958c8ld.aistudio-hub.baidu.com/table-recognition","table_api_url":"","token":"4d377e625b3f2d11d1fe52b86616237bb2fefb06"},"coze":{"token":"pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0","workflow_id_post_pic":"7496900622433812531","workflow_id_post_registration":"7501566019660939279","workflow_id_user_info":"7501680491335614516","space_id":"7331689003143544832","app_id":"7496871719090077733"},"cloud_function":{"registrations_url":"https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice","screenshot_records_url":"https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/screenshot-records/createOrUpdateScreenshotRecord","siteInfoByDeviceMAC_url":"https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC","mark_patient_completed_url":"https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/markPatientCompleted","user_detect_raw_result_data_url":"https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/saveUserDetectRawResultData"}},"device_info":{"mac_address":"00:15:5d:ed:4a:58","device_name":""},"user_info":{"name":"","birth":"","id_number":""},"use_system_notification":true,"color_detection":{"debug_mode":true,"save_debug_files":true},"environment":"development","debug":true},"error":null,"callbackid":"main.App.GetConfig-**********"}

{"level":"INFO","timestamp":"2025-07-01T23:57:54.373+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.385+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}     
DEB | [AssetHandler] Handling request '/favicon.ico' (file='favicon.ico')
{"level":"INFO","timestamp":"2025-07-01T23:57:54.385+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
DEB | [AssetHandler] File 'favicon.ico' not found, serving '/favicon.ico' by AssetHandler
{"level":"INFO","timestamp":"2025-07-01T23:57:54.385+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
DEB | [ExternalAssetHandler] Loading 'http://localhost:5173/favicon.ico'
TRA | json call result data: {"result":{"site_id":"YL-BJ-TZ-001","site_name":"北京市通州区潞城镇潞城社区卫生服务中心","site_type":"社区医院","parent_org":"","location":{"province":"","city":"","district":"","address":"北京市通州区潞城镇潞城社区卫生服务中心"},"contact":{"manager":"李医生","phone":"13800138002"}},"error":null,"callbackid":"main.App.GetSiteInfo-3968338037"}

{"level":"INFO","timestamp":"2025-07-01T23:57:54.394+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
TRA | json call result data: {"result":{"A":{"code":"A01","name":"器官问题来源分析"},"B":{"code":"B02","name":"生化平衡分析"},"C":{"code":"C03","name":"病理形态学分析"},"器官问题来源分析":{"code":"A01","name":"器官问题来源分析"},"生化平衡分析":{"code":"B02","name":"生化平衡分析"},"病理形态学分析":{"code":"C03","name":"病理形态学分析"}},"error":null,"callbackid":"main.App.GetModeConfig-**********"}

{"level":"INFO","timestamp":"2025-07-01T23:57:54.399+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.399+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.403+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:57:54 Today's date: 2025-07-01
2025/07/01 23:57:54 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:57:54 -------------------------
2025/07/01 23:57:54 -------------------------
2025/07/01 23:57:54 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
TRA | json call result data: {"result":null,"error":null,"callbackid":"main.App.GetRegistrations-**********"}
