<template>
  <div class="device-info-panel">
    <div class="panel-header">
      <h3>设备信息</h3>
      <button @click="refreshInfo" class="refresh-btn" :disabled="refreshing">
        <span v-if="refreshing">刷新中...</span>
        <span v-else>刷新</span>
      </button>
    </div>
    
    <div class="panel-content">
      <div class="info-grid">
        <div class="info-item">
          <label>设备名称:</label>
          <span>{{ config?.DeviceInfo?.DeviceName || '获取中...' }}</span>
        </div>
        
        <div class="info-item">
          <label>MAC地址:</label>
          <span class="mac-address">{{ config?.DeviceInfo?.MacAddress || '获取中...' }}</span>
        </div>
        
        <div class="info-item">
          <label>IP地址:</label>
          <span class="ip-address">{{ config?.DeviceInfo?.IPAddress || '获取中...' }}</span>
        </div>
        
        <div class="info-item">
          <label>操作系统:</label>
          <span>{{ config?.DeviceInfo?.OS || '获取中...' }}</span>
        </div>
        
        <div class="info-item">
          <label>系统版本:</label>
          <span>{{ config?.DeviceInfo?.OSVersion || '获取中...' }}</span>
        </div>
        
        <div class="info-item">
          <label>CPU架构:</label>
          <span>{{ config?.DeviceInfo?.Architecture || '获取中...' }}</span>
        </div>
        
        <div class="info-item">
          <label>内存信息:</label>
          <span>{{ formatMemory(config?.DeviceInfo?.TotalMemory) }}</span>
        </div>
        
        <div class="info-item">
          <label>磁盘空间:</label>
          <span>{{ formatDisk(config?.DeviceInfo?.DiskSpace) }}</span>
        </div>
        
        <div class="info-item">
          <label>屏幕分辨率:</label>
          <span>{{ formatResolution(config?.DeviceInfo?.ScreenResolution) }}</span>
        </div>
        
        <div class="info-item">
          <label>网络状态:</label>
          <span :class="['network-status', networkStatusClass]">
            {{ networkStatusText }}
          </span>
        </div>
        
        <div class="info-item">
          <label>应用版本:</label>
          <span>{{ config?.DeviceInfo?.AppVersion || 'v1.0.0' }}</span>
        </div>
        
        <div class="info-item">
          <label>最后更新:</label>
          <span class="last-update">{{ formatTime(config?.DeviceInfo?.LastUpdate) }}</span>
        </div>
      </div>
      
      <div class="system-status">
        <h4>系统状态</h4>
        <div class="status-indicators">
          <div class="status-item">
            <div :class="['status-dot', cpuStatusClass]"></div>
            <span>CPU: {{ formatPercent(config?.DeviceInfo?.CPUUsage) }}</span>
          </div>
          
          <div class="status-item">
            <div :class="['status-dot', memoryStatusClass]"></div>
            <span>内存: {{ formatPercent(config?.DeviceInfo?.MemoryUsage) }}</span>
          </div>
          
          <div class="status-item">
            <div :class="['status-dot', diskStatusClass]"></div>
            <span>磁盘: {{ formatPercent(config?.DeviceInfo?.DiskUsage) }}</span>
          </div>
        </div>
      </div>
      
      <div class="performance-chart" v-if="showChart">
        <h4>性能监控</h4>
        <div class="chart-placeholder">
          <div class="chart-bar" v-for="(value, index) in performanceData" :key="index">
            <div class="bar-fill" :style="{ height: value + '%' }"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DeviceInfoPanel',
  props: {
    config: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      refreshing: false,
      showChart: true,
      performanceData: [65, 45, 78, 56, 89, 34, 67, 45, 78, 56]
    }
  },
  computed: {
    networkStatusClass() {
      const status = this.config?.DeviceInfo?.NetworkStatus
      if (status === 'connected') return 'connected'
      if (status === 'disconnected') return 'disconnected'
      return 'unknown'
    },
    
    networkStatusText() {
      const status = this.config?.DeviceInfo?.NetworkStatus
      if (status === 'connected') return '已连接'
      if (status === 'disconnected') return '已断开'
      return '未知'
    },
    
    cpuStatusClass() {
      const usage = this.config?.DeviceInfo?.CPUUsage || 0
      if (usage < 50) return 'good'
      if (usage < 80) return 'warning'
      return 'danger'
    },
    
    memoryStatusClass() {
      const usage = this.config?.DeviceInfo?.MemoryUsage || 0
      if (usage < 60) return 'good'
      if (usage < 85) return 'warning'
      return 'danger'
    },
    
    diskStatusClass() {
      const usage = this.config?.DeviceInfo?.DiskUsage || 0
      if (usage < 70) return 'good'
      if (usage < 90) return 'warning'
      return 'danger'
    }
  },
  mounted() {
    this.startPerformanceMonitoring()
  },
  beforeUnmount() {
    this.stopPerformanceMonitoring()
  },
  methods: {
    async refreshInfo() {
      this.refreshing = true
      try {
        // 模拟刷新延迟
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.$emit('refresh-device-info')
      } finally {
        this.refreshing = false
      }
    },
    
    formatMemory(bytes) {
      if (!bytes) return '获取中...'
      const gb = bytes / (1024 * 1024 * 1024)
      return `${gb.toFixed(1)} GB`
    },
    
    formatDisk(bytes) {
      if (!bytes) return '获取中...'
      const gb = bytes / (1024 * 1024 * 1024)
      return `${gb.toFixed(1)} GB`
    },
    
    formatResolution(resolution) {
      if (!resolution) return '获取中...'
      return resolution
    },
    
    formatTime(timestamp) {
      if (!timestamp) return '从未更新'
      return new Date(timestamp).toLocaleString()
    },
    
    formatPercent(value) {
      if (typeof value !== 'number') return '0%'
      return `${value.toFixed(1)}%`
    },
    
    startPerformanceMonitoring() {
      this.performanceInterval = setInterval(() => {
        // 模拟性能数据更新
        this.performanceData = this.performanceData.map(() => 
          Math.floor(Math.random() * 100)
        )
      }, 2000)
    },
    
    stopPerformanceMonitoring() {
      if (this.performanceInterval) {
        clearInterval(this.performanceInterval)
      }
    }
  }
}
</script>

<style scoped>
.device-info-panel {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  width: 100%;
  box-sizing: border-box;
}

.panel-header {
  background: #f8f9fa;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.refresh-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  background: #218838;
}

.refresh-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.panel-content {
  padding: 16px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 500;
  color: #555;
  font-size: 13px;
  flex: 0 0 auto;
  margin-right: 12px;
}

.info-item span {
  color: #333;
  font-size: 13px;
  text-align: right;
  word-break: break-all;
}

.mac-address,
.ip-address {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.network-status {
  font-weight: 500;
}

.network-status.connected {
  color: #28a745;
}

.network-status.disconnected {
  color: #dc3545;
}

.network-status.unknown {
  color: #6c757d;
}

.last-update {
  font-size: 12px;
  color: #666;
}

.system-status {
  margin-bottom: 20px;
}

.system-status h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.status-indicators {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot.good {
  background: #28a745;
}

.status-dot.warning {
  background: #ffc107;
}

.status-dot.danger {
  background: #dc3545;
}

.performance-chart {
  margin-top: 20px;
}

.performance-chart h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.chart-placeholder {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 60px;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
}

.chart-bar {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: end;
}

.bar-fill {
  width: 100%;
  background: linear-gradient(to top, #3498db, #5dade2);
  border-radius: 2px 2px 0 0;
  transition: height 0.3s ease;
  min-height: 2px;
}
</style>