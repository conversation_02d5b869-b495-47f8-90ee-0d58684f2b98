
{"level":"INFO","timestamp":"2025-07-01T23:57:54.645+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.645+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:57:54 Today's date: 2025-07-01
{"level":"INFO","timestamp":"2025-07-01T23:57:54.645+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患 者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:57:54 Today's date: 2025-07-01
2025/07/01 23:57:54 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:57:54 -------------------------
2025/07/01 23:57:54 Today's date: 2025-07-01
2025/07/01 23:57:54 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:57:54 -------------------------
2025/07/01 23:57:54 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:57:54 -------------------------
2025/07/01 23:57:54 -------------------------
2025/07/01 23:57:54 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
TRA | json call result data: {"result":null,"error":null,"callbackid":"main.App.GetUnanalyzedPatients-329687771"}

2025/07/01 23:57:54 -------------------------
2025/07/01 23:57:54 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
TRA | json call result data: {"result":[],"error":null,"callbackid":"main.App.GetPendingRegistrations-**********"}

2025/07/01 23:57:54 -------------------------
2025/07/01 23:57:54 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
TRA | json call result data: {"result":null,"error":null,"callbackid":"main.App.GetCompletedPatientsByDate-214081822"}

{"level":"INFO","timestamp":"2025-07-01T23:57:54.878+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.878+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
TRA | json call result data: {"result":{"file_path":"pic\\YL-BJ-TZ-001_00155ded4a58.png","qr_code_base64":"iVBORw0KGgoAAAANSUhEUgAAAQAAAAEAAQMAAABmvDolAAAABlBMVEX///8AAABVwtN+AAADY0lEQVR42uyZMY7rOBBEi1DAkDcwL2JI13JggAIc6FoydBHqBgwZEKxFUR7/PxttsNYwGEa25wVSd7O6uge/5/f832ciuUUAo3fz1RP3KTIPO8naDTACdvXkPEWSe0JgTEEf9KeTAM9lNQnDMyKsnjkwcp72xNIXsDHl4RmTLRfkO3y6Xc3pQHQ5PMmFJMsU3YzOAMBuTCSjo0Kq2nt9OAtoZb+nfB+9W/768P1e/DCgM+y0j6oLYhIU0nmI30Tkw8AIt7DCFhNbNm27pEW3dewH8C5fPTMmunlSABndUnRJ19MAuKU0fSDnq0+4m5jsRqIjAEiAIbl62C3CPqreyST7uptnAB6W0eEOz3lgsg9GKQbYEWCk7dVxBqBOZAuQrGJ7x3lAE/lWaRkX8FGRQjGN7AUYfbrByDa0/u1yqP4l++NZgNFNvLgjbtPOPFQpxs4cak+A0m3LFDmrZT8YJWmJj/UsYIqwZFIkjyTOqv+rZL8fAD5hqE4lJ+1Kdh7huFW5nfOAljuWicluFZbVO8p6vbLZB9C8H4b2e2x5dyw+5WE9C5CtYnV5UCSpZ3seHSd/+YcOAPnPoYIS2LAauR3vZHK+InkCYOiWrTolMdltT3aeZI+b6+sH0FswaaxwGnZ4OK4Icj0LaDMgZUQPobDNwyuboR/AROBq9BZKN5wiebzXeUATUhXYU7/vtHrmWdL2rsmfBwyZBcmxLwVOjrTNsO+e9XlAAyl3ffXJsgogQlFI0Q0gAZnaxsPjNrD17hTWC/6k++PACATpw6P6pO8SCl1L9zZ7HQBAuk0VWTPOskXXKi1s8f0WJwBG1uVy9MdQLi43n6y0dgRMMeHl2HGDP1qS/MPXfHECMKra9zYQ6pHbZq+JmP3j7X8eAOQGEdbmuFLzz0sxfAvI5wETEYpPLGOzps2ItqVHfg+DHQDUlTxmnFAM7Tx6l4edOA+YCLteWiQRSGpyf7Xm2g3QtmoVnEfZGkNbNB7+ZXJOANp2sTbtSmHTHAGwrRlDP0Db/OsWtBWVIQbym20+AziW6kcklc02Pk/klyvuBZBbnmUb5CHUDcPq+a9/kXwcuABNFtaLO4Yd5Xeo/QDHHpvNNhS4fNReBL6V3EeBVvZK4mtzpcn0j5HoBPg9v+e/n38CAAD//7dzxjt890SSAAAAAElFTkSuQmCC"},"error":null,"callbackid":"main.App.GenerateRegistrationQRCode-**********"}

{"level":"INFO","timestamp":"2025-07-01T23:57:54.885+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:57:54 Today's date: 2025-07-01
2025/07/01 23:57:54 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:57:54 -------------------------
2025/07/01 23:57:55 -------------------------
2025/07/01 23:57:55 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
TRA | json call result data: {"result":null,"error":null,"callbackid":"main.App.GetRegistrations-**********"}

{"level":"INFO","timestamp":"2025-07-01T23:57:55.102+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:57:55 Today's date: 2025-07-01
2025/07/01 23:57:55 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:57:55 -------------------------
2025/07/01 23:57:55 -------------------------
2025/07/01 23:57:55 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
TRA | json call result data: {"result":[],"error":null,"callbackid":"main.App.GetPendingRegistrations-**********"}

{"level":"INFO","timestamp":"2025-07-01T23:57:55.302+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患 者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:57:55 Today's date: 2025-07-01
2025/07/01 23:57:55 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:57:55 -------------------------
2025/07/01 23:57:55 -------------------------
2025/07/01 23:57:55 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
TRA | json call result data: {"result":null,"error":null,"callbackid":"main.App.GetCompletedPatientsByDate-**********"}



To develop in the browser and call your bound Go methods from Javascript, navigate to: http://localhost:34115
{"level":"INFO","timestamp":"2025-07-01T23:58:05.879+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
=== 快捷键触发截图任务 ===
模式: B
{"level":"INFO","timestamp":"2025-07-01T23:58:05.879+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
=== 收到截图任务请求 ===
任务类型: screenshot_B, 模式: B, 描述: 快捷键B模式截图
{"level":"INFO","timestamp":"2025-07-01T23:58:05.880+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
任务管理器可用，提交任务到队列
{"level":"INFO","timestamp":"2025-07-01T23:58:05.880+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:58:05.880+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:58:05 Today's date: 2025-07-01
2025/07/01 23:58:05 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:58:05 -------------------------
2025/07/01 23:58:06 -------------------------
2025/07/01 23:58:06 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
[开发模式] 候检者列表为空，自动生成测试患者: test-11200
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_B_1751385486126981900_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_B_1751385486126981900_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
TRA | No listeners for event 'task-submitted'
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751385486126981900_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751385486126981900_test-11200, User: test-11200, Round: 0"}
TRA | No listeners for event 'showToastNotification'
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_B_1751385486126981900_test-11200","taskType":"screenshot_B","workerID":0}
TRA | No listeners for event 'task-started'
截图任务已提交到任务管理器: 快捷键B模式截图
{"level":"INFO","timestamp":"2025-07-01T23:58:06.127+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_B_1751385486126981900_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.127+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.127+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.128+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:58:06 Today's date: 2025-07-01
2025/07/01 23:58:06 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:58:06 -------------------------
2025/07/01 23:58:06 -------------------------
2025/07/01 23:58:06 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
[开发模式] 候检者列表为空，自动生成测试患者: test-11200
[轮次调试] 初始化轮次管理器
[轮次调试] 用户标识: test-11200_20250701
[轮次调试] 创建新用户轮次状态 - 从第1轮开始
[DEBUG] 开始截图工作流程，模式: B, 患者: test-11200, 轮次: R01
{"level":"INFO","timestamp":"2025-07-01T23:58:06.345+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.345+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:58:06 Today's date: 2025-07-01
2025/07/01 23:58:06 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:58:06 -------------------------
2025/07/01 23:58:06 -------------------------
2025/07/01 23:58:06 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.530+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者， 处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
[INFO] 当前没有选中受检者，处于本系统操作者自行研究模式...
{"level":"INFO","timestamp":"2025-07-01T23:58:06.530+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.530+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
[DEBUG] 开始处理截图工作流程 - 模式: B, 用户: test-11200
[DEBUG] 第一步: 截取完整屏幕截图并进行预处理...
[DEBUG] 第一步成功: 预处理截图已保存到 pic\temp\temp_screenshot_B_test-11200_20250701_235806.png
[DEBUG] 第二步: 对预处理后的图片进行OCR API调用和识别...
[截图工作流] 当前轮次: 1, 患者: test-11200, 模式: B
[OCR-TASK] 检查重复任务 - 用户: test-11200, 模式: B, 轮次: 1, 图片: pic\temp\temp_screenshot_B_test-11200_20250701_235806.png
[OCR-TASK] 当前任务上下文数量: 0
[OCR-TASK] 重复任务检测结果: 无重复任务
[OCR-TASK] 创建OCR任务上下文: test-11200_B_R01_1751385486917285800
[截图工作流] 创建OCR任务上下文: test-11200_B_R01_1751385486917285800
{"level":"INFO","timestamp":"2025-07-01T23:58:06.917+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:58:06 Today's date: 2025-07-01
2025/07/01 23:58:06 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:58:06 -------------------------
2025/07/01 23:58:07 -------------------------
2025/07/01 23:58:07 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
[开发模式] 候检者列表为空，自动生成测试患者: test-11200
{"level":"INFO","timestamp":"2025-07-01T23:58:07.088+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
[DEBUG] 开始对图片进行OCR API识别: pic\temp\temp_screenshot_B_test-11200_20250701_235806.png
[网络重试] 第1次尝试，请求体大小: 475470 字节
{"level":"INFO","timestamp":"2025-07-01T23:58:08.444+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
=== 快捷键触发截图任务 ===
模式: C
{"level":"INFO","timestamp":"2025-07-01T23:58:08.444+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
=== 收到截图任务请求 ===
任务类型: screenshot_C, 模式: C, 描述: 快捷键C模式截图
{"level":"INFO","timestamp":"2025-07-01T23:58:08.444+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
任务管理器可用，提交任务到队列
{"level":"INFO","timestamp":"2025-07-01T23:58:08.444+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.444+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:58:08 Today's date: 2025-07-01
2025/07/01 23:58:08 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:58:08 -------------------------
2025/07/01 23:58:08 -------------------------
2025/07/01 23:58:08 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
[开发模式] 候检者列表为空，自动生成测试患者: test-11200
{"level":"INFO","timestamp":"2025-07-01T23:58:08.667+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.667+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_C_1751385488668060000_test-11200","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_C_1751385488668060000_test-11200","taskType":"screenshot_C","userName":"test-11200","priority":2}
TRA | No listeners for event 'task-submitted'
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751385488668060000_test-11200","taskType":"screenshot_C","userName":"test-11200","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751385488668060000_test-11200, User: test-11200, Round: 0"}
TRA | No listeners for event 'showToastNotification'{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_C_1751385488668060000_test-11200","taskType":"screenshot_C","workerID":1}

TRA | No listeners for event 'task-started'
截图任务已提交到任务管理器: 快捷键C模式截图
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_C_1751385488668060000_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-C","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:58:08 Today's date: 2025-07-01
2025/07/01 23:58:08 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:58:08 -------------------------
2025/07/01 23:58:08 -------------------------
2025/07/01 23:58:08 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
[开发模式] 候检者列表为空，自动生成测试患者: test-11200
[轮次调试] 用户标识: test-11200_20250701
[轮次调试] 找到现有轮次状态 - 当前轮次: 1, B02完成: false, C03完成: false
[DEBUG] 开始截图工作流程，模式: C, 患者: test-11200, 轮次: R01
{"level":"INFO","timestamp":"2025-07-01T23:58:08.845+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式C] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.846+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:58:08 Today's date: 2025-07-01
2025/07/01 23:58:08 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:58:08 -------------------------
2025/07/01 23:58:09 -------------------------
2025/07/01 23:58:09 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
{"level":"INFO","timestamp":"2025-07-01T23:58:09.077+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者， 处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
[INFO] 当前没有选中受检者，处于本系统操作者自行研究模式...
{"level":"INFO","timestamp":"2025-07-01T23:58:09.077+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:09.079+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: C, 用户: test-11200\n"}
[DEBUG] 开始处理截图工作流程 - 模式: C, 用户: test-11200
[DEBUG] 第一步: 截取完整屏幕截图并进行预处理...
[DEBUG] 第一步成功: 预处理截图已保存到 pic\temp\temp_screenshot_C_test-11200_20250701_235809.png
[DEBUG] 第二步: 对预处理后的图片进行OCR API调用和识别...
[截图工作流] 当前轮次: 1, 患者: test-11200, 模式: C
[OCR-TASK] 检查重复任务 - 用户: test-11200, 模式: C, 轮次: 1, 图片: pic\temp\temp_screenshot_C_test-11200_20250701_235809.png
[OCR-TASK] 当前任务上下文数量: 1
[OCR-TASK] 重复任务检测结果: 无重复任务
[OCR-TASK] 创建OCR任务上下文: test-11200_C_R01_1751385489464844100
[截图工作流] 创建OCR任务上下文: test-11200_C_R01_1751385489464844100
{"level":"INFO","timestamp":"2025-07-01T23:58:09.464+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/01 23:58:09 Today's date: 2025-07-01
2025/07/01 23:58:09 Request JSON data: {"date":"2025-07-01","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/01 23:58:09 -------------------------
2025/07/01 23:58:09 -------------------------
2025/07/01 23:58:09 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-01"}}
[开发模式] 候检者列表为空，自动生成测试患者: test-11200
{"level":"INFO","timestamp":"2025-07-01T23:58:09.676+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
[DEBUG] 开始对图片进行OCR API识别: pic\temp\temp_screenshot_C_test-11200_20250701_235809.png
[网络重试] 第1次尝试，请求体大小: 373714 字节
[OCR_API响应返回值] 已经从OCR响应返回JSON值responseBody，太长注释了暂不显示。[信息] 找到rec_texts字段，共128个元素
[器官名称提取] 通过0.000标志找到器官名称: 消化系统—夷腺;汁二指肠正面图
[OCR_API响应返回值] 从 rec_texts 提取的键值对数据: map[0.000:消化系统—夷腺;汁二指肠正面图 0.048:血浆非酯化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA 0.054:血浆中性脂肪NEUTRALFATSOFPLASMA 0.057:游离胆固醇FREEPLASMACHOLESTERIN 0.060:血尿素BLOODUREA 0.062:抗利尿激素 0.063:总铁结合力（TIBC）* 0.064:血清中的氨基酸NITROGENOFAMINOACIDSINSERUM 0.065:血清淀粉酵素SERUMALPHAAMYLASE 0.066:维生素D3* 0.067:血清甘油三酯SERUMTRIGLYCERIDES 0.068:血红蛋白HAEMOGLOBIN 0.069:甲状腺素结合球蛋白 0.071:甲状旁腺* 0.072:血脂COMMON LIPIDS OF PLASMA 0.073:单核细胞MONOCYTES 0.075:红细胞沉降率(ESR) 0.076:游离甲状腺素 0.077:前列腺特异性抗原（PSA） 0.078:分段的中性粒细胞SEGMENTEDNEUTROPHILS 0.081:维生素A（视黄醇）* 0.082:肿瘤标 志物MELANOGENE在尿* 0.083:血清铁SERUM IRON 0.084:胰蛋白酶* 0.085:转铁蛋白 0.087:凝血酵素PROTHROMBININDEX 0.089:降钙素* 0.090:血清蛋白SERUMABUMEN]
器官名称校验: OCR识别='消化系统—夷腺;汁二指肠正面图' -> 标准名称='消化系统--胰腺；十二指肠；正面图' (相似度: 0.65)
[解析响应并提取信息] 原始器官名称: 消化系统—夷腺;汁二指肠正面图 -> 校准后器官名称: 消化系统--胰腺；十二指肠；正面图
[OCR-TASK] 完成OCR任务上下文: test-11200_B_R01_1751385486917285800, 成功: true
[DEBUG] 第二步成功: OCR API调用和识别完成 - 任务ID: test-11200_B_R01_1751385486917285800
[DEBUG] OCR API返回值详情:
[DEBUG] - 校验后器官名称: 消化系统--胰腺；十二指肠；正面图
[DEBUG] - 键值对数量: 28
[DEBUG] - 置信度: 0.00
[DEBUG] - 图片路径: pic\temp\temp_screenshot_B_test-11200_20250701_235806.png
{"level":"INFO","timestamp":"2025-07-01T23:58:36.562+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 消化系统--胰腺；十二指肠；正面图, 键值对数量: 28, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250701_235806.png\n"}
[DEBUG] - 原始响应长度: 49754 字节
[DEBUG] - 原始响应预览: {"logId":"5604d906-fd90-4554-94ed-db99dd8873d4","result":{"tableRecResults":[{"prunedResult":{"model_settings":{"use_doc_preprocessor":false,"use_layout_detection":true,"use_ocr_model":true},"layout_det_res":{"boxes":[{"cls_id":8,"label":"table","score":0.9886482357978821,"coordinate":[14.01123046875,71.5833969116211,767.5036010742188,1714.4365234375]},{"cls_id":9,"label":"table_title","score":0.6792432069778442,"coordinate":[19.7296142578125,27.130123138427734,520.5859375,62.96539306640625]},{"...
[DEBUG] 第二步完成，准备进入第三步...
[DEBUG] 第三步: 检测到B02模式（生化平衡分析），开始调用颜色检测算法进行文字颜色分析...
2025/07/01 23:58:36 [颜色检测] 开始B02模式颜色识别: pic\temp\temp_screenshot_B_test-11200_20250701_235806.png
2025/07/01 23:58:36 四区域高级颜色分析器
2025/07/01 23:58:36 图像文件: pic\temp\temp_screenshot_B_test-11200_20250701_235806.png
2025/07/01 23:58:36 OCR数据: 直接传入JSON数据
2025/07/01 23:58:36 输出目录: ./temp/ocr_text_color
2025/07/01 23:58:36 OCR数据加载成功（格式1），文本元素数量: 128
2025/07/01 23:58:36 图像加载成功，尺寸: 768x1716
2025/07/01 23:58:36 开始图像增强处理
2025/07/01 23:58:36 图像增强处理完成
2025/07/01 23:58:36 [调试] 使用格式1，TableRecResults数量: 1
2025/07/01 23:58:36 [调试] 成功提取RecTexts，数量: 128
2025/07/01 23:58:36 [调试] 前几个文本: [按照标准图谱相似度递减列表： 0.000 消化系统—夷腺;汁二指肠正面图 2.167 优化配置]
2025/07/01 23:58:36 开始四区域分析，文本元素数量: 128
2025/07/01 23:58:36 第1行分析完成 - 数字: 按照标准图谱相似度递减列表：, 文本: 0.000, 最终颜色: 蓝色
2025/07/01 23:58:36 第2行分析完成 - 数字: 消化系统—夷腺;汁二指肠正面图, 文本: 2.167, 最终颜色: 蓝色
2025/07/01 23:58:36 第3行分析完成 - 数字: 优化配置, 文本: 虚拟模式-健康问题发展趋势列表：, 最终颜色: 蓝色
2025/07/01 23:58:36 第4行分析完成 - 数字: 0.048, 文本: 血浆非酯化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA, 最终颜色: 蓝色
2025/07/01 23:58:36 第5行分析完成 - 数字: 0.054, 文本: 血清溶菌酵SERUMLYSOZYME, 最终颜色: 蓝色
2025/07/01 23:58:36 第6行分析完成 - 数字: 0.054, 文本: 血浆中性脂肪NEUTRALFATSOFPLASMA, 最终颜色: 蓝色
2025/07/01 23:58:36 第7行分析完成 - 数字: 0.057, 文本: 游离胆固醇FREEPLASMACHOLESTERIN, 最终颜色: 蓝色
2025/07/01 23:58:36 第8行分析完成 - 数字: 0.060, 文本: 血清补体SERUM COMPLEMENT, 最终颜色: 蓝色
2025/07/01 23:58:36 第9行分析完成 - 数字: 0.060, 文本: 血尿素BLOODUREA, 最终颜色: 蓝色
2025/07/01 23:58:36 第10行分析完成 - 数字: 0.062, 文本: 伽马球蛋白GAMMA-GLOBULINS, 最终颜色: 蓝色
2025/07/01 23:58:36 第11行分析完成 - 数字: 0.062, 文本: 抗利尿激素, 最终颜色: 蓝色
2025/07/01 23:58:36 第12行分析完成 - 数字: 0.063, 文本: 总铁结合力（TIBC）*, 最终颜色: 蓝色
2025/07/01 23:58:36 第13行分析完成 - 数字: 0.064, 文本: 胆固醇COMMON PLASMA CHOLESTERIN, 最终颜色: 蓝色
2025/07/01 23:58:36 第14行分析完成 - 数字: 0.064, 文本: 血清中的氨基酸NITROGENOFAMINOACIDSINSERUM, 最终颜色: 蓝色
2025/07/01 23:58:36 第15行分析完成 - 数字: 0.065, 文本: 血清淀粉酵素SERUMALPHAAMYLASE, 最终颜色: 蓝色
2025/07/01 23:58:36 第16行分析完成 - 数字: 0.066, 文本: BETA球蛋白*, 最终颜色: 蓝色
2025/07/01 23:58:36 第17行分析完成 - 数字: 0.066, 文本: 维生素D3*, 最终颜色: 蓝色
2025/07/01 23:58:36 第18行分析完成 - 数字: 0.067, 文本: AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE, 最终颜色: 蓝色
2025/07/01 23:58:36 第19行分析完成 - 数字: 0.067, 文本: 血清甘油三酯SERUMTRIGLYCERIDES, 最终颜色: 蓝色
2025/07/01 23:58:36 第20行分析完成 - 数字: 0.068, 文本: 血红蛋白HAEMOGLOBIN, 最终颜色: 蓝色
2025/07/01 23:58:36 第21行分析完成 - 数字: 0.069, 文本: 淋巴细胞LYMPHOCYTES, 最终颜色: 蓝色
2025/07/01 23:58:36 第22行分析完成 - 数字: 0.069, 文本: 甲状腺素结合球蛋白, 最终颜色: 蓝色
2025/07/01 23:58:36 第23行分析完成 - 数字: 0.071, 文本: PERIPHERICBLOODLEUCOCYTES, 最终颜色: 蓝色
2025/07/01 23:58:36 第24行分析完成 - 数字: 0.071, 文本: 血胆红素COMMONBLOODBILIRUBIN, 最终颜色: 蓝色
2025/07/01 23:58:36 第25行分析完成 - 数字: 0.071, 文本: 甲状旁腺*, 最终颜色: 蓝色
2025/07/01 23:58:36 第26行分析完成 - 数字: 0.072, 文本: 糖基化血红蛋白*, 最终颜色: 蓝色
2025/07/01 23:58:36 第27行分析完成 - 数字: 0.072, 文本: 血脂COMMON LIPIDS OF PLASMA, 最终颜色: 蓝色
2025/07/01 23:58:36 第28行分析完成 - 数字: 0.073, 文本: 血清蛋白SERUMPROTEIN, 最终颜色: 蓝色
2025/07/01 23:58:36 第29行分析完成 - 数字: 0.073, 文本: C反应蛋白C-REACTIVEPROTEIN, 最终颜色: 蓝色
2025/07/01 23:58:36 第30行分析完成 - 数字: 0.073, 文本: 血红血球ERYTHROCYTES, 最终颜色: 蓝色
2025/07/01 23:58:36 第31行分析完成 - 数字: 0.073, 文本: 单核细胞MONOCYTES, 最终颜色: 蓝色
2025/07/01 23:58:36 第32行分析完成 - 数字: 0.075, 文本: 嗜碱性粒细胞BASOPHILS, 最终颜色: 蓝色
2025/07/01 23:58:36 第33行分析完成 - 数字: 0.075, 文本: 网织红细胞PERIPHERICBLOODRETICULOCYTES, 最终颜色: 蓝色
2025/07/01 23:58:36 第34行分析完成 - 数字: 0.075, 文本: 红细胞沉降率(ESR), 最终颜色: 蓝色
2025/07/01 23:58:36 第35行分析完成 - 数字: 0.076, 文本: 血小板PERIPHERICBLOOD THROMBOCYTES, 最终颜色: 蓝色
2025/07/01 23:58:36 第36行分析完成 - 数字: 0.076, 文本: 游离甲状腺素, 最终颜色: 蓝色
2025/07/01 23:58:36 第37行分析完成 - 数字: 0.077, 文本: 血浆磷脂PLASMA PHOSPHOTIDES, 最终颜色: 蓝色
2025/07/01 23:58:36 第38行分析完成 - 数字: 0.077, 文本: 前列腺特异性抗原（PSA）, 最终颜色: 蓝色
2025/07/01 23:58:36 第39行分析完成 - 数字: 0.078, 文本: 分段的中性粒细胞SEGMENTEDNEUTROPHILS, 最终颜色: 蓝色
2025/07/01 23:58:36 第40行分析完成 - 数字: 0.081, 文本: 甲状腺球蛋白*, 最终颜色: 蓝色
2025/07/01 23:58:36 第41行分析完成 - 数字: 0.081, 文本: 乳酸脱氢酵素COMMONLACTADEHYDROGENASE, 最终颜色: 蓝色
2025/07/01 23:58:36 第42行分析完成 - 数字: 0.081, 文本: GLUTAMATEDEHYDROGENASE*, 最终颜色: 蓝色
2025/07/01 23:58:36 第43行分析完成 - 数字: 0.081, 文本: 备解素*, 最终颜色: 蓝色
2025/07/01 23:58:36 第44行分析完成 - 数字: 0.081, 文本: ALPHA1球蛋白*, 最终颜色: 蓝色
2025/07/01 23:58:36 第45行分析完成 - 数字: 0.081, 文本: 肌红蛋白, 最终颜色: 蓝色
2025/07/01 23:58:36 第46行分析完成 - 数字: 0.081, 文本: 维生素A（视黄醇）*, 最终颜色: 蓝色
2025/07/01 23:58:36 第47行分析完成 - 数字: 0.082, 文本: ALPHA2球蛋白, 最终颜色: 蓝色
2025/07/01 23:58:36 第48行分析完成 - 数字: 0.082, 文本: 催乳素*, 最终颜色: 蓝色
2025/07/01 23:58:36 第49行分析完成 - 数字: 0.082, 文本: 肿瘤标志物MELANOGENE在尿*, 最终颜色: 蓝色
2025/07/01 23:58:36 第50行分析完成 - 数字: 0.083, 文本: 血管紧张素I*, 最终颜色: 蓝色
2025/07/01 23:58:36 第51行分析完成 - 数字: 0.083, 文本: 血清铁SERUM IRON, 最终颜色: 蓝色
2025/07/01 23:58:36 第52行分析完成 - 数字: 0.084, 文本: 蛋白C*, 最终颜色: 蓝色
2025/07/01 23:58:36 第53行分析完成 - 数字: 0.084, 文本: GAMMA谷氨酰, 最终颜色: 蓝色
2025/07/01 23:58:36 第54行分析完成 - 数字: 0.084, 文本: 胰岛素*, 最终颜色: 蓝色
2025/07/01 23:58:36 第55行分析完成 - 数字: 0.084, 文本: 血管紧张素I*, 最终颜色: 蓝色
2025/07/01 23:58:36 第56行分析完成 - 数字: 0.084, 文本: ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM, 最终颜色: 蓝色
2025/07/01 23:58:36 第57行分析完成 - 数字: 0.084, 文本: 胰蛋白酶*, 最终颜色: 蓝色
2025/07/01 23:58:36 第58行分析完成 - 数字: 0.085, 文本: 转铁蛋白, 最终颜色: 蓝色
2025/07/01 23:58:36 第59行分析完成 - 数字: 0.087, 文本: 凝血酵素PROTHROMBININDEX, 最终颜色: 蓝色
2025/07/01 23:58:36 第60行分析完成 - 数字: 0.089, 文本: 降钙素*, 最终颜色: 蓝色
2025/07/01 23:58:36 第61行分析完成 - 数字: 0.090, 文本: 肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE, 最终颜色: 蓝色
2025/07/01 23:58:36 第62行分析完成 - 数字: 0.090, 文本: 红血球啉*, 最终颜色: 蓝色
2025/07/01 23:58:36 第63行分析完成 - 数字: 0.090, 文本: 嗜酸性粒细胞EOSINOPHILES, 最终颜色: 蓝色
2025/07/01 23:58:36 第64行分析完成 - 数字: 0.090, 文本: 血清蛋白SERUMABUMEN, 最终颜色: 蓝色
2025/07/01 23:58:36 四区域分析结果已保存:
2025/07/01 23:58:36   JSON报告: temp\ocr_text_color\four_region_analysis_results.json
2025/07/01 23:58:36   文本报告: temp\ocr_text_color\four_region_analysis_summary.txt
2025/07/01 23:58:36 四区域分析完成，耗时: 275.9973ms
2025/07/01 23:58:36 成功分析 64/64 行
2025/07/01 23:58:36 [颜色检测] 图片: pic\temp\temp_screenshot_B_test-11200_20250701_235806.png
2025/07/01 23:58:36 [颜色检测] 变量名: temp_screenshot_B_test_11200_20250701_235806
2025/07/01 23:58:36 [颜色检测] 总行数: 64, 成功行数: 64
2025/07/01 23:58:36 [颜色检测] 颜色分布: map[蓝色:64]
2025/07/01 23:58:36 [颜色检测] 第1行 - 数字: 按照标准图谱相似度递减列表：, 文本: [0.000], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第2行 - 数字: 消化系统—夷腺;汁二指肠正面图, 文本: [2.167], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第3行 - 数字: 优化配置, 文本: [虚拟模式-健康问题发展趋势列表：], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第4行 - 数字: 0.048, 文本: [血浆非酯化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA], 最终颜色: 蓝色, 置信度: 1.00    
2025/07/01 23:58:36 [颜色检测] 第5行 - 数字: 0.054, 文本: [血清溶菌酵SERUMLYSOZYME], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第6行 - 数字: 0.054, 文本: [血浆中性脂肪NEUTRALFATSOFPLASMA], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第7行 - 数字: 0.057, 文本: [游离胆固醇FREEPLASMACHOLESTERIN], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第8行 - 数字: 0.060, 文本: [血清补体SERUM COMPLEMENT], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第9行 - 数字: 0.060, 文本: [血尿素BLOODUREA], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第10行 - 数字: 0.062, 文本: [伽马球蛋白GAMMA-GLOBULINS], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第11行 - 数字: 0.062, 文本: [抗利尿激素], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第12行 - 数字: 0.063, 文本: [总铁结合力（TIBC）*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第13行 - 数字: 0.064, 文本: [胆固醇COMMON PLASMA CHOLESTERIN], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第14行 - 数字: 0.064, 文本: [血清中的氨基酸NITROGENOFAMINOACIDSINSERUM], 最终颜色: 蓝色, 置信度: 1.00        
2025/07/01 23:58:36 [颜色检测] 第15行 - 数字: 0.065, 文本: [血清淀粉酵素SERUMALPHAAMYLASE], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第16行 - 数字: 0.066, 文本: [BETA球蛋白*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第17行 - 数字: 0.066, 文本: [维生素D3*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第18行 - 数字: 0.067, 文本: [AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第19行 - 数字: 0.067, 文本: [血清甘油三酯SERUMTRIGLYCERIDES], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第20行 - 数字: 0.068, 文本: [血红蛋白HAEMOGLOBIN], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第21行 - 数字: 0.069, 文本: [淋巴细胞LYMPHOCYTES], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第22行 - 数字: 0.069, 文本: [甲状腺素结合球蛋白], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第23行 - 数字: 0.071, 文本: [PERIPHERICBLOODLEUCOCYTES], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第24行 - 数字: 0.071, 文本: [血胆红素COMMONBLOODBILIRUBIN], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第25行 - 数字: 0.071, 文本: [甲状旁腺*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第26行 - 数字: 0.072, 文本: [糖基化血红蛋白*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第27行 - 数字: 0.072, 文本: [血脂COMMON LIPIDS OF PLASMA], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第28行 - 数字: 0.073, 文本: [血清蛋白SERUMPROTEIN], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第29行 - 数字: 0.073, 文本: [C反应蛋白C-REACTIVEPROTEIN], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第30行 - 数字: 0.073, 文本: [血红血球ERYTHROCYTES], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第31行 - 数字: 0.073, 文本: [单核细胞MONOCYTES], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第32行 - 数字: 0.075, 文本: [嗜碱性粒细胞BASOPHILS], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第33行 - 数字: 0.075, 文本: [网织红细胞PERIPHERICBLOODRETICULOCYTES], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第34行 - 数字: 0.075, 文本: [红细胞沉降率(ESR)], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第35行 - 数字: 0.076, 文本: [血小板PERIPHERICBLOOD THROMBOCYTES], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第36行 - 数字: 0.076, 文本: [游离甲状腺素], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第37行 - 数字: 0.077, 文本: [血浆磷脂PLASMA PHOSPHOTIDES], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第38行 - 数字: 0.077, 文本: [前列腺特异性抗原（PSA）], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第39行 - 数字: 0.078, 文本: [分段的中性粒细胞SEGMENTEDNEUTROPHILS], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第40行 - 数字: 0.081, 文本: [甲状腺球蛋白*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第41行 - 数字: 0.081, 文本: [乳酸脱氢酵素COMMONLACTADEHYDROGENASE], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第42行 - 数字: 0.081, 文本: [GLUTAMATEDEHYDROGENASE*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第43行 - 数字: 0.081, 文本: [备解素*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第44行 - 数字: 0.081, 文本: [ALPHA1球蛋白*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第45行 - 数字: 0.081, 文本: [肌红蛋白], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第46行 - 数字: 0.081, 文本: [维生素A（视黄醇）*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第47行 - 数字: 0.082, 文本: [ALPHA2球蛋白], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第48行 - 数字: 0.082, 文本: [催乳素*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第49行 - 数字: 0.082, 文本: [肿瘤标志物MELANOGENE在尿*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第50行 - 数字: 0.083, 文本: [血管紧张素I*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第51行 - 数字: 0.083, 文本: [血清铁SERUM IRON], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第52行 - 数字: 0.084, 文本: [蛋白C*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第53行 - 数字: 0.084, 文本: [GAMMA谷氨酰], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第54行 - 数字: 0.084, 文本: [胰岛素*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第55行 - 数字: 0.084, 文本: [血管紧张素I*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第56行 - 数字: 0.084, 文本: [ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM], 最终颜色: 蓝色, 置信度: 1.00   
2025/07/01 23:58:36 [颜色检测] 第57行 - 数字: 0.084, 文本: [胰蛋白酶*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第58行 - 数字: 0.085, 文本: [转铁蛋白], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第59行 - 数字: 0.087, 文本: [凝血酵素PROTHROMBININDEX], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第60行 - 数字: 0.089, 文本: [降钙素*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第61行 - 数字: 0.090, 文本: [肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第62行 - 数字: 0.090, 文本: [红血球啉*], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第63行 - 数字: 0.090, 文本: [嗜酸性粒细胞EOSINOPHILES], 最终颜色: 蓝色, 置信度: 1.00
2025/07/01 23:58:36 [颜色检测] 第64行 - 数字: 0.090, 文本: [血清蛋白SERUMABUMEN], 最终颜色: 蓝色, 置信度: 1.00
[DEBUG] 颜色检测成功完成
{"level":"INFO","timestamp":"2025-07-01T23:58:36.866+0800","caller":"utils/logger.go:94","msg":"颜色检测成功完成","tempFilePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_235806.png"}
[DEBUG] 第三步完成，准备进入第四步...
[DEBUG] 第三步结束，即将进入第四步
[DEBUG] 第四步开始: 提取D值列表数据并更新用户检测信息...
{"level":"INFO","timestamp":"2025-07-01T23:58:36.867+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"B"}
[DEBUG] 从OCR结果中提取到 0 个D值数据
[DEBUG] 第四步成功: 提取到 0 个D值数据
{"level":"INFO","timestamp":"2025-07-01T23:58:36.869+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
[DEBUG] 第四步完成，准备更新用户检测信息...
{"level":"INFO","timestamp":"2025-07-01T23:58:36.869+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.869+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_235806.png","organName":"消化系统--胰腺；十二指肠；正面图","operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.870+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751385516869754500","用户":"test-11200"}
[轮次调试] 用户标识: test-11200_20250701
[轮次调试] 找到现有轮次状态 - 当前轮次: 1, B02完成: false, C03完成: false
{"level":"INFO","timestamp":"2025-07-01T23:58:36.870+0800","caller":"utils/logger.go:94","msg":"当前轮次","轮次":1,"operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.870+0800","caller":"utils/logger.go:94","msg":"扩展轮次数组","轮次数":1,"operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.870+0800","caller":"utils/logger.go:94","msg":"设置轮次器官名称","轮次":1,"器官":"消化系统--胰腺；十二指肠；正面图","operationID":"test-11200_B_1751385516869754500"}
[轮次调试] 标记模式完成 - 用户: test-11200, 模式: B02
[轮次调试] 用户标识: test-11200_20250701
[轮次调试] 标记前状态 - 当前轮次: 1, B02完成: false, C03完成: false
[轮次调试] 标记B02模式完成
TRA | No listeners for event 'updateOperationStatus'
[操作状态] B02生化平衡分析已加入队列，等待C03病理形态学分析启动...
{"level":"INFO","timestamp":"2025-07-01T23:58:36.873+0800","caller":"utils/logger.go:94","msg":"更新操作状态","status":"B02生化平衡分析已加 入队列，等待C03病理形态学分析启动...","mode":"B","round":1,"completed":true}
TRA | No listeners for event 'showToastNotification'
[Toast] 系统提示: B02生化平衡分析截图成功
[轮次调试] 标记后状态 - 当前轮次: 1, B02完成: true, C03完成: false, 轮次完成: false
[轮次调试] 轮次未完成，保持当前轮次: 1
TRA | No listeners for event 'updateProgress'
[进度更新] 5.0% - 模式: B, 轮次: 1, 已完成: 0
{"level":"INFO","timestamp":"2025-07-01T23:58:36.873+0800","caller":"utils/logger.go:94","msg":"开始计算已完成轮次数","operationID":"test-11200_B_1751385516869754500"}
[DEBUG] 更新用户检测信息: 用户=test-11200, 轮次=1, 模式=B, 器官=消化系统--胰腺；十二指肠；正面图, 已完成轮次=0/1
{"level":"INFO","timestamp":"2025-07-01T23:58:36.873+0800","caller":"utils/logger.go:94","msg":"轮次数据更新","userName":"test-11200","currentRound":1,"mode":"B","organName":"消化系统--胰腺；十二指肠；正面图","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.873+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751385516869754500"}
[OCR_API响应返回值] 已经从OCR响应返回JSON值responseBody，太长注释了暂不显示。[信息] 找到rec_texts字段，共124个元素
[器官名称提取] 通过0.000标志找到器官名称: 消化系统—胰腺；十二指肠正面图
[OCR_API响应返回值] 从 rec_texts 提取的键值对数据: map[0.000:消化系统—胰腺；十二指肠正面图]
器官名称校验: OCR识别='消化系统—胰腺；十二指肠正面图' -> 标准名称='消化系统--胰腺；十二指肠；正面图' (相似度: 0.82)
[解析响应并提取信息] 原始器官名称: 消化系统—胰腺；十二指肠正面图 -> 校准后器官名称: 消化系统--胰腺；十二指肠；正面图
[OCR-TASK] 完成OCR任务上下文: test-11200_C_R01_1751385489464844100, 成功: true
[DEBUG] 第二步成功: OCR API调用和识别完成 - 任务ID: test-11200_C_R01_1751385489464844100
[DEBUG] OCR API返回值详情:
[DEBUG] - 校验后器官名称: 消化系统--胰腺；十二指肠；正面图
[DEBUG] - 键值对数量: 1
[DEBUG] - 置信度: 0.00
[DEBUG] - 图片路径: pic\temp\temp_screenshot_C_test-11200_20250701_235809.png
{"level":"INFO","timestamp":"2025-07-01T23:59:02.926+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 消化系统--胰腺；十二指肠；正面图, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_test-11200_20250701_235809.png\n"}
[DEBUG] - 原始响应长度: 52537 字节
[DEBUG] - 原始响应预览: {"logId":"b85553c4-e3cc-49a7-98c2-ae798a76fbd7","result":{"tableRecResults":[{"prunedResult":{"model_settings":{"use_doc_preprocessor":false,"use_layout_detection":true,"use_ocr_model":true},"layout_det_res":{"boxes":[{"cls_id":8,"label":"table","score":0.9888850450515747,"coordinate":[13.54595947265625,75.98855590820312,768,1659.6885986328125]},{"cls_id":9,"label":"table_title","score":0.635001003742218,"coordinate":[19.546119689941406,27.423938751220703,520.6099853515625,63.13751220703125]},{"...
[DEBUG] 第二步完成，准备进入第三步...
[DEBUG] 第三步: 检测到C03模式（病理形态学分析），跳过颜色检测
[DEBUG] 第三步结束，即将进入第四步
[DEBUG] 第四步开始: 提取D值列表数据并更新用户检测信息...
{"level":"INFO","timestamp":"2025-07-01T23:59:02.926+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"C"}
[DEBUG] 从OCR结果中提取到 0 个D值数据
[DEBUG] 第四步成功: 提取到 0 个D值数据
{"level":"INFO","timestamp":"2025-07-01T23:59:02.928+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
[DEBUG] 第四步完成，准备更新用户检测信息...
{"level":"INFO","timestamp":"2025-07-01T23:59:02.928+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_C_1751385542928617400"}
{"level":"INFO","timestamp":"2025-07-01T23:59:02.929+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"C","imagePath":"pic\\temp\\temp_screenshot_C_test-11200_20250701_235809.png","organName":"消化系统--胰腺；十二指肠；正面图","operationID":"test-11200_C_1751385542928617400"}
{"level":"INFO","timestamp":"2025-07-02T00:01:15.762+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
=== 快捷键触发截图任务 ===
模式: B
{"level":"INFO","timestamp":"2025-07-02T00:01:15.762+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
=== 收到截图任务请求 ===
任务类型: screenshot_B, 模式: B, 描述: 快捷键B模式截图
{"level":"INFO","timestamp":"2025-07-02T00:01:15.762+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
任务管理器可用，提交任务到队列
{"level":"INFO","timestamp":"2025-07-02T00:01:15.762+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-02T00:01:15.762+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/02 00:01:15 Today's date: 2025-07-02
2025/07/02 00:01:15 Request JSON data: {"date":"2025-07-02","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/02 00:01:15 -------------------------
2025/07/02 00:01:16 -------------------------
2025/07/02 00:01:16 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-02"}}
[开发模式] 候检者列表为空，自动生成测试患者: test-11200
{"level":"INFO","timestamp":"2025-07-02T00:01:16.394+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-02T00:01:16.394+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-02T00:01:16.394+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键B模式截图 任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
TRA | No listeners for event 'showToastNotification'
截图任务已提交到任务管理器: 快捷键B模式截图
{"level":"INFO","timestamp":"2025-07-02T00:01:16.394+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-02T00:01:23.187+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
=== 快捷键触发截图任务 ===
模式: C
{"level":"INFO","timestamp":"2025-07-02T00:01:23.187+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
=== 收到截图任务请求 ===
任务类型: screenshot_C, 模式: C, 描述: 快捷键C模式截图
{"level":"INFO","timestamp":"2025-07-02T00:01:23.188+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
任务管理器可用，提交任务到队列
{"level":"INFO","timestamp":"2025-07-02T00:01:23.188+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-02T00:01:23.188+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025/07/02 00:01:23 Today's date: 2025-07-02
2025/07/02 00:01:23 Request JSON data: {"date":"2025-07-02","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}     
2025/07/02 00:01:23 -------------------------
2025/07/02 00:01:23 -------------------------
2025/07/02 00:01:23 读取响应respBody: {"errCode":"0","errMsg":"查询成功","data":[],"total":0,"queryCondition":{"site_id":"YL-BJ-TZ-001","device_no":"00155ded4a58","registration_date":"2025-07-02"}}
[开发模式] 候检者列表为空，自动生成测试患者: test-11200
{"level":"INFO","timestamp":"2025-07-02T00:01:23.809+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-02T00:01:23.809+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-02T00:01:23.809+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键C模式截图 任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
TRA | No listeners for event 'showToastNotification'
截图任务已提交到任务管理器: 快捷键C模式截图
{"level":"INFO","timestamp":"2025-07-02T00:01:23.810+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-02T00:02:53.763+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
