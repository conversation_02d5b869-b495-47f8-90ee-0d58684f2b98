{"logId": "ec699e69-7f36-4d58-ac6a-597f4be023d7", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 1, "label": "image", "score": 0.5452666282653809, "coordinate": [209.8216094970703, 12.512514114379883, 768, 1466.9698486328125]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[87, 11], [336, 11], [336, 34], [87, 34]], [[345, 11], [390, 11], [390, 32], [345, 32]], [[417, 9], [489, 9], [489, 34], [417, 34]], [[530, 13], [598, 13], [598, 32], [530, 32]], [[615, 13], [655, 13], [655, 32], [615, 32]], [[17, 23], [41, 23], [41, 48], [17, 48]], [[17, 86], [42, 86], [42, 111], [17, 111]], [[225, 120], [384, 120], [384, 150], [225, 150]], [[13, 136], [46, 136], [46, 168], [13, 168]], [[227, 177], [310, 177], [310, 204], [227, 204]], [[480, 179], [766, 179], [766, 202], [480, 202]], [[18, 197], [37, 197], [37, 214], [18, 214]], [[227, 213], [310, 213], [310, 240], [227, 240]], [[382, 214], [498, 214], [498, 234], [382, 234]], [[530, 213], [613, 213], [613, 238], [530, 238]], [[676, 214], [764, 214], [764, 234], [676, 234]], [[20, 250], [37, 250], [37, 268], [20, 268]], [[227, 247], [310, 247], [310, 273], [227, 273]], [[465, 245], [506, 245], [506, 275], [465, 275]], [[530, 247], [613, 247], [613, 273], [530, 273]], [[10, 309], [34, 297], [44, 318], [21, 330]], [[17, 358], [42, 358], [42, 379], [17, 379]], [[17, 409], [42, 409], [42, 433], [17, 433]], [[13, 459], [44, 459], [44, 492], [13, 492]], [[13, 513], [44, 513], [44, 545], [13, 545]], [[15, 570], [42, 570], [42, 597], [15, 597]], [[295, 585], [423, 585], [423, 604], [295, 604]], [[449, 583], [583, 583], [583, 608], [449, 608]], [[615, 583], [740, 583], [740, 608], [615, 608]], [[20, 629], [37, 629], [37, 647], [20, 647]], [[17, 679], [42, 679], [42, 704], [17, 704]], [[238, 670], [334, 670], [334, 695], [238, 695]], [[15, 733], [42, 733], [42, 760], [15, 760]], [[458, 729], [583, 729], [583, 760], [458, 760]], [[15, 786], [42, 786], [42, 813], [15, 813]], [[11, 837], [46, 837], [46, 871], [11, 871]], [[308, 854], [382, 854], [382, 887], [308, 887]], [[482, 854], [556, 854], [556, 887], [482, 887]], [[655, 853], [729, 853], [729, 887], [655, 887]], [[275, 872], [290, 872], [290, 892], [275, 892]], [[443, 871], [469, 871], [469, 896], [443, 896]], [[617, 871], [642, 871], [642, 897], [617, 897]], [[13, 888], [46, 888], [46, 924], [13, 924]], [[329, 885], [360, 885], [360, 910], [329, 910]], [[500, 883], [535, 883], [535, 913], [500, 913]], [[672, 881], [711, 881], [711, 913], [672, 913]], [[11, 942], [46, 942], [46, 978], [11, 978]], [[11, 999], [46, 999], [46, 1030], [11, 1030]], [[489, 1040], [554, 1040], [554, 1117], [489, 1117]], [[17, 1064], [38, 1059], [42, 1077], [22, 1082]], [[414, 1149], [630, 1149], [630, 1174], [414, 1174]], [[449, 1283], [593, 1283], [593, 1308], [449, 1308]], [[397, 1319], [646, 1319], [646, 1342], [397, 1342]], [[425, 1351], [617, 1351], [617, 1375], [425, 1375]], [[450, 1380], [593, 1380], [593, 1405], [450, 1405]], [[449, 1410], [593, 1410], [593, 1435], [449, 1435]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["尺寸：Samsung Galaxy …", "412", "× 914", "10… ▼", "无...", "51", "O", "磁感分析操作台", "Q", "检测站点：", "北京市通州区潞城镇城社区卫生服务", "Q", "站点编号：", "YL-BJ-TZ-001", "设备编号：", "00155ded", "Q", "今日报到：", "0人", "今日检测:", "🌸", "中", "中", "51", "51", "51", "应用程序已启动", "测试前端Toast", "测试后端通知", "Q", ",", "当前受检者", "133", "暂无受检者", "13", "ü", "待检测", "已完成", "待分析", "1", "☑", "ı", "ü", "(0)", "(0)", "(0)", "凶", "🚀", "自", "?", "暂无待检测的候检者", "Toast调试信息", "Wails Runtime状态: available", "事件监听器状态：active", "当前Toast数量：0", "最大Toast数量:3"], "rec_scores": [0.8856245279312134, 0.999903678894043, 0.9592230916023254, 0.7070773243904114, 0.7809499502182007, 0.9994118213653564, 0.43476563692092896, 0.9996126890182495, 0.29507511854171753, 0.9561117887496948, 0.9730056524276733, 0.9787058234214783, 0.9815399050712585, 0.9947431683540344, 0.9918606877326965, 0.9929540157318115, 0.9948177933692932, 0.9639095067977905, 0.9858094453811646, 0.9401857256889343, 0.24194329977035522, 0.8828482031822205, 0.9614054560661316, 0.9914751052856445, 0.9920990467071533, 0.9977153539657593, 0.999899685382843, 0.9972163438796997, 0.9989232420921326, 0.9927694797515869, 0.16812235116958618, 0.9998493194580078, 0.5843570232391357, 0.9997931718826294, 0.6414237022399902, 0.10921308398246765, 0.9993316531181335, 0.9996479153633118, 0.9997561573982239, 0.20584243535995483, 0.46425384283065796, 0.5770863890647888, 0.18654578924179077, 0.9276222586631775, 0.9762107729911804, 0.9582128524780273, 0.11987447738647461, 0.19895656406879425, 0.7434195280075073, 0.11419402062892914, 0.9993027448654175, 0.9994940161705017, 0.9498792886734009, 0.9869338870048523, 0.9713556170463562, 0.9594814777374268], "rec_polys": [[[87, 11], [336, 11], [336, 34], [87, 34]], [[345, 11], [390, 11], [390, 32], [345, 32]], [[417, 9], [489, 9], [489, 34], [417, 34]], [[530, 13], [598, 13], [598, 32], [530, 32]], [[615, 13], [655, 13], [655, 32], [615, 32]], [[17, 23], [41, 23], [41, 48], [17, 48]], [[17, 86], [42, 86], [42, 111], [17, 111]], [[225, 120], [384, 120], [384, 150], [225, 150]], [[13, 136], [46, 136], [46, 168], [13, 168]], [[227, 177], [310, 177], [310, 204], [227, 204]], [[480, 179], [766, 179], [766, 202], [480, 202]], [[18, 197], [37, 197], [37, 214], [18, 214]], [[227, 213], [310, 213], [310, 240], [227, 240]], [[382, 214], [498, 214], [498, 234], [382, 234]], [[530, 213], [613, 213], [613, 238], [530, 238]], [[676, 214], [764, 214], [764, 234], [676, 234]], [[20, 250], [37, 250], [37, 268], [20, 268]], [[227, 247], [310, 247], [310, 273], [227, 273]], [[465, 245], [506, 245], [506, 275], [465, 275]], [[530, 247], [613, 247], [613, 273], [530, 273]], [[10, 309], [34, 297], [44, 318], [21, 330]], [[17, 358], [42, 358], [42, 379], [17, 379]], [[17, 409], [42, 409], [42, 433], [17, 433]], [[13, 459], [44, 459], [44, 492], [13, 492]], [[13, 513], [44, 513], [44, 545], [13, 545]], [[15, 570], [42, 570], [42, 597], [15, 597]], [[295, 585], [423, 585], [423, 604], [295, 604]], [[449, 583], [583, 583], [583, 608], [449, 608]], [[615, 583], [740, 583], [740, 608], [615, 608]], [[20, 629], [37, 629], [37, 647], [20, 647]], [[17, 679], [42, 679], [42, 704], [17, 704]], [[238, 670], [334, 670], [334, 695], [238, 695]], [[15, 733], [42, 733], [42, 760], [15, 760]], [[458, 729], [583, 729], [583, 760], [458, 760]], [[15, 786], [42, 786], [42, 813], [15, 813]], [[11, 837], [46, 837], [46, 871], [11, 871]], [[308, 854], [382, 854], [382, 887], [308, 887]], [[482, 854], [556, 854], [556, 887], [482, 887]], [[655, 853], [729, 853], [729, 887], [655, 887]], [[275, 872], [290, 872], [290, 892], [275, 892]], [[443, 871], [469, 871], [469, 896], [443, 896]], [[617, 871], [642, 871], [642, 897], [617, 897]], [[13, 888], [46, 888], [46, 924], [13, 924]], [[329, 885], [360, 885], [360, 910], [329, 910]], [[500, 883], [535, 883], [535, 913], [500, 913]], [[672, 881], [711, 881], [711, 913], [672, 913]], [[11, 942], [46, 942], [46, 978], [11, 978]], [[11, 999], [46, 999], [46, 1030], [11, 1030]], [[489, 1040], [554, 1040], [554, 1117], [489, 1117]], [[17, 1064], [38, 1059], [42, 1077], [22, 1082]], [[414, 1149], [630, 1149], [630, 1174], [414, 1174]], [[449, 1283], [593, 1283], [593, 1308], [449, 1308]], [[397, 1319], [646, 1319], [646, 1342], [397, 1342]], [[425, 1351], [617, 1351], [617, 1375], [425, 1375]], [[450, 1380], [593, 1380], [593, 1405], [450, 1405]], [[449, 1410], [593, 1410], [593, 1435], [449, 1435]]], "rec_boxes": [[87, 11, 336, 34], [345, 11, 390, 32], [417, 9, 489, 34], [530, 13, 598, 32], [615, 13, 655, 32], [17, 23, 41, 48], [17, 86, 42, 111], [225, 120, 384, 150], [13, 136, 46, 168], [227, 177, 310, 204], [480, 179, 766, 202], [18, 197, 37, 214], [227, 213, 310, 240], [382, 214, 498, 234], [530, 213, 613, 238], [676, 214, 764, 234], [20, 250, 37, 268], [227, 247, 310, 273], [465, 245, 506, 275], [530, 247, 613, 273], [10, 297, 44, 330], [17, 358, 42, 379], [17, 409, 42, 433], [13, 459, 44, 492], [13, 513, 44, 545], [15, 570, 42, 597], [295, 585, 423, 604], [449, 583, 583, 608], [615, 583, 740, 608], [20, 629, 37, 647], [17, 679, 42, 704], [238, 670, 334, 695], [15, 733, 42, 760], [458, 729, 583, 760], [15, 786, 42, 813], [11, 837, 46, 871], [308, 854, 382, 887], [482, 854, 556, 887], [655, 853, 729, 887], [275, 872, 290, 892], [443, 871, 469, 896], [617, 871, 642, 897], [13, 888, 46, 924], [329, 885, 360, 910], [500, 883, 535, 913], [672, 881, 711, 913], [11, 942, 46, 978], [11, 999, 46, 1030], [489, 1040, 554, 1117], [17, 1059, 42, 1082], [414, 1149, 630, 1174], [449, 1283, 593, 1308], [397, 1319, 646, 1342], [425, 1351, 617, 1375], [450, 1380, 593, 1405], [449, 1410, 593, 1435]]}, "table_res_list": []}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//ec699e69-7f36-4d58-ac6a-597f4be023d7/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T14%3A46%3A34Z%2F-1%2F%2F33afb053e21d19efbb7fb21fa58dcf1a6fa9537201cd80d7f17dd6305fba7623", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//ec699e69-7f36-4d58-ac6a-597f4be023d7/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T14%3A46%3A34Z%2F-1%2F%2F6faaeb5a89392f2e1099f99c05a4fbfd2daa2e41b22fcc721c50dbc4d8dbb9ce"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//ec699e69-7f36-4d58-ac6a-597f4be023d7/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T14%3A46%3A34Z%2F-1%2F%2F553a35d6405ab32f0b319983b5b33038c71419af6d2497f7c8e53aa84fbdb06b"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}