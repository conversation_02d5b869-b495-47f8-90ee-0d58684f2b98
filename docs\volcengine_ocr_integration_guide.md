# 火山引擎OCR集成指南

## 概述

本文档介绍了如何在MagneticOperator应用中集成和使用火山引擎OCR服务。火山引擎OCR提供了高精度的文字识别能力，支持多种语言和复杂场景。

## 配置说明

### 1. 配置文件结构

在 `config/development.json` 中添加火山引擎OCR配置：

```json
{
  "APIKeys": {
    "VolcEngine_ocr": {
      "api_url": "https://imagex.volcengineapi.com",
      "AccessKeyID": "您的AccessKeyID",
      "SecretAccessKey": "您的SecretAccessKey"
    }
  }
}
```

### 2. 配置参数说明

- `api_url`: 火山引擎OCR API的基础URL
- `AccessKeyID`: 火山引擎访问密钥ID
- `SecretAccessKey`: 火山引擎访问密钥

## 功能特性

### 1. 自动切换机制

应用会自动检测配置：
- 如果配置了 `VolcEngine_ocr`，优先使用火山引擎OCR
- 如果未配置火山引擎OCR，回退到原有的百度OCR

### 2. 统一接口

火山引擎OCR客户端实现了与原有OCR服务相同的接口，确保无缝集成：
- 相同的输入参数格式
- 相同的返回结果结构
- 相同的错误处理机制

### 3. 签名认证

火山引擎OCR使用HMAC-SHA256签名认证：
- 自动生成时间戳
- 构建规范请求字符串
- 计算签名并添加到请求头

## 技术实现

### 1. 核心组件

#### VolcEngineOCRClient
```go
type VolcEngineOCRClient struct {
    config *models.VolcEngineOCRConfig
    client *RetryableHTTPClient
}
```

#### 主要方法
- `NewVolcEngineOCRClient()`: 创建客户端实例
- `ProcessImage()`: 处理图片OCR识别
- `generateSignature()`: 生成API签名
- `buildAuthorizationHeader()`: 构建认证头

### 2. API调用流程

1. **读取图片文件**
   - 支持常见图片格式
   - 自动进行Base64编码

2. **构建请求**
   - 使用GetImageOCRV2接口
   - 设置通用识别场景
   - 添加必要的查询参数

3. **签名认证**
   - 生成UTC时间戳
   - 构建规范请求字符串
   - 计算HMAC-SHA256签名

4. **发送请求**
   - 使用重试机制
   - 处理网络异常
   - 解析响应结果

5. **结果处理**
   - 提取识别文本
   - 计算置信度
   - 转换为统一格式

### 3. 响应格式转换

火山引擎OCR返回的响应格式：
```json
{
  "ResponseMetadata": {
    "RequestId": "...",
    "Action": "GetImageOCRV2",
    "Version": "2018-08-01"
  },
  "Result": {
    "Scene": "general",
    "GeneralResult": [
      {
        "Content": "识别的文本",
        "Location": [[x1, y1], [x2, y2]],
        "Confidence": "0.99"
      }
    ]
  }
}
```

转换为应用统一格式：
```go
type OCRResult struct {
    ImagePath     string
    OrganName     string
    Confidence    float64
    KeyValuePairs map[string]string
    RawResponse   json.RawMessage
}
```

## 使用方法

### 1. 配置火山引擎OCR

1. 在火山引擎控制台创建应用
2. 获取AccessKeyID和SecretAccessKey
3. 在配置文件中添加相关配置
4. 重启应用

### 2. 验证配置

应用启动时会显示使用的OCR服务：
```
[OCR处理] 使用火山引擎OCR处理图片: /path/to/image.png
```

### 3. 监控日志

火山引擎OCR会输出详细的处理日志：
- 识别完成状态
- 置信度信息
- 器官名称提取
- 文本数量统计

## 错误处理

### 1. 常见错误

- **配置错误**: 检查AccessKeyID和SecretAccessKey是否正确
- **网络错误**: 检查网络连接和API地址
- **签名错误**: 检查时间同步和签名算法
- **响应解析错误**: 检查API版本和响应格式

### 2. 故障转移

如果火山引擎OCR出现问题，应用会：
1. 记录错误信息
2. 返回详细的错误描述
3. 不会自动回退到百度OCR（需要手动修改配置）

## 性能优化

### 1. 重试机制

- 使用指数退避重试
- 最大重试次数：5次
- 基础延迟：3秒
- 最大延迟：60秒

### 2. 连接复用

- 使用长连接
- 连接池管理
- 超时控制

### 3. 内存管理

- 及时释放响应对象
- 避免内存泄漏
- 优化图片编码

## 安全考虑

### 1. 密钥管理

- 密钥存储在配置文件中
- 避免在代码中硬编码
- 定期轮换访问密钥

### 2. 网络安全

- 使用HTTPS协议
- 验证服务器证书
- 防止中间人攻击

### 3. 数据安全

- 图片数据仅用于OCR识别
- 不存储敏感信息
- 遵循数据保护法规

## 故障排除

### 1. 调试步骤

1. 检查配置文件格式
2. 验证网络连接
3. 查看应用日志
4. 测试API调用
5. 检查响应格式

### 2. 常用调试命令

```bash
# 检查配置
cat config/development.json

# 查看日志
tail -f logs/app_*.log

# 测试网络连接
ping imagex.volcengineapi.com
```

## 更新日志

### v1.0.0 (2025-01-XX)
- 初始版本
- 支持火山引擎OCR集成
- 实现自动切换机制
- 添加签名认证
- 统一接口格式

## 参考资料

- [火山引擎OCR官方文档](https://www.volcengine.com/docs/508/79995)
- [GetImageOCRV2接口文档](https://www.volcengine.com/docs/508/192412)
- [火山引擎认证机制](https://www.volcengine.com/docs/6346/66405)