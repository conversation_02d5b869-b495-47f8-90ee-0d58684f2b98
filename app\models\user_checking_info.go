package models

import "time"

// UserMedicalHistory 用户病史信息
type UserMedicalHistory struct {
	Gaoxueya       bool   `json:"gaoxueya"`       // 高血压
	Tangniaobing   bool   `json:"tangniaobing"`   // 糖尿病
	Zhongfeng      bool   `json:"zhongfeng"`      // 中风
	Aizheng        bool   `json:"aizheng"`        // 癌症
	Huxitongxibing bool   `json:"huxitongxibing"` // 呼吸系统疾病
	Details        string `json:"details"`        // 详细信息
}

// BiochemicalAnalysis B02生化分析结果
type BiochemicalAnalysis struct {
	DValue     string `json:"d_value"`     // D值
	Text       string `json:"text"`       // 文本内容
	FinalColor string `json:"final_color"` // 最终颜色
}

// PathologyAnalysis C03病理分析结果
type PathologyAnalysis struct {
	DValue string `json:"d_value"` // D值
	Text   string `json:"text"`   // 文本内容
}

// ScreenshotTask 单个截图任务数据结构
type ScreenshotTask struct {
	TaskID                 string                 `json:"taskId"`                 // 任务唯一ID
	TaskIndex              int                    `json:"taskIndex"`              // 任务序号(1-20)
	Mode                   string                 `json:"mode"`                   // 模式：B02或C03
	OrganName              string                 `json:"organName"`              // 器官名称
	InputImage             string                 `json:"inputImage"`             // 输入图片路径
	RecTexts               []string               `json:"recTexts"`               // OCR识别文本
	RecPolys               []interface{}          `json:"recPolys"`               // OCR识别多边形
	BiochemicalAnalysis    []BiochemicalAnalysis  `json:"biochemicalAnalysis"`    // B02生化分析结果
	PathologyAnalysis      []PathologyAnalysis    `json:"pathologyAnalysis"`      // C03病理分析结果
	CompletedTime          string                 `json:"completedTime"`          // 完成时间
	Status                 string                 `json:"status"`                 // 任务状态：pending/processing/completed/failed
	CreatedTime            string                 `json:"createdTime"`            // 创建时间
}

// CurrentUserCheckingInfo 用户检测信息汇总结构体
// 用于收集20个截图任务的OCR完整数据，最终传递给扣子API进行大模型分析
type CurrentUserCheckingInfo struct {
	// 基本用户信息
	UserID            string              `json:"UserID"`            // 用户唯一ID
	UserName          string              `json:"UserName"`          // 当前用户名
	UserSex           string              `json:"UserSex"`           // 用户性别
	UserAge           string              `json:"UserAge"`           // 用户年龄
	UserMedicalHistory UserMedicalHistory `json:"UserMedicalHistory"` // 用户病史信息
	SiteID            string              `json:"SiteID"`            // 站点ID
	SiteName          string              `json:"SiteName"`          // 检测站点名
	RegistrationNo    string              `json:"RegistrationNo"`    // 用户报到号
	CheckingTime      string              `json:"CheckingTime"`      // 检测开始时间
	CompletionTime    string              `json:"CompletionTime"`    // 20个任务完成时间

	// 截图任务数据
	TotalTasks      int               `json:"TotalTasks"`      // 总任务数（固定为20）
	ScreenshotTasks []ScreenshotTask  `json:"ScreenshotTasks"` // 20个截图任务
	CompletedTasks  int               `json:"CompletedTasks"`  // 已完成任务数

	// 汇总统计
	TotalScreenshots int            `json:"TotalScreenshots"` // 总截图数（应为20）
	ProcessedOCRs    int            `json:"ProcessedOCRs"`    // 已处理的OCR数量
	DetectedOrgans   map[string]int `json:"DetectedOrgans"`   // 检测到的器官统计 {器官名: 出现次数}
}


// NewCurrentUserCheckingInfo 创建新的用户检测信息实例
func NewCurrentUserCheckingInfo(userID, userName, siteName, registrationNo string) *CurrentUserCheckingInfo {
	return &CurrentUserCheckingInfo{
		UserID:         userID,
		UserName:       userName,
		UserSex:        "",
		UserAge:        "",
		UserMedicalHistory: UserMedicalHistory{
			Gaoxueya:       false,
			Tangniaobing:   false,
			Zhongfeng:      false,
			Aizheng:        false,
			Huxitongxibing: false,
			Details:        "",
		},
		SiteID:         "",
		SiteName:       siteName,
		RegistrationNo: registrationNo,
		CheckingTime:   time.Now().Format("2006-01-02 15:04:05"),
		CompletionTime: "",
		TotalTasks:      20,
		ScreenshotTasks: make([]ScreenshotTask, 0, 20),
		CompletedTasks:  0,
		TotalScreenshots: 0,
		ProcessedOCRs:   0,
		DetectedOrgans:  make(map[string]int),
	}
}

// AddScreenshotTask 添加截图任务
func (info *CurrentUserCheckingInfo) AddScreenshotTask(task ScreenshotTask) {
	info.ScreenshotTasks = append(info.ScreenshotTasks, task)

	// 更新统计信息
	if task.Status == "completed" {
		info.CompletedTasks++
		info.TotalScreenshots++
		info.ProcessedOCRs++
		if task.OrganName != "" && task.OrganName != "未知器官" {
			info.DetectedOrgans[task.OrganName]++
		}
	}
}

// UpdateTaskStatus 更新任务状态
func (info *CurrentUserCheckingInfo) UpdateTaskStatus(taskID string, status string) bool {
	for i := range info.ScreenshotTasks {
		if info.ScreenshotTasks[i].TaskID == taskID {
			oldStatus := info.ScreenshotTasks[i].Status
			info.ScreenshotTasks[i].Status = status
			
			// 如果从非完成状态变为完成状态，更新统计
			if oldStatus != "completed" && status == "completed" {
				info.CompletedTasks++
				info.TotalScreenshots++
				info.ProcessedOCRs++
				if info.ScreenshotTasks[i].OrganName != "" && info.ScreenshotTasks[i].OrganName != "未知器官" {
					info.DetectedOrgans[info.ScreenshotTasks[i].OrganName]++
				}
				info.ScreenshotTasks[i].CompletedTime = time.Now().Format("2006-01-02 15:04:05")
			}
			return true
		}
	}
	return false
}

// GetNextTaskIndex 获取下一个可用的任务序号
func (info *CurrentUserCheckingInfo) GetNextTaskIndex() int {
	return len(info.ScreenshotTasks) + 1
}

// GetTaskByID 根据任务ID获取任务
func (info *CurrentUserCheckingInfo) GetTaskByID(taskID string) *ScreenshotTask {
	for i := range info.ScreenshotTasks {
		if info.ScreenshotTasks[i].TaskID == taskID {
			return &info.ScreenshotTasks[i]
		}
	}
	return nil
}

// GetTasksByMode 根据模式获取任务列表
func (info *CurrentUserCheckingInfo) GetTasksByMode(mode string) []ScreenshotTask {
	var tasks []ScreenshotTask
	for _, task := range info.ScreenshotTasks {
		if task.Mode == mode {
			tasks = append(tasks, task)
		}
	}
	return tasks
}

// GetTasksByStatus 根据状态获取任务列表
func (info *CurrentUserCheckingInfo) GetTasksByStatus(status string) []ScreenshotTask {
	var tasks []ScreenshotTask
	for _, task := range info.ScreenshotTasks {
		if task.Status == status {
			tasks = append(tasks, task)
		}
	}
	return tasks
}

// IsCompleted 检查是否完成所有20个截图任务
func (info *CurrentUserCheckingInfo) IsCompleted() bool {
	return info.CompletedTasks >= 20
}

// GetProgress 获取任务进度
func (info *CurrentUserCheckingInfo) GetProgress() (completed, total int, percentage float64) {
	completed = info.CompletedTasks
	total = info.TotalTasks
	if total > 0 {
		percentage = float64(completed) / float64(total) * 100
	}
	return
}

// MarkCompleted 标记检测完成
func (info *CurrentUserCheckingInfo) MarkCompleted() {
	info.CompletionTime = time.Now().Format("2006-01-02 15:04:05")
}
