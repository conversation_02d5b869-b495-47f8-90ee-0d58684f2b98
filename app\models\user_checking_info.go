package models

import "time"

// UserMedicalHistory 用户病史信息
type UserMedicalHistory struct {
	Gao<PERSON><PERSON>ya       bool   `json:"gaoxueya"`       // 高血压
	Tangniaobing   bool   `json:"tangniaobing"`   // 糖尿病
	Zhongfeng      bool   `json:"zhongfeng"`      // 中风
	Aizheng        bool   `json:"aizheng"`        // 癌症
	Huxitongxibing bool   `json:"huxitongxibing"` // 呼吸系统疾病
	Details        string `json:"details"`        // 详细信息
}

// BiochemicalAnalysis B02生化分析结果
type BiochemicalAnalysis struct {
	DValue     string `json:"d_value"`     // D值
	Text       string `json:"text"`       // 文本内容
	FinalColor string `json:"final_color"` // 最终颜色
}

// PathologyAnalysis C03病理分析结果
type PathologyAnalysis struct {
	DValue string `json:"d_value"` // D值
	Text   string `json:"text"`   // 文本内容
}

// RoundData 单轮检测数据结构体
type RoundData struct {
	OrganName              string                 `json:"organName"`              // 器官名称
	B02InputImage          string                 `json:"B02_inputImage"`         // B02输入图片路径
	B02RecTexts            []string               `json:"B02_rec_texts"`          // B02识别文本数组
	B02RecPolys            []interface{}          `json:"B02_rec_polys"`          // B02识别多边形数组
	B02BiochemicalAnalysis []BiochemicalAnalysis  `json:"B02_BiochemicalAnalysis"` // B02生化分析结果
	C03InputImage          string                 `json:"C03_inputImage"`         // C03输入图片路径
	C03RecTexts            []string               `json:"C03_rec_texts"`          // C03识别文本数组
	C03RecPolys            []interface{}          `json:"C03_rec_polys"`          // C03识别多边形数组
	C03PathologyAnalysis   []PathologyAnalysis    `json:"C03_PathologyAnalysis"`  // C03病理分析结果
}

// CurrentUserCheckingInfo 用户检测信息汇总结构体
// 用于收集10轮截图OCR的完整数据，最终传递给扣子API进行大模型分析
type CurrentUserCheckingInfo struct {
	// 基本用户信息
	UserID            string              `json:"UserID"`            // 用户唯一ID
	UserName          string              `json:"UserName"`          // 当前用户名
	UserSex           string              `json:"UserSex"`           // 用户性别
	UserAge           string              `json:"UserAge"`           // 用户年龄
	UserMedicalHistory UserMedicalHistory `json:"UserMedicalHistory"` // 用户病史信息
	SiteID            string              `json:"SiteID"`            // 站点ID
	SiteName          string              `json:"SiteName"`          // 检测站点名
	RegistrationNo    string              `json:"RegistrationNo"`    // 用户报到号
	CheckingTime      string              `json:"CheckingTime"`      // 检测开始时间
	CompletionTime    string              `json:"CompletionTime"`    // 10轮完成时间

	// 检测轮次数据
	TotalRounds     int         `json:"TotalRounds"`     // 总轮次数（固定为10）
	CompletedRounds int         `json:"CompletedRounds"` // 已完成轮次数
	RoundsData      []RoundData `json:"RoundsData"`      // 每轮的详细数据

	// 汇总统计
	TotalScreenshots int            `json:"TotalScreenshots"` // 总截图数（应为20）
	ProcessedOCRs    int            `json:"ProcessedOCRs"`    // 已处理的OCR数量
	DetectedOrgans   map[string]int `json:"DetectedOrgans"`   // 检测到的器官统计 {器官名: 出现次数}
}


// NewCurrentUserCheckingInfo 创建新的用户检测信息实例
func NewCurrentUserCheckingInfo(userID, userName, siteName, registrationNo string) *CurrentUserCheckingInfo {
	return &CurrentUserCheckingInfo{
		UserID:         userID,
		UserName:       userName,
		UserSex:        "",
		UserAge:        "",
		UserMedicalHistory: UserMedicalHistory{
			Gaoxueya:       false,
			Tangniaobing:   false,
			Zhongfeng:      false,
			Aizheng:        false,
			Huxitongxibing: false,
			Details:        "",
		},
		SiteID:         "",
		SiteName:       siteName,
		RegistrationNo: registrationNo,
		CheckingTime:   time.Now().Format("2006-01-02 15:04:05"),
		CompletionTime: "",
		TotalRounds:    10,
		CompletedRounds: 0,
		RoundsData:     make([]RoundData, 0, 10),
		TotalScreenshots: 0,
		ProcessedOCRs:   0,
		DetectedOrgans:  make(map[string]int),
	}
}

// AddRoundData 添加轮次数据
func (info *CurrentUserCheckingInfo) AddRoundData(roundData RoundData) {
	info.RoundsData = append(info.RoundsData, roundData)
	info.CompletedRounds = len(info.RoundsData)

	// 更新统计信息
	if roundData.B02InputImage != "" {
		info.TotalScreenshots++
		info.ProcessedOCRs++
		if roundData.OrganName != "" && roundData.OrganName != "未知器官" {
			info.DetectedOrgans[roundData.OrganName]++
		}
	}

	if roundData.C03InputImage != "" {
		info.TotalScreenshots++
		info.ProcessedOCRs++
		if roundData.OrganName != "" && roundData.OrganName != "未知器官" {
			info.DetectedOrgans[roundData.OrganName]++
		}
	}
}

// IsCompleted 检查是否完成所有10轮检测
func (info *CurrentUserCheckingInfo) IsCompleted() bool {
	return info.CompletedRounds >= 10
}

// MarkCompleted 标记检测完成
func (info *CurrentUserCheckingInfo) MarkCompleted() {
	info.CompletionTime = time.Now().Format("2006-01-02 15:04:05")
}
