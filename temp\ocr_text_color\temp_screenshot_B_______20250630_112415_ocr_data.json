{"logId": "707a0840-eaa3-4398-971c-d46fdc448535", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 8, "label": "table", "score": 0.9860857725143433, "coordinate": [13.200302124023438, 74.84366607666016, 768, 1712.185546875]}, {"cls_id": 9, "label": "table_title", "score": 0.6657945513725281, "coordinate": [19.719566345214844, 27.22345542907715, 520.631591796875, 62.8687744140625]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[18, 27], [521, 27], [521, 63], [18, 63]], [[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 77], [338, 77], [338, 102], [192, 102]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[192, 152], [388, 152], [388, 177], [192, 177]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[192, 179], [321, 179], [321, 204], [192, 204]], [[98, 204], [162, 204], [162, 229], [98, 229]], [[192, 206], [342, 206], [342, 229], [192, 229]], [[98, 231], [161, 231], [161, 256], [98, 256]], [[192, 231], [303, 231], [303, 256], [192, 256]], [[98, 256], [155, 256], [155, 281], [98, 281]], [[194, 259], [474, 259], [474, 277], [194, 277]], [[98, 281], [155, 281], [155, 306], [98, 306]], [[194, 282], [393, 282], [393, 306], [194, 306]], [[98, 306], [155, 306], [155, 332], [98, 332]], [[196, 311], [402, 311], [402, 329], [196, 329]], [[98, 332], [155, 332], [155, 358], [98, 358]], [[190, 332], [275, 332], [275, 358], [190, 358]], [[98, 358], [155, 358], [155, 383], [98, 383]], [[190, 356], [229, 356], [229, 384], [190, 384]], [[98, 383], [157, 383], [157, 409], [98, 409]], [[192, 383], [342, 383], [342, 408], [192, 408]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[192, 409], [447, 409], [447, 433], [192, 433]], [[98, 433], [157, 433], [157, 459], [98, 459]], [[192, 434], [443, 434], [443, 459], [192, 459]], [[98, 459], [157, 459], [157, 484], [98, 484]], [[190, 459], [260, 459], [260, 486], [190, 486]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[192, 486], [733, 486], [733, 509], [192, 509]], [[98, 509], [157, 509], [157, 536], [98, 536]], [[192, 511], [447, 511], [447, 534], [192, 534]], [[98, 536], [157, 536], [157, 561], [98, 561]], [[194, 538], [504, 538], [504, 561], [194, 561]], [[98, 561], [157, 561], [157, 586], [98, 586]], [[192, 563], [524, 563], [524, 586], [192, 586]], [[98, 586], [157, 586], [157, 613], [98, 613]], [[194, 588], [524, 588], [524, 611], [194, 611]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[196, 617], [687, 617], [687, 635], [196, 635]], [[98, 636], [157, 636], [157, 663], [98, 663]], [[192, 640], [606, 640], [606, 663], [192, 663]], [[98, 663], [157, 663], [157, 688], [98, 688]], [[190, 665], [476, 665], [476, 688], [190, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[192, 690], [570, 690], [570, 713], [192, 713]], [[98, 713], [155, 713], [155, 740], [98, 740]], [[192, 715], [305, 715], [305, 740], [192, 740]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[188, 738], [363, 734], [364, 765], [189, 769]], [[98, 765], [157, 765], [157, 792], [98, 792]], [[192, 767], [412, 767], [412, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[188, 788], [260, 788], [260, 820], [188, 820]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [641, 817], [641, 840], [192, 840]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[192, 844], [412, 844], [412, 867], [192, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[190, 865], [384, 867], [384, 892], [190, 890]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[190, 892], [244, 892], [244, 921], [190, 921]], [[98, 917], [157, 917], [157, 944], [98, 944]], [[192, 919], [417, 919], [417, 944], [192, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[190, 944], [279, 944], [279, 969], [190, 969]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[192, 971], [696, 971], [696, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[190, 996], [364, 996], [364, 1019], [190, 1019]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[188, 1019], [244, 1019], [244, 1047], [188, 1047]], [[98, 1044], [157, 1044], [157, 1071], [98, 1071]], [[192, 1046], [360, 1046], [360, 1071], [192, 1071]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[188, 1071], [311, 1067], [312, 1097], [189, 1101]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[191, 1094], [314, 1098], [313, 1125], [190, 1121]], [[98, 1121], [157, 1121], [157, 1148], [98, 1148]], [[192, 1123], [330, 1123], [330, 1148], [192, 1148]], [[98, 1148], [157, 1148], [157, 1174], [98, 1174]], [[192, 1149], [476, 1149], [476, 1173], [192, 1173]], [[98, 1173], [155, 1173], [155, 1199], [98, 1199]], [[191, 1171], [347, 1175], [347, 1200], [190, 1196]], [[98, 1198], [155, 1198], [155, 1224], [98, 1224]], [[190, 1199], [329, 1199], [329, 1224], [190, 1224]], [[98, 1223], [157, 1223], [157, 1249], [98, 1249]], [[186, 1223], [263, 1218], [265, 1251], [188, 1255]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[190, 1248], [508, 1250], [508, 1275], [190, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[190, 1274], [284, 1274], [284, 1303], [190, 1303]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[188, 1298], [286, 1298], [286, 1328], [188, 1328]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[192, 1326], [295, 1326], [295, 1351], [192, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [478, 1353], [478, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[188, 1378], [291, 1374], [292, 1401], [189, 1405]], [[98, 1401], [157, 1401], [157, 1428], [98, 1428]], [[190, 1403], [277, 1403], [277, 1428], [190, 1428]], [[100, 1428], [155, 1428], [155, 1453], [100, 1453]], [[192, 1430], [460, 1430], [460, 1453], [192, 1453]], [[98, 1453], [155, 1453], [155, 1478], [98, 1478]], [[190, 1455], [591, 1455], [591, 1478], [190, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[190, 1480], [297, 1480], [297, 1505], [190, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[192, 1505], [367, 1505], [367, 1530], [192, 1530]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[194, 1532], [356, 1532], [356, 1555], [194, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[192, 1557], [618, 1557], [618, 1580], [192, 1580]], [[98, 1582], [157, 1582], [157, 1607], [98, 1607]], [[194, 1586], [428, 1586], [428, 1603], [194, 1603]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[194, 1609], [414, 1609], [414, 1632], [194, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[194, 1634], [401, 1634], [401, 1657], [194, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1657], [262, 1657], [262, 1684], [192, 1684]], [[100, 1684], [157, 1684], [157, 1709], [100, 1709]], [[194, 1684], [373, 1684], [373, 1709], [194, 1709]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["按照标准图谱相似度递减列表：", "0.000", "男性膀胱；后视图", "2.554", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.037", "单核细胞MONOCYTES", "0.055", "免疫球蛋白M*", "0.065", "SEROMUCOIDS*", "0.084", "结合珠蛋白", "0.048", "PERIPHERICBLOODLEUCOCYTES", "0.065", "血红蛋白HAEMOGLOBIN", "0.080", "血红血球ERYTHROCYTES", "0.093", "转铁蛋白", "0.097", "锂*", "0.032", "肿瘤标志物CA50*", "0.043", "血清补体SERUMCOMPLEMENT", "0.044", "血清溶菌酵SERUMLYSOZYME", "0.044", "备解素*", "0.044", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.045", "肿瘤标志物MELANOGENE在尿*", "0.045", "血清淀粉酵素SERUMALPHAAMYLASE", "0.046", "游离胆固醇FREEPLASMACHOLESTERIN", "0.046", "胆固醇COMMON PLASMA CHOLESTERIN", "0.049", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.050", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.050", "嗜中性粒细胞STABNEUTROPHILS", "0.051", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.051", "血管紧张素I*", "0.052", "维生素E(生育酚)*", "0.052", "尿肌配URINECREATININE", "0.052", "胰岛素*", "0.052", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.053", "血清蛋白SERUMALBUMEN", "0.053", "17-血浆氧皮质类固醇类", "0.053", "肾素*", "0.053", "血肌酥SERUM CREATININE", "0.053", "醛固酮尿*", "0.053", "血浆非酯化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA", "0.054", "红细胞沉降率（ESR)", "0.054", "糖苷*", "0.054", "血尿素BLOODUREA", "0.054", "血管紧张素I*", "0.055", "甲状腺球蛋白", "0.055", "17-尿中酮类固醇", "0.056", "血浆磷脂PLASMAPHOSPHOTIDES", "0.057", "血细胞比容，全血", "0.057", "抗链球菌溶血素", "0.057", "催乳素*", "0.058", "11 - PLASMA OXYCORTICOSTEROIDS", "0.058", "维生素B2*", "0.058", "维生素B6*", "0.059", "胰高血糖素*", "0.059", "酸性磷酸酵素ACIDPHOSPHATASE", "0.059", "维生素B12*", "0.059", "醛固酮血*", "0.059", "尿中肾上腺素URINEADRENALIN", "0.060", "乳酸脱氢酵素COMMONLACTADEHYDROGENASE", "0.060", "抗利尿激素*", "0.060", "甲状腺素结合球蛋白", "0.060", "血糖BLOOD SUGAR", "0.061", "碱性磷酸酯酵素COMMONALKALINEPHOSPHATASE", "0.062", "嗜酸性粒细胞EOSINOPHILES", "0.062", "尿中尿酸URINEURICACID", "0.062", "血尿酸SERUMURICACID", "0.063", "脂肪酶*", "0.063", "总铁结合力(TIBC)*"], "rec_scores": [0.9965823888778687, 0.9992006421089172, 0.8448677062988281, 0.9987779855728149, 0.9983981251716614, 0.9665411710739136, 0.99988853931427, 0.9974513649940491, 0.9999510645866394, 0.9928655624389648, 0.9999055862426758, 0.9890060424804688, 0.9999068975448608, 0.9965609312057495, 0.9995183944702148, 0.9963439106941223, 0.9996567964553833, 0.9984849095344543, 0.9995536804199219, 0.9940184354782104, 0.9996921420097351, 0.9984321594238281, 0.9995640516281128, 0.9702817797660828, 0.9996942281723022, 0.984752357006073, 0.9996527433395386, 0.9985573291778564, 0.9995285868644714, 0.991339385509491, 0.9994535446166992, 0.9195845127105713, 0.9995214343070984, 0.9955445528030396, 0.9997155070304871, 0.968664288520813, 0.9996341466903687, 0.9982089996337891, 0.9996221661567688, 0.9958582520484924, 0.9996681213378906, 0.9635776877403259, 0.9997016787528992, 0.9957727193832397, 0.9996639490127563, 0.997855544090271, 0.999547004699707, 0.9957587122917175, 0.999665379524231, 0.9948965907096863, 0.9997023344039917, 0.9344069361686707, 0.9997509717941284, 0.9166479110717773, 0.9997024536132812, 0.9582784175872803, 0.9997352361679077, 0.9042901992797852, 0.9997650980949402, 0.9953368902206421, 0.9997808337211609, 0.9966993927955627, 0.9996727705001831, 0.9894955158233643, 0.9997941851615906, 0.9613390564918518, 0.9998162388801575, 0.9397323131561279, 0.9997060894966125, 0.9739959836006165, 0.9997370839118958, 0.992355227470398, 0.9997330904006958, 0.9119753241539001, 0.9997552037239075, 0.9629817008972168, 0.9997202157974243, 0.9939408898353577, 0.9996761083602905, 0.9709552526473999, 0.9997807741165161, 0.9968481063842773, 0.9998112916946411, 0.9886061549186707, 0.9997008442878723, 0.9985840320587158, 0.9997403025627136, 0.9893007874488831, 0.999691367149353, 0.9961222410202026, 0.9997220039367676, 0.9338442087173462, 0.9996737241744995, 0.9465644955635071, 0.9997797012329102, 0.9910664558410645, 0.9996509552001953, 0.994053065776825, 0.9996148347854614, 0.9432416558265686, 0.9996505975723267, 0.9971507787704468, 0.9997652769088745, 0.9702712297439575, 0.9997429847717285, 0.9023411870002747, 0.9995561838150024, 0.9974650144577026, 0.9995156526565552, 0.9922578930854797, 0.9997450709342957, 0.9777355790138245, 0.9995661973953247, 0.9967443943023682, 0.9994962811470032, 0.96152263879776, 0.999686598777771, 0.9885543584823608, 0.9995957612991333, 0.9947459101676941, 0.9996110796928406, 0.9964948296546936, 0.9996069073677063, 0.9990896582603455, 0.9995424151420593, 0.97796630859375, 0.9996079206466675, 0.9191208481788635], "rec_polys": [[[18, 27], [521, 27], [521, 63], [18, 63]], [[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 77], [338, 77], [338, 102], [192, 102]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[192, 152], [388, 152], [388, 177], [192, 177]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[192, 179], [321, 179], [321, 204], [192, 204]], [[98, 204], [162, 204], [162, 229], [98, 229]], [[192, 206], [342, 206], [342, 229], [192, 229]], [[98, 231], [161, 231], [161, 256], [98, 256]], [[192, 231], [303, 231], [303, 256], [192, 256]], [[98, 256], [155, 256], [155, 281], [98, 281]], [[194, 259], [474, 259], [474, 277], [194, 277]], [[98, 281], [155, 281], [155, 306], [98, 306]], [[194, 282], [393, 282], [393, 306], [194, 306]], [[98, 306], [155, 306], [155, 332], [98, 332]], [[196, 311], [402, 311], [402, 329], [196, 329]], [[98, 332], [155, 332], [155, 358], [98, 358]], [[190, 332], [275, 332], [275, 358], [190, 358]], [[98, 358], [155, 358], [155, 383], [98, 383]], [[190, 356], [229, 356], [229, 384], [190, 384]], [[98, 383], [157, 383], [157, 409], [98, 409]], [[192, 383], [342, 383], [342, 408], [192, 408]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[192, 409], [447, 409], [447, 433], [192, 433]], [[98, 433], [157, 433], [157, 459], [98, 459]], [[192, 434], [443, 434], [443, 459], [192, 459]], [[98, 459], [157, 459], [157, 484], [98, 484]], [[190, 459], [260, 459], [260, 486], [190, 486]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[192, 486], [733, 486], [733, 509], [192, 509]], [[98, 509], [157, 509], [157, 536], [98, 536]], [[192, 511], [447, 511], [447, 534], [192, 534]], [[98, 536], [157, 536], [157, 561], [98, 561]], [[194, 538], [504, 538], [504, 561], [194, 561]], [[98, 561], [157, 561], [157, 586], [98, 586]], [[192, 563], [524, 563], [524, 586], [192, 586]], [[98, 586], [157, 586], [157, 613], [98, 613]], [[194, 588], [524, 588], [524, 611], [194, 611]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[196, 617], [687, 617], [687, 635], [196, 635]], [[98, 636], [157, 636], [157, 663], [98, 663]], [[192, 640], [606, 640], [606, 663], [192, 663]], [[98, 663], [157, 663], [157, 688], [98, 688]], [[190, 665], [476, 665], [476, 688], [190, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[192, 690], [570, 690], [570, 713], [192, 713]], [[98, 713], [155, 713], [155, 740], [98, 740]], [[192, 715], [305, 715], [305, 740], [192, 740]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[188, 738], [363, 734], [364, 765], [189, 769]], [[98, 765], [157, 765], [157, 792], [98, 792]], [[192, 767], [412, 767], [412, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[188, 788], [260, 788], [260, 820], [188, 820]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [641, 817], [641, 840], [192, 840]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[192, 844], [412, 844], [412, 867], [192, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[190, 865], [384, 867], [384, 892], [190, 890]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[190, 892], [244, 892], [244, 921], [190, 921]], [[98, 917], [157, 917], [157, 944], [98, 944]], [[192, 919], [417, 919], [417, 944], [192, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[190, 944], [279, 944], [279, 969], [190, 969]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[192, 971], [696, 971], [696, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[190, 996], [364, 996], [364, 1019], [190, 1019]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[188, 1019], [244, 1019], [244, 1047], [188, 1047]], [[98, 1044], [157, 1044], [157, 1071], [98, 1071]], [[192, 1046], [360, 1046], [360, 1071], [192, 1071]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[188, 1071], [311, 1067], [312, 1097], [189, 1101]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[191, 1094], [314, 1098], [313, 1125], [190, 1121]], [[98, 1121], [157, 1121], [157, 1148], [98, 1148]], [[192, 1123], [330, 1123], [330, 1148], [192, 1148]], [[98, 1148], [157, 1148], [157, 1174], [98, 1174]], [[192, 1149], [476, 1149], [476, 1173], [192, 1173]], [[98, 1173], [155, 1173], [155, 1199], [98, 1199]], [[191, 1171], [347, 1175], [347, 1200], [190, 1196]], [[98, 1198], [155, 1198], [155, 1224], [98, 1224]], [[190, 1199], [329, 1199], [329, 1224], [190, 1224]], [[98, 1223], [157, 1223], [157, 1249], [98, 1249]], [[186, 1223], [263, 1218], [265, 1251], [188, 1255]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[190, 1248], [508, 1250], [508, 1275], [190, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[190, 1274], [284, 1274], [284, 1303], [190, 1303]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[188, 1298], [286, 1298], [286, 1328], [188, 1328]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[192, 1326], [295, 1326], [295, 1351], [192, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [478, 1353], [478, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[188, 1378], [291, 1374], [292, 1401], [189, 1405]], [[98, 1401], [157, 1401], [157, 1428], [98, 1428]], [[190, 1403], [277, 1403], [277, 1428], [190, 1428]], [[100, 1428], [155, 1428], [155, 1453], [100, 1453]], [[192, 1430], [460, 1430], [460, 1453], [192, 1453]], [[98, 1453], [155, 1453], [155, 1478], [98, 1478]], [[190, 1455], [591, 1455], [591, 1478], [190, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[190, 1480], [297, 1480], [297, 1505], [190, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[192, 1505], [367, 1505], [367, 1530], [192, 1530]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[194, 1532], [356, 1532], [356, 1555], [194, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[192, 1557], [618, 1557], [618, 1580], [192, 1580]], [[98, 1582], [157, 1582], [157, 1607], [98, 1607]], [[194, 1586], [428, 1586], [428, 1603], [194, 1603]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[194, 1609], [414, 1609], [414, 1632], [194, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[194, 1634], [401, 1634], [401, 1657], [194, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1657], [262, 1657], [262, 1684], [192, 1684]], [[100, 1684], [157, 1684], [157, 1709], [100, 1709]], [[194, 1684], [373, 1684], [373, 1709], [194, 1709]]], "rec_boxes": [[18, 27, 521, 63], [98, 77, 157, 102], [192, 77, 338, 102], [98, 102, 157, 129], [192, 102, 270, 129], [194, 129, 462, 152], [98, 154, 161, 179], [192, 152, 388, 177], [98, 179, 162, 204], [192, 179, 321, 204], [98, 204, 162, 229], [192, 206, 342, 229], [98, 231, 161, 256], [192, 231, 303, 256], [98, 256, 155, 281], [194, 259, 474, 277], [98, 281, 155, 306], [194, 282, 393, 306], [98, 306, 155, 332], [196, 311, 402, 329], [98, 332, 155, 358], [190, 332, 275, 358], [98, 358, 155, 383], [190, 356, 229, 384], [98, 383, 157, 409], [192, 383, 342, 408], [98, 408, 157, 434], [192, 409, 447, 433], [98, 433, 157, 459], [192, 434, 443, 459], [98, 459, 157, 484], [190, 459, 260, 486], [98, 484, 157, 509], [192, 486, 733, 509], [98, 509, 157, 536], [192, 511, 447, 534], [98, 536, 157, 561], [194, 538, 504, 561], [98, 561, 157, 586], [192, 563, 524, 586], [98, 586, 157, 613], [194, 588, 524, 611], [98, 611, 157, 638], [196, 617, 687, 635], [98, 636, 157, 663], [192, 640, 606, 663], [98, 663, 157, 688], [190, 665, 476, 688], [98, 688, 155, 715], [192, 690, 570, 713], [98, 713, 155, 740], [192, 715, 305, 740], [98, 740, 157, 767], [188, 734, 364, 769], [98, 765, 157, 792], [192, 767, 412, 790], [98, 790, 157, 817], [188, 788, 260, 820], [98, 815, 157, 842], [192, 817, 641, 840], [98, 840, 157, 867], [192, 844, 412, 867], [98, 867, 157, 892], [190, 865, 384, 892], [98, 892, 157, 919], [190, 892, 244, 921], [98, 917, 157, 944], [192, 919, 417, 944], [98, 944, 157, 969], [190, 944, 279, 969], [98, 969, 157, 996], [192, 971, 696, 994], [98, 994, 157, 1021], [190, 996, 364, 1019], [98, 1019, 157, 1046], [188, 1019, 244, 1047], [98, 1044, 157, 1071], [192, 1046, 360, 1071], [98, 1071, 157, 1098], [188, 1067, 312, 1101], [98, 1096, 157, 1123], [190, 1094, 314, 1125], [98, 1121, 157, 1148], [192, 1123, 330, 1148], [98, 1148, 157, 1174], [192, 1149, 476, 1173], [98, 1173, 155, 1199], [190, 1171, 347, 1200], [98, 1198, 155, 1224], [190, 1199, 329, 1224], [98, 1223, 157, 1249], [186, 1218, 265, 1255], [98, 1249, 157, 1274], [190, 1248, 508, 1275], [98, 1274, 157, 1301], [190, 1274, 284, 1303], [98, 1300, 157, 1326], [188, 1298, 286, 1328], [98, 1326, 157, 1351], [192, 1326, 295, 1351], [98, 1351, 157, 1376], [192, 1353, 478, 1376], [98, 1376, 157, 1403], [188, 1374, 292, 1405], [98, 1401, 157, 1428], [190, 1403, 277, 1428], [100, 1428, 155, 1453], [192, 1430, 460, 1453], [98, 1453, 155, 1478], [190, 1455, 591, 1478], [98, 1478, 157, 1505], [190, 1480, 297, 1505], [98, 1505, 157, 1530], [192, 1505, 367, 1530], [98, 1530, 157, 1555], [194, 1532, 356, 1555], [98, 1555, 155, 1582], [192, 1557, 618, 1580], [98, 1582, 157, 1607], [194, 1586, 428, 1603], [98, 1607, 157, 1632], [194, 1609, 414, 1632], [100, 1632, 157, 1657], [194, 1634, 401, 1657], [100, 1657, 157, 1684], [192, 1657, 262, 1684], [100, 1684, 157, 1709], [194, 1684, 373, 1709]]}, "table_res_list": [{"cell_box_list": [[97.87898254394531, 77.71490240097046, 169.97251892089844, 128.70756149291992], [193.69606018066406, 76.81317412853241, 767.8902435302734, 104.40817260742188], [42.25689125061035, 103.49239730834961, 69.45258331298828, 128.6985206604004], [69.43128204345703, 103.47687530517578, 97.9770736694336, 128.7402572631836], [192.0, 102.0, 270.0, 129.0], [42.188337326049805, 128.80546951293945, 69.45058822631836, 154.37100219726562], [69.39983749389648, 128.81209182739258, 97.9410171508789, 154.39922332763672], [97.84088134765625, 128.7509307861328, 170.01206970214844, 154.28555297851562], [193.4871368408203, 128.20167541503906, 768.0, 154.47532653808594], [42.201820373535156, 154.19873809814453, 69.42126846313477, 180.07733917236328], [69.37538528442383, 154.24401092529297, 97.89913940429688, 180.10179138183594], [97.7388916015625, 154.1438751220703, 170.12660217285156, 180.09679412841797], [192.82595825195312, 154.16085815429688, 768.0, 180.0529556274414], [42.16994285583496, 179.8704833984375, 69.36828994750977, 205.62914276123047], [69.3543472290039, 179.85253143310547, 97.88628387451172, 231.00382232666016], [97.83592224121094, 179.85645294189453, 170.30836486816406, 205.73172760009766], [192.0, 179.0, 321.0, 204.0], [42.1798210144043, 205.44989776611328, 69.34383773803711, 230.9190902709961], [97.7401351928711, 205.59749603271484, 170.41387939453125, 231.1204605102539], [191.8019256591797, 205.75701141357422, 767.9786224365234, 231.10009002685547], [69.35303497314453, 231.05542755126953, 97.91590881347656, 256.6958694458008], [97.79591369628906, 231.10065460205078, 170.75698852539062, 256.7064743041992], [191.68995666503906, 231.28760528564453, 768.0, 257.0969467163086], [42.12343978881836, 256.54418182373047, 69.38037872314453, 282.36962127685547], [69.318115234375, 256.5244369506836, 97.9000244140625, 282.3483352661133], [97.74056243896484, 256.53284454345703, 171.013671875, 282.3799514770508], [194.0, 259.0, 474.0, 277.0], [42.12211608886719, 282.2075729370117, 69.33308792114258, 307.8594741821289], [69.31608581542969, 282.1340866088867, 97.89131164550781, 307.88292694091797], [97.82430267333984, 282.16275787353516, 171.21908569335938, 307.96446990966797], [192.01914978027344, 282.3301773071289, 768.0, 332.96460723876953], [69.35113525390625, 307.73621368408203, 97.89268493652344, 333.26966094970703], [97.76229858398438, 307.8618850708008, 171.2179412841797, 333.3183059692383], [97.78926086425781, 333.29508209228516, 171.2117919921875, 409.0873794555664], [192.1817169189453, 333.5028762817383, 768.0, 359.11524200439453], [42.145668029785156, 358.35535430908203, 69.38712310791016, 383.90076446533203], [69.32662200927734, 358.4567642211914, 97.9358139038086, 433.90982818603516], [190.0, 356.0, 229.0, 384.0], [192.0, 383.0, 342.0, 408.0], [42.24249076843262, 408.74878692626953, 69.39948272705078, 433.80545806884766], [97.76239776611328, 409.04471588134766, 171.3276824951172, 459.8782730102539], [192.4663848876953, 409.44754791259766, 768.0, 459.91419219970703], [69.3626823425293, 433.9277114868164, 97.9444351196289, 484.9153518676758], [42.23030662536621, 459.20594024658203, 69.44235229492188, 485.03748321533203], [97.74578094482422, 459.5988540649414, 171.1816864013672, 510.78018951416016], [190.0, 459.0, 260.0, 486.0], [69.361328125, 484.9102249145508, 97.88383483886719, 535.916633605957], [192.7417755126953, 485.5265884399414, 768.0, 511.3660659790039], [42.25620079040527, 510.3185806274414, 69.41670608520508, 535.882942199707], [97.74463653564453, 510.7210159301758, 171.18324279785156, 536.3962783813477], [192.0, 511.0, 447.0, 534.0], [42.304351806640625, 536.0486221313477, 69.5037956237793, 561.5233535766602], [69.4526596069336, 535.9512710571289, 97.93753051757812, 561.5007095336914], [97.80396270751953, 536.2418899536133, 171.24803161621094, 561.9356155395508], [192.5806121826172, 536.8132705688477, 768.0, 562.7804641723633], [42.303321838378906, 561.2779006958008, 69.48640823364258, 587.2362442016602], [69.37507629394531, 561.2536087036133, 97.8635482788086, 587.1681900024414], [97.7562255859375, 561.668586730957, 171.14356994628906, 612.8450698852539], [192.3758544921875, 562.6262588500977, 768.0, 588.1377944946289], [42.23635482788086, 587.2654800415039, 69.43296813964844, 638.1723403930664], [69.35484313964844, 587.1895523071289, 97.85887145996094, 638.1592788696289], [194.0, 588.0, 524.0, 611.0], [97.7177963256836, 612.7858657836914, 171.1602020263672, 638.3681411743164], [192.251708984375, 613.3983535766602, 768.0, 638.6287612915039], [42.35030746459961, 638.3415298461914, 69.49665451049805, 689.4901504516602], [69.44973754882812, 638.1761245727539, 97.93213653564453, 663.6774063110352], [97.80558013916016, 638.4042739868164, 171.16082763671875, 663.8852920532227], [192.3772430419922, 638.7443618774414, 768.0, 664.6305313110352], [69.38356399536133, 663.5187759399414, 97.85465240478516, 689.4369277954102], [97.75723266601562, 663.6651382446289, 171.04550170898438, 689.5630874633789], [190.0, 665.0, 476.0, 688.0], [42.25709533691406, 689.6077041625977, 69.41801834106445, 740.5613784790039], [69.36314010620117, 689.4570083618164, 97.86473846435547, 740.5970840454102], [97.82254028320312, 689.4995498657227, 171.12063598632812, 715.0967178344727], [192.2503204345703, 689.8418350219727, 768.0, 715.5424575805664], [97.74628448486328, 715.0489883422852, 171.07350158691406, 740.7124404907227], [192.0, 715.0, 305.0, 740.0], [42.358572006225586, 740.7464981079102, 69.50426483154297, 791.8755874633789], [69.4362564086914, 740.5007705688477, 97.9164047241211, 766.0109024047852], [97.80432891845703, 740.7922744750977, 170.98880004882812, 766.1124649047852], [192.3473358154297, 740.9186172485352, 768.0, 766.8469619750977], [69.3858528137207, 765.8909683227539, 97.85930633544922, 791.7616348266602], [97.77787780761719, 765.9982681274414, 170.85633850097656, 791.9075698852539], [192.0, 767.0, 412.0, 790.0], [42.277191162109375, 791.9154434204102, 69.43961715698242, 842.9123306274414], [69.37217330932617, 791.7530899047852, 97.8684310913086, 817.4148941040039], [97.84922790527344, 791.8377456665039, 170.94815063476562, 817.2877578735352], [188.0, 788.0, 260.0, 820.0], [69.36589050292969, 817.2432022094727, 97.89717864990234, 842.9430313110352], [97.7730712890625, 817.3674697875977, 170.93731689453125, 842.8765029907227], [192.28305053710938, 817.8049087524414, 768.0, 842.9427261352539], [42.36823654174805, 843.0372085571289, 69.5029067993164, 868.4662857055664], [69.44355773925781, 842.7869644165039, 97.93671417236328, 868.3739395141602], [97.80378723144531, 842.9045181274414, 170.92579650878906, 868.3046646118164], [192.5653076171875, 843.1542739868164, 768.0, 869.0979385375977], [42.326847076416016, 868.2631607055664, 69.4973258972168, 894.2430801391602], [69.39644622802734, 868.2657241821289, 97.88048553466797, 894.1852798461914], [97.78108215332031, 868.3384780883789, 170.86788940429688, 894.3173599243164], [190.0, 865.0, 384.0, 892.0], [69.36420822143555, 894.1566543579102, 97.90316009521484, 919.8244400024414], [97.87701416015625, 894.2417984008789, 171.00343322753906, 919.6506729125977], [192.4822998046875, 894.6199111938477, 768.0, 920.5409317016602], [42.28374671936035, 919.6438369750977, 69.43748092651367, 945.3136978149414], [69.37788772583008, 919.6647720336914, 97.93022155761719, 945.3970108032227], [97.80504608154297, 919.7530899047852, 170.95201110839844, 945.2335586547852], [192.0, 919.0, 417.0, 944.0], [69.4453239440918, 945.2490005493164, 97.97071075439453, 970.8805923461914], [97.84683227539062, 945.4045181274414, 170.9213104248047, 970.9453506469727], [192.49563598632812, 945.7762832641602, 768.0, 970.7608413696289], [69.3624038696289, 970.6996841430664, 97.92000579833984, 996.5851821899414], [97.80673217773438, 970.8693618774414, 170.9058074951172, 996.5954971313477], [192.44056701660156, 971.5590591430664, 768.0, 997.0971450805664], [69.32168197631836, 996.5976943969727, 97.95049285888672, 1072.3085708618164], [97.91593933105469, 996.5015029907227, 171.0424041748047, 1046.0], [190.0, 996.0, 364.0, 1019.0], [192.49205017089844, 1022.0509414672852, 768.0, 1047.2233047485352], [97.72307586669922, 1047.6342544555664, 171.144775390625, 1098.0], [192.0, 1046.0, 360.0, 1071.0], [192.75167846679688, 1073.396583557129, 768.0, 1122.7818984985352], [42.183197021484375, 1097.734474182129, 69.41471481323242, 1122.9109268188477], [69.30480194091797, 1097.9081802368164, 97.90438842773438, 1148.2005996704102], [97.78365325927734, 1097.470802307129, 171.1262664794922, 1148.0], [192.0, 1123.0, 330.0, 1148.0], [69.3129997253418, 1148.0581436157227, 97.83844757080078, 1173.4132461547852], [97.73942565917969, 1148.4281387329102, 171.0286102294922, 1173.8349990844727], [193.15667724609375, 1148.0637588500977, 767.9706268310547, 1173.6078262329102], [98.0, 1173.0, 155.0, 1199.0], [192.878173828125, 1173.9050674438477, 768.0, 1199.6942520141602], [69.26551818847656, 1198.7245254516602, 97.8829116821289, 1224.4827041625977], [97.71839904785156, 1199.0755996704102, 170.8902587890625, 1224.7790908813477], [190.0, 1199.0, 329.0, 1224.0], [42.14856719970703, 1224.3470840454102, 69.30454635620117, 1250.0123672485352], [69.2524642944336, 1224.3033828735352, 97.86907958984375, 1275.4316787719727], [97.8139877319336, 1224.4977188110352, 170.82711791992188, 1250.4719619750977], [192.69577026367188, 1225.1713027954102, 767.9452972412109, 1250.9269180297852], [97.72876739501953, 1250.2661514282227, 170.7849884033203, 1301.0], [190.0, 1248.0, 508.0, 1275.0], [192.7498779296875, 1276.2374649047852, 768.0, 1301.8841934204102], [42.134315490722656, 1300.9687881469727, 69.34097671508789, 1326.8269424438477], [69.25203704833984, 1300.9311904907227, 97.86022186279297, 1326.7568740844727], [97.718017578125, 1301.0980606079102, 170.81268310546875, 1326.8959121704102], [192.79942321777344, 1301.9789199829102, 768.0, 1327.2185440063477], [42.1422061920166, 1326.6960830688477, 69.30440521240234, 1377.7988662719727], [69.24416732788086, 1326.6282119750977, 97.83763885498047, 1352.2910537719727], [97.80904388427734, 1326.6688613891602, 170.7875518798828, 1352.6339492797852], [192.0, 1326.0, 295.0, 1351.0], [69.29059600830078, 1352.1764297485352, 97.79681396484375, 1377.8033828735352], [97.70660400390625, 1352.4504776000977, 170.80426025390625, 1378.1068496704102], [192.96690368652344, 1352.8662490844727, 767.9899749755859, 1378.3373184204102], [69.28282928466797, 1377.8088760375977, 97.85872650146484, 1403.3108291625977], [97.74903106689453, 1377.9698867797852, 170.81324768066406, 1403.5134658813477], [192.70298767089844, 1378.5229873657227, 767.9420623779297, 1404.1432266235352], [42.13255310058594, 1403.3258438110352, 69.35550689697266, 1429.1794815063477], [69.25748825073242, 1403.2699356079102, 97.85142517089844, 1429.1200332641602], [97.71487426757812, 1403.4033584594727, 170.95755004882812, 1429.2110977172852], [190.0, 1403.0, 277.0, 1428.0], [42.12535858154297, 1429.0287246704102, 69.3069839477539, 1454.6620254516602], [69.24483108520508, 1428.9787979125977, 97.83599853515625, 1480.1498184204102], [97.79234313964844, 1428.9985733032227, 171.0060577392578, 1454.9136123657227], [192.6105499267578, 1429.6146621704102, 768.0, 1455.4935684204102], [42.11819076538086, 1454.5670547485352, 69.30078887939453, 1480.0870742797852], [97.70726013183594, 1454.7546768188477, 170.99549865722656, 1480.4210586547852], [190.0, 1455.0, 591.0, 1478.0], [69.26944732666016, 1480.1982803344727, 97.833984375, 1505.7380752563477], [97.73413848876953, 1480.4007949829102, 170.95901489257812, 1505.8967666625977], [192.72958374023438, 1480.7532119750977, 768.0, 1506.3286514282227], [42.107988357543945, 1505.6940078735352, 69.34195327758789, 1531.5427627563477], [69.24634170532227, 1505.6620254516602, 97.81812286376953, 1531.5080947875977], [97.6928482055664, 1505.7589492797852, 171.02810668945312, 1531.6046524047852], [192.55406188964844, 1506.3633193969727, 768.0, 1531.7534561157227], [42.12307357788086, 1531.4206924438477, 69.30797958374023, 1556.9776992797852], [69.24358749389648, 1531.3771133422852, 97.8009033203125, 1556.9800186157227], [97.78268432617188, 1531.3877334594727, 171.05996704101562, 1557.1988906860352], [194.0, 1532.0, 356.0, 1555.0], [42.11833190917969, 1556.9601211547852, 69.32323837280273, 1582.4017715454102], [69.27730560302734, 1556.9064102172852, 97.77424621582031, 1582.4668350219727], [97.69696807861328, 1557.1070938110352, 171.01197814941406, 1582.7272109985352], [192.52523803710938, 1557.4491348266602, 768.0, 1582.7541885375977], [69.2866325378418, 1582.5614395141602, 97.8318099975586, 1608.0577774047852], [97.76103973388672, 1582.6808242797852, 171.02000427246094, 1608.2230606079102], [192.5525665283203, 1582.9398574829102, 768.0, 1608.6355361938477], [42.11079406738281, 1608.0008926391602, 69.33509063720703, 1633.8867568969727], [69.24364471435547, 1607.9989395141602, 97.82125091552734, 1633.8646621704102], [97.6817398071289, 1608.0889053344727, 171.0854949951172, 1633.9653701782227], [194.0, 1609.0, 414.0, 1632.0], [42.14345932006836, 1633.6886367797852, 69.30768966674805, 1659.2978897094727], [69.23501968383789, 1633.6326065063477, 97.81014251708984, 1659.2472305297852], [97.78639221191406, 1633.6776504516602, 171.12327575683594, 1659.4823379516602], [192.6419677734375, 1633.7952041625977, 768.0, 1659.9409561157227], [97.74523162841797, 1659.2916641235352, 170.99490356445312, 1709.0], [192.0, 1657.0, 262.0, 1684.0], [194.0, 1684.0, 373.0, 1709.0]], "pred_html": "<html><body><table><tbody><tr><td>0.000 2.554</td><td>男性膀胱；后视图</td><td></td></tr><tr><td></td><td></td><td>优化配置</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.037</td></tr><tr><td></td><td></td><td>0.055</td></tr><tr><td></td><td>0.065</td><td>SEROMUCOIDS*</td></tr><tr><td></td><td>0.084</td><td>结合珠蛋白</td></tr><tr><td></td><td></td><td>0.048</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.065</td></tr><tr><td>0.093 0.097 0.032</td><td>转铁蛋白</td><td></td></tr><tr><td></td><td></td><td>锂*</td></tr><tr><td></td><td>0.043 0.044</td><td>血清补体SERUMCOMPLEMENT 血清溶菌酵SERUMLYSOZYME</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td>0.044 0.044</td><td>备解素*</td></tr><tr><td></td><td>0.045</td><td>肿瘤标志物MELANOGENE在尿*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.045</td></tr><tr><td></td><td></td><td>0.046 0.046</td></tr><tr><td></td><td></td><td>胆固醇COMMON PLASMA CHOLESTERIN</td></tr><tr><td>0.049</td><td>ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM</td><td></td></tr><tr><td></td><td></td><td>0.050</td></tr><tr><td></td><td>0.050</td><td>嗜中性粒细胞STABNEUTROPHILS</td></tr><tr><td></td><td></td><td>0.051</td></tr><tr><td>0.051</td><td>血管紧张素I*</td><td></td></tr><tr><td></td><td></td><td>0.052</td></tr><tr><td></td><td>0.052</td><td>尿肌配URINECREATININE</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.052</td></tr><tr><td></td><td>0.052</td><td>血清中的氨基酸NITROGENOFAMINOACIDSINSERUM</td></tr><tr><td></td><td></td><td>0.053</td></tr><tr><td></td><td></td><td>0.053</td></tr><tr><td></td><td>0.053</td><td>肾素*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.053</td></tr><tr><td></td><td>0.053</td><td>醛固酮尿*</td></tr><tr><td></td><td>0.053</td><td>血浆非酯化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA</td></tr><tr><td></td><td>0.054 0.054</td><td>红细胞沉降率（ESR)</td></tr><tr><td>0.054 0.054</td><td>血尿素BLOODUREA</td><td>血管紧张素I* 甲状腺球蛋白</td></tr><tr><td></td><td></td><td>0.055 0.055</td></tr><tr><td></td><td>0.056</td><td>血浆磷脂PLASMAPHOSPHOTIDES</td></tr><tr><td>0.057</td><td>血细胞比容，全血</td><td></td></tr><tr><td></td><td>0.057</td><td>抗链球菌溶血素</td></tr><tr><td></td><td></td><td>0.057</td></tr><tr><td>0.058 0.058</td><td>11 - PLASMA OXYCORTICOSTEROIDS</td><td>维生素B2*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.058</td></tr><tr><td></td><td></td><td>0.059</td></tr><tr><td></td><td>0.059</td><td>酸性磷酸酵素ACIDPHOSPHATASE</td></tr><tr><td></td><td>0.059</td><td>维生素B12*</td></tr><tr><td></td><td></td><td>0.059</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.059</td></tr><tr><td></td><td>0.060</td><td>乳酸脱氢酵素COMMONLACTADEHYDROGENASE</td></tr><tr><td></td><td>0.060</td><td>抗利尿激素*</td></tr><tr><td></td><td></td><td>0.060</td></tr><tr><td></td><td></td><td>0.060</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.061</td></tr><tr><td></td><td>0.062</td><td>嗜酸性粒细胞EOSINOPHILES</td></tr><tr><td></td><td></td><td>0.062</td></tr><tr><td></td><td></td><td>0.062</td></tr><tr><td>0.063 0.063</td><td>脂肪酶*</td><td>总铁结合力(TIBC)*</td></tr></tbody></table></body></html>", "table_ocr_pred": {"rec_polys": [[[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 77], [338, 77], [338, 102], [192, 102]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[192, 152], [388, 152], [388, 177], [192, 177]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[192, 179], [321, 179], [321, 204], [192, 204]], [[98, 204], [162, 204], [162, 229], [98, 229]], [[192, 206], [342, 206], [342, 229], [192, 229]], [[98, 231], [161, 231], [161, 256], [98, 256]], [[192, 231], [303, 231], [303, 256], [192, 256]], [[98, 256], [155, 256], [155, 281], [98, 281]], [[194, 259], [474, 259], [474, 277], [194, 277]], [[98, 281], [155, 281], [155, 306], [98, 306]], [[194, 282], [393, 282], [393, 306], [194, 306]], [[98, 306], [155, 306], [155, 332], [98, 332]], [[196, 311], [402, 311], [402, 329], [196, 329]], [[98, 332], [155, 332], [155, 358], [98, 358]], [[190, 332], [275, 332], [275, 358], [190, 358]], [[98, 358], [155, 358], [155, 383], [98, 383]], [[190, 356], [229, 356], [229, 384], [190, 384]], [[98, 383], [157, 383], [157, 409], [98, 409]], [[192, 383], [342, 383], [342, 408], [192, 408]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[192, 409], [447, 409], [447, 433], [192, 433]], [[98, 433], [157, 433], [157, 459], [98, 459]], [[192, 434], [443, 434], [443, 459], [192, 459]], [[98, 459], [157, 459], [157, 484], [98, 484]], [[190, 459], [260, 459], [260, 486], [190, 486]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[192, 486], [733, 486], [733, 509], [192, 509]], [[98, 509], [157, 509], [157, 536], [98, 536]], [[192, 511], [447, 511], [447, 534], [192, 534]], [[98, 536], [157, 536], [157, 561], [98, 561]], [[194, 538], [504, 538], [504, 561], [194, 561]], [[98, 561], [157, 561], [157, 586], [98, 586]], [[192, 563], [524, 563], [524, 586], [192, 586]], [[98, 586], [157, 586], [157, 613], [98, 613]], [[194, 588], [524, 588], [524, 611], [194, 611]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[196, 617], [687, 617], [687, 635], [196, 635]], [[98, 636], [157, 636], [157, 663], [98, 663]], [[192, 640], [606, 640], [606, 663], [192, 663]], [[98, 663], [157, 663], [157, 688], [98, 688]], [[190, 665], [476, 665], [476, 688], [190, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[192, 690], [570, 690], [570, 713], [192, 713]], [[98, 713], [155, 713], [155, 740], [98, 740]], [[192, 715], [305, 715], [305, 740], [192, 740]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[188, 738], [363, 734], [364, 765], [189, 769]], [[98, 765], [157, 765], [157, 792], [98, 792]], [[192, 767], [412, 767], [412, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[188, 788], [260, 788], [260, 820], [188, 820]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [641, 817], [641, 840], [192, 840]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[192, 844], [412, 844], [412, 867], [192, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[190, 865], [384, 867], [384, 892], [190, 890]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[190, 892], [244, 892], [244, 921], [190, 921]], [[98, 917], [157, 917], [157, 944], [98, 944]], [[192, 919], [417, 919], [417, 944], [192, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[190, 944], [279, 944], [279, 969], [190, 969]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[192, 971], [696, 971], [696, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[190, 996], [364, 996], [364, 1019], [190, 1019]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[188, 1019], [244, 1019], [244, 1047], [188, 1047]], [[98, 1044], [157, 1044], [157, 1071], [98, 1071]], [[192, 1046], [360, 1046], [360, 1071], [192, 1071]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[188, 1071], [311, 1067], [312, 1097], [189, 1101]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[191, 1094], [314, 1098], [313, 1125], [190, 1121]], [[98, 1121], [157, 1121], [157, 1148], [98, 1148]], [[192, 1123], [330, 1123], [330, 1148], [192, 1148]], [[98, 1148], [157, 1148], [157, 1174], [98, 1174]], [[192, 1149], [476, 1149], [476, 1173], [192, 1173]], [[98, 1173], [155, 1173], [155, 1199], [98, 1199]], [[191, 1171], [347, 1175], [347, 1200], [190, 1196]], [[98, 1198], [155, 1198], [155, 1224], [98, 1224]], [[190, 1199], [329, 1199], [329, 1224], [190, 1224]], [[98, 1223], [157, 1223], [157, 1249], [98, 1249]], [[186, 1223], [263, 1218], [265, 1251], [188, 1255]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[190, 1248], [508, 1250], [508, 1275], [190, 1273]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[190, 1274], [284, 1274], [284, 1303], [190, 1303]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[188, 1298], [286, 1298], [286, 1328], [188, 1328]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[192, 1326], [295, 1326], [295, 1351], [192, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [478, 1353], [478, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[188, 1378], [291, 1374], [292, 1401], [189, 1405]], [[98, 1401], [157, 1401], [157, 1428], [98, 1428]], [[190, 1403], [277, 1403], [277, 1428], [190, 1428]], [[100, 1428], [155, 1428], [155, 1453], [100, 1453]], [[192, 1430], [460, 1430], [460, 1453], [192, 1453]], [[98, 1453], [155, 1453], [155, 1478], [98, 1478]], [[190, 1455], [591, 1455], [591, 1478], [190, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[190, 1480], [297, 1480], [297, 1505], [190, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[192, 1505], [367, 1505], [367, 1530], [192, 1530]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[194, 1532], [356, 1532], [356, 1555], [194, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[192, 1557], [618, 1557], [618, 1580], [192, 1580]], [[98, 1582], [157, 1582], [157, 1607], [98, 1607]], [[194, 1586], [428, 1586], [428, 1603], [194, 1603]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[194, 1609], [414, 1609], [414, 1632], [194, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[194, 1634], [401, 1634], [401, 1657], [194, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1657], [262, 1657], [262, 1684], [192, 1684]], [[100, 1684], [157, 1684], [157, 1709], [100, 1709]], [[194, 1684], [373, 1684], [373, 1709], [194, 1709]]], "rec_texts": ["0.000", "男性膀胱；后视图", "2.554", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.037", "单核细胞MONOCYTES", "0.055", "免疫球蛋白M*", "0.065", "SEROMUCOIDS*", "0.084", "结合珠蛋白", "0.048", "PERIPHERICBLOODLEUCOCYTES", "0.065", "血红蛋白HAEMOGLOBIN", "0.080", "血红血球ERYTHROCYTES", "0.093", "转铁蛋白", "0.097", "锂*", "0.032", "肿瘤标志物CA50*", "0.043", "血清补体SERUMCOMPLEMENT", "0.044", "血清溶菌酵SERUMLYSOZYME", "0.044", "备解素*", "0.044", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.045", "肿瘤标志物MELANOGENE在尿*", "0.045", "血清淀粉酵素SERUMALPHAAMYLASE", "0.046", "游离胆固醇FREEPLASMACHOLESTERIN", "0.046", "胆固醇COMMON PLASMA CHOLESTERIN", "0.049", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.050", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.050", "嗜中性粒细胞STABNEUTROPHILS", "0.051", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.051", "血管紧张素I*", "0.052", "维生素E(生育酚)*", "0.052", "尿肌配URINECREATININE", "0.052", "胰岛素*", "0.052", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.053", "血清蛋白SERUMALBUMEN", "0.053", "17-血浆氧皮质类固醇类", "0.053", "肾素*", "0.053", "血肌酥SERUM CREATININE", "0.053", "醛固酮尿*", "0.053", "血浆非酯化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA", "0.054", "红细胞沉降率（ESR)", "0.054", "糖苷*", "0.054", "血尿素BLOODUREA", "0.054", "血管紧张素I*", "0.055", "甲状腺球蛋白", "0.055", "17-尿中酮类固醇", "0.056", "血浆磷脂PLASMAPHOSPHOTIDES", "0.057", "血细胞比容，全血", "0.057", "抗链球菌溶血素", "0.057", "催乳素*", "0.058", "11 - PLASMA OXYCORTICOSTEROIDS", "0.058", "维生素B2*", "0.058", "维生素B6*", "0.059", "胰高血糖素*", "0.059", "酸性磷酸酵素ACIDPHOSPHATASE", "0.059", "维生素B12*", "0.059", "醛固酮血*", "0.059", "尿中肾上腺素URINEADRENALIN", "0.060", "乳酸脱氢酵素COMMONLACTADEHYDROGENASE", "0.060", "抗利尿激素*", "0.060", "甲状腺素结合球蛋白", "0.060", "血糖BLOOD SUGAR", "0.061", "碱性磷酸酯酵素COMMONALKALINEPHOSPHATASE", "0.062", "嗜酸性粒细胞EOSINOPHILES", "0.062", "尿中尿酸URINEURICACID", "0.062", "血尿酸SERUMURICACID", "0.063", "脂肪酶*", "0.063", "总铁结合力(TIBC)*"], "rec_scores": [0.9992006421089172, 0.8448677062988281, 0.9987779855728149, 0.9983981251716614, 0.9665411710739136, 0.99988853931427, 0.9974513649940491, 0.9999510645866394, 0.9928655624389648, 0.9999055862426758, 0.9890060424804688, 0.9999068975448608, 0.9965609312057495, 0.9995183944702148, 0.9963439106941223, 0.9996567964553833, 0.9984849095344543, 0.9995536804199219, 0.9940184354782104, 0.9996921420097351, 0.9984321594238281, 0.9995640516281128, 0.9702817797660828, 0.9996942281723022, 0.984752357006073, 0.9996527433395386, 0.9985573291778564, 0.9995285868644714, 0.991339385509491, 0.9994535446166992, 0.9195845127105713, 0.9995214343070984, 0.9955445528030396, 0.9997155070304871, 0.968664288520813, 0.9996341466903687, 0.9982089996337891, 0.9996221661567688, 0.9958582520484924, 0.9996681213378906, 0.9635776877403259, 0.9997016787528992, 0.9957727193832397, 0.9996639490127563, 0.997855544090271, 0.999547004699707, 0.9957587122917175, 0.999665379524231, 0.9948965907096863, 0.9997023344039917, 0.9344069361686707, 0.9997509717941284, 0.9166479110717773, 0.9997024536132812, 0.9582784175872803, 0.9997352361679077, 0.9042901992797852, 0.9997650980949402, 0.9953368902206421, 0.9997808337211609, 0.9966993927955627, 0.9996727705001831, 0.9894955158233643, 0.9997941851615906, 0.9613390564918518, 0.9998162388801575, 0.9397323131561279, 0.9997060894966125, 0.9739959836006165, 0.9997370839118958, 0.992355227470398, 0.9997330904006958, 0.9119753241539001, 0.9997552037239075, 0.9629817008972168, 0.9997202157974243, 0.9939408898353577, 0.9996761083602905, 0.9709552526473999, 0.9997807741165161, 0.9968481063842773, 0.9998112916946411, 0.9886061549186707, 0.9997008442878723, 0.9985840320587158, 0.9997403025627136, 0.9893007874488831, 0.999691367149353, 0.9961222410202026, 0.9997220039367676, 0.9338442087173462, 0.9996737241744995, 0.9465644955635071, 0.9997797012329102, 0.9910664558410645, 0.9996509552001953, 0.994053065776825, 0.9996148347854614, 0.9432416558265686, 0.9996505975723267, 0.9971507787704468, 0.9997652769088745, 0.9702712297439575, 0.9997429847717285, 0.9023411870002747, 0.9995561838150024, 0.9974650144577026, 0.9995156526565552, 0.9922578930854797, 0.9997450709342957, 0.9777355790138245, 0.9995661973953247, 0.9967443943023682, 0.9994962811470032, 0.96152263879776, 0.999686598777771, 0.9885543584823608, 0.9995957612991333, 0.9947459101676941, 0.9996110796928406, 0.9964948296546936, 0.9996069073677063, 0.9990896582603455, 0.9995424151420593, 0.97796630859375, 0.9996079206466675, 0.9191208481788635], "rec_boxes": [[98, 77, 157, 102], [192, 77, 338, 102], [98, 102, 157, 129], [192, 102, 270, 129], [194, 129, 462, 152], [98, 154, 161, 179], [192, 152, 388, 177], [98, 179, 162, 204], [192, 179, 321, 204], [98, 204, 162, 229], [192, 206, 342, 229], [98, 231, 161, 256], [192, 231, 303, 256], [98, 256, 155, 281], [194, 259, 474, 277], [98, 281, 155, 306], [194, 282, 393, 306], [98, 306, 155, 332], [196, 311, 402, 329], [98, 332, 155, 358], [190, 332, 275, 358], [98, 358, 155, 383], [190, 356, 229, 384], [98, 383, 157, 409], [192, 383, 342, 408], [98, 408, 157, 434], [192, 409, 447, 433], [98, 433, 157, 459], [192, 434, 443, 459], [98, 459, 157, 484], [190, 459, 260, 486], [98, 484, 157, 509], [192, 486, 733, 509], [98, 509, 157, 536], [192, 511, 447, 534], [98, 536, 157, 561], [194, 538, 504, 561], [98, 561, 157, 586], [192, 563, 524, 586], [98, 586, 157, 613], [194, 588, 524, 611], [98, 611, 157, 638], [196, 617, 687, 635], [98, 636, 157, 663], [192, 640, 606, 663], [98, 663, 157, 688], [190, 665, 476, 688], [98, 688, 155, 715], [192, 690, 570, 713], [98, 713, 155, 740], [192, 715, 305, 740], [98, 740, 157, 767], [188, 734, 364, 769], [98, 765, 157, 792], [192, 767, 412, 790], [98, 790, 157, 817], [188, 788, 260, 820], [98, 815, 157, 842], [192, 817, 641, 840], [98, 840, 157, 867], [192, 844, 412, 867], [98, 867, 157, 892], [190, 865, 384, 892], [98, 892, 157, 919], [190, 892, 244, 921], [98, 917, 157, 944], [192, 919, 417, 944], [98, 944, 157, 969], [190, 944, 279, 969], [98, 969, 157, 996], [192, 971, 696, 994], [98, 994, 157, 1021], [190, 996, 364, 1019], [98, 1019, 157, 1046], [188, 1019, 244, 1047], [98, 1044, 157, 1071], [192, 1046, 360, 1071], [98, 1071, 157, 1098], [188, 1067, 312, 1101], [98, 1096, 157, 1123], [190, 1094, 314, 1125], [98, 1121, 157, 1148], [192, 1123, 330, 1148], [98, 1148, 157, 1174], [192, 1149, 476, 1173], [98, 1173, 155, 1199], [190, 1171, 347, 1200], [98, 1198, 155, 1224], [190, 1199, 329, 1224], [98, 1223, 157, 1249], [186, 1218, 265, 1255], [98, 1249, 157, 1274], [190, 1248, 508, 1275], [98, 1274, 157, 1301], [190, 1274, 284, 1303], [98, 1300, 157, 1326], [188, 1298, 286, 1328], [98, 1326, 157, 1351], [192, 1326, 295, 1351], [98, 1351, 157, 1376], [192, 1353, 478, 1376], [98, 1376, 157, 1403], [188, 1374, 292, 1405], [98, 1401, 157, 1428], [190, 1403, 277, 1428], [100, 1428, 155, 1453], [192, 1430, 460, 1453], [98, 1453, 155, 1478], [190, 1455, 591, 1478], [98, 1478, 157, 1505], [190, 1480, 297, 1505], [98, 1505, 157, 1530], [192, 1505, 367, 1530], [98, 1530, 157, 1555], [194, 1532, 356, 1555], [98, 1555, 155, 1582], [192, 1557, 618, 1580], [98, 1582, 157, 1607], [194, 1586, 428, 1603], [98, 1607, 157, 1632], [194, 1609, 414, 1632], [100, 1632, 157, 1657], [194, 1634, 401, 1657], [100, 1657, 157, 1684], [192, 1657, 262, 1684], [100, 1684, 157, 1709], [194, 1684, 373, 1709]]}}]}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//707a0840-eaa3-4398-971c-d46fdc448535/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-30T03%3A24%3A51Z%2F-1%2F%2F65641c5bfb1c470212e993cefd1faff98081363b86997fb14ffd34d5f144506b", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//707a0840-eaa3-4398-971c-d46fdc448535/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-30T03%3A24%3A51Z%2F-1%2F%2F2a55eac306d159ba4ff1026f9cd7ef28721f159409f7cf4e27bc8e03b6f96e64", "table_cell_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//707a0840-eaa3-4398-971c-d46fdc448535/table_cell_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-30T03%3A24%3A51Z%2F-1%2F%2Fd65487234ddd573c500768ded25be13308ae1322127ec77d29cae46b59f068ee"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//707a0840-eaa3-4398-971c-d46fdc448535/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-30T03%3A24%3A51Z%2F-1%2F%2Fb0e9ebcdcca6997cfd345ce5bf98b0aa7ce65f7362abbff9caa2a95e9bfd803c"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}