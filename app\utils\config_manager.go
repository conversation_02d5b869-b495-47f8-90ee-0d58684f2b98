package utils

import (
	json "github.com/goccy/go-json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"MagneticOperator/app/models"
)

// ConfigManager 配置管理器
type ConfigManager struct {
	mu           sync.RWMutex
	configPath   string
	lastModTime  time.Time
	config       *models.AppConfig
	watchers     []func(*models.AppConfig)
	stopWatching chan bool
	// 环境配置管理器
	envManager   *EnvConfigManager
	useEnvConfig bool
}

// NewConfigManager 创建新的配置管理器
func NewConfigManager(configPath string) *ConfigManager {
	return &ConfigManager{
		configPath:   configPath,
		watchers:     make([]func(*models.AppConfig), 0),
		stopWatching: make(chan bool),
		useEnvConfig: false,
	}
}

// NewConfigManagerWithEnv 创建支持环境配置的配置管理器
func NewConfigManagerWithEnv(configDir string, environment string) *ConfigManager {
	envManager := NewEnvConfigManager(configDir, environment)
	configPath := filepath.Join(configDir, "app_config.json")

	return &ConfigManager{
		configPath:   configPath,
		watchers:     make([]func(*models.AppConfig), 0),
		stopWatching: make(chan bool),
		envManager:   envManager,
		useEnvConfig: true,
	}
}

// LoadConfig 加载配置文件
func (cm *ConfigManager) LoadConfig() (*models.AppConfig, error) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	var config *models.AppConfig
	var err error

	// 如果使用环境配置
	if cm.useEnvConfig && cm.envManager != nil {
		config, err = cm.envManager.LoadConfig()
		if err != nil {
			return nil, fmt.Errorf("加载环境配置失败: %v", err)
		}
	} else {
		// 传统配置加载方式
		configFile, err := os.ReadFile(cm.configPath)
		if err != nil {
			return nil, fmt.Errorf("读取配置文件失败: %v", err)
		}

		var appConfig models.AppConfig
		if err := json.Unmarshal(configFile, &appConfig); err != nil {
			return nil, fmt.Errorf("解析配置文件失败: %v", err)
		}
		config = &appConfig
	}

	// 验证配置
	if err := cm.validateConfig(config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	// 更新文件修改时间
	if stat, err := os.Stat(cm.configPath); err == nil {
		cm.lastModTime = stat.ModTime()
	}

	cm.config = config
	return config, nil
}

// GetConfig 获取当前配置
func (cm *ConfigManager) GetConfig() *models.AppConfig {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.config
}

// SaveConfig 保存配置到文件
func (cm *ConfigManager) SaveConfig(config *models.AppConfig) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// 验证配置
	if err := cm.validateConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	// 创建目录
	os.MkdirAll(filepath.Dir(cm.configPath), 0755)

	// 序列化配置
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(cm.configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	// 更新内存中的配置
	cm.config = config

	// 更新文件修改时间
	if stat, err := os.Stat(cm.configPath); err == nil {
		cm.lastModTime = stat.ModTime()
	}

	return nil
}

// AddWatcher 添加配置变更监听器
func (cm *ConfigManager) AddWatcher(watcher func(*models.AppConfig)) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.watchers = append(cm.watchers, watcher)
}

// StartWatching 开始监听配置文件变更
func (cm *ConfigManager) StartWatching() {
	go func() {
		ticker := time.NewTicker(2 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if cm.checkConfigChanged() {
					if newConfig, err := cm.LoadConfig(); err == nil {
						cm.notifyWatchers(newConfig)
					}
				}
			case <-cm.stopWatching:
				return
			}
		}
	}()
}

// StopWatching 停止监听配置文件变更
func (cm *ConfigManager) StopWatching() {
	select {
	case cm.stopWatching <- true:
	default:
	}
}

// checkConfigChanged 检查配置文件是否已更改
func (cm *ConfigManager) checkConfigChanged() bool {
	stat, err := os.Stat(cm.configPath)
	if err != nil {
		return false
	}

	cm.mu.RLock()
	lastModTime := cm.lastModTime
	cm.mu.RUnlock()

	return stat.ModTime().After(lastModTime)
}

// notifyWatchers 通知所有监听器
func (cm *ConfigManager) notifyWatchers(config *models.AppConfig) {
	cm.mu.RLock()
	watchers := make([]func(*models.AppConfig), len(cm.watchers))
	copy(watchers, cm.watchers)
	cm.mu.RUnlock()

	for _, watcher := range watchers {
		watcher(config)
	}
}

// validateConfig 验证配置的有效性
func (cm *ConfigManager) validateConfig(config *models.AppConfig) error {
	// 验证API配置
	if config.APIKeys.OCR.APIURL == "" {
		return fmt.Errorf("OCR API URL不能为空")
	}

	if config.APIKeys.OCR.Token == "" {
		return fmt.Errorf("OCR API Token不能为空")
	}

	return nil
}

// GetEnvironment 获取当前环境
func (cm *ConfigManager) GetEnvironment() string {
	if cm.useEnvConfig && cm.envManager != nil {
		return cm.envManager.GetEnvironment()
	}
	return "unknown"
}

// IsProduction 是否是生产环境
func (cm *ConfigManager) IsProduction() bool {
	if cm.useEnvConfig && cm.envManager != nil {
		return cm.envManager.IsProduction()
	}
	return false
}

// IsDevelopment 是否是开发环境
func (cm *ConfigManager) IsDevelopment() bool {
	if cm.useEnvConfig && cm.envManager != nil {
		return cm.envManager.IsDevelopment()
	}
	return false
}

// IsDebugMode 是否是调试模式
func (cm *ConfigManager) IsDebugMode() bool {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	if cm.config != nil {
		return cm.config.Debug
	}
	return false
}
