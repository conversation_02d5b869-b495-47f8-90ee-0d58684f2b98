package utils

import (
	json "github.com/goccy/go-json"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"MagneticOperator/app/models"
)

// EnvConfigManager 环境配置管理器
type EnvConfigManager struct {
	configDir    string
	environment  string
	envConfig    *models.EnvironmentConfig
	baseConfig   *models.AppConfig
	mergedConfig *models.AppConfig
}

// NewEnvConfigManager 创建新的环境配置管理器
func NewEnvConfigManager(configDir string, environment string) *EnvConfigManager {
	// 如果未指定环境，默认使用开发环境
	if environment == "" {
		environment = "development"
	}

	return &EnvConfigManager{
		configDir:   configDir,
		environment: environment,
	}
}

// LoadConfig 加载配置
func (ecm *EnvConfigManager) LoadConfig() (*models.AppConfig, error) {
	// 1. 加载基础配置
	baseConfigPath := filepath.Join(ecm.configDir, "app_config.json")
	baseConfig, err := ecm.loadBaseConfig(baseConfigPath)
	if err != nil {
		return nil, fmt.Errorf("加载基础配置失败: %v", err)
	}
	ecm.baseConfig = baseConfig

	// 2. 加载环境配置
	envConfigPath := filepath.Join(ecm.configDir, fmt.Sprintf("%s.json", ecm.environment))
	envConfig, err := ecm.loadEnvConfig(envConfigPath)
	if err != nil {
		// 如果环境配置不存在，使用默认环境
		if os.IsNotExist(err) && ecm.environment != "development" {
			fmt.Printf("警告: %s 环境配置不存在，使用 development 环境\n", ecm.environment)
			ecm.environment = "development"
			envConfigPath = filepath.Join(ecm.configDir, "development.json")
			envConfig, err = ecm.loadEnvConfig(envConfigPath)
			if err != nil {
				return nil, fmt.Errorf("加载默认环境配置失败: %v", err)
			}
		} else {
			return nil, fmt.Errorf("加载环境配置失败: %v", err)
		}
	}
	ecm.envConfig = envConfig

	// 3. 合并配置
	mergedConfig := ecm.mergeConfigs()
	ecm.mergedConfig = mergedConfig

	return mergedConfig, nil
}

// loadBaseConfig 加载基础配置
func (ecm *EnvConfigManager) loadBaseConfig(configPath string) (*models.AppConfig, error) {
	configFile, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var config models.AppConfig
	if err := json.Unmarshal(configFile, &config); err != nil {
		return nil, err
	}

	return &config, nil
}

// loadEnvConfig 加载环境配置
func (ecm *EnvConfigManager) loadEnvConfig(configPath string) (*models.EnvironmentConfig, error) {
	configFile, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var config models.EnvironmentConfig
	if err := json.Unmarshal(configFile, &config); err != nil {
		return nil, err
	}

	return &config, nil
}

// mergeConfigs 合并基础配置和环境配置
func (ecm *EnvConfigManager) mergeConfigs() *models.AppConfig {
	// 创建合并后的配置（基于基础配置）
	mergedConfig := *ecm.baseConfig

	// 设置环境和调试模式
	mergedConfig.Environment = ecm.envConfig.Environment
	mergedConfig.Debug = ecm.envConfig.Debug

	// 智能合并API密钥配置
	if ecm.envConfig.APIKeys.OCR.APIURL != "" {
		mergedConfig.APIKeys.OCR.APIURL = ecm.envConfig.APIKeys.OCR.APIURL
	}
	if ecm.envConfig.APIKeys.OCR.TableAPIURL != "" {
		mergedConfig.APIKeys.OCR.TableAPIURL = ecm.envConfig.APIKeys.OCR.TableAPIURL
	}
	if ecm.envConfig.APIKeys.OCR.Token != "" {
		mergedConfig.APIKeys.OCR.Token = ecm.envConfig.APIKeys.OCR.Token
	}

	// 合并Coze配置
	if ecm.envConfig.APIKeys.Coze.Token != "" {
		mergedConfig.APIKeys.Coze = ecm.envConfig.APIKeys.Coze
	}

	// 合并CloudFunction配置
	if ecm.envConfig.APIKeys.CloudFunction.RegistrationsURL != "" {
		mergedConfig.APIKeys.CloudFunction = ecm.envConfig.APIKeys.CloudFunction
	}

	// 合并颜色检测配置
	mergedConfig.ColorDetection = ecm.envConfig.ColorDetection

	return &mergedConfig
}

// GetEnvironment 获取当前环境
func (ecm *EnvConfigManager) GetEnvironment() string {
	return ecm.environment
}

// IsProduction 是否是生产环境
func (ecm *EnvConfigManager) IsProduction() bool {
	return strings.ToLower(ecm.environment) == "production"
}

// IsDevelopment 是否是开发环境
func (ecm *EnvConfigManager) IsDevelopment() bool {
	return strings.ToLower(ecm.environment) == "development"
}

// GetConfig 获取合并后的配置
func (ecm *EnvConfigManager) GetConfig() *models.AppConfig {
	return ecm.mergedConfig
}
