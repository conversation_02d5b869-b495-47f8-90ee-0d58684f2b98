{"level":"INFO","timestamp":"2025-07-01T17:14:54.605+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T17:14:54.606+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T17:14:54.606+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T17:14:54.606+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:54.606+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T17:14:54.606+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T17:14:54.606+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:54.606+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":42464}
{"level":"INFO","timestamp":"2025-07-01T17:14:54.694+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T17:14:54.694+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T17:14:54.694+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:54.694+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:54.694+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.644+0800","caller":"utils/logger.go:96","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.644+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.644+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.644+0800","caller":"utils/logger.go:96","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.645+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.649+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.651+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.651+0800","caller":"utils/logger.go:96","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.652+0800","caller":"utils/logger.go:96","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.653+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.653+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:55.653+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.221+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.293+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.303+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.304+0800","caller":"utils/logger.go:96","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.304+0800","caller":"utils/logger.go:96","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.304+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.315+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.321+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.321+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.329+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.956+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.956+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:14:56.956+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:14:57.596+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:14:57.596+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:14:57.603+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:14:57.827+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:14:58.085+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:19.895+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:15:19.895+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T17:15:19.895+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T17:15:19.895+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T17:15:19.895+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.084+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.084+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.084+0800","caller":"utils/logger.go:96","msg":"任务创建成功","taskID":"screenshot_B_1751361320084981400_暂无候检者","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.084+0800","caller":"utils/logger.go:96","msg":"任务提交成功","taskID":"screenshot_B_1751361320084981400_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","priority":2}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.084+0800","caller":"utils/logger.go:96","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751361320084981400_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.085+0800","caller":"utils/logger.go:96","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751361320084981400_暂无候检者, User: 暂无候检者, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.085+0800","caller":"utils/logger.go:96","msg":"开始处理任务","taskID":"screenshot_B_1751361320084981400_暂无候检者","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.085+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.085+0800","caller":"utils/logger.go:96","msg":"执行任务处理器","taskID":"screenshot_B_1751361320084981400_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.085+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"执行截图任务-B","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.085+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.357+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.357+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.650+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.650+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:20.650+0800","caller":"utils/logger.go:96","msg":"开始处理截图工作流程 - 模式: B, 用户: 暂无候检者\n"}
{"level":"INFO","timestamp":"2025-07-01T17:15:21.064+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:21.253+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:23.809+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:15:23.809+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T17:15:23.809+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T17:15:23.809+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T17:15:23.809+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.056+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.056+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.056+0800","caller":"utils/logger.go:96","msg":"任务创建成功","taskID":"screenshot_C_1751361324056170000_暂无候检者","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.056+0800","caller":"utils/logger.go:96","msg":"任务提交成功","taskID":"screenshot_C_1751361324056170000_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","priority":2}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.056+0800","caller":"utils/logger.go:96","msg":"开始处理任务","workerID":2,"taskID":"screenshot_C_1751361324056170000_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.056+0800","caller":"utils/logger.go:96","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751361324056170000_暂无候检者, User: 暂无候检者, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.056+0800","caller":"utils/logger.go:96","msg":"开始处理任务","taskID":"screenshot_C_1751361324056170000_暂无候检者","taskType":"screenshot_C","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.056+0800","caller":"utils/logger.go:96","msg":"执行任务处理器","taskID":"screenshot_C_1751361324056170000_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.056+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"执行截图任务-C","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.056+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.056+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.242+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.242+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.453+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.453+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.453+0800","caller":"utils/logger.go:96","msg":"开始处理截图工作流程 - 模式: C, 用户: 暂无候检者\n"}
{"level":"INFO","timestamp":"2025-07-01T17:15:24.862+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:25.040+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:41.361+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:15:41.361+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T17:15:41.361+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T17:15:41.362+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T17:15:41.362+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:41.603+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T17:15:41.603+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-01T17:15:41.603+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"暂无候检者","error":"task screenshot_B for user 暂无候检者 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:419\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T17:15:41.603+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T17:15:44.470+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:15:44.470+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T17:15:44.470+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T17:15:44.470+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T17:15:44.470+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:15:44.717+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T17:15:44.717+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-01T17:15:44.717+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"暂无候检者","error":"task screenshot_C for user 暂无候检者 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:419\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T17:15:44.718+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T17:15:50.313+0800","caller":"utils/logger.go:96","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 40, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_暂无候检者_20250701_171520.png\n"}
{"level":"INFO","timestamp":"2025-07-01T17:15:50.609+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"截图任务完成-B","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:15:50.609+0800","caller":"utils/logger.go:96","msg":"任务执行成功","taskID":"screenshot_B_1751361320084981400_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T17:15:50.609+0800","caller":"utils/logger.go:96","msg":"任务执行完成","taskID":"screenshot_B_1751361320084981400_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","duration":30.524548,"workerID":0}
{"level":"INFO","timestamp":"2025-07-01T17:15:50.609+0800","caller":"utils/logger.go:96","msg":"任务执行成功 - TaskID: screenshot_B_1751361320084981400_暂无候检者, Type: screenshot_B, Worker: 0, Duration: 30.524548s"}
{"level":"INFO","timestamp":"2025-07-01T17:15:50.609+0800","caller":"utils/logger.go:96","msg":"发送任务完成事件","taskID":"screenshot_B_1751361320084981400_暂无候检者","status":"completed"}
{"level":"INFO","timestamp":"2025-07-01T17:15:50.610+0800","caller":"utils/logger.go:96","msg":"收到任务完成通知 - 任务ID: screenshot_B_1751361320084981400_暂无候检者, 状态: completed"}
{"level":"INFO","timestamp":"2025-07-01T17:15:50.610+0800","caller":"utils/logger.go:96","msg":"任务执行成功，准备发送成功通知"}
{"level":"INFO","timestamp":"2025-07-01T17:15:50.610+0800","caller":"utils/logger.go:96","msg":"准备发送Toast通知 - 类型: success, 标题: 任务完成, 消息: 截图任务已完成"}
{"level":"INFO","timestamp":"2025-07-01T17:15:50.610+0800","caller":"utils/logger.go:96","msg":"Toast通知已发送到前端: 任务完成 - 截图任务已完成"}
{"level":"INFO","timestamp":"2025-07-01T17:16:17.536+0800","caller":"utils/logger.go:96","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_暂无候检者_20250701_171524.png\n"}
{"level":"INFO","timestamp":"2025-07-01T17:16:17.555+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"截图任务完成-C","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:16:17.555+0800","caller":"utils/logger.go:96","msg":"任务执行成功","taskID":"screenshot_C_1751361324056170000_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T17:16:17.555+0800","caller":"utils/logger.go:96","msg":"任务执行完成","taskID":"screenshot_C_1751361324056170000_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","duration":53.4996077,"workerID":2}
{"level":"INFO","timestamp":"2025-07-01T17:16:17.555+0800","caller":"utils/logger.go:96","msg":"任务执行成功 - TaskID: screenshot_C_1751361324056170000_暂无候检者, Type: screenshot_C, Worker: 2, Duration: 53.4996077s"}
{"level":"INFO","timestamp":"2025-07-01T17:16:17.555+0800","caller":"utils/logger.go:96","msg":"发送任务完成事件","taskID":"screenshot_C_1751361324056170000_暂无候检者","status":"completed"}
{"level":"INFO","timestamp":"2025-07-01T17:16:17.556+0800","caller":"utils/logger.go:96","msg":"收到任务完成通知 - 任务ID: screenshot_C_1751361324056170000_暂无候检者, 状态: completed"}
{"level":"INFO","timestamp":"2025-07-01T17:16:17.556+0800","caller":"utils/logger.go:96","msg":"任务执行成功，准备发送成功通知"}
{"level":"INFO","timestamp":"2025-07-01T17:16:17.556+0800","caller":"utils/logger.go:96","msg":"准备发送Toast通知 - 类型: success, 标题: 任务完成, 消息: 截图任务已完成"}
{"level":"INFO","timestamp":"2025-07-01T17:16:17.556+0800","caller":"utils/logger.go:96","msg":"Toast通知已发送到前端: 任务完成 - 截图任务已完成"}
{"level":"INFO","timestamp":"2025-07-01T17:19:55.652+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":2}
{"level":"INFO","timestamp":"2025-07-01T17:24:55.652+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":2}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.103+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.105+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.106+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.108+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.108+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.108+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.109+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.109+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.109+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.109+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.109+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.109+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.113+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.113+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.113+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.779+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.779+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.779+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.779+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.780+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.780+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.785+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.785+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:34.785+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.390+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.399+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.400+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.602+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.602+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.603+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.604+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.604+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.606+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.637+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.777+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.807+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:35.832+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.010+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.072+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.898+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.898+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.898+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.898+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.899+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.899+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T17:14:54.606+0800"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.899+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"42464"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.899+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":42464}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.899+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":42464}
{"level":"WARN","timestamp":"2025-07-01T17:26:36.899+0800","caller":"utils/logger.go:103","msg":"查找进程失败","pid":42464,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.899+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":42464}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.899+0800","caller":"utils/logger.go:96","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.899+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.899+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":49804}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.923+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.924+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.924+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.924+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:36.924+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.406+0800","caller":"utils/logger.go:96","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.407+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.407+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.407+0800","caller":"utils/logger.go:96","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.407+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.414+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.417+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.417+0800","caller":"utils/logger.go:96","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.417+0800","caller":"utils/logger.go:96","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.417+0800","caller":"utils/logger.go:96","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.417+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.417+0800","caller":"utils/logger.go:96","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.417+0800","caller":"utils/logger.go:96","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.418+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.419+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.419+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.644+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.709+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.721+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.721+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.721+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.732+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.737+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.737+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.749+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.907+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.907+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:37.908+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.106+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.106+0800","caller":"utils/logger.go:96","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.106+0800","caller":"utils/logger.go:96","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.107+0800","caller":"utils/logger.go:96","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.107+0800","caller":"utils/logger.go:96","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.107+0800","caller":"utils/logger.go:96","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.107+0800","caller":"utils/logger.go:96","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.107+0800","caller":"utils/logger.go:96","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.107+0800","caller":"utils/logger.go:96","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.107+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.107+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.107+0800","caller":"utils/logger.go:96","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.107+0800","caller":"utils/logger.go:96","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.107+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.155+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.155+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.158+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.319+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:26:38.485+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:45.988+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T17:29:45.990+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T17:29:45.992+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T17:29:45.996+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:29:45.996+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:29:45.996+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T17:29:45.996+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:29:45.996+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:29:45.996+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T17:29:45.997+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:29:45.997+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:29:45.997+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T17:29:46.003+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:46.003+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:46.004+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:46.660+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:46.660+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:46.660+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:46.667+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:46.667+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:46.667+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:46.668+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:46.668+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:46.668+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.286+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.286+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.288+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.290+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.290+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.291+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.293+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.293+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.294+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.518+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.530+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.530+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.727+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.728+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:29:47.742+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:31:37.419+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T17:33:21.643+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:21.646+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:33:21.646+0800","caller":"utils/logger.go:96","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-01T17:33:21.646+0800","caller":"utils/logger.go:96","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T17:33:21.646+0800","caller":"utils/logger.go:96","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T17:33:21.646+0800","caller":"utils/logger.go:96","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-01T17:33:21.689+0800","caller":"utils/logger.go:96","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T17:33:21.689+0800","caller":"utils/logger.go:96","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T17:33:21.690+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:21.690+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T17:33:21.692+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.126+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.127+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.127+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.127+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.127+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.127+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.127+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.127+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":51808}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.159+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.159+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.159+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.159+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.159+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.578+0800","caller":"utils/logger.go:96","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.578+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.578+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.578+0800","caller":"utils/logger.go:96","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.578+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.583+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.585+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.586+0800","caller":"utils/logger.go:96","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.586+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.586+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:50.586+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.147+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.207+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.216+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.216+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.216+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.229+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.235+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.235+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.240+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:96","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:96","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:96","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:96","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:96","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:96","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:96","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:96","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:96","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.366+0800","caller":"utils/logger.go:96","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.367+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.856+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.856+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:33:51.857+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:33:52.613+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:33:52.619+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:33:52.621+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:33:52.834+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:33:53.012+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:35.983+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:34:35.983+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T17:34:35.983+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T17:34:35.984+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T17:34:35.984+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.175+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.175+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.175+0800","caller":"utils/logger.go:96","msg":"任务创建成功","taskID":"screenshot_B_1751362476175209300_暂无候检者","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.175+0800","caller":"utils/logger.go:96","msg":"任务提交成功","taskID":"screenshot_B_1751362476175209300_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","priority":2}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.175+0800","caller":"utils/logger.go:96","msg":"开始处理任务","workerID":1,"taskID":"screenshot_B_1751362476175209300_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.175+0800","caller":"utils/logger.go:96","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751362476175209300_暂无候检者, User: 暂无候检者, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.175+0800","caller":"utils/logger.go:96","msg":"开始处理任务","taskID":"screenshot_B_1751362476175209300_暂无候检者","taskType":"screenshot_B","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.176+0800","caller":"utils/logger.go:96","msg":"执行任务处理器","taskID":"screenshot_B_1751362476175209300_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.176+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.176+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"执行截图任务-B","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.176+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.369+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.369+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.619+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.619+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:36.619+0800","caller":"utils/logger.go:96","msg":"开始处理截图工作流程 - 模式: B, 用户: 暂无候检者\n"}
{"level":"INFO","timestamp":"2025-07-01T17:34:37.360+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:37.542+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.045+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.045+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.045+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.045+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.045+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.268+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.268+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.268+0800","caller":"utils/logger.go:96","msg":"任务创建成功","taskID":"screenshot_C_1751362479268336300_暂无候检者","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.268+0800","caller":"utils/logger.go:96","msg":"任务提交成功","taskID":"screenshot_C_1751362479268336300_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","priority":2}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.268+0800","caller":"utils/logger.go:96","msg":"开始处理任务","workerID":2,"taskID":"screenshot_C_1751362479268336300_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.268+0800","caller":"utils/logger.go:96","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751362479268336300_暂无候检者, User: 暂无候检者, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.268+0800","caller":"utils/logger.go:96","msg":"开始处理任务","taskID":"screenshot_C_1751362479268336300_暂无候检者","taskType":"screenshot_C","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.268+0800","caller":"utils/logger.go:96","msg":"执行任务处理器","taskID":"screenshot_C_1751362479268336300_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.268+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.268+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"执行截图任务-C","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.268+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.456+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.456+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.696+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.696+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:39.696+0800","caller":"utils/logger.go:96","msg":"开始处理截图工作流程 - 模式: C, 用户: 暂无候检者\n"}
{"level":"INFO","timestamp":"2025-07-01T17:34:40.054+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:40.331+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:49.824+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:34:49.824+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T17:34:49.824+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T17:34:49.824+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T17:34:49.824+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:50.062+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T17:34:50.062+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-01T17:34:50.062+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"暂无候检者","error":"task screenshot_B for user 暂无候检者 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:419\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T17:34:50.062+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T17:34:53.380+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:34:53.380+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T17:34:53.380+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T17:34:53.380+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T17:34:53.380+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:34:53.789+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T17:34:53.789+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-01T17:34:53.789+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"暂无候检者","error":"task screenshot_C for user 暂无候检者 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:419\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T17:34:53.789+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T17:35:06.433+0800","caller":"utils/logger.go:96","msg":"OCR API返回值详情 - 校验后器官名称: 男性器官小骨盆；左侧, 键值对数量: 35, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_暂无候检者_20250701_173436.png\n"}
{"level":"INFO","timestamp":"2025-07-01T17:35:06.710+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"截图任务完成-B","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:35:06.710+0800","caller":"utils/logger.go:96","msg":"任务执行成功","taskID":"screenshot_B_1751362476175209300_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T17:35:06.710+0800","caller":"utils/logger.go:96","msg":"任务执行完成","taskID":"screenshot_B_1751362476175209300_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","duration":30.5352355,"workerID":1}
{"level":"INFO","timestamp":"2025-07-01T17:35:06.710+0800","caller":"utils/logger.go:96","msg":"任务执行成功 - TaskID: screenshot_B_1751362476175209300_暂无候检者, Type: screenshot_B, Worker: 1, Duration: 30.5352355s"}
{"level":"INFO","timestamp":"2025-07-01T17:35:06.710+0800","caller":"utils/logger.go:96","msg":"发送任务完成事件","taskID":"screenshot_B_1751362476175209300_暂无候检者","status":"completed"}
{"level":"INFO","timestamp":"2025-07-01T17:35:06.711+0800","caller":"utils/logger.go:96","msg":"收到任务完成通知 - 任务ID: screenshot_B_1751362476175209300_暂无候检者, 状态: completed"}
{"level":"INFO","timestamp":"2025-07-01T17:35:06.711+0800","caller":"utils/logger.go:96","msg":"任务执行成功，准备发送成功通知"}
{"level":"INFO","timestamp":"2025-07-01T17:35:06.711+0800","caller":"utils/logger.go:96","msg":"准备发送Toast通知 - 类型: success, 标题: 任务完成, 消息: 截图任务已完成"}
{"level":"INFO","timestamp":"2025-07-01T17:35:06.711+0800","caller":"utils/logger.go:96","msg":"Toast通知已发送到前端: 任务完成 - 截图任务已完成"}
{"level":"INFO","timestamp":"2025-07-01T17:35:28.635+0800","caller":"utils/logger.go:96","msg":"OCR API返回值详情 - 校验后器官名称: 男性器官小骨盆；左侧, 键值对数量: 2, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_暂无候检者_20250701_173439.png\n"}
{"level":"INFO","timestamp":"2025-07-01T17:35:28.984+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"截图任务完成-C","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:35:28.984+0800","caller":"utils/logger.go:96","msg":"任务执行成功","taskID":"screenshot_C_1751362479268336300_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T17:35:28.984+0800","caller":"utils/logger.go:96","msg":"任务执行完成","taskID":"screenshot_C_1751362479268336300_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","duration":49.7156815,"workerID":2}
{"level":"INFO","timestamp":"2025-07-01T17:35:28.984+0800","caller":"utils/logger.go:96","msg":"任务执行成功 - TaskID: screenshot_C_1751362479268336300_暂无候检者, Type: screenshot_C, Worker: 2, Duration: 49.7156815s"}
{"level":"INFO","timestamp":"2025-07-01T17:35:28.984+0800","caller":"utils/logger.go:96","msg":"发送任务完成事件","taskID":"screenshot_C_1751362479268336300_暂无候检者","status":"completed"}
{"level":"INFO","timestamp":"2025-07-01T17:35:28.984+0800","caller":"utils/logger.go:96","msg":"收到任务完成通知 - 任务ID: screenshot_C_1751362479268336300_暂无候检者, 状态: completed"}
{"level":"INFO","timestamp":"2025-07-01T17:35:28.984+0800","caller":"utils/logger.go:96","msg":"任务执行成功，准备发送成功通知"}
{"level":"INFO","timestamp":"2025-07-01T17:35:28.984+0800","caller":"utils/logger.go:96","msg":"准备发送Toast通知 - 类型: success, 标题: 任务完成, 消息: 截图任务已完成"}
{"level":"INFO","timestamp":"2025-07-01T17:35:28.984+0800","caller":"utils/logger.go:96","msg":"Toast通知已发送到前端: 任务完成 - 截图任务已完成"}
{"level":"INFO","timestamp":"2025-07-01T17:38:50.586+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":2}
{"level":"INFO","timestamp":"2025-07-01T17:39:59.467+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:39:59.467+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T17:39:59.467+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T17:39:59.467+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T17:39:59.467+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:00.227+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T17:40:00.227+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T17:40:00.227+0800","caller":"utils/logger.go:96","msg":"任务创建成功","taskID":"screenshot_B_1751362800227875400_暂无候检者","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T17:40:00.227+0800","caller":"utils/logger.go:96","msg":"任务提交成功","taskID":"screenshot_B_1751362800227875400_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","priority":2}
{"level":"INFO","timestamp":"2025-07-01T17:40:00.227+0800","caller":"utils/logger.go:96","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751362800227875400_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T17:40:00.228+0800","caller":"utils/logger.go:96","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751362800227875400_暂无候检者, User: 暂无候检者, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T17:40:00.228+0800","caller":"utils/logger.go:96","msg":"开始处理任务","taskID":"screenshot_B_1751362800227875400_暂无候检者","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T17:40:00.228+0800","caller":"utils/logger.go:96","msg":"执行任务处理器","taskID":"screenshot_B_1751362800227875400_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T17:40:00.228+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T17:40:00.228+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"执行截图任务-B","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:40:00.228+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:01.465+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-07-01T17:40:01.465+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:01.739+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:01.739+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:01.739+0800","caller":"utils/logger.go:96","msg":"开始处理截图工作流程 - 模式: B, 用户: 暂无候检者\n"}
{"level":"INFO","timestamp":"2025-07-01T17:40:02.435+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:02.625+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.450+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.450+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.450+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.450+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.450+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.704+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.704+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.704+0800","caller":"utils/logger.go:96","msg":"任务创建成功","taskID":"screenshot_C_1751362803704356400_暂无候检者","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.704+0800","caller":"utils/logger.go:96","msg":"任务提交成功","taskID":"screenshot_C_1751362803704356400_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","priority":2}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.704+0800","caller":"utils/logger.go:96","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751362803704356400_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.704+0800","caller":"utils/logger.go:96","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751362803704356400_暂无候检者, User: 暂无候检者, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.704+0800","caller":"utils/logger.go:96","msg":"开始处理任务","taskID":"screenshot_C_1751362803704356400_暂无候检者","taskType":"screenshot_C","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.704+0800","caller":"utils/logger.go:96","msg":"执行任务处理器","taskID":"screenshot_C_1751362803704356400_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.704+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.704+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"执行截图任务-C","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.704+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.934+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-07-01T17:40:03.934+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:04.181+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:04.181+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:04.181+0800","caller":"utils/logger.go:96","msg":"开始处理截图工作流程 - 模式: C, 用户: 暂无候检者\n"}
{"level":"INFO","timestamp":"2025-07-01T17:40:04.560+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:04.802+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:15.829+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:40:15.829+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T17:40:15.829+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T17:40:15.829+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T17:40:15.829+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:16.043+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T17:40:16.043+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-01T17:40:16.043+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"暂无候检者","error":"task screenshot_B for user 暂无候检者 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:419\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T17:40:16.044+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T17:40:18.709+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:40:18.709+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T17:40:18.710+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T17:40:18.710+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T17:40:18.710+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:40:18.932+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"暂无候检者","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T17:40:18.932+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-01T17:40:18.932+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"暂无候检者","error":"task screenshot_C for user 暂无候检者 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:419\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T17:40:18.932+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T17:40:31.546+0800","caller":"utils/logger.go:96","msg":"OCR API返回值详情 - 校验后器官名称: 经腹在第2腰椎水平横截面, 键值对数量: 38, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_暂无候检者_20250701_174001.png\n"}
{"level":"INFO","timestamp":"2025-07-01T17:40:31.857+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"截图任务完成-B","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:40:31.858+0800","caller":"utils/logger.go:96","msg":"任务执行成功","taskID":"screenshot_B_1751362800227875400_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T17:40:31.858+0800","caller":"utils/logger.go:96","msg":"任务执行完成","taskID":"screenshot_B_1751362800227875400_暂无候检者","taskType":"screenshot_B","userName":"暂无候检者","duration":31.6305591,"workerID":0}
{"level":"INFO","timestamp":"2025-07-01T17:40:31.858+0800","caller":"utils/logger.go:96","msg":"任务执行成功 - TaskID: screenshot_B_1751362800227875400_暂无候检者, Type: screenshot_B, Worker: 0, Duration: 31.6305591s"}
{"level":"INFO","timestamp":"2025-07-01T17:40:31.858+0800","caller":"utils/logger.go:96","msg":"发送任务完成事件","taskID":"screenshot_B_1751362800227875400_暂无候检者","status":"completed"}
{"level":"INFO","timestamp":"2025-07-01T17:40:31.858+0800","caller":"utils/logger.go:96","msg":"收到任务完成通知 - 任务ID: screenshot_B_1751362800227875400_暂无候检者, 状态: completed"}
{"level":"INFO","timestamp":"2025-07-01T17:40:31.858+0800","caller":"utils/logger.go:96","msg":"任务执行成功，准备发送成功通知"}
{"level":"INFO","timestamp":"2025-07-01T17:40:31.858+0800","caller":"utils/logger.go:96","msg":"准备发送Toast通知 - 类型: success, 标题: 任务完成, 消息: 截图任务已完成"}
{"level":"INFO","timestamp":"2025-07-01T17:40:31.858+0800","caller":"utils/logger.go:96","msg":"Toast通知已发送到前端: 任务完成 - 截图任务已完成"}
{"level":"INFO","timestamp":"2025-07-01T17:40:58.006+0800","caller":"utils/logger.go:96","msg":"OCR API返回值详情 - 校验后器官名称: 经腹在第2腰椎水平横截面, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_暂无候检者_20250701_174004.png\n"}
{"level":"INFO","timestamp":"2025-07-01T17:40:58.016+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"截图任务完成-C","patient":"暂无候检者","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:40:58.016+0800","caller":"utils/logger.go:96","msg":"任务执行成功","taskID":"screenshot_C_1751362803704356400_暂无候检者","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T17:40:58.016+0800","caller":"utils/logger.go:96","msg":"任务执行完成","taskID":"screenshot_C_1751362803704356400_暂无候检者","taskType":"screenshot_C","userName":"暂无候检者","duration":54.3117359,"workerID":1}
{"level":"INFO","timestamp":"2025-07-01T17:40:58.016+0800","caller":"utils/logger.go:96","msg":"任务执行成功 - TaskID: screenshot_C_1751362803704356400_暂无候检者, Type: screenshot_C, Worker: 1, Duration: 54.3117359s"}
{"level":"INFO","timestamp":"2025-07-01T17:40:58.016+0800","caller":"utils/logger.go:96","msg":"发送任务完成事件","taskID":"screenshot_C_1751362803704356400_暂无候检者","status":"completed"}
{"level":"INFO","timestamp":"2025-07-01T17:40:58.016+0800","caller":"utils/logger.go:96","msg":"收到任务完成通知 - 任务ID: screenshot_C_1751362803704356400_暂无候检者, 状态: completed"}
{"level":"INFO","timestamp":"2025-07-01T17:40:58.016+0800","caller":"utils/logger.go:96","msg":"任务执行成功，准备发送成功通知"}
{"level":"INFO","timestamp":"2025-07-01T17:40:58.016+0800","caller":"utils/logger.go:96","msg":"准备发送Toast通知 - 类型: success, 标题: 任务完成, 消息: 截图任务已完成"}
{"level":"INFO","timestamp":"2025-07-01T17:40:58.016+0800","caller":"utils/logger.go:96","msg":"Toast通知已发送到前端: 任务完成 - 截图任务已完成"}
{"level":"INFO","timestamp":"2025-07-01T17:43:50.586+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":4}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.818+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.818+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.818+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.818+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.818+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.818+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T17:33:50.127+0800"}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.818+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"51808"}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.818+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":51808}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.818+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":51808}
{"level":"WARN","timestamp":"2025-07-01T17:45:44.818+0800","caller":"utils/logger.go:103","msg":"查找进程失败","pid":51808,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.818+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":51808}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.819+0800","caller":"utils/logger.go:96","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.819+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.819+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":51352}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.933+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.933+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.933+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.933+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:44.933+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.405+0800","caller":"utils/logger.go:96","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.405+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.406+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.406+0800","caller":"utils/logger.go:96","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.406+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.411+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.414+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.415+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.665+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.732+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.744+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.744+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.744+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.754+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.761+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.761+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T17:45:45.768+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.085+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.085+0800","caller":"utils/logger.go:96","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.086+0800","caller":"utils/logger.go:96","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.086+0800","caller":"utils/logger.go:96","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.086+0800","caller":"utils/logger.go:96","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.086+0800","caller":"utils/logger.go:96","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.086+0800","caller":"utils/logger.go:96","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.086+0800","caller":"utils/logger.go:96","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.086+0800","caller":"utils/logger.go:96","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.086+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.086+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.086+0800","caller":"utils/logger.go:96","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.086+0800","caller":"utils/logger.go:96","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.086+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.434+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.434+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:45:46.434+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:45:47.082+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:45:47.083+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T17:45:47.088+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:45:47.312+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:45:47.525+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T17:50:45.415+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T17:55:45.415+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T18:00:45.415+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T18:03:20.778+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-01T18:03:20.786+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:03:20.786+0800","caller":"utils/logger.go:96","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-01T18:03:20.786+0800","caller":"utils/logger.go:96","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T18:03:20.786+0800","caller":"utils/logger.go:96","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T18:03:20.786+0800","caller":"utils/logger.go:96","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-01T18:03:20.808+0800","caller":"utils/logger.go:96","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T18:03:20.808+0800","caller":"utils/logger.go:96","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T18:03:20.808+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T18:03:20.808+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T18:03:20.809+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.763+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.764+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.764+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.764+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.764+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.764+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.764+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.764+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":43180}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.870+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.870+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.870+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.870+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:09.870+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.320+0800","caller":"utils/logger.go:96","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.320+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.320+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.320+0800","caller":"utils/logger.go:96","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.320+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.326+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.327+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.328+0800","caller":"utils/logger.go:96","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.328+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.328+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.328+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.892+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.965+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.994+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.994+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.994+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.997+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:96","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:96","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:96","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:96","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:96","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:96","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:96","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:96","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:96","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:96","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:10.998+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:17:11.008+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T18:17:11.067+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:11.068+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T18:17:11.068+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T18:17:11.733+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:11.733+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:11.733+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:12.409+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:12.409+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:17:12.415+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:12.646+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:12.878+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:41.727+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:17:41.727+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T18:17:41.727+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T18:17:41.727+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T18:17:41.727+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.215+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.215+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.215+0800","caller":"utils/logger.go:96","msg":"任务创建成功","taskID":"screenshot_B_1751365062215094000_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.215+0800","caller":"utils/logger.go:96","msg":"任务提交成功","taskID":"screenshot_B_1751365062215094000_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.215+0800","caller":"utils/logger.go:96","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751365062215094000_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.215+0800","caller":"utils/logger.go:96","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751365062215094000_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.215+0800","caller":"utils/logger.go:96","msg":"开始处理任务","taskID":"screenshot_B_1751365062215094000_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.215+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.216+0800","caller":"utils/logger.go:96","msg":"执行任务处理器","taskID":"screenshot_B_1751365062215094000_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.216+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.216+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.567+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.567+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.746+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.746+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:42.746+0800","caller":"utils/logger.go:96","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T18:17:43.201+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:43.417+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.643+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.643+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.644+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.644+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.644+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.876+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.876+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.876+0800","caller":"utils/logger.go:96","msg":"任务创建成功","taskID":"screenshot_C_1751365066876190200_test-11200","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.876+0800","caller":"utils/logger.go:96","msg":"任务提交成功","taskID":"screenshot_C_1751365066876190200_test-11200","taskType":"screenshot_C","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.876+0800","caller":"utils/logger.go:96","msg":"开始处理任务","workerID":2,"taskID":"screenshot_C_1751365066876190200_test-11200","taskType":"screenshot_C","userName":"test-11200","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.876+0800","caller":"utils/logger.go:96","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751365066876190200_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.876+0800","caller":"utils/logger.go:96","msg":"开始处理任务","taskID":"screenshot_C_1751365066876190200_test-11200","taskType":"screenshot_C","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.876+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.876+0800","caller":"utils/logger.go:96","msg":"执行任务处理器","taskID":"screenshot_C_1751365066876190200_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.876+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"执行截图任务-C","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:17:46.876+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:47.106+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:test-11200] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-07-01T18:17:47.106+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:47.280+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:47.280+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:47.280+0800","caller":"utils/logger.go:96","msg":"开始处理截图工作流程 - 模式: C, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T18:17:47.671+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:17:47.884+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:18:07.990+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:18:07.990+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T18:18:07.990+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T18:18:07.990+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T18:18:07.990+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:18:08.236+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T18:18:08.236+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-01T18:18:08.236+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:420\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T18:18:08.236+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T18:18:12.106+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:18:12.106+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T18:18:12.106+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T18:18:12.106+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T18:18:12.106+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:18:12.397+0800","caller":"utils/logger.go:96","msg":"OCR API返回值详情 - 校验后器官名称: 消化系统--胰腺；十二指肠；正面图, 键值对数量: 28, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250701_181743.png\n"}
{"level":"INFO","timestamp":"2025-07-01T18:18:12.400+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T18:18:12.401+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-01T18:18:12.401+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:420\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T18:18:12.401+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T18:18:26.830+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:18:26.830+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T18:18:26.830+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T18:18:26.831+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T18:18:26.831+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:18:27.068+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T18:18:27.068+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-01T18:18:27.068+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:420\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T18:18:27.068+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T18:18:30.335+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:18:30.335+0800","caller":"utils/logger.go:96","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T18:18:30.335+0800","caller":"utils/logger.go:96","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T18:18:30.335+0800","caller":"utils/logger.go:96","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T18:18:30.335+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:18:30.609+0800","caller":"utils/logger.go:96","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T18:18:30.609+0800","caller":"utils/logger.go:96","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-01T18:18:30.609+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:420\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T18:18:30.609+0800","caller":"utils/logger.go:96","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T18:18:38.345+0800","caller":"utils/logger.go:96","msg":"OCR API返回值详情 - 校验后器官名称: 消化系统--胰腺；十二指肠；正面图, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_test-11200_20250701_181747.png\n"}
{"level":"INFO","timestamp":"2025-07-01T18:22:10.328+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.186+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.187+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.189+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.191+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.192+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.192+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.192+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.192+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.192+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.193+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.193+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.193+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.197+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.197+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.197+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.850+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.850+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.850+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.854+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.854+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.854+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.856+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.856+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:27.856+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.489+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.495+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.497+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.508+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.508+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.509+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.534+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.535+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.536+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.726+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.726+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.797+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.938+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:28.977+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.262+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.900+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.900+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.900+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.900+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.900+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.900+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T18:17:09.764+0800"}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.900+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"43180"}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.900+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":43180}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.900+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":43180}
{"level":"WARN","timestamp":"2025-07-01T18:26:29.900+0800","caller":"utils/logger.go:103","msg":"查找进程失败","pid":43180,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.900+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":43180}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.901+0800","caller":"utils/logger.go:96","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.901+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.901+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":50292}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.943+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.944+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.944+0800","caller":"utils/logger.go:96","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.944+0800","caller":"utils/logger.go:96","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:29.944+0800","caller":"utils/logger.go:96","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.366+0800","caller":"utils/logger.go:96","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.367+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.367+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.367+0800","caller":"utils/logger.go:96","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.367+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.371+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.373+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.373+0800","caller":"utils/logger.go:96","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.373+0800","caller":"utils/logger.go:96","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.373+0800","caller":"utils/logger.go:96","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.373+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.373+0800","caller":"utils/logger.go:96","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.373+0800","caller":"utils/logger.go:96","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.373+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.373+0800","caller":"utils/logger.go:96","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.373+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.373+0800","caller":"utils/logger.go:96","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.374+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.615+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.679+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.689+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.689+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.689+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.701+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.705+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.705+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.708+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.885+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.884+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:30.885+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.039+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.039+0800","caller":"utils/logger.go:96","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.039+0800","caller":"utils/logger.go:96","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.040+0800","caller":"utils/logger.go:96","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.040+0800","caller":"utils/logger.go:96","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.040+0800","caller":"utils/logger.go:96","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.040+0800","caller":"utils/logger.go:96","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.040+0800","caller":"utils/logger.go:96","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.040+0800","caller":"utils/logger.go:96","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.040+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.040+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.040+0800","caller":"utils/logger.go:96","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.040+0800","caller":"utils/logger.go:96","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.040+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.094+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.094+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.103+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.301+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:26:31.489+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:31:30.374+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T18:36:30.374+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T18:41:30.374+0800","caller":"utils/logger.go:96","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T18:42:02.386+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-01T18:42:02.402+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:42:02.402+0800","caller":"utils/logger.go:96","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-01T18:42:02.402+0800","caller":"utils/logger.go:96","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T18:42:02.402+0800","caller":"utils/logger.go:96","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T18:42:02.402+0800","caller":"utils/logger.go:96","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-01T18:42:02.423+0800","caller":"utils/logger.go:96","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T18:42:02.423+0800","caller":"utils/logger.go:96","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T18:42:02.423+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T18:42:02.423+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T18:42:02.424+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.374+0800","caller":"utils/logger.go:97","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.375+0800","caller":"utils/logger.go:97","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.375+0800","caller":"utils/logger.go:97","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.375+0800","caller":"utils/logger.go:97","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.375+0800","caller":"utils/logger.go:97","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.375+0800","caller":"utils/logger.go:97","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.375+0800","caller":"utils/logger.go:97","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.375+0800","caller":"utils/logger.go:97","msg":"写入当前PID到锁文件","pid":9684}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.478+0800","caller":"utils/logger.go:97","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.478+0800","caller":"utils/logger.go:97","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.478+0800","caller":"utils/logger.go:97","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.478+0800","caller":"utils/logger.go:97","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.478+0800","caller":"utils/logger.go:97","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.898+0800","caller":"utils/logger.go:97","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.899+0800","caller":"utils/logger.go:97","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.899+0800","caller":"utils/logger.go:97","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.899+0800","caller":"utils/logger.go:97","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.899+0800","caller":"utils/logger.go:97","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.904+0800","caller":"utils/logger.go:97","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.906+0800","caller":"utils/logger.go:97","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.906+0800","caller":"utils/logger.go:97","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.906+0800","caller":"utils/logger.go:97","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.907+0800","caller":"utils/logger.go:97","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.907+0800","caller":"utils/logger.go:97","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.907+0800","caller":"utils/logger.go:97","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.907+0800","caller":"utils/logger.go:97","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.907+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.907+0800","caller":"utils/logger.go:97","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.907+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.907+0800","caller":"utils/logger.go:97","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.907+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.907+0800","caller":"utils/logger.go:97","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.908+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.908+0800","caller":"utils/logger.go:97","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.908+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.908+0800","caller":"utils/logger.go:97","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.908+0800","caller":"utils/logger.go:97","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.908+0800","caller":"utils/logger.go:97","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.908+0800","caller":"utils/logger.go:97","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.908+0800","caller":"utils/logger.go:97","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.908+0800","caller":"utils/logger.go:97","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.908+0800","caller":"utils/logger.go:97","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.908+0800","caller":"utils/logger.go:97","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.909+0800","caller":"utils/logger.go:97","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.909+0800","caller":"utils/logger.go:97","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.909+0800","caller":"utils/logger.go:97","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.909+0800","caller":"utils/logger.go:97","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.909+0800","caller":"utils/logger.go:97","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.909+0800","caller":"utils/logger.go:97","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:32.910+0800","caller":"utils/logger.go:97","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.468+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.526+0800","caller":"utils/logger.go:97","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.536+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.536+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.536+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.546+0800","caller":"utils/logger.go:97","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.553+0800","caller":"utils/logger.go:97","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.553+0800","caller":"utils/logger.go:97","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.559+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.830+0800","caller":"utils/logger.go:97","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.831+0800","caller":"utils/logger.go:97","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.831+0800","caller":"utils/logger.go:97","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.831+0800","caller":"utils/logger.go:97","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.831+0800","caller":"utils/logger.go:97","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.831+0800","caller":"utils/logger.go:97","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.831+0800","caller":"utils/logger.go:97","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.831+0800","caller":"utils/logger.go:97","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.831+0800","caller":"utils/logger.go:97","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.831+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.831+0800","caller":"utils/logger.go:97","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.832+0800","caller":"utils/logger.go:97","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.832+0800","caller":"utils/logger.go:97","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:33.832+0800","caller":"utils/logger.go:97","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:46:34.260+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:34.260+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:34.260+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:34.875+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:34.875+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:46:34.883+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:35.144+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:35.377+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:54.762+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:46:54.762+0800","caller":"utils/logger.go:97","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T18:46:54.762+0800","caller":"utils/logger.go:97","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T18:46:54.763+0800","caller":"utils/logger.go:97","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T18:46:54.763+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.051+0800","caller":"utils/logger.go:97","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.051+0800","caller":"utils/logger.go:97","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.052+0800","caller":"utils/logger.go:97","msg":"任务创建成功","taskID":"screenshot_B_1751366815052336900_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.052+0800","caller":"utils/logger.go:97","msg":"任务提交成功","taskID":"screenshot_B_1751366815052336900_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.052+0800","caller":"utils/logger.go:97","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751366815052336900_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.052+0800","caller":"utils/logger.go:97","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751366815052336900_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.052+0800","caller":"utils/logger.go:97","msg":"开始处理任务","taskID":"screenshot_B_1751366815052336900_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.052+0800","caller":"utils/logger.go:97","msg":"执行任务处理器","taskID":"screenshot_B_1751366815052336900_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.052+0800","caller":"utils/logger.go:97","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.053+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.053+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.287+0800","caller":"utils/logger.go:97","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.288+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.523+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.523+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.523+0800","caller":"utils/logger.go:97","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T18:46:55.916+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:56.154+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.074+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.074+0800","caller":"utils/logger.go:97","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.074+0800","caller":"utils/logger.go:97","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.074+0800","caller":"utils/logger.go:97","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.074+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.248+0800","caller":"utils/logger.go:97","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.248+0800","caller":"utils/logger.go:97","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.248+0800","caller":"utils/logger.go:97","msg":"任务创建成功","taskID":"screenshot_C_1751366818248249300_test-11200","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.249+0800","caller":"utils/logger.go:97","msg":"任务提交成功","taskID":"screenshot_C_1751366818248249300_test-11200","taskType":"screenshot_C","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.249+0800","caller":"utils/logger.go:97","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751366818248249300_test-11200","taskType":"screenshot_C","userName":"test-11200","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.249+0800","caller":"utils/logger.go:97","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751366818248249300_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.249+0800","caller":"utils/logger.go:97","msg":"开始处理任务","taskID":"screenshot_C_1751366818248249300_test-11200","taskType":"screenshot_C","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.250+0800","caller":"utils/logger.go:97","msg":"执行任务处理器","taskID":"screenshot_C_1751366818248249300_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.250+0800","caller":"utils/logger.go:97","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.250+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"执行截图任务-C","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.250+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.469+0800","caller":"utils/logger.go:97","msg":"[操作:快捷键截图-模式C] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.469+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.708+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.708+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:58.708+0800","caller":"utils/logger.go:97","msg":"开始处理截图工作流程 - 模式: C, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T18:46:59.086+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:46:59.297+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:47:14.756+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:47:14.756+0800","caller":"utils/logger.go:97","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T18:47:14.756+0800","caller":"utils/logger.go:97","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T18:47:14.756+0800","caller":"utils/logger.go:97","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T18:47:14.756+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:47:15.021+0800","caller":"utils/logger.go:97","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T18:47:15.021+0800","caller":"utils/logger.go:97","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-01T18:47:15.021+0800","caller":"utils/logger.go:86","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:86\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:421\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T18:47:15.022+0800","caller":"utils/logger.go:97","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T18:47:18.594+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:47:18.594+0800","caller":"utils/logger.go:97","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T18:47:18.595+0800","caller":"utils/logger.go:97","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T18:47:18.595+0800","caller":"utils/logger.go:97","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T18:47:18.595+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:47:18.831+0800","caller":"utils/logger.go:97","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T18:47:18.831+0800","caller":"utils/logger.go:97","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-01T18:47:18.831+0800","caller":"utils/logger.go:86","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:86\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:421\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T18:47:18.832+0800","caller":"utils/logger.go:97","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T18:47:24.724+0800","caller":"utils/logger.go:97","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 40, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250701_184655.png\n"}
{"level":"INFO","timestamp":"2025-07-01T18:47:25.006+0800","caller":"utils/logger.go:97","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_184655.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751366845005707600"}
{"level":"INFO","timestamp":"2025-07-01T18:47:25.009+0800","caller":"utils/logger.go:97","msg":"轮次数据更新","userName":"test-11200","currentRound":1,"mode":"B","organName":"腹部第1腰椎水平截面","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751366845005707600"}
{"level":"INFO","timestamp":"2025-07-01T18:47:51.128+0800","caller":"utils/logger.go:97","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_test-11200_20250701_184658.png\n"}
{"level":"INFO","timestamp":"2025-07-01T18:47:51.132+0800","caller":"utils/logger.go:97","msg":"开始更新用户检测信息","userName":"test-11200","mode":"C","imagePath":"pic\\temp\\temp_screenshot_C_test-11200_20250701_184658.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_C_1751366871132027700"}
{"level":"INFO","timestamp":"2025-07-01T18:51:32.909+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T18:56:32.909+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.034+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.035+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.037+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.040+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.040+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.040+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.040+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.040+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.040+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.040+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.041+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.041+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.044+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.044+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.045+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.682+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.682+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.682+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.691+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.691+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.691+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.694+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.694+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:56.694+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.302+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.302+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.304+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.304+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.307+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.308+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.313+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.314+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.315+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.472+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.531+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.584+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.686+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.773+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:57.790+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.637+0800","caller":"utils/logger.go:97","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.637+0800","caller":"utils/logger.go:97","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.637+0800","caller":"utils/logger.go:97","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.637+0800","caller":"utils/logger.go:97","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.637+0800","caller":"utils/logger.go:97","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.638+0800","caller":"utils/logger.go:97","msg":"发现现有锁文件","size":4,"modified":"2025-07-01T18:46:32.375+0800"}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.638+0800","caller":"utils/logger.go:97","msg":"锁文件内容","pid":"9684"}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.638+0800","caller":"utils/logger.go:97","msg":"检查进程是否运行","pid":9684}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.638+0800","caller":"utils/logger.go:97","msg":"检查进程是否运行","pid":9684}
{"level":"WARN","timestamp":"2025-07-01T18:57:58.638+0800","caller":"utils/logger.go:104","msg":"查找进程失败","pid":9684,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.638+0800","caller":"utils/logger.go:97","msg":"进程未找到，清理旧锁文件","pid":9684}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.638+0800","caller":"utils/logger.go:97","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.638+0800","caller":"utils/logger.go:97","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.639+0800","caller":"utils/logger.go:97","msg":"写入当前PID到锁文件","pid":52952}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.670+0800","caller":"utils/logger.go:97","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.670+0800","caller":"utils/logger.go:97","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.670+0800","caller":"utils/logger.go:97","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.670+0800","caller":"utils/logger.go:97","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:58.670+0800","caller":"utils/logger.go:97","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.082+0800","caller":"utils/logger.go:97","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.083+0800","caller":"utils/logger.go:97","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.083+0800","caller":"utils/logger.go:97","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.083+0800","caller":"utils/logger.go:97","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.083+0800","caller":"utils/logger.go:97","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.089+0800","caller":"utils/logger.go:97","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.091+0800","caller":"utils/logger.go:97","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.091+0800","caller":"utils/logger.go:97","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.092+0800","caller":"utils/logger.go:97","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.092+0800","caller":"utils/logger.go:97","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.092+0800","caller":"utils/logger.go:97","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.092+0800","caller":"utils/logger.go:97","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.092+0800","caller":"utils/logger.go:97","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.092+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.092+0800","caller":"utils/logger.go:97","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.092+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.092+0800","caller":"utils/logger.go:97","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.092+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.093+0800","caller":"utils/logger.go:97","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.094+0800","caller":"utils/logger.go:97","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.094+0800","caller":"utils/logger.go:97","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.094+0800","caller":"utils/logger.go:97","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.094+0800","caller":"utils/logger.go:97","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.094+0800","caller":"utils/logger.go:97","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.303+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.365+0800","caller":"utils/logger.go:97","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.374+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.374+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.374+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.389+0800","caller":"utils/logger.go:97","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.393+0800","caller":"utils/logger.go:97","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.393+0800","caller":"utils/logger.go:97","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.397+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.581+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.581+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.581+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.847+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.847+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.853+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.856+0800","caller":"utils/logger.go:97","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.856+0800","caller":"utils/logger.go:97","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.857+0800","caller":"utils/logger.go:97","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.857+0800","caller":"utils/logger.go:97","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.857+0800","caller":"utils/logger.go:97","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.857+0800","caller":"utils/logger.go:97","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.857+0800","caller":"utils/logger.go:97","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.857+0800","caller":"utils/logger.go:97","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.857+0800","caller":"utils/logger.go:97","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.857+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.858+0800","caller":"utils/logger.go:97","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.858+0800","caller":"utils/logger.go:97","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.858+0800","caller":"utils/logger.go:97","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:57:59.858+0800","caller":"utils/logger.go:97","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T18:58:00.060+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:58:00.277+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.699+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.702+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.703+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.707+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.707+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.707+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.707+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.707+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.707+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.708+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.708+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.708+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.712+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.712+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.712+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.894+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.894+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.894+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.950+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.950+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.950+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.974+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.974+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:32.975+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.119+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.120+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.121+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.146+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.146+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.148+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.163+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.163+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.164+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.348+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.385+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.480+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.523+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.629+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T18:59:33.735+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T19:02:59.094+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T19:07:59.094+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T19:12:59.094+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T19:17:59.094+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T19:22:59.094+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T19:27:59.094+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T19:32:59.094+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T19:37:59.094+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T19:42:59.094+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T19:47:59.094+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T20:28:05.511+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T20:28:08.691+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T20:28:08.708+0800","caller":"utils/logger.go:97","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T20:28:08.711+0800","caller":"utils/logger.go:97","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T20:28:08.711+0800","caller":"utils/logger.go:97","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T20:28:08.712+0800","caller":"utils/logger.go:97","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T20:28:08.732+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T20:28:08.733+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T20:28:08.733+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T20:28:08.747+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:28:09.406+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:28:09.406+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:28:09.406+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:28:10.475+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:28:10.506+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T20:28:10.697+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:28:11.655+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:28:12.310+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:29:13.962+0800","caller":"utils/logger.go:97","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-01T20:29:13.978+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T20:29:13.978+0800","caller":"utils/logger.go:97","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-01T20:29:13.978+0800","caller":"utils/logger.go:97","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T20:29:13.978+0800","caller":"utils/logger.go:97","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T20:29:13.978+0800","caller":"utils/logger.go:97","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-01T20:29:14.013+0800","caller":"utils/logger.go:97","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T20:29:14.013+0800","caller":"utils/logger.go:97","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T20:29:14.013+0800","caller":"utils/logger.go:97","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T20:29:14.013+0800","caller":"utils/logger.go:97","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T20:29:14.013+0800","caller":"utils/logger.go:97","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.091+0800","caller":"utils/logger.go:97","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.091+0800","caller":"utils/logger.go:97","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.091+0800","caller":"utils/logger.go:97","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.091+0800","caller":"utils/logger.go:97","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.092+0800","caller":"utils/logger.go:97","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.092+0800","caller":"utils/logger.go:97","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.092+0800","caller":"utils/logger.go:97","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.092+0800","caller":"utils/logger.go:97","msg":"写入当前PID到锁文件","pid":52416}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.136+0800","caller":"utils/logger.go:97","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.136+0800","caller":"utils/logger.go:97","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.136+0800","caller":"utils/logger.go:97","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.136+0800","caller":"utils/logger.go:97","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:03.136+0800","caller":"utils/logger.go:97","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.121+0800","caller":"utils/logger.go:97","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.122+0800","caller":"utils/logger.go:97","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.122+0800","caller":"utils/logger.go:97","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.122+0800","caller":"utils/logger.go:97","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.122+0800","caller":"utils/logger.go:97","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.129+0800","caller":"utils/logger.go:97","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.131+0800","caller":"utils/logger.go:97","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.131+0800","caller":"utils/logger.go:97","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.132+0800","caller":"utils/logger.go:97","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.132+0800","caller":"utils/logger.go:97","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.132+0800","caller":"utils/logger.go:97","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.132+0800","caller":"utils/logger.go:97","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.132+0800","caller":"utils/logger.go:97","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.132+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.132+0800","caller":"utils/logger.go:97","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.133+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.133+0800","caller":"utils/logger.go:97","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.133+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.133+0800","caller":"utils/logger.go:97","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.133+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.133+0800","caller":"utils/logger.go:97","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.133+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.133+0800","caller":"utils/logger.go:97","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.133+0800","caller":"utils/logger.go:97","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.133+0800","caller":"utils/logger.go:97","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.133+0800","caller":"utils/logger.go:97","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.134+0800","caller":"utils/logger.go:97","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.134+0800","caller":"utils/logger.go:97","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.134+0800","caller":"utils/logger.go:97","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.134+0800","caller":"utils/logger.go:97","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.134+0800","caller":"utils/logger.go:97","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.134+0800","caller":"utils/logger.go:97","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.134+0800","caller":"utils/logger.go:97","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.135+0800","caller":"utils/logger.go:97","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.135+0800","caller":"utils/logger.go:97","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.135+0800","caller":"utils/logger.go:97","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.135+0800","caller":"utils/logger.go:97","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.707+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.771+0800","caller":"utils/logger.go:97","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.802+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.803+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.803+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.818+0800","caller":"utils/logger.go:97","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.844+0800","caller":"utils/logger.go:97","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.844+0800","caller":"utils/logger.go:97","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.844+0800","caller":"utils/logger.go:97","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.844+0800","caller":"utils/logger.go:97","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.844+0800","caller":"utils/logger.go:97","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.845+0800","caller":"utils/logger.go:97","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.845+0800","caller":"utils/logger.go:97","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.845+0800","caller":"utils/logger.go:97","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.845+0800","caller":"utils/logger.go:97","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.845+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.845+0800","caller":"utils/logger.go:97","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.845+0800","caller":"utils/logger.go:97","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.845+0800","caller":"utils/logger.go:97","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.845+0800","caller":"utils/logger.go:97","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.881+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.882+0800","caller":"utils/logger.go:97","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T20:30:04.882+0800","caller":"utils/logger.go:97","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T20:30:05.083+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:05.083+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:05.084+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:05.312+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:05.333+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T20:30:05.335+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:05.556+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:05.765+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.524+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.524+0800","caller":"utils/logger.go:97","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.524+0800","caller":"utils/logger.go:97","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.524+0800","caller":"utils/logger.go:97","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.524+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.755+0800","caller":"utils/logger.go:97","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.756+0800","caller":"utils/logger.go:97","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.756+0800","caller":"utils/logger.go:97","msg":"任务创建成功","taskID":"screenshot_B_1751373016756605600_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.756+0800","caller":"utils/logger.go:97","msg":"任务提交成功","taskID":"screenshot_B_1751373016756605600_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.756+0800","caller":"utils/logger.go:97","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751373016756605600_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.757+0800","caller":"utils/logger.go:97","msg":"开始处理任务","taskID":"screenshot_B_1751373016756605600_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.757+0800","caller":"utils/logger.go:97","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751373016756605600_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.757+0800","caller":"utils/logger.go:97","msg":"执行任务处理器","taskID":"screenshot_B_1751373016756605600_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.757+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.757+0800","caller":"utils/logger.go:97","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T20:30:16.757+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:17.007+0800","caller":"utils/logger.go:97","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T20:30:17.007+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:17.186+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:17.186+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:17.186+0800","caller":"utils/logger.go:97","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T20:30:17.635+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:17.817+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T20:30:48.220+0800","caller":"utils/logger.go:97","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 40, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250701_203017.png\n"}
{"level":"INFO","timestamp":"2025-07-01T20:30:48.499+0800","caller":"utils/logger.go:97","msg":"颜色检测成功完成","tempFilePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_203017.png"}
{"level":"INFO","timestamp":"2025-07-01T20:30:48.507+0800","caller":"utils/logger.go:97","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"B"}
{"level":"INFO","timestamp":"2025-07-01T20:30:48.510+0800","caller":"utils/logger.go:97","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T20:30:48.510+0800","caller":"utils/logger.go:97","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_203017.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751373048510379500"}
{"level":"INFO","timestamp":"2025-07-01T20:30:48.513+0800","caller":"utils/logger.go:97","msg":"轮次数据更新","userName":"test-11200","currentRound":1,"mode":"B","organName":"腹部第1腰椎水平截面","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751373048510379500"}
{"level":"INFO","timestamp":"2025-07-01T20:35:04.135+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T20:40:04.135+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T20:45:04.134+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T20:50:04.135+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T20:55:04.135+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T21:00:04.135+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T21:05:04.134+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T21:10:04.135+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.541+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.543+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.546+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.552+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.552+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.552+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.552+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.552+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.553+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.553+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.553+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.553+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.557+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.560+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:17.560+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.177+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.178+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.178+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.184+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.185+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.185+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.191+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.191+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.191+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.856+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.856+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.858+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.863+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.863+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.864+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.874+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.874+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T21:13:18.878+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:19.029+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:19.033+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:19.062+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:19.245+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:19.268+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:19.304+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.549+0800","caller":"utils/logger.go:97","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.549+0800","caller":"utils/logger.go:97","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.549+0800","caller":"utils/logger.go:97","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.549+0800","caller":"utils/logger.go:97","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.549+0800","caller":"utils/logger.go:97","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.549+0800","caller":"utils/logger.go:97","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T20:30:03.092+0800"}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.549+0800","caller":"utils/logger.go:97","msg":"锁文件内容","pid":"52416"}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.549+0800","caller":"utils/logger.go:97","msg":"检查进程是否运行","pid":52416}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.549+0800","caller":"utils/logger.go:97","msg":"检查进程是否运行","pid":52416}
{"level":"WARN","timestamp":"2025-07-01T21:13:20.551+0800","caller":"utils/logger.go:104","msg":"查找进程失败","pid":52416,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.551+0800","caller":"utils/logger.go:97","msg":"进程未找到，清理旧锁文件","pid":52416}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.551+0800","caller":"utils/logger.go:97","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.551+0800","caller":"utils/logger.go:97","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.551+0800","caller":"utils/logger.go:97","msg":"写入当前PID到锁文件","pid":11496}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.573+0800","caller":"utils/logger.go:97","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.574+0800","caller":"utils/logger.go:97","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.574+0800","caller":"utils/logger.go:97","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.574+0800","caller":"utils/logger.go:97","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:20.574+0800","caller":"utils/logger.go:97","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.078+0800","caller":"utils/logger.go:97","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.079+0800","caller":"utils/logger.go:97","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.079+0800","caller":"utils/logger.go:97","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.079+0800","caller":"utils/logger.go:97","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.079+0800","caller":"utils/logger.go:97","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.084+0800","caller":"utils/logger.go:97","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.087+0800","caller":"utils/logger.go:97","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.087+0800","caller":"utils/logger.go:97","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.089+0800","caller":"utils/logger.go:97","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.089+0800","caller":"utils/logger.go:97","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.089+0800","caller":"utils/logger.go:97","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.089+0800","caller":"utils/logger.go:97","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.089+0800","caller":"utils/logger.go:97","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.089+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.089+0800","caller":"utils/logger.go:97","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.089+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.090+0800","caller":"utils/logger.go:97","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.090+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.090+0800","caller":"utils/logger.go:97","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.090+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.090+0800","caller":"utils/logger.go:97","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.090+0800","caller":"utils/logger.go:97","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.091+0800","caller":"utils/logger.go:97","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.091+0800","caller":"utils/logger.go:97","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.091+0800","caller":"utils/logger.go:97","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.091+0800","caller":"utils/logger.go:97","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.091+0800","caller":"utils/logger.go:97","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.091+0800","caller":"utils/logger.go:97","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.092+0800","caller":"utils/logger.go:97","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.092+0800","caller":"utils/logger.go:97","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.093+0800","caller":"utils/logger.go:97","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.092+0800","caller":"utils/logger.go:97","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.092+0800","caller":"utils/logger.go:97","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.094+0800","caller":"utils/logger.go:97","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.094+0800","caller":"utils/logger.go:97","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.094+0800","caller":"utils/logger.go:97","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.094+0800","caller":"utils/logger.go:97","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.362+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.427+0800","caller":"utils/logger.go:97","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.437+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.437+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.437+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.452+0800","caller":"utils/logger.go:97","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.462+0800","caller":"utils/logger.go:97","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.463+0800","caller":"utils/logger.go:97","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.465+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.711+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.711+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.711+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.795+0800","caller":"utils/logger.go:97","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.795+0800","caller":"utils/logger.go:97","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.795+0800","caller":"utils/logger.go:97","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.795+0800","caller":"utils/logger.go:97","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.795+0800","caller":"utils/logger.go:97","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.796+0800","caller":"utils/logger.go:97","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.796+0800","caller":"utils/logger.go:97","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.796+0800","caller":"utils/logger.go:97","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.796+0800","caller":"utils/logger.go:97","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.796+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.796+0800","caller":"utils/logger.go:97","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.796+0800","caller":"utils/logger.go:97","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.796+0800","caller":"utils/logger.go:97","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:21.796+0800","caller":"utils/logger.go:97","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T21:13:22.005+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:22.006+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T21:13:22.013+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:22.250+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:22.461+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.273+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.275+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.278+0800","caller":"utils/logger.go:97","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.281+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.281+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.281+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.281+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.281+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.281+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.282+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.282+0800","caller":"utils/logger.go:97","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.282+0800","caller":"utils/logger.go:97","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.284+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.284+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.285+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.481+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.481+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.482+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.512+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.512+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.512+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.870+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.870+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.870+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.931+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.932+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T21:13:27.934+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:28.062+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:28.062+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T21:13:28.063+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:28.094+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:28.095+0800","caller":"utils/logger.go:97","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T21:13:28.096+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:28.160+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:28.290+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:28.313+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:28.378+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:28.536+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:13:28.559+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:26.780+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T21:15:26.780+0800","caller":"utils/logger.go:97","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T21:15:26.781+0800","caller":"utils/logger.go:97","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T21:15:26.781+0800","caller":"utils/logger.go:97","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T21:15:26.781+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.050+0800","caller":"utils/logger.go:97","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.050+0800","caller":"utils/logger.go:97","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.051+0800","caller":"utils/logger.go:97","msg":"任务创建成功","taskID":"screenshot_B_1751375727051142000_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.051+0800","caller":"utils/logger.go:97","msg":"任务提交成功","taskID":"screenshot_B_1751375727051142000_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.051+0800","caller":"utils/logger.go:97","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751375727051142000_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.051+0800","caller":"utils/logger.go:97","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751375727051142000_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.051+0800","caller":"utils/logger.go:97","msg":"开始处理任务","taskID":"screenshot_B_1751375727051142000_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.051+0800","caller":"utils/logger.go:97","msg":"执行任务处理器","taskID":"screenshot_B_1751375727051142000_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.051+0800","caller":"utils/logger.go:97","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.051+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.052+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.226+0800","caller":"utils/logger.go:97","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.226+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.451+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.451+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:27.451+0800","caller":"utils/logger.go:97","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T21:15:28.168+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:28.384+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.730+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.730+0800","caller":"utils/logger.go:97","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.730+0800","caller":"utils/logger.go:97","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.730+0800","caller":"utils/logger.go:97","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.730+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.987+0800","caller":"utils/logger.go:97","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.987+0800","caller":"utils/logger.go:97","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.987+0800","caller":"utils/logger.go:97","msg":"任务创建成功","taskID":"screenshot_C_1751375731987710500_test-11200","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.988+0800","caller":"utils/logger.go:97","msg":"任务提交成功","taskID":"screenshot_C_1751375731987710500_test-11200","taskType":"screenshot_C","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.988+0800","caller":"utils/logger.go:97","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751375731987710500_test-11200","taskType":"screenshot_C","userName":"test-11200","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.988+0800","caller":"utils/logger.go:97","msg":"开始处理任务","taskID":"screenshot_C_1751375731987710500_test-11200","taskType":"screenshot_C","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.988+0800","caller":"utils/logger.go:97","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751375731987710500_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.988+0800","caller":"utils/logger.go:97","msg":"执行任务处理器","taskID":"screenshot_C_1751375731987710500_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.988+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"执行截图任务-C","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.988+0800","caller":"utils/logger.go:97","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T21:15:31.988+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:32.217+0800","caller":"utils/logger.go:97","msg":"[操作:快捷键截图-模式C] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T21:15:32.217+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:32.452+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:32.452+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:32.452+0800","caller":"utils/logger.go:97","msg":"开始处理截图工作流程 - 模式: C, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T21:15:32.870+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:15:33.084+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T21:16:01.829+0800","caller":"utils/logger.go:97","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 40, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250701_211527.png\n"}
{"level":"INFO","timestamp":"2025-07-01T21:16:02.119+0800","caller":"utils/logger.go:97","msg":"颜色检测成功完成","tempFilePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_211527.png"}
{"level":"INFO","timestamp":"2025-07-01T21:16:02.126+0800","caller":"utils/logger.go:97","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"B"}
{"level":"INFO","timestamp":"2025-07-01T21:16:02.128+0800","caller":"utils/logger.go:97","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T21:16:02.129+0800","caller":"utils/logger.go:97","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_211527.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751375762129898800"}
{"level":"INFO","timestamp":"2025-07-01T21:16:02.133+0800","caller":"utils/logger.go:97","msg":"轮次数据更新","userName":"test-11200","currentRound":1,"mode":"B","organName":"腹部第1腰椎水平截面","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751375762129898800"}
{"level":"INFO","timestamp":"2025-07-01T21:16:28.411+0800","caller":"utils/logger.go:97","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_test-11200_20250701_211532.png\n"}
{"level":"INFO","timestamp":"2025-07-01T21:16:28.412+0800","caller":"utils/logger.go:97","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"C"}
{"level":"INFO","timestamp":"2025-07-01T21:16:28.414+0800","caller":"utils/logger.go:97","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T21:16:28.414+0800","caller":"utils/logger.go:97","msg":"开始更新用户检测信息","userName":"test-11200","mode":"C","imagePath":"pic\\temp\\temp_screenshot_C_test-11200_20250701_211532.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_C_1751375788414344700"}
{"level":"INFO","timestamp":"2025-07-01T21:18:21.093+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T21:23:21.093+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T21:28:21.093+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T21:33:21.093+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T21:38:21.093+0800","caller":"utils/logger.go:97","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T21:40:36.489+0800","caller":"utils/logger.go:97","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-01T21:40:36.490+0800","caller":"utils/logger.go:75","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T21:40:36.490+0800","caller":"utils/logger.go:97","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-01T21:40:36.490+0800","caller":"utils/logger.go:97","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T21:40:36.490+0800","caller":"utils/logger.go:97","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T21:40:36.490+0800","caller":"utils/logger.go:97","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-01T21:40:36.529+0800","caller":"utils/logger.go:97","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T21:40:36.530+0800","caller":"utils/logger.go:97","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T21:40:36.530+0800","caller":"utils/logger.go:97","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T21:40:36.531+0800","caller":"utils/logger.go:97","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T21:40:36.532+0800","caller":"utils/logger.go:97","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
