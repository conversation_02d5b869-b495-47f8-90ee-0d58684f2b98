{"logId": "46eff436-7d11-4ff8-9b3c-c8af33eb49ff", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 1, "label": "image", "score": 0.7220432162284851, "coordinate": [4.0693817138671875, 1.0797865390777588, 766.005126953125, 1713.66650390625]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[642, 7], [714, 7], [714, 34], [642, 34]], [[103, 18], [203, 18], [203, 43], [103, 43]], [[735, 11], [748, 11], [748, 32], [735, 32]], [[2, 39], [15, 46], [6, 64], [0, 57]], [[123, 50], [248, 58], [246, 85], [121, 76]], [[530, 46], [559, 46], [559, 70], [530, 70]], [[122, 86], [355, 91], [354, 122], [122, 116]], [[530, 82], [559, 82], [559, 105], [530, 105]], [[644, 80], [735, 80], [735, 107], [644, 107]], [[0, 102], [13, 102], [13, 127], [0, 127]], [[528, 118], [559, 118], [559, 141], [528, 141]], [[669, 114], [768, 118], [767, 145], [668, 141]], [[107, 130], [129, 130], [129, 147], [107, 147]], [[127, 130], [205, 130], [205, 150], [127, 150]], [[441, 134], [450, 134], [450, 143], [441, 143]], [[0, 157], [13, 157], [13, 182], [0, 182]], [[106, 159], [286, 163], [286, 193], [105, 189]], [[530, 154], [559, 154], [559, 177], [530, 177]], [[668, 150], [768, 150], [768, 181], [668, 181]], [[530, 189], [559, 189], [559, 214], [530, 214]], [[667, 184], [768, 188], [767, 218], [666, 214]], [[106, 196], [299, 200], [299, 225], [105, 221]], [[530, 225], [559, 225], [559, 250], [530, 250]], [[668, 223], [766, 223], [766, 248], [668, 248]], [[0, 241], [13, 241], [13, 268], [0, 268]], [[113, 243], [124, 243], [124, 252], [113, 252]], [[124, 234], [314, 236], [314, 261], [124, 259]], [[443, 243], [452, 243], [452, 254], [443, 254]], [[530, 261], [559, 261], [559, 286], [530, 286]], [[669, 259], [768, 263], [767, 292], [668, 287]], [[126, 270], [318, 274], [317, 299], [125, 295]], [[530, 297], [559, 297], [559, 322], [530, 322]], [[668, 295], [768, 295], [768, 325], [668, 325]], [[109, 311], [127, 311], [127, 325], [109, 325]], [[122, 305], [310, 309], [310, 334], [122, 331]], [[528, 332], [559, 332], [559, 358], [528, 358]], [[651, 331], [667, 354], [652, 363], [636, 340]], [[111, 347], [129, 347], [129, 361], [111, 361]], [[120, 341], [320, 345], [319, 370], [120, 366]], [[528, 370], [559, 370], [559, 395], [528, 395]], [[642, 366], [768, 366], [768, 397], [642, 397]], [[104, 377], [320, 381], [319, 406], [103, 402]], [[526, 404], [563, 404], [563, 433], [526, 433]], [[666, 400], [768, 404], [768, 435], [664, 430]], [[105, 413], [353, 417], [352, 442], [105, 438]], [[441, 420], [452, 420], [452, 436], [441, 436]], [[528, 442], [559, 442], [559, 465], [528, 465]], [[668, 437], [768, 444], [768, 471], [666, 464]], [[79, 450], [175, 450], [175, 475], [79, 475]], [[528, 477], [559, 477], [559, 502], [528, 502]], [[666, 474], [768, 474], [768, 504], [666, 504]], [[59, 492], [68, 492], [68, 500], [59, 500]], [[67, 484], [157, 488], [156, 513], [66, 509]], [[528, 513], [559, 513], [559, 538], [528, 538]], [[666, 507], [768, 512], [768, 542], [664, 538]], [[74, 524], [168, 524], [168, 552], [74, 552]], [[98, 554], [344, 558], [343, 588], [98, 584]], [[526, 547], [561, 547], [561, 577], [526, 577]], [[96, 591], [279, 595], [278, 626], [96, 622]], [[528, 586], [559, 586], [559, 610], [528, 610]], [[626, 582], [644, 605], [627, 617], [610, 595]], [[75, 599], [94, 591], [101, 610], [83, 617]], [[528, 622], [559, 622], [559, 647], [528, 647]], [[617, 616], [768, 621], [767, 651], [616, 647]], [[102, 629], [443, 629], [443, 654], [102, 654]], [[528, 658], [559, 658], [559, 683], [528, 683]], [[641, 652], [768, 656], [768, 687], [640, 683]], [[102, 665], [292, 669], [291, 694], [101, 690]], [[528, 694], [559, 694], [559, 719], [528, 719]], [[641, 686], [768, 690], [768, 722], [640, 718]], [[98, 699], [275, 703], [275, 733], [98, 729]], [[69, 736], [154, 740], [152, 765], [68, 761]], [[528, 729], [559, 729], [559, 754], [528, 754]], [[618, 729], [641, 729], [641, 756], [618, 756]], [[72, 774], [186, 774], [186, 799], [72, 799]], [[528, 765], [559, 765], [559, 790], [528, 790]], [[618, 765], [768, 765], [768, 790], [618, 790]], [[70, 808], [290, 812], [290, 837], [70, 833]], [[528, 803], [559, 803], [559, 826], [528, 826]], [[643, 799], [735, 803], [734, 830], [642, 825]], [[100, 845], [235, 849], [234, 874], [99, 870]], [[522, 865], [569, 865], [569, 887], [522, 887]], [[574, 863], [602, 863], [602, 885], [574, 885]], [[644, 862], [692, 862], [692, 890], [644, 890]], [[726, 863], [768, 863], [768, 890], [726, 890]], [[100, 879], [253, 883], [252, 914], [99, 910]], [[102, 916], [150, 921], [147, 948], [99, 943]], [[522, 910], [757, 910], [757, 933], [522, 933]], [[524, 937], [757, 937], [757, 960], [524, 960]], [[98, 951], [255, 955], [254, 985], [98, 981]], [[521, 962], [766, 964], [766, 989], [521, 987]], [[72, 988], [238, 992], [238, 1017], [72, 1013]], [[524, 990], [757, 990], [757, 1014], [524, 1014]], [[71, 1024], [141, 1028], [139, 1055], [70, 1051]], [[522, 1017], [757, 1017], [757, 1040], [522, 1040]], [[521, 1042], [764, 1044], [764, 1069], [521, 1067]], [[81, 1065], [159, 1065], [159, 1090], [81, 1090]], [[524, 1071], [757, 1071], [757, 1096], [524, 1096]], [[78, 1099], [148, 1099], [148, 1124], [78, 1124]], [[522, 1098], [757, 1098], [757, 1123], [522, 1123]], [[523, 1122], [757, 1124], [757, 1149], [522, 1148]], [[98, 1133], [358, 1137], [358, 1162], [98, 1158]], [[521, 1149], [761, 1149], [761, 1174], [521, 1174]], [[96, 1169], [275, 1173], [275, 1198], [96, 1194]], [[524, 1180], [766, 1180], [766, 1205], [524, 1205]], [[79, 1210], [98, 1210], [98, 1226], [79, 1226]], [[103, 1208], [196, 1208], [196, 1233], [103, 1233]], [[73, 1242], [174, 1246], [173, 1271], [72, 1267]], [[522, 1233], [755, 1233], [755, 1257], [522, 1257]], [[522, 1260], [755, 1260], [755, 1283], [522, 1283]], [[103, 1280], [179, 1280], [179, 1307], [103, 1307]], [[521, 1285], [755, 1287], [755, 1312], [521, 1310]], [[105, 1319], [175, 1319], [175, 1339], [105, 1339]], [[521, 1312], [755, 1314], [755, 1339], [521, 1337]], [[521, 1339], [753, 1339], [753, 1364], [521, 1364]], [[79, 1357], [100, 1357], [100, 1371], [79, 1371]], [[92, 1351], [303, 1353], [303, 1378], [92, 1376]], [[524, 1367], [757, 1367], [757, 1392], [524, 1392]], [[71, 1385], [183, 1389], [182, 1416], [70, 1412]], [[522, 1394], [762, 1394], [762, 1419], [522, 1419]], [[79, 1428], [100, 1428], [100, 1443], [79, 1443]], [[103, 1425], [264, 1425], [264, 1450], [103, 1450]], [[522, 1423], [766, 1423], [766, 1448], [522, 1448]], [[76, 1460], [102, 1460], [102, 1482], [76, 1482]], [[100, 1460], [223, 1460], [223, 1485], [100, 1485]], [[524, 1476], [757, 1476], [757, 1500], [524, 1500]], [[52, 1502], [127, 1502], [127, 1534], [52, 1534]], [[522, 1503], [766, 1503], [766, 1528], [522, 1528]], [[524, 1532], [764, 1532], [764, 1555], [524, 1555]], [[52, 1560], [146, 1560], [146, 1589], [52, 1589]], [[522, 1557], [766, 1557], [766, 1582], [522, 1582]], [[521, 1609], [755, 1611], [755, 1636], [521, 1634]], [[57, 1625], [66, 1625], [66, 1637], [57, 1637]], [[78, 1625], [109, 1625], [109, 1646], [78, 1646]], [[521, 1637], [753, 1637], [753, 1662], [521, 1662]], [[522, 1666], [755, 1666], [755, 1689], [522, 1689]], [[54, 1677], [229, 1677], [229, 1702], [54, 1702]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["\"ocr\"", " models", "-", "S", "c config.go", "46", "co user_checking_info.go", "47", "\"coze\":", "送", "48", "\"token\":", "口", "services", "-", "冈", " ∞ api_service.go", "49", "\"workflo", "50", "\"workflo", "• ∞o color_detect.go", "51", "\"workflo", "0", "", "∞config_service.go", "6", "52", "\"space_i", "co hotkey_service.go", "53", "\"app_id\"", "•", "∞ ocr_api_client.go", "54", "～", "•", "co patient_service.go", "55", "\"cloud_fur", " ∞ qrcode_service.go", "56", "\"registr", "• co screenshot_service.go", "2", "57", "\"screens", ">□ utils", "58", "\"siteInf", "、", "build", "59", "\"mark_pa", "config", "{} app_config_backup.json", "60", "{ } app_config.json", "61", "~", "·", "62", "\"device_info", "{} detection_lndicator_organ_part.json", "63", "\"mac_addre", "{} developmentt.jon", "64", "\"device_na", "{ } production.json", "□ docs", "65", "},", "□frontend", "66", "\"user_info\":", "hc-actions-CloudObj", "67", "\"name\":", "JS index.obj.js", "问题", "29", "输出", "调试", "{ } package.json", "logs", "2025/06/2814:18:47", "2025/06/2815:01:30", "operation.log", "ndDevice\": contextd", "□ node_modules", "2025/06/2815:01:30", "□pic", "2025/06/2815:01:31", "ndDevice\":dial tcp:", "temp", "2025/06/2815:01:31", "test", "2025/06/2815:01:34", "2025/06/2815:01:34", "□ screenshot_responsebody", "no\":\"00155deda58\",'", "□ word_color_test", "TRA I json call resu", "P", ".giti<PERSON>re", "∞ app.go", "2025/06/2815:01:34", "2025/06/2815:01:34", "go.mod", "2025/06/2815:01:34", "go.sum", "2025/06/2815:01:34", "2025/06/2815:01:34", "T", "MagneticOperator.exe", "2025/06/2815:01:34", "co main.go", "no\":\"00155ded4a58\",\"", "{0}", "package-lock.json", "TRA I json call resu", "{}", "package.json", "2025/06/2815:01:34", "〉大纲", "TRA I json call resu", "registration_number\"", "时间线", "d_number\":\"\"}],\"name", "2025/06/2816:01:01", ">", "go", "2025/06/2816:01:01", "2025/06/2816:01:01", "> Package Outline"], "rec_scores": [0.9647486805915833, 0.9719145894050598, 0.5008828043937683, 0.3291219472885132, 0.9130756855010986, 0.9999467134475708, 0.9894292950630188, 0.9998105764389038, 0.988666832447052, 0.19637027382850647, 0.9999289512634277, 0.9046748876571655, 0.11436238139867783, 0.994049072265625, 0.2362561970949173, 0.2773376703262329, 0.9138530492782593, 0.9997539520263672, 0.9298732876777649, 0.9996223449707031, 0.8916900157928467, 0.920937716960907, 0.9997561573982239, 0.8677068948745728, 0.5425348281860352, 0.0, 0.9806137084960938, 0.9984459280967712, 0.999801754951477, 0.9520887136459351, 0.9583551287651062, 0.9998747706413269, 0.9631944894790649, 0.8698360323905945, 0.9910145998001099, 0.999722957611084, 0.601096510887146, 0.8067896366119385, 0.9836767315864563, 0.9997850656509399, 0.9730798006057739, 0.9771324396133423, 0.9999437928199768, 0.9259347319602966, 0.9374021291732788, 0.999466598033905, 0.9995957016944885, 0.9783378839492798, 0.9491788744926453, 0.9998364448547363, 0.9312067031860352, 0.17189311981201172, 0.9986844062805176, 0.9997432827949524, 0.9563143253326416, 0.9997372627258301, 0.9477988481521606, 0.9996031522750854, 0.9880648255348206, 0.9998966455459595, 0.8404320478439331, 0.5372580289840698, 0.9999085068702698, 0.7890241742134094, 0.9719384908676147, 0.9998920559883118, 0.9770612716674805, 0.9514310956001282, 0.9998770356178284, 0.9326475858688354, 0.9716241955757141, 0.8491513729095459, 0.9998545050621033, 0.689979076385498, 0.9694817662239075, 0.9999468922615051, 0.9461802840232849, 0.996634840965271, 0.9999384880065918, 0.9735135436058044, 0.989496648311615, 0.9998981952667236, 0.9997766613960266, 0.9999256730079651, 0.9994556903839111, 0.9800451993942261, 0.994874119758606, 0.9922009706497192, 0.9921393990516663, 0.9997831583023071, 0.9484136700630188, 0.9294847846031189, 0.989801824092865, 0.8836802244186401, 0.9842316508293152, 0.9608214497566223, 0.9996975660324097, 0.9946190714836121, 0.9992888569831848, 0.9914373755455017, 0.9918375015258789, 0.9563020467758179, 0.9506404995918274, 0.9461812376976013, 0.9846611022949219, 0.8725367188453674, 0.9995608329772949, 0.9206317663192749, 0.9946634769439697, 0.9916746616363525, 0.9998114705085754, 0.9908696413040161, 0.9987942576408386, 0.9908689856529236, 0.9885356426239014, 0.09931798279285431, 0.9917036294937134, 0.9919834136962891, 0.9592992663383484, 0.9333457946777344, 0.7435922622680664, 0.9996537566184998, 0.9244518280029297, 0.6778736710548401, 0.9997567534446716, 0.9897674918174744, 0.7914684414863586, 0.9222339391708374, 0.9977246522903442, 0.9995980858802795, 0.9785019755363464, 0.9921953678131104, 0.6590086221694946, 0.9992787837982178, 0.98940110206604, 0.9935505986213684, 0.9824883937835693], "rec_polys": [[[642, 7], [714, 7], [714, 34], [642, 34]], [[103, 18], [203, 18], [203, 43], [103, 43]], [[735, 11], [748, 11], [748, 32], [735, 32]], [[2, 39], [15, 46], [6, 64], [0, 57]], [[123, 50], [248, 58], [246, 85], [121, 76]], [[530, 46], [559, 46], [559, 70], [530, 70]], [[122, 86], [355, 91], [354, 122], [122, 116]], [[530, 82], [559, 82], [559, 105], [530, 105]], [[644, 80], [735, 80], [735, 107], [644, 107]], [[0, 102], [13, 102], [13, 127], [0, 127]], [[528, 118], [559, 118], [559, 141], [528, 141]], [[669, 114], [768, 118], [767, 145], [668, 141]], [[107, 130], [129, 130], [129, 147], [107, 147]], [[127, 130], [205, 130], [205, 150], [127, 150]], [[441, 134], [450, 134], [450, 143], [441, 143]], [[0, 157], [13, 157], [13, 182], [0, 182]], [[106, 159], [286, 163], [286, 193], [105, 189]], [[530, 154], [559, 154], [559, 177], [530, 177]], [[668, 150], [768, 150], [768, 181], [668, 181]], [[530, 189], [559, 189], [559, 214], [530, 214]], [[667, 184], [768, 188], [767, 218], [666, 214]], [[106, 196], [299, 200], [299, 225], [105, 221]], [[530, 225], [559, 225], [559, 250], [530, 250]], [[668, 223], [766, 223], [766, 248], [668, 248]], [[0, 241], [13, 241], [13, 268], [0, 268]], [[113, 243], [124, 243], [124, 252], [113, 252]], [[124, 234], [314, 236], [314, 261], [124, 259]], [[443, 243], [452, 243], [452, 254], [443, 254]], [[530, 261], [559, 261], [559, 286], [530, 286]], [[669, 259], [768, 263], [767, 292], [668, 287]], [[126, 270], [318, 274], [317, 299], [125, 295]], [[530, 297], [559, 297], [559, 322], [530, 322]], [[668, 295], [768, 295], [768, 325], [668, 325]], [[109, 311], [127, 311], [127, 325], [109, 325]], [[122, 305], [310, 309], [310, 334], [122, 331]], [[528, 332], [559, 332], [559, 358], [528, 358]], [[651, 331], [667, 354], [652, 363], [636, 340]], [[111, 347], [129, 347], [129, 361], [111, 361]], [[120, 341], [320, 345], [319, 370], [120, 366]], [[528, 370], [559, 370], [559, 395], [528, 395]], [[642, 366], [768, 366], [768, 397], [642, 397]], [[104, 377], [320, 381], [319, 406], [103, 402]], [[526, 404], [563, 404], [563, 433], [526, 433]], [[666, 400], [768, 404], [768, 435], [664, 430]], [[105, 413], [353, 417], [352, 442], [105, 438]], [[441, 420], [452, 420], [452, 436], [441, 436]], [[528, 442], [559, 442], [559, 465], [528, 465]], [[668, 437], [768, 444], [768, 471], [666, 464]], [[79, 450], [175, 450], [175, 475], [79, 475]], [[528, 477], [559, 477], [559, 502], [528, 502]], [[666, 474], [768, 474], [768, 504], [666, 504]], [[59, 492], [68, 492], [68, 500], [59, 500]], [[67, 484], [157, 488], [156, 513], [66, 509]], [[528, 513], [559, 513], [559, 538], [528, 538]], [[666, 507], [768, 512], [768, 542], [664, 538]], [[74, 524], [168, 524], [168, 552], [74, 552]], [[98, 554], [344, 558], [343, 588], [98, 584]], [[526, 547], [561, 547], [561, 577], [526, 577]], [[96, 591], [279, 595], [278, 626], [96, 622]], [[528, 586], [559, 586], [559, 610], [528, 610]], [[626, 582], [644, 605], [627, 617], [610, 595]], [[75, 599], [94, 591], [101, 610], [83, 617]], [[528, 622], [559, 622], [559, 647], [528, 647]], [[617, 616], [768, 621], [767, 651], [616, 647]], [[102, 629], [443, 629], [443, 654], [102, 654]], [[528, 658], [559, 658], [559, 683], [528, 683]], [[641, 652], [768, 656], [768, 687], [640, 683]], [[102, 665], [292, 669], [291, 694], [101, 690]], [[528, 694], [559, 694], [559, 719], [528, 719]], [[641, 686], [768, 690], [768, 722], [640, 718]], [[98, 699], [275, 703], [275, 733], [98, 729]], [[69, 736], [154, 740], [152, 765], [68, 761]], [[528, 729], [559, 729], [559, 754], [528, 754]], [[618, 729], [641, 729], [641, 756], [618, 756]], [[72, 774], [186, 774], [186, 799], [72, 799]], [[528, 765], [559, 765], [559, 790], [528, 790]], [[618, 765], [768, 765], [768, 790], [618, 790]], [[70, 808], [290, 812], [290, 837], [70, 833]], [[528, 803], [559, 803], [559, 826], [528, 826]], [[643, 799], [735, 803], [734, 830], [642, 825]], [[100, 845], [235, 849], [234, 874], [99, 870]], [[522, 865], [569, 865], [569, 887], [522, 887]], [[574, 863], [602, 863], [602, 885], [574, 885]], [[644, 862], [692, 862], [692, 890], [644, 890]], [[726, 863], [768, 863], [768, 890], [726, 890]], [[100, 879], [253, 883], [252, 914], [99, 910]], [[102, 916], [150, 921], [147, 948], [99, 943]], [[522, 910], [757, 910], [757, 933], [522, 933]], [[524, 937], [757, 937], [757, 960], [524, 960]], [[98, 951], [255, 955], [254, 985], [98, 981]], [[521, 962], [766, 964], [766, 989], [521, 987]], [[72, 988], [238, 992], [238, 1017], [72, 1013]], [[524, 990], [757, 990], [757, 1014], [524, 1014]], [[71, 1024], [141, 1028], [139, 1055], [70, 1051]], [[522, 1017], [757, 1017], [757, 1040], [522, 1040]], [[521, 1042], [764, 1044], [764, 1069], [521, 1067]], [[81, 1065], [159, 1065], [159, 1090], [81, 1090]], [[524, 1071], [757, 1071], [757, 1096], [524, 1096]], [[78, 1099], [148, 1099], [148, 1124], [78, 1124]], [[522, 1098], [757, 1098], [757, 1123], [522, 1123]], [[523, 1122], [757, 1124], [757, 1149], [522, 1148]], [[98, 1133], [358, 1137], [358, 1162], [98, 1158]], [[521, 1149], [761, 1149], [761, 1174], [521, 1174]], [[96, 1169], [275, 1173], [275, 1198], [96, 1194]], [[524, 1180], [766, 1180], [766, 1205], [524, 1205]], [[79, 1210], [98, 1210], [98, 1226], [79, 1226]], [[103, 1208], [196, 1208], [196, 1233], [103, 1233]], [[73, 1242], [174, 1246], [173, 1271], [72, 1267]], [[522, 1233], [755, 1233], [755, 1257], [522, 1257]], [[522, 1260], [755, 1260], [755, 1283], [522, 1283]], [[103, 1280], [179, 1280], [179, 1307], [103, 1307]], [[521, 1285], [755, 1287], [755, 1312], [521, 1310]], [[105, 1319], [175, 1319], [175, 1339], [105, 1339]], [[521, 1312], [755, 1314], [755, 1339], [521, 1337]], [[521, 1339], [753, 1339], [753, 1364], [521, 1364]], [[79, 1357], [100, 1357], [100, 1371], [79, 1371]], [[92, 1351], [303, 1353], [303, 1378], [92, 1376]], [[524, 1367], [757, 1367], [757, 1392], [524, 1392]], [[71, 1385], [183, 1389], [182, 1416], [70, 1412]], [[522, 1394], [762, 1394], [762, 1419], [522, 1419]], [[79, 1428], [100, 1428], [100, 1443], [79, 1443]], [[103, 1425], [264, 1425], [264, 1450], [103, 1450]], [[522, 1423], [766, 1423], [766, 1448], [522, 1448]], [[76, 1460], [102, 1460], [102, 1482], [76, 1482]], [[100, 1460], [223, 1460], [223, 1485], [100, 1485]], [[524, 1476], [757, 1476], [757, 1500], [524, 1500]], [[52, 1502], [127, 1502], [127, 1534], [52, 1534]], [[522, 1503], [766, 1503], [766, 1528], [522, 1528]], [[524, 1532], [764, 1532], [764, 1555], [524, 1555]], [[52, 1560], [146, 1560], [146, 1589], [52, 1589]], [[522, 1557], [766, 1557], [766, 1582], [522, 1582]], [[521, 1609], [755, 1611], [755, 1636], [521, 1634]], [[57, 1625], [66, 1625], [66, 1637], [57, 1637]], [[78, 1625], [109, 1625], [109, 1646], [78, 1646]], [[521, 1637], [753, 1637], [753, 1662], [521, 1662]], [[522, 1666], [755, 1666], [755, 1689], [522, 1689]], [[54, 1677], [229, 1677], [229, 1702], [54, 1702]]], "rec_boxes": [[642, 7, 714, 34], [103, 18, 203, 43], [735, 11, 748, 32], [0, 39, 15, 64], [121, 50, 248, 85], [530, 46, 559, 70], [122, 86, 355, 122], [530, 82, 559, 105], [644, 80, 735, 107], [0, 102, 13, 127], [528, 118, 559, 141], [668, 114, 768, 145], [107, 130, 129, 147], [127, 130, 205, 150], [441, 134, 450, 143], [0, 157, 13, 182], [105, 159, 286, 193], [530, 154, 559, 177], [668, 150, 768, 181], [530, 189, 559, 214], [666, 184, 768, 218], [105, 196, 299, 225], [530, 225, 559, 250], [668, 223, 766, 248], [0, 241, 13, 268], [113, 243, 124, 252], [124, 234, 314, 261], [443, 243, 452, 254], [530, 261, 559, 286], [668, 259, 768, 292], [125, 270, 318, 299], [530, 297, 559, 322], [668, 295, 768, 325], [109, 311, 127, 325], [122, 305, 310, 334], [528, 332, 559, 358], [636, 331, 667, 363], [111, 347, 129, 361], [120, 341, 320, 370], [528, 370, 559, 395], [642, 366, 768, 397], [103, 377, 320, 406], [526, 404, 563, 433], [664, 400, 768, 435], [105, 413, 353, 442], [441, 420, 452, 436], [528, 442, 559, 465], [666, 437, 768, 471], [79, 450, 175, 475], [528, 477, 559, 502], [666, 474, 768, 504], [59, 492, 68, 500], [66, 484, 157, 513], [528, 513, 559, 538], [664, 507, 768, 542], [74, 524, 168, 552], [98, 554, 344, 588], [526, 547, 561, 577], [96, 591, 279, 626], [528, 586, 559, 610], [610, 582, 644, 617], [75, 591, 101, 617], [528, 622, 559, 647], [616, 616, 768, 651], [102, 629, 443, 654], [528, 658, 559, 683], [640, 652, 768, 687], [101, 665, 292, 694], [528, 694, 559, 719], [640, 686, 768, 722], [98, 699, 275, 733], [68, 736, 154, 765], [528, 729, 559, 754], [618, 729, 641, 756], [72, 774, 186, 799], [528, 765, 559, 790], [618, 765, 768, 790], [70, 808, 290, 837], [528, 803, 559, 826], [642, 799, 735, 830], [99, 845, 235, 874], [522, 865, 569, 887], [574, 863, 602, 885], [644, 862, 692, 890], [726, 863, 768, 890], [99, 879, 253, 914], [99, 916, 150, 948], [522, 910, 757, 933], [524, 937, 757, 960], [98, 951, 255, 985], [521, 962, 766, 989], [72, 988, 238, 1017], [524, 990, 757, 1014], [70, 1024, 141, 1055], [522, 1017, 757, 1040], [521, 1042, 764, 1069], [81, 1065, 159, 1090], [524, 1071, 757, 1096], [78, 1099, 148, 1124], [522, 1098, 757, 1123], [522, 1122, 757, 1149], [98, 1133, 358, 1162], [521, 1149, 761, 1174], [96, 1169, 275, 1198], [524, 1180, 766, 1205], [79, 1210, 98, 1226], [103, 1208, 196, 1233], [72, 1242, 174, 1271], [522, 1233, 755, 1257], [522, 1260, 755, 1283], [103, 1280, 179, 1307], [521, 1285, 755, 1312], [105, 1319, 175, 1339], [521, 1312, 755, 1339], [521, 1339, 753, 1364], [79, 1357, 100, 1371], [92, 1351, 303, 1378], [524, 1367, 757, 1392], [70, 1385, 183, 1416], [522, 1394, 762, 1419], [79, 1428, 100, 1443], [103, 1425, 264, 1450], [522, 1423, 766, 1448], [76, 1460, 102, 1482], [100, 1460, 223, 1485], [524, 1476, 757, 1500], [52, 1502, 127, 1534], [522, 1503, 766, 1528], [524, 1532, 764, 1555], [52, 1560, 146, 1589], [522, 1557, 766, 1582], [521, 1609, 755, 1636], [57, 1625, 66, 1637], [78, 1625, 109, 1646], [521, 1637, 753, 1662], [522, 1666, 755, 1689], [54, 1677, 229, 1702]]}, "table_res_list": []}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//46eff436-7d11-4ff8-9b3c-c8af33eb49ff/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-28T08%3A01%3A24Z%2F-1%2F%2F69f5fc8976de08cf700305034f6dc75d74e4fff2baae361b310658b5852bf248", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//46eff436-7d11-4ff8-9b3c-c8af33eb49ff/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-28T08%3A01%3A24Z%2F-1%2F%2F600ad70b1905416151739649b6c06781ea6f7fcfec7dfd2a6f8608bdae11553f"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//46eff436-7d11-4ff8-9b3c-c8af33eb49ff/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-28T08%3A01%3A24Z%2F-1%2F%2F1ebf899a79960ff093d79807a9379cfa48d658dddca36fd95c5abea77a92a79a"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}