/**
 * Wails运行时检测和管理的可复用组合式函数
 * 提供统一的Wails runtime状态管理和事件监听器设置
 */

import { ref, onMounted, onUnmounted } from 'vue'

// 全局状态管理
const globalRuntimeStatus = ref('checking')
const globalEventListeners = new Map()
const retryTimeouts = []

// 默认配置
const DEFAULT_CONFIG = {
  retryAttempts: 10,
  retryDelay: 100,
  debug: false,
  autoCleanup: true
}

/**
 * 使用Wails运行时的组合式函数
 * @param {Object} options 配置选项
 * @returns {Object} 运行时状态和方法
 */
export function useWailsRuntime(options = {}) {
  const config = { ...DEFAULT_CONFIG, ...options }
  const isReady = ref(false)
  const error = ref(null)
  const retryCount = ref(0)
  
  // 日志记录
  const log = (message, data = null) => {
    if (config.debug || process.env.NODE_ENV === 'development') {
      console.log(`[useWailsRuntime] ${message}`, data || '')
    }
  }

  // 错误记录
  const logError = (message, err = null) => {
    console.error(`[useWailsRuntime] ${message}`, err || '')
    error.value = err || new Error(message)
  }

  // 检查Wails运行时是否可用
  const checkRuntime = () => {
    try {
      if (window.runtime && window.runtime.EventsOn && window.runtime.EventsEmit) {
        return true
      }
      return false
    } catch (err) {
      logError('检查运行时时发生错误:', err)
      return false
    }
  }

  // 等待运行时准备就绪
  const waitForRuntime = () => {
    return new Promise((resolve, reject) => {
      const attempt = (currentRetry = 0) => {
        if (checkRuntime()) {
          globalRuntimeStatus.value = 'ready'
          isReady.value = true
          error.value = null
          log('Wails运行时已准备就绪')
          resolve(true)
          return
        }

        if (currentRetry >= config.retryAttempts) {
          const errorMsg = `Wails运行时在 ${config.retryAttempts} 次重试后仍不可用`
          globalRuntimeStatus.value = 'failed'
          logError(errorMsg)
          reject(new Error(errorMsg))
          return
        }

        retryCount.value = currentRetry + 1
        globalRuntimeStatus.value = 'retrying'
        log(`等待Wails运行时，第 ${currentRetry + 1}/${config.retryAttempts} 次重试`)
        
        const timeoutId = setTimeout(() => {
          attempt(currentRetry + 1)
        }, config.retryDelay)
        
        retryTimeouts.push(timeoutId)
      }
      
      attempt()
    })
  }

  // 注册事件监听器
  const addEventListener = (eventName, handler, options = {}) => {
    return new Promise((resolve, reject) => {
      const setup = () => {
        try {
          if (!checkRuntime()) {
            reject(new Error('Wails运行时不可用'))
            return
          }

          // 检查是否已存在监听器
          if (globalEventListeners.has(eventName)) {
            log(`事件 ${eventName} 已有监听器，将替换`)
            removeEventListener(eventName)
          }

          // 包装处理器以添加错误处理
          const wrappedHandler = (data) => {
            try {
              log(`收到事件 ${eventName}:`, data)
              handler(data)
            } catch (err) {
              logError(`处理事件 ${eventName} 时发生错误:`, err)
            }
          }

          // 注册监听器
          window.runtime.EventsOn(eventName, wrappedHandler)
          globalEventListeners.set(eventName, {
            handler: wrappedHandler,
            originalHandler: handler,
            options
          })
          
          log(`事件监听器 ${eventName} 注册成功`)
          resolve(wrappedHandler)
        } catch (err) {
          logError(`注册事件监听器 ${eventName} 时发生错误:`, err)
          reject(err)
        }
      }

      if (isReady.value) {
        setup()
      } else {
        waitForRuntime().then(setup).catch(reject)
      }
    })
  }

  // 移除事件监听器
  const removeEventListener = (eventName) => {
    try {
      if (globalEventListeners.has(eventName)) {
        const listenerInfo = globalEventListeners.get(eventName)
        
        // Wails的EventsOff方法（如果存在）
        if (window.runtime && window.runtime.EventsOff) {
          window.runtime.EventsOff(eventName)
        }
        
        globalEventListeners.delete(eventName)
        log(`事件监听器 ${eventName} 已移除`)
        return true
      }
      return false
    } catch (err) {
      logError(`移除事件监听器 ${eventName} 时发生错误:`, err)
      return false
    }
  }

  // 发送事件到后端
  const emitEvent = (eventName, data = null) => {
    return new Promise((resolve, reject) => {
      const emit = () => {
        try {
          if (!checkRuntime()) {
            reject(new Error('Wails运行时不可用'))
            return
          }

          log(`发送事件 ${eventName}:`, data)
          window.runtime.EventsEmit(eventName, data)
          resolve(true)
        } catch (err) {
          logError(`发送事件 ${eventName} 时发生错误:`, err)
          reject(err)
        }
      }

      if (isReady.value) {
        emit()
      } else {
        waitForRuntime().then(emit).catch(reject)
      }
    })
  }

  // 获取运行时状态
  const getStatus = () => {
    return {
      isReady: isReady.value,
      status: globalRuntimeStatus.value,
      error: error.value,
      retryCount: retryCount.value,
      activeListeners: Array.from(globalEventListeners.keys()),
      runtimeAvailable: checkRuntime()
    }
  }

  // 健康检查
  const healthCheck = () => {
    const status = getStatus()
    const issues = []

    if (!status.runtimeAvailable) {
      issues.push('Wails运行时不可用')
    }

    if (status.error) {
      issues.push(`错误: ${status.error.message}`)
    }

    if (status.retryCount > 0) {
      issues.push(`已重试 ${status.retryCount} 次`)
    }

    return {
      ...status,
      healthy: issues.length === 0,
      issues
    }
  }

  // 清理资源
  const cleanup = () => {
    try {
      log('开始清理Wails运行时资源')
      
      // 清理重试定时器
      retryTimeouts.forEach(timeoutId => clearTimeout(timeoutId))
      retryTimeouts.length = 0
      
      // 清理事件监听器（如果启用自动清理）
      if (config.autoCleanup) {
        const listeners = Array.from(globalEventListeners.keys())
        listeners.forEach(eventName => removeEventListener(eventName))
      }
      
      // 重置状态
      isReady.value = false
      error.value = null
      retryCount.value = 0
      
      log('Wails运行时资源清理完成')
    } catch (err) {
      logError('清理Wails运行时资源时发生错误:', err)
    }
  }

  // 组件挂载时初始化
  onMounted(() => {
    log('开始初始化Wails运行时')
    waitForRuntime().catch(err => {
      logError('初始化Wails运行时失败:', err)
    })
  })

  // 组件卸载时清理
  onUnmounted(() => {
    if (config.autoCleanup) {
      cleanup()
    }
  })

  return {
    // 状态
    isReady,
    status: globalRuntimeStatus,
    error,
    retryCount,
    
    // 方法
    waitForRuntime,
    addEventListener,
    removeEventListener,
    emitEvent,
    getStatus,
    healthCheck,
    cleanup,
    
    // 工具方法
    checkRuntime
  }
}

/**
 * 专门用于Toast通知的Wails运行时Hook
 * @param {Function} notificationHandler 通知处理函数
 * @param {Object} options 配置选项
 * @returns {Object} Toast相关的运行时方法
 */
export function useWailsToastRuntime(notificationHandler, options = {}) {
  const runtime = useWailsRuntime(options)
  const listenerRegistered = ref(false)
  
  // 注册Toast通知监听器
  const registerToastListener = async () => {
    try {
      await runtime.addEventListener('showToastNotification', notificationHandler)
      listenerRegistered.value = true
      return true
    } catch (err) {
      console.error('[useWailsToastRuntime] 注册Toast监听器失败:', err)
      return false
    }
  }

  // 发送Toast通知到后端
  const sendToastToBackend = (data) => {
    return runtime.emitEvent('frontendToastNotification', data)
  }

  // 自动注册监听器
  onMounted(() => {
    runtime.waitForRuntime().then(() => {
      registerToastListener()
    })
  })

  return {
    ...runtime,
    listenerRegistered,
    registerToastListener,
    sendToastToBackend
  }
}

export default useWailsRuntime