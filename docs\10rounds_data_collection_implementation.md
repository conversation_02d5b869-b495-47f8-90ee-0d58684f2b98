# 10轮数据收集系统实现文档

## 概述

本文档描述了10轮检测数据收集系统的实现，该系统用于收集用户在10轮B/C模式截图过程中的OCR数据，并在完成后调用扣子API和云函数API进行数据上传和分析。

## 核心功能

### 1. 数据结构

#### CurrentUserCheckingInfo
- **位置**: `app/models/user_checking_info.go`
- **用途**: 主要数据容器，包含用户信息、轮次数据和统计信息
- **关键字段**:
  - `RoundsData []RoundData`: 10轮检测数据数组
  - `CompletedRounds int`: 已完成轮次数
  - `DetectedOrgans map[string]int`: 检测到的器官统计

#### RoundData
- **用途**: 单轮检测数据，包含B02和C03模式的所有信息
- **关键字段**:
  - `B02InputImage string`: B02模式截图路径
  - `C03InputImage string`: C03模式截图路径
  - `B02RecTexts []string`: B02 OCR识别文本
  - `C03RecTexts []string`: C03 OCR识别文本
  - `B02BiochemicalAnalysis []BiochemicalAnalysis`: B02生化分析结果
  - `C03PathologyAnalysis []PathologyAnalysis`: C03病理分析结果

### 2. 数据收集流程

#### 自动数据收集
- **触发点**: 每次B/C模式截图完成后
- **处理函数**: `updateCurrentUserCheckingInfo()`
- **流程**:
  1. 获取当前轮次
  2. 确保RoundsData数组有足够空间
  3. 根据模式(B02/C03)更新对应字段
  4. 更新统计信息
  5. 检查是否完成10轮

#### 轮次管理
- **轮次计算**: 使用现有的`getCurrentRound()`函数
- **模式标记**: 使用现有的`markModeCompleted()`函数
- **完成判断**: 当B02和C03都有图片路径时，该轮次视为完成

### 3. API调用机制

#### 10轮完成检测
- **检测逻辑**: `completedRounds >= 10`
- **触发函数**: `handleTenRoundsCompleted()`
- **执行方式**: 异步执行，避免阻塞当前操作

#### 扣子API调用
- **函数**: `callCozeAPI()`
- **配置**: 使用`config.APIKeys.Coze`
- **数据**: 完整的`CurrentUserCheckingInfo`结构

#### 云函数API调用
- **函数**: `callCloudFunctionAPI()`
- **配置**: 使用`config.APIKeys.CloudFunction.UserDetectRawResultDataURL`
- **数据**: 包含用户检测信息和设备信息

### 4. 用户通知系统

#### Toast通知
- **开始通知**: "AI分析进行中 - 进行AI大模型检测结果分析，并生成健康评估报告"
- **成功通知**: "检测完成 - 10轮检测数据已成功上传，AI分析报告生成中"
- **失败通知**: 根据具体错误显示相应信息

## 使用方法

### 1. 正常使用流程
1. 用户进行B/C模式截图（通过快捷键或任务管理器）
2. 系统自动收集OCR数据到对应轮次
3. 完成10轮后自动调用API
4. 用户收到Toast通知

### 2. 开发调试功能

#### 查看用户检测状态
```javascript
// 前端调用
const status = await GetUserCheckingInfoStatus("用户名");
console.log(status);
```

#### 测试10轮完成功能
```javascript
// 前端调用（仅用于开发测试）
await TestTenRoundsCompletion("用户名");
```

#### 导出检测数据
```javascript
// 前端调用
const jsonData = await ExportCurrentUserCheckingInfoJSON("用户名");
console.log(JSON.parse(jsonData));
```

### 3. 配置要求

#### config.json配置示例
```json
{
  "api_keys": {
    "coze": {
      "token": "your_coze_token",
      "workflow_id_user_info": "your_workflow_id",
      "space_id": "your_space_id",
      "app_id": "your_app_id"
    },
    "cloud_function": {
      "user_detect_raw_result_data_url": "https://your-cloud-function-url"
    }
  }
}
```

## 技术实现细节

### 1. 并发安全
- 使用`checkingInfoMutex`保护数据访问
- 读写锁分离，提高并发性能

### 2. 错误处理
- OCR失败时记录错误但不中断流程
- API调用失败时显示用户友好的错误信息
- 详细的日志记录用于问题排查

### 3. 性能优化
- 异步API调用，避免阻塞用户操作
- 增量数据更新，避免重复处理
- 内存中数据管理，提高访问速度

## 日志和调试

### 关键日志输出
- `[DEBUG] 更新用户检测信息`: 每次数据更新
- `[DEBUG] 轮次X状态`: 每轮的B02/C03完成状态
- `[INFO] 用户X已完成10轮检测`: 10轮完成触发
- `[DEBUG] 扣子API调用`: API调用详情
- `[DEBUG] 云函数API调用`: 云函数调用详情

### 调试建议
1. 检查日志中的轮次状态输出
2. 验证配置文件中的API配置
3. 使用`GetUserCheckingInfoStatus`查看实时状态
4. 使用`TestTenRoundsCompletion`测试完成流程

## 注意事项

1. **数据持久化**: 当前实现仅在内存中保存数据，应用重启后数据丢失
2. **API实现**: 当前API调用为占位实现，需要根据实际API文档完善
3. **错误恢复**: OCR失败时需要手动重试，暂无自动重试机制
4. **轮次重置**: 每天或每个用户的轮次管理需要根据业务需求调整

## 后续改进计划

1. 实现实际的HTTP API调用
2. 添加数据持久化存储
3. 实现OCR失败自动重试
4. 添加更详细的进度显示
5. 支持轮次数据的手动编辑和修正
