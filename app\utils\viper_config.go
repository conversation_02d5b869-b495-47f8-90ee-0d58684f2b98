package utils

import (
	"fmt"
	"path/filepath"
	"strings"

	"MagneticOperator/app/models"

	"github.com/spf13/viper"
)

// ViperConfig Viper配置管理器
type ViperConfig struct {
	v *viper.Viper
}

// NewViperConfig 创建Viper配置管理器
func NewViperConfig(configDir, environment string) *ViperConfig {
	v := viper.New()

	// 设置配置文件
	v.SetConfigName("app_config")
	v.SetConfigType("json")
	v.AddConfigPath(configDir)

	// 环境变量支持
	v.SetEnvPrefix("MAGNETIC_OPERATOR")
	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 设置默认值
	setDefaultValues(v)

	return &ViperConfig{v: v}
}

// setDefaultValues 设置默认配置值
func setDefaultValues(v *viper.Viper) {
	v.SetDefault("mp_app_info.target_page", "pages/p_scan/p_scan")
	v.SetDefault("crop_settings.top_percent", 0.155)
	v.SetDefault("crop_settings.bottom_percent", 0.051)
	v.SetDefault("crop_settings.left_percent", 0.05)
	v.SetDefault("crop_settings.right_percent", 0.75)
	v.SetDefault("use_system_notification", true)
	v.SetDefault("color_detection.debug_mode", false)
}

// LoadConfig 加载配置
func (vc *ViperConfig) LoadConfig() (*models.AppConfig, error) {
	if err := vc.v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("读取配置失败: %w", err)
		}
	}

	var config models.AppConfig
	if err := vc.v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	return &config, nil
}

// SaveConfig 保存配置
func (vc *ViperConfig) SaveConfig(configDir string) error {
	configPath := filepath.Join(configDir, "app_config.json")
	return vc.v.WriteConfigAs(configPath)
}

// Get 获取配置值
func (vc *ViperConfig) Get(key string) interface{} {
	return vc.v.Get(key)
}

// Set 设置配置值
func (vc *ViperConfig) Set(key string, value interface{}) {
	vc.v.Set(key, value)
}

// GetString 获取字符串值
func (vc *ViperConfig) GetString(key string) string {
	return vc.v.GetString(key)
}

// GetBool 获取布尔值
func (vc *ViperConfig) GetBool(key string) bool {
	return vc.v.GetBool(key)
}

// GetFloat64 获取浮点数值
func (vc *ViperConfig) GetFloat64(key string) float64 {
	return vc.v.GetFloat64(key)
}
