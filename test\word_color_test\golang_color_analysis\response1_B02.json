{"logId": "66a4ab1e-891f-4ff3-a5e1-c3143565da0f", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 8, "label": "table", "score": 0.9868529438972473, "coordinate": [3.3065590858459473, 78.7399673461914, 967, 1716.3529052734375]}, {"cls_id": 9, "label": "table_title", "score": 0.6208784580230713, "coordinate": [26.020795822143555, 31.501909255981445, 527.6366577148438, 66.25834655761719]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.5342310667037964, "coordinate": [26.020795822143555, 31.501909255981445, 527.6366577148438, 66.25834655761719]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[27, 30], [528, 30], [528, 66], [27, 66]], [[105, 79], [164, 79], [164, 106], [105, 106]], [[197, 79], [430, 81], [430, 106], [197, 104]], [[103, 102], [165, 102], [165, 134], [103, 134]], [[199, 106], [277, 106], [277, 133], [199, 133]], [[201, 133], [468, 133], [468, 156], [201, 156]], [[105, 158], [169, 158], [169, 183], [105, 183]], [[201, 158], [501, 158], [501, 181], [201, 181]], [[105, 183], [169, 183], [169, 208], [105, 208]], [[201, 183], [430, 183], [430, 208], [201, 208]], [[105, 208], [169, 208], [169, 235], [105, 235]], [[199, 208], [272, 208], [272, 235], [199, 235]], [[105, 233], [167, 233], [167, 260], [105, 260]], [[197, 233], [328, 229], [329, 260], [198, 264]], [[105, 258], [169, 258], [169, 285], [105, 285]], [[199, 258], [562, 258], [562, 281], [199, 281]], [[105, 283], [169, 283], [169, 310], [105, 310]], [[199, 285], [734, 285], [734, 310], [199, 310]], [[105, 310], [167, 310], [167, 335], [105, 335]], [[199, 310], [318, 310], [318, 335], [199, 335]], [[105, 335], [167, 335], [167, 362], [105, 362]], [[199, 337], [436, 337], [436, 360], [199, 360]], [[105, 360], [164, 360], [164, 387], [105, 387]], [[199, 362], [418, 362], [418, 387], [199, 387]], [[105, 387], [162, 387], [162, 412], [105, 412]], [[199, 387], [482, 387], [482, 410], [199, 410]], [[105, 412], [162, 412], [162, 439], [105, 439]], [[201, 412], [450, 412], [450, 437], [201, 437]], [[105, 437], [162, 437], [162, 464], [105, 464]], [[201, 439], [400, 439], [400, 462], [201, 462]], [[105, 462], [162, 462], [162, 489], [105, 489]], [[199, 464], [574, 464], [574, 487], [199, 487]], [[105, 487], [162, 487], [162, 514], [105, 514]], [[199, 487], [407, 487], [407, 511], [199, 511]], [[105, 512], [162, 512], [162, 539], [105, 539]], [[197, 514], [410, 512], [411, 537], [197, 539]], [[105, 535], [162, 540], [160, 569], [102, 564]], [[199, 541], [443, 541], [443, 564], [199, 564]], [[105, 564], [162, 564], [162, 591], [105, 591]], [[199, 564], [311, 564], [311, 589], [199, 589]], [[105, 591], [162, 591], [162, 616], [105, 616]], [[197, 591], [382, 591], [382, 615], [197, 615]], [[105, 616], [160, 616], [160, 641], [105, 641]], [[199, 615], [315, 615], [315, 640], [199, 640]], [[105, 641], [162, 641], [162, 668], [105, 668]], [[199, 641], [316, 641], [316, 666], [199, 666]], [[105, 666], [162, 666], [162, 693], [105, 693]], [[199, 668], [412, 668], [412, 693], [199, 693]], [[105, 692], [162, 692], [162, 718], [105, 718]], [[196, 690], [231, 690], [231, 720], [196, 720]], [[105, 717], [164, 717], [164, 744], [105, 744]], [[197, 717], [391, 719], [391, 744], [197, 742]], [[105, 742], [164, 742], [164, 769], [105, 769]], [[199, 744], [340, 744], [340, 769], [199, 769]], [[105, 769], [162, 769], [162, 796], [105, 796]], [[199, 770], [453, 770], [453, 794], [199, 794]], [[105, 794], [164, 794], [164, 821], [105, 821]], [[197, 794], [288, 794], [288, 822], [197, 822]], [[105, 819], [164, 819], [164, 846], [105, 846]], [[199, 821], [450, 821], [450, 846], [199, 846]], [[105, 846], [164, 846], [164, 871], [105, 871]], [[201, 847], [455, 847], [455, 871], [201, 871]], [[105, 871], [164, 871], [164, 898], [105, 898]], [[199, 873], [695, 873], [695, 896], [199, 896]], [[105, 896], [164, 896], [164, 923], [105, 923]], [[199, 898], [613, 898], [613, 921], [199, 921]], [[105, 921], [164, 921], [164, 948], [105, 948]], [[197, 923], [362, 921], [363, 946], [198, 948]], [[105, 946], [164, 946], [164, 973], [105, 973]], [[199, 948], [466, 948], [466, 973], [199, 973]], [[105, 971], [164, 971], [164, 998], [105, 998]], [[199, 975], [484, 975], [484, 998], [199, 998]], [[105, 998], [164, 998], [164, 1025], [105, 1025]], [[195, 998], [354, 996], [354, 1021], [196, 1023]], [[105, 1023], [164, 1023], [164, 1050], [105, 1050]], [[194, 1019], [254, 1019], [254, 1054], [194, 1054]], [[105, 1050], [164, 1050], [164, 1077], [105, 1077]], [[201, 1052], [510, 1052], [510, 1075], [201, 1075]], [[105, 1075], [164, 1075], [164, 1100], [105, 1100]], [[199, 1077], [531, 1077], [531, 1100], [199, 1100]], [[105, 1100], [164, 1100], [164, 1127], [105, 1127]], [[197, 1102], [370, 1102], [370, 1127], [197, 1127]], [[105, 1125], [164, 1125], [164, 1152], [105, 1152]], [[196, 1125], [251, 1125], [251, 1154], [196, 1154]], [[105, 1150], [164, 1150], [164, 1177], [105, 1177]], [[197, 1150], [738, 1152], [738, 1177], [197, 1175]], [[105, 1175], [164, 1175], [164, 1202], [105, 1202]], [[199, 1177], [649, 1177], [649, 1202], [199, 1202]], [[105, 1200], [164, 1200], [164, 1227], [105, 1227]], [[197, 1204], [338, 1204], [338, 1229], [197, 1229]], [[105, 1227], [164, 1227], [164, 1254], [105, 1254]], [[196, 1227], [268, 1227], [268, 1254], [196, 1254]], [[105, 1252], [164, 1252], [164, 1279], [105, 1279]], [[197, 1252], [288, 1252], [288, 1281], [197, 1281]], [[105, 1279], [162, 1279], [162, 1304], [105, 1304]], [[197, 1279], [370, 1279], [370, 1304], [197, 1304]], [[105, 1304], [162, 1304], [162, 1329], [105, 1329]], [[197, 1304], [484, 1304], [484, 1328], [197, 1328]], [[105, 1329], [164, 1329], [164, 1356], [105, 1356]], [[197, 1331], [482, 1331], [482, 1354], [197, 1354]], [[105, 1354], [164, 1354], [164, 1381], [105, 1381]], [[199, 1356], [368, 1356], [368, 1381], [199, 1381]], [[105, 1380], [164, 1380], [164, 1406], [105, 1406]], [[196, 1378], [272, 1378], [272, 1410], [196, 1410]], [[105, 1405], [164, 1405], [164, 1432], [105, 1432]], [[199, 1406], [418, 1406], [418, 1432], [199, 1432]], [[105, 1432], [164, 1432], [164, 1458], [105, 1458]], [[193, 1430], [271, 1426], [273, 1460], [195, 1464]], [[105, 1457], [164, 1457], [164, 1484], [105, 1484]], [[197, 1457], [288, 1457], [288, 1485], [197, 1485]], [[105, 1482], [164, 1482], [164, 1509], [105, 1509]], [[197, 1480], [324, 1480], [324, 1510], [197, 1510]], [[105, 1504], [164, 1509], [162, 1536], [102, 1531]], [[199, 1509], [375, 1509], [375, 1534], [199, 1534]], [[105, 1534], [164, 1534], [164, 1561], [105, 1561]], [[199, 1534], [329, 1534], [329, 1559], [199, 1559]], [[105, 1559], [164, 1559], [164, 1586], [105, 1586]], [[199, 1561], [437, 1561], [437, 1584], [199, 1584]], [[105, 1584], [164, 1584], [164, 1611], [105, 1611]], [[199, 1586], [357, 1586], [357, 1611], [199, 1611]], [[105, 1609], [165, 1609], [165, 1636], [105, 1636]], [[201, 1611], [436, 1611], [436, 1636], [201, 1636]], [[105, 1636], [164, 1636], [164, 1663], [105, 1663]], [[199, 1638], [393, 1638], [393, 1661], [199, 1661]], [[105, 1661], [164, 1661], [164, 1688], [105, 1688]], [[201, 1664], [334, 1664], [334, 1684], [201, 1684]], [[103, 1684], [167, 1684], [167, 1716], [103, 1716]], [[197, 1686], [304, 1686], [304, 1716], [197, 1716]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["按照标准图谱相似度递减列表：", "0.000", "经厚在第2暖推水平横 截面", "3.896", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.049", "C反应蛋白C-REACTIVEPROTEIN", "0.058", "血尿酸SERUMURICACID", "0.064", "脂肪酶*", "0.067", "血管紧张素Ⅱ*", "0.078", "胆固酸COMMON PLASMACHOLESTERIN", "0.088", "血浆丰配化脂肪酸NONETHERIZED FATTYACIDSOF PLASMA", "0.089", "血管紧张素I*", "0.137", "血钾PLASMA POTASSIUM", "0.065", "血清蛋白SERUM ALBUMEN", "0.079", "PERIPHERIC BLOOD LEUCOCYTES", "0.082", "尿中蛋白质PROTEIN INURINE", "0.083", "血红蛋白HAEMOGLOBIN", "0.087", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.089", "嗜碱性粒细胞BASOPHILS", "0.092", "血红血球ERYTHROCYTES", "0.099", "尿白血球URINELEUCOCYTES", "0.107", "BETA球蛋白*", "0.110", "单核细胞MONOCYTES", "0.111", "免疫球蛋白G*", "0.114", "免疫球蛋白M*", "0.117", "血清蛋白SERUMPROTEIN", "0.174", "锂*", "0.063", "17-血浆氧皮质类固醇类", "0.066", "17-尿中酮类固醇", "0.067", "肿瘤标志物MELANOGENE在尿*", "0.072", "醛固酮尿*", "0.074", "血清溶菌酵SERUMLYSOZYME", "0.075", "血清补体SERUM COMPLEMENT", "0.076", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.076", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.079", "血糖BLOOD SUGAR", "0.080", "尿中肾上腺素URINEADRENALIN", "0.082", "血浆磷脂PLASMAPHOSPHOTIDES", "0.083", "RHEUMOFACTOR*", "0.084", "肾素*", "0.085", "血清淀粉酵素SERUMALPHAAMYLASE", "0.085", "游离胆固醇FREEPLASMACHOLESTERIN", "0.086", "肿瘤标志物胸苷激酶", "0.086", "糖苷*", "0.086", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.086", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.086", "抗链球菌溶血素", "0.088", "铁蛋白*", "0.089", "醛固酮血*", "0.091", "红细胞沉降率(ESR)", "0.091", "酸性磷酸酵素ACIDPHOSPHATASE", "0.093", "嗜中性粒细胞STABNEUTROPHILS", "0.093", "血尿素BLOODUREA", "0.093", "胆汁酸*", "0.095", "尿肌配URINECREATININE", "0.096", "催乳素*", "0.096", "葡萄糖浆*", "0.097", "甲状腺球蛋白*", "0.097", "甲状腺素结合球蛋白", "0.098", "ALPHA2球蛋白", "0.098", "嗜酸性粒细胞EOSINOPHILES", "0.098", "血细胞比容，全血*", "0.098", "血组织胺BLOOD HISTAMINE", "0.099", "维生素B1（THIAMINE）", "0.099", "糖基化血红蛋白", "0.099", "胰高血糖素*"], "rec_scores": [0.9895560145378113, 0.9992481470108032, 0.8813357353210449, 0.9996616244316101, 0.9983981251716614, 0.9640860557556152, 0.9999086260795593, 0.9874621033668518, 0.9999423027038574, 0.9979388117790222, 0.9999420046806335, 0.9784912467002869, 0.9998849630355835, 0.9394027590751648, 0.999956488609314, 0.941057562828064, 0.9999154806137085, 0.9386655688285828, 0.9998348355293274, 0.9409072995185852, 0.9998716115951538, 0.9822050929069519, 0.9998233914375305, 0.9814016819000244, 0.9996112585067749, 0.97152179479599, 0.9997431039810181, 0.976643443107605, 0.9997656941413879, 0.9981578588485718, 0.9997803568840027, 0.9966168403625488, 0.99980628490448, 0.9983251690864563, 0.9997758865356445, 0.9867699146270752, 0.9997366070747375, 0.9928971529006958, 0.9995795488357544, 0.9590877294540405, 0.9992464780807495, 0.9883661866188049, 0.9985781908035278, 0.9826647639274597, 0.9312297105789185, 0.980752170085907, 0.9994608163833618, 0.9972257614135742, 0.9991631507873535, 0.8812094926834106, 0.9998235702514648, 0.9876624941825867, 0.999659538269043, 0.9848162531852722, 0.9997014999389648, 0.9690084457397461, 0.9996992349624634, 0.9791863560676575, 0.9997485876083374, 0.9938246607780457, 0.9996604919433594, 0.9743426442146301, 0.9996326565742493, 0.9959184527397156, 0.9997272491455078, 0.9964339137077332, 0.9997580647468567, 0.9660680294036865, 0.9996770620346069, 0.9974356889724731, 0.9997841715812683, 0.9813632965087891, 0.9997931718826294, 0.9969139099121094, 0.9997332692146301, 0.9378030896186829, 0.9997544288635254, 0.9968136548995972, 0.9996091723442078, 0.9958581328392029, 0.9997441172599792, 0.978713870048523, 0.999790370464325, 0.9471707344055176, 0.9997652173042297, 0.9939124584197998, 0.9997861981391907, 0.9972487092018127, 0.9996930360794067, 0.9974395036697388, 0.999783992767334, 0.9549832344055176, 0.9997812509536743, 0.9852700233459473, 0.9995215535163879, 0.9253935217857361, 0.9995633363723755, 0.9960333704948425, 0.9998371005058289, 0.9951410889625549, 0.9997886419296265, 0.9944164752960205, 0.9996522068977356, 0.9588331580162048, 0.9997732043266296, 0.9675629138946533, 0.9996237754821777, 0.8078666925430298, 0.999738335609436, 0.9789459109306335, 0.9998100399971008, 0.9713653326034546, 0.999829113483429, 0.9810749888420105, 0.9996970891952515, 0.9868845343589783, 0.9997416734695435, 0.9977938532829285, 0.9998056292533875, 0.9600962400436401, 0.9997445344924927, 0.9672539234161377, 0.9996654391288757, 0.9473199248313904, 0.9997137188911438, 0.9949651956558228, 0.9996391534805298, 0.9498085379600525], "rec_polys": [[[27, 30], [528, 30], [528, 66], [27, 66]], [[105, 79], [164, 79], [164, 106], [105, 106]], [[197, 79], [430, 81], [430, 106], [197, 104]], [[103, 102], [165, 102], [165, 134], [103, 134]], [[199, 106], [277, 106], [277, 133], [199, 133]], [[201, 133], [468, 133], [468, 156], [201, 156]], [[105, 158], [169, 158], [169, 183], [105, 183]], [[201, 158], [501, 158], [501, 181], [201, 181]], [[105, 183], [169, 183], [169, 208], [105, 208]], [[201, 183], [430, 183], [430, 208], [201, 208]], [[105, 208], [169, 208], [169, 235], [105, 235]], [[199, 208], [272, 208], [272, 235], [199, 235]], [[105, 233], [167, 233], [167, 260], [105, 260]], [[197, 233], [328, 229], [329, 260], [198, 264]], [[105, 258], [169, 258], [169, 285], [105, 285]], [[199, 258], [562, 258], [562, 281], [199, 281]], [[105, 283], [169, 283], [169, 310], [105, 310]], [[199, 285], [734, 285], [734, 310], [199, 310]], [[105, 310], [167, 310], [167, 335], [105, 335]], [[199, 310], [318, 310], [318, 335], [199, 335]], [[105, 335], [167, 335], [167, 362], [105, 362]], [[199, 337], [436, 337], [436, 360], [199, 360]], [[105, 360], [164, 360], [164, 387], [105, 387]], [[199, 362], [418, 362], [418, 387], [199, 387]], [[105, 387], [162, 387], [162, 412], [105, 412]], [[199, 387], [482, 387], [482, 410], [199, 410]], [[105, 412], [162, 412], [162, 439], [105, 439]], [[201, 412], [450, 412], [450, 437], [201, 437]], [[105, 437], [162, 437], [162, 464], [105, 464]], [[201, 439], [400, 439], [400, 462], [201, 462]], [[105, 462], [162, 462], [162, 489], [105, 489]], [[199, 464], [574, 464], [574, 487], [199, 487]], [[105, 487], [162, 487], [162, 514], [105, 514]], [[199, 487], [407, 487], [407, 511], [199, 511]], [[105, 512], [162, 512], [162, 539], [105, 539]], [[197, 514], [410, 512], [411, 537], [197, 539]], [[105, 535], [162, 540], [160, 569], [102, 564]], [[199, 541], [443, 541], [443, 564], [199, 564]], [[105, 564], [162, 564], [162, 591], [105, 591]], [[199, 564], [311, 564], [311, 589], [199, 589]], [[105, 591], [162, 591], [162, 616], [105, 616]], [[197, 591], [382, 591], [382, 615], [197, 615]], [[105, 616], [160, 616], [160, 641], [105, 641]], [[199, 615], [315, 615], [315, 640], [199, 640]], [[105, 641], [162, 641], [162, 668], [105, 668]], [[199, 641], [316, 641], [316, 666], [199, 666]], [[105, 666], [162, 666], [162, 693], [105, 693]], [[199, 668], [412, 668], [412, 693], [199, 693]], [[105, 692], [162, 692], [162, 718], [105, 718]], [[196, 690], [231, 690], [231, 720], [196, 720]], [[105, 717], [164, 717], [164, 744], [105, 744]], [[197, 717], [391, 719], [391, 744], [197, 742]], [[105, 742], [164, 742], [164, 769], [105, 769]], [[199, 744], [340, 744], [340, 769], [199, 769]], [[105, 769], [162, 769], [162, 796], [105, 796]], [[199, 770], [453, 770], [453, 794], [199, 794]], [[105, 794], [164, 794], [164, 821], [105, 821]], [[197, 794], [288, 794], [288, 822], [197, 822]], [[105, 819], [164, 819], [164, 846], [105, 846]], [[199, 821], [450, 821], [450, 846], [199, 846]], [[105, 846], [164, 846], [164, 871], [105, 871]], [[201, 847], [455, 847], [455, 871], [201, 871]], [[105, 871], [164, 871], [164, 898], [105, 898]], [[199, 873], [695, 873], [695, 896], [199, 896]], [[105, 896], [164, 896], [164, 923], [105, 923]], [[199, 898], [613, 898], [613, 921], [199, 921]], [[105, 921], [164, 921], [164, 948], [105, 948]], [[197, 923], [362, 921], [363, 946], [198, 948]], [[105, 946], [164, 946], [164, 973], [105, 973]], [[199, 948], [466, 948], [466, 973], [199, 973]], [[105, 971], [164, 971], [164, 998], [105, 998]], [[199, 975], [484, 975], [484, 998], [199, 998]], [[105, 998], [164, 998], [164, 1025], [105, 1025]], [[195, 998], [354, 996], [354, 1021], [196, 1023]], [[105, 1023], [164, 1023], [164, 1050], [105, 1050]], [[194, 1019], [254, 1019], [254, 1054], [194, 1054]], [[105, 1050], [164, 1050], [164, 1077], [105, 1077]], [[201, 1052], [510, 1052], [510, 1075], [201, 1075]], [[105, 1075], [164, 1075], [164, 1100], [105, 1100]], [[199, 1077], [531, 1077], [531, 1100], [199, 1100]], [[105, 1100], [164, 1100], [164, 1127], [105, 1127]], [[197, 1102], [370, 1102], [370, 1127], [197, 1127]], [[105, 1125], [164, 1125], [164, 1152], [105, 1152]], [[196, 1125], [251, 1125], [251, 1154], [196, 1154]], [[105, 1150], [164, 1150], [164, 1177], [105, 1177]], [[197, 1150], [738, 1152], [738, 1177], [197, 1175]], [[105, 1175], [164, 1175], [164, 1202], [105, 1202]], [[199, 1177], [649, 1177], [649, 1202], [199, 1202]], [[105, 1200], [164, 1200], [164, 1227], [105, 1227]], [[197, 1204], [338, 1204], [338, 1229], [197, 1229]], [[105, 1227], [164, 1227], [164, 1254], [105, 1254]], [[196, 1227], [268, 1227], [268, 1254], [196, 1254]], [[105, 1252], [164, 1252], [164, 1279], [105, 1279]], [[197, 1252], [288, 1252], [288, 1281], [197, 1281]], [[105, 1279], [162, 1279], [162, 1304], [105, 1304]], [[197, 1279], [370, 1279], [370, 1304], [197, 1304]], [[105, 1304], [162, 1304], [162, 1329], [105, 1329]], [[197, 1304], [484, 1304], [484, 1328], [197, 1328]], [[105, 1329], [164, 1329], [164, 1356], [105, 1356]], [[197, 1331], [482, 1331], [482, 1354], [197, 1354]], [[105, 1354], [164, 1354], [164, 1381], [105, 1381]], [[199, 1356], [368, 1356], [368, 1381], [199, 1381]], [[105, 1380], [164, 1380], [164, 1406], [105, 1406]], [[196, 1378], [272, 1378], [272, 1410], [196, 1410]], [[105, 1405], [164, 1405], [164, 1432], [105, 1432]], [[199, 1406], [418, 1406], [418, 1432], [199, 1432]], [[105, 1432], [164, 1432], [164, 1458], [105, 1458]], [[193, 1430], [271, 1426], [273, 1460], [195, 1464]], [[105, 1457], [164, 1457], [164, 1484], [105, 1484]], [[197, 1457], [288, 1457], [288, 1485], [197, 1485]], [[105, 1482], [164, 1482], [164, 1509], [105, 1509]], [[197, 1480], [324, 1480], [324, 1510], [197, 1510]], [[105, 1504], [164, 1509], [162, 1536], [102, 1531]], [[199, 1509], [375, 1509], [375, 1534], [199, 1534]], [[105, 1534], [164, 1534], [164, 1561], [105, 1561]], [[199, 1534], [329, 1534], [329, 1559], [199, 1559]], [[105, 1559], [164, 1559], [164, 1586], [105, 1586]], [[199, 1561], [437, 1561], [437, 1584], [199, 1584]], [[105, 1584], [164, 1584], [164, 1611], [105, 1611]], [[199, 1586], [357, 1586], [357, 1611], [199, 1611]], [[105, 1609], [165, 1609], [165, 1636], [105, 1636]], [[201, 1611], [436, 1611], [436, 1636], [201, 1636]], [[105, 1636], [164, 1636], [164, 1663], [105, 1663]], [[199, 1638], [393, 1638], [393, 1661], [199, 1661]], [[105, 1661], [164, 1661], [164, 1688], [105, 1688]], [[201, 1664], [334, 1664], [334, 1684], [201, 1684]], [[103, 1684], [167, 1684], [167, 1716], [103, 1716]], [[197, 1686], [304, 1686], [304, 1716], [197, 1716]]], "rec_boxes": [[27, 30, 528, 66], [105, 79, 164, 106], [197, 79, 430, 106], [103, 102, 165, 134], [199, 106, 277, 133], [201, 133, 468, 156], [105, 158, 169, 183], [201, 158, 501, 181], [105, 183, 169, 208], [201, 183, 430, 208], [105, 208, 169, 235], [199, 208, 272, 235], [105, 233, 167, 260], [197, 229, 329, 264], [105, 258, 169, 285], [199, 258, 562, 281], [105, 283, 169, 310], [199, 285, 734, 310], [105, 310, 167, 335], [199, 310, 318, 335], [105, 335, 167, 362], [199, 337, 436, 360], [105, 360, 164, 387], [199, 362, 418, 387], [105, 387, 162, 412], [199, 387, 482, 410], [105, 412, 162, 439], [201, 412, 450, 437], [105, 437, 162, 464], [201, 439, 400, 462], [105, 462, 162, 489], [199, 464, 574, 487], [105, 487, 162, 514], [199, 487, 407, 511], [105, 512, 162, 539], [197, 512, 411, 539], [102, 535, 162, 569], [199, 541, 443, 564], [105, 564, 162, 591], [199, 564, 311, 589], [105, 591, 162, 616], [197, 591, 382, 615], [105, 616, 160, 641], [199, 615, 315, 640], [105, 641, 162, 668], [199, 641, 316, 666], [105, 666, 162, 693], [199, 668, 412, 693], [105, 692, 162, 718], [196, 690, 231, 720], [105, 717, 164, 744], [197, 717, 391, 744], [105, 742, 164, 769], [199, 744, 340, 769], [105, 769, 162, 796], [199, 770, 453, 794], [105, 794, 164, 821], [197, 794, 288, 822], [105, 819, 164, 846], [199, 821, 450, 846], [105, 846, 164, 871], [201, 847, 455, 871], [105, 871, 164, 898], [199, 873, 695, 896], [105, 896, 164, 923], [199, 898, 613, 921], [105, 921, 164, 948], [197, 921, 363, 948], [105, 946, 164, 973], [199, 948, 466, 973], [105, 971, 164, 998], [199, 975, 484, 998], [105, 998, 164, 1025], [195, 996, 354, 1023], [105, 1023, 164, 1050], [194, 1019, 254, 1054], [105, 1050, 164, 1077], [201, 1052, 510, 1075], [105, 1075, 164, 1100], [199, 1077, 531, 1100], [105, 1100, 164, 1127], [197, 1102, 370, 1127], [105, 1125, 164, 1152], [196, 1125, 251, 1154], [105, 1150, 164, 1177], [197, 1150, 738, 1177], [105, 1175, 164, 1202], [199, 1177, 649, 1202], [105, 1200, 164, 1227], [197, 1204, 338, 1229], [105, 1227, 164, 1254], [196, 1227, 268, 1254], [105, 1252, 164, 1279], [197, 1252, 288, 1281], [105, 1279, 162, 1304], [197, 1279, 370, 1304], [105, 1304, 162, 1329], [197, 1304, 484, 1328], [105, 1329, 164, 1356], [197, 1331, 482, 1354], [105, 1354, 164, 1381], [199, 1356, 368, 1381], [105, 1380, 164, 1406], [196, 1378, 272, 1410], [105, 1405, 164, 1432], [199, 1406, 418, 1432], [105, 1432, 164, 1458], [193, 1426, 273, 1464], [105, 1457, 164, 1484], [197, 1457, 288, 1485], [105, 1482, 164, 1509], [197, 1480, 324, 1510], [102, 1504, 164, 1536], [199, 1509, 375, 1534], [105, 1534, 164, 1561], [199, 1534, 329, 1559], [105, 1559, 164, 1586], [199, 1561, 437, 1584], [105, 1584, 164, 1611], [199, 1586, 357, 1611], [105, 1609, 165, 1636], [201, 1611, 436, 1636], [105, 1636, 164, 1663], [199, 1638, 393, 1661], [105, 1661, 164, 1688], [201, 1664, 334, 1684], [103, 1684, 167, 1716], [197, 1686, 304, 1716]]}, "table_res_list": [{"cell_box_list": [[49.2692666053772, 81.03772330284119, 76.26854181289673, 107.70055198669434], [76.39913415908813, 81.41120219230652, 104.86917352676392, 132.49509048461914], [104.53739404678345, 81.7894012928009, 176.84176111221313, 132.53581619262695], [199.30355310440063, 80.70629012584686, 966.712809085846, 108.48708534240723], [49.24898386001587, 107.21329116821289, 76.24567651748657, 132.6026268005371], [199.0, 106.0, 277.0, 133.0], [49.254202365875244, 132.6772918701172, 76.25812005996704, 158.22138214111328], [76.25594568252563, 132.67603302001953, 104.82409906387329, 158.23670959472656], [104.68362665176392, 132.52558135986328, 176.9306435585022, 158.27346801757812], [196.24283838272095, 132.03717041015625, 966.0948281288147, 158.36465454101562], [23.187714099884033, 158.21846771240234, 49.02535104751587, 183.78217315673828], [76.29000329971313, 158.10514068603516, 104.69447565078735, 183.9269256591797], [104.43724298477173, 158.06246948242188, 177.0842843055725, 184.0359115600586], [185.9133095741272, 158.03057861328125, 967.0, 184.19066619873047], [49.120108127593994, 183.5853500366211, 76.17440271377563, 209.31099700927734], [76.30082941055298, 183.6379852294922, 104.63405084609985, 209.39087677001953], [104.46983575820923, 183.7320556640625, 177.3650918006897, 209.53958892822266], [201.0, 183.0, 430.0, 208.0], [22.876137256622314, 209.27141571044922, 49.002249240875244, 234.8066635131836], [49.07972955703735, 209.21016693115234, 76.17396020889282, 234.8661117553711], [76.25715112686157, 209.1747817993164, 104.55311059951782, 234.70409393310547], [104.38226556777954, 209.32466888427734, 177.36644983291626, 234.92574310302734], [184.95295190811157, 209.3414535522461, 966.671305179596, 234.7956314086914], [49.10327768325806, 234.99933624267578, 76.26327753067017, 260.64513397216797], [76.2372841835022, 234.84066009521484, 104.6154580116272, 260.51175689697266], [104.4944863319397, 234.85022735595703, 177.58709192276, 260.6725845336914], [194.33675622940063, 234.70172882080078, 966.7745156288147, 260.8220901489258], [23.34752893447876, 260.65637969970703, 48.91899347305298, 286.28992462158203], [76.29330682754517, 260.3975601196289, 104.63947534561157, 286.06392669677734], [104.40678644180298, 260.43041229248047, 177.63167810440063, 286.04561614990234], [199.0, 258.0, 562.0, 281.0], [23.22757387161255, 286.26551055908203, 48.935572147369385, 311.8336868286133], [49.029512882232666, 285.90526580810547, 76.19328546524048, 311.5997848510742], [76.30642175674438, 285.90995025634766, 104.60875940322876, 336.7464065551758], [104.46635675430298, 285.80699920654297, 177.69425439834595, 311.6216049194336], [193.30361413955688, 285.86690521240234, 966.245768070221, 312.5707321166992], [48.955957889556885, 311.63333892822266, 76.19706201553345, 336.9065628051758], [104.41239404678345, 311.5743179321289, 177.66950464248657, 336.93164825439453], [199.0, 310.0, 318.0, 335.0], [104.45859003067017, 336.90894317626953, 177.70759057998657, 412.9123306274414], [194.39306116104126, 336.9629898071289, 966.7856240272522, 362.8300247192383], [22.9505295753479, 362.38492584228516, 48.85109567642212, 387.57044219970703], [76.32043695449829, 362.15555572509766, 104.68929529190063, 412.40970611572266], [192.64852380752563, 362.7471389770508, 966.4799599647522, 387.9151382446289], [22.62644052505493, 387.55728912353516, 48.90667200088501, 437.7151565551758], [48.96652841567993, 387.35843658447266, 76.25859308242798, 437.56925201416016], [199.0, 387.0, 482.0, 410.0], [76.37169885635376, 412.3056411743164, 104.62301874160767, 437.47608184814453], [104.50338983535767, 412.83597564697266, 177.6121163368225, 438.0559768676758], [194.23218774795532, 413.1311721801758, 966.7210488319397, 437.87329864501953], [22.697781085968018, 437.8689651489258, 48.90140771865845, 488.69864654541016], [48.91105127334595, 437.84461212158203, 76.3195366859436, 488.7034683227539], [76.36349725723267, 437.66834259033203, 104.6067910194397, 463.0784683227539], [104.46912622451782, 437.99967193603516, 177.57287073135376, 463.69080352783203], [196.6410927772522, 438.0336685180664, 966.7605996131897, 463.8821487426758], [76.30338525772095, 462.89798736572266, 104.62854242324829, 488.54322052001953], [104.42674493789673, 463.38773345947266, 177.519464969635, 488.9955825805664], [199.0, 464.0, 574.0, 487.0], [49.062254428863525, 488.6972427368164, 76.20069360733032, 514.1379776000977], [76.28595209121704, 488.5641555786133, 104.63847589492798, 513.9997329711914], [104.42791986465454, 488.75565338134766, 177.56316614151, 514.5754165649414], [195.6399483680725, 489.1990737915039, 966.3834023475647, 515.0592422485352], [22.503403186798096, 514.360237121582, 48.9707818031311, 539.6105422973633], [49.09046030044556, 514.1686477661133, 76.19800806045532, 539.5761184692383], [76.29362726211548, 513.961555480957, 104.64667749404907, 539.4895095825195], [104.50396966934204, 514.4870681762695, 177.48096704483032, 540.0641250610352], [197.0, 512.0, 411.0, 539.0], [22.79675531387329, 539.7413101196289, 48.972525119781494, 565.3640518188477], [49.07982873916626, 539.7608413696289, 76.23785638809204, 565.2704238891602], [76.26722192764282, 539.7351455688477, 104.6527886390686, 565.2122268676758], [104.57766199111938, 540.0781021118164, 177.48536157608032, 565.7238845825195], [196.78422021865845, 540.1685562133789, 966.9885048866272, 566.2172622680664], [22.90651559829712, 565.0875625610352, 49.00788354873657, 590.8326187133789], [76.2355751991272, 565.018196105957, 104.69642877578735, 590.8741836547852], [104.55365228652954, 565.4330825805664, 177.53229761123657, 591.2049331665039], [195.99030542373657, 566.0989761352539, 966.8100991249084, 591.7133560180664], [49.08511972427368, 590.8843154907227, 76.12755060195923, 616.4443740844727], [76.23716974258423, 590.8776016235352, 104.68819665908813, 616.3570327758789], [104.54704523086548, 591.0035171508789, 177.6583046913147, 616.7653579711914], [197.0, 591.0, 382.0, 615.0], [22.887539386749268, 616.5137710571289, 49.001314640045166, 641.8993911743164], [76.25702142715454, 616.2962417602539, 104.70487451553345, 641.8569107055664], [104.5656533241272, 616.7171401977539, 177.65445947647095, 642.3110122680664], [196.56222009658813, 617.1691055297852, 967.0, 642.0147476196289], [23.128571033477783, 642.1377334594727, 48.98620843887329, 667.6766128540039], [49.13204050064087, 642.1242446899414, 76.20058679580688, 667.5839004516602], [76.24366998672485, 642.0774307250977, 104.66500329971313, 667.5757217407227], [104.60906457901001, 642.2981948852539, 177.5702919960022, 667.9755020141602], [196.8940224647522, 642.2693252563477, 967.0, 668.2842788696289], [23.11428689956665, 667.4228897094727, 49.04332208633423, 693.1808242797852], [76.21325159072876, 667.3655166625977, 104.69504022598267, 693.1907730102539], [104.61481714248657, 667.7482070922852, 177.52149438858032, 693.5416641235352], [199.0, 668.0, 412.0, 693.0], [49.11273813247681, 693.2295913696289, 76.09255456924438, 718.7625503540039], [76.20014429092407, 693.2012100219727, 104.71557092666626, 718.6189346313477], [104.53498315811157, 693.3097305297852, 177.62951135635376, 718.9639053344727], [197.19979333877563, 693.4047012329102, 966.631510257721, 719.4798965454102], [22.963486194610596, 718.8093643188477, 49.031118869781494, 744.1819229125977], [49.13586664199829, 718.7408828735352, 76.1145806312561, 744.2569351196289], [76.24923944473267, 718.5419082641602, 104.72267389297485, 744.1589736938477], [104.55655908584595, 718.8935317993164, 177.6028847694397, 744.5932998657227], [197.0, 717.0, 391.0, 744.0], [23.102665424346924, 744.4992446899414, 48.998255252838135, 769.9771499633789], [49.12433099746704, 744.4370498657227, 76.20152521133423, 769.8898696899414], [76.25446557998657, 744.3475723266602, 104.69530725479126, 769.7612075805664], [104.57215356826782, 744.6642837524414, 177.6083779335022, 795.5686416625977], [197.48555994033813, 744.5462417602539, 967.0, 770.7443008422852], [22.985348224639893, 769.7542495727539, 49.04289102554321, 795.5280532836914], [76.26534509658813, 769.6184463500977, 104.6906533241272, 795.4538345336914], [197.59386682510376, 770.5993423461914, 967.0, 796.4046401977539], [49.06550073623657, 795.5124282836914, 76.12890863418579, 821.0120010375977], [76.27019739151001, 795.4341201782227, 104.69264459609985, 821.0825576782227], [104.54360437393188, 795.5896377563477, 177.6150460243225, 821.3737564086914], [197.0, 794.0, 288.0, 822.0], [22.976276874542236, 821.1205825805664, 49.041929721832275, 846.3328018188477], [49.11903238296509, 821.0678482055664, 76.17127466201782, 846.5278091430664], [76.27295160293579, 820.9875869750977, 104.71599817276001, 846.5198135375977], [104.55768060684204, 821.4101943969727, 177.63302087783813, 846.9911270141602], [197.25483179092407, 821.9703140258789, 967.0, 847.0217666625977], [23.078728199005127, 846.7101211547852, 48.97594690322876, 872.3796768188477], [49.11945962905884, 846.7480239868164, 76.23427057266235, 872.2491226196289], [76.29999017715454, 846.5633926391602, 104.68161249160767, 872.0970230102539], [104.63678216934204, 846.8591690063477, 177.47933435440063, 872.3672866821289], [197.48490381240845, 846.8868789672852, 966.3658242225647, 872.6563491821289], [23.08943223953247, 872.0778579711914, 49.016844272613525, 897.9692153930664], [76.29724359512329, 872.0607070922852, 104.67746210098267, 897.8741836547852], [104.52328729629517, 872.2752456665039, 177.42690515518188, 898.0250625610352], [199.0, 873.0, 695.0, 896.0], [49.07084131240845, 897.9319839477539, 76.16021203994751, 923.4283218383789], [76.30150079727173, 897.7864151000977, 104.67318201065063, 923.5104141235352], [104.48270654678345, 897.9692764282227, 177.54738855361938, 923.7629776000977], [197.86493921279907, 898.3305435180664, 966.4416909217834, 923.9895401000977], [22.741526126861572, 923.5974502563477, 48.9590630531311, 948.9406509399414], [49.097986698150635, 923.4783096313477, 76.2040581703186, 948.9966812133789], [76.32240533828735, 923.4164810180664, 104.6867241859436, 948.9694595336914], [104.54217767715454, 923.7391738891602, 177.5035195350647, 949.0011978149414], [197.0, 921.0, 363.0, 948.0], [22.75417947769165, 949.1976089477539, 48.91215753555298, 974.8965835571289], [49.0619912147522, 949.2242813110352, 76.26668787002563, 974.7016372680664], [76.32362604141235, 949.0162124633789, 104.61903619766235, 974.5515518188477], [104.5314507484436, 949.0510635375977, 177.4456124305725, 974.6828994750977], [197.73346948623657, 949.1749649047852, 966.9915566444397, 974.9485244750977], [22.79918146133423, 974.5928726196289, 48.95030450820923, 1000.3338394165039], [48.948328495025635, 974.5081558227539, 76.27716302871704, 1000.3309097290039], [76.32011651992798, 974.5127334594727, 104.64053583145142, 1000.2035293579102], [104.44822931289673, 974.6859512329102, 177.50814294815063, 1000.1273574829102], [197.85830163955688, 975.1349258422852, 967.0, 1000.7782974243164], [48.959139347076416, 1000.2716445922852, 76.19041681289673, 1025.7718887329102], [76.31964349746704, 1000.1849746704102, 104.58404779434204, 1025.694923400879], [104.46927118301392, 1000.1667861938477, 177.5674386024475, 1025.935890197754], [195.0, 996.0, 354.0, 1023.0], [22.301727771759033, 1025.9541397094727, 48.87584924697876, 1075.9327774047852], [48.98842477798462, 1025.8050918579102, 76.21845483779907, 1051.0385513305664], [76.33160638809204, 1025.7012100219727, 104.60496759414673, 1076.0881118774414], [104.5025200843811, 1025.9083633422852, 177.61074304580688, 1051.512306213379], [197.8484444618225, 1026.5112686157227, 967.0, 1051.4303359985352], [48.90333795547485, 1050.8495254516602, 76.28275537490845, 1075.9039688110352], [104.43065118789673, 1051.5406875610352, 177.74105310440063, 1101.4955825805664], [201.0, 1052.0, 510.0, 1075.0], [48.887850284576416, 1076.2181777954102, 76.30622339248657, 1101.5063247680664], [197.74628686904907, 1076.6130142211914, 967.0, 1127.0673599243164], [22.738089084625244, 1101.565773010254, 48.879724979400635, 1126.968116760254], [76.36664819717407, 1101.702003479004, 104.66562128067017, 1151.8964614868164], [104.45903253555298, 1101.7357559204102, 177.84278345108032, 1126.7878189086914], [22.441582202911377, 1126.7428970336914, 48.952505588531494, 1177.3505630493164], [104.54125452041626, 1126.761329650879, 177.7623085975647, 1152.3705825805664], [196.0, 1125.0, 251.0, 1154.0], [48.929354190826416, 1151.990333557129, 76.20374536514282, 1177.1803970336914], [76.34835290908813, 1151.8205337524414, 104.62533807754517, 1177.153907775879], [104.47115564346313, 1152.1855239868164, 177.75377893447876, 1177.4975357055664], [197.916925907135, 1152.412208557129, 966.773600101471, 1203.7541275024414], [48.97560739517212, 1177.349464416504, 76.30957269668579, 1202.7685317993164], [76.27914667129517, 1177.228614807129, 104.6424126625061, 1202.653907775879], [104.57331323623657, 1177.4719009399414, 177.72130823135376, 1203.026710510254], [23.003174304962158, 1202.847267150879, 48.883909702301025, 1228.326271057129], [76.29690790176392, 1202.6547622680664, 104.64819574356079, 1228.2619400024414], [104.43425989151001, 1202.9262466430664, 177.86608362197876, 1228.4899673461914], [197.0, 1204.0, 338.0, 1229.0], [22.892454624176025, 1228.3425064086914, 48.972925662994385, 1253.9411392211914], [76.35851526260376, 1228.0800552368164, 104.71620416641235, 1253.733985900879], [104.55462121963501, 1228.2060317993164, 177.78084802627563, 1254.143898010254], [197.5589394569397, 1229.0622329711914, 966.8098549842834, 1279.513282775879], [22.67003870010376, 1253.818702697754, 48.94444513320923, 1279.2358169555664], [49.007051944732666, 1253.728614807129, 76.18705224990845, 1279.2807388305664], [76.29337549209595, 1253.641212463379, 104.63344049453735, 1279.2219009399414], [104.49383783340454, 1254.005470275879, 177.67133569717407, 1279.575294494629], [49.05106973648071, 1279.418556213379, 76.29924249649048, 1304.993507385254], [76.2519097328186, 1279.308448791504, 104.71063470840454, 1304.848487854004], [104.62802362442017, 1279.471290588379, 177.5906319618225, 1305.1923599243164], [197.87743616104126, 1279.603614807129, 967.0, 1305.779640197754], [23.20413827896118, 1305.0363540649414, 48.96628427505493, 1330.576759338379], [76.29619836807251, 1304.868019104004, 104.71161127090454, 1330.558204650879], [104.5236611366272, 1305.0446548461914, 177.73957300186157, 1330.769630432129], [197.67244958877563, 1305.8449478149414, 967.0, 1331.021827697754], [23.167253971099854, 1330.6147232055664, 49.024691104888916, 1356.232521057129], [76.34113550186157, 1330.4445571899414, 104.75490808486938, 1356.020118713379], [104.60045862197876, 1330.4736099243164, 177.70447778701782, 1356.329933166504], [197.0, 1331.0, 482.0, 1354.0], [23.009430408477783, 1356.1545181274414, 48.999868869781494, 1381.657814025879], [49.049829959869385, 1356.092140197754, 76.1812539100647, 1381.6498794555664], [76.27507257461548, 1355.957618713379, 104.69432306289673, 1381.5622329711914], [104.55944299697876, 1356.1823501586914, 177.734308719635, 1381.7072525024414], [197.82580041885376, 1356.6288833618164, 966.838297367096, 1381.715675354004], [23.099728107452393, 1381.695655822754, 49.02959680557251, 1407.489845275879], [49.10857629776001, 1381.7817153930664, 76.28686761856079, 1407.3715591430664], [76.20870447158813, 1381.6337661743164, 104.72752618789673, 1407.1891860961914], [104.61307001113892, 1381.5986099243164, 177.72095727920532, 1407.3991470336914], [197.71927881240845, 1381.7834243774414, 966.6131386756897, 1407.877052307129], [23.282227039337158, 1407.4289321899414, 48.990477085113525, 1432.920753479004], [76.29506158828735, 1407.184913635254, 104.76166772842407, 1432.852882385254], [104.55240869522095, 1407.2406997680664, 177.7996621131897, 1433.0451431274414], [199.0, 1406.0, 418.0, 1432.0], [23.019949436187744, 1432.9631118774414, 49.05826425552368, 1483.8820571899414], [76.31430292129517, 1432.7616958618164, 104.79142999649048, 1458.354347229004], [104.64486932754517, 1432.8324966430664, 177.73635339736938, 1458.7038345336914], [198.19913721084595, 1433.4396743774414, 966.8324990272522, 1459.499855041504], [49.08308267593384, 1458.4286880493164, 76.19875574111938, 1483.968116760254], [76.27700281143188, 1458.290382385254, 104.73212671279907, 1483.879493713379], [104.59649896621704, 1458.549171447754, 177.71990442276, 1484.081886291504], [197.0, 1457.0, 288.0, 1485.0], [23.158888339996338, 1484.0988540649414, 49.024202823638916, 1509.8476333618164], [49.11778116226196, 1484.142677307129, 76.2975869178772, 1509.6899185180664], [76.20689630508423, 1484.012794494629, 104.75268793106079, 1509.5644302368164], [104.64090967178345, 1483.9513931274414, 177.72382593154907, 1509.6835708618164], [197.98783349990845, 1484.1838150024414, 966.8821816444397, 1510.123146057129], [23.395630359649658, 1509.7773208618164, 49.0111985206604, 1535.298194885254], [76.29430627822876, 1509.525733947754, 104.76663446426392, 1535.1796646118164], [104.5562539100647, 1509.5668716430664, 177.8018136024475, 1535.3410415649414], [198.04090356826782, 1510.273536682129, 966.8814492225647, 1535.4809341430664], [23.359907627105713, 1535.357765197754, 49.01810693740845, 1560.8976821899414], [76.27724695205688, 1535.1427993774414, 104.75592279434204, 1586.2272720336914], [104.58606195449829, 1535.1066665649414, 177.822199344635, 1560.9767837524414], [199.0, 1534.0, 329.0, 1559.0], [23.320011615753174, 1560.7651138305664, 49.056639194488525, 1586.3390884399414], [49.068079471588135, 1560.7294692993164, 76.21582269668579, 1586.294044494629], [104.55687189102173, 1560.816017150879, 177.68897485733032, 1586.4367446899414], [197.9196572303772, 1561.2050552368164, 966.776773929596, 1586.3588638305664], [49.10655450820923, 1586.463478088379, 76.30534601211548, 1612.007423400879], [76.22076654434204, 1586.3368911743164, 104.70656824111938, 1611.8803482055664], [104.59322595596313, 1586.248634338379, 177.64596033096313, 1612.132911682129], [198.3390908241272, 1586.6000747680664, 966.7749428749084, 1612.503761291504], [23.18639039993286, 1612.0886001586914, 48.947927951812744, 1637.720802307129], [76.30140924453735, 1611.8498306274414, 104.71376276016235, 1637.5861587524414], [104.51772546768188, 1611.933204650879, 177.7720742225647, 1637.768898010254], [201.0, 1611.0, 436.0, 1636.0], [23.036186695098877, 1637.694679260254, 49.042940616607666, 1663.3132095336914], [76.42653131484985, 1637.365333557129, 104.73143243789673, 1687.8798599243164], [103.0, 1637.4347915649414, 177.71952295303345, 1716.0], [198.83046960830688, 1637.760841369629, 966.6296792030334, 1663.7868423461914], [49.123404026031494, 1662.877784729004, 76.4651665687561, 1688.2038345336914], [201.0, 1664.0, 334.0, 1684.0], [199.17580652236938, 1688.715431213379, 965.973062992096, 1715.1176528930664]], "pred_html": "<html><body><table><tbody><tr><td></td><td></td><td>0.000 3.896</td><td>经厚在第2暖推水平横截面</td></tr><tr><td></td><td>优化配置</td><td></td><td></td></tr><tr><td></td><td></td><td></td><td>虚拟模式-健康问题发展趋势列表：</td></tr><tr><td></td><td></td><td>0.049</td><td>C 反应蛋白C-REACTIVEPROTEIN</td></tr><tr><td></td><td></td><td>0.058</td><td>血尿酸SERUMURICACID</td></tr><tr><td></td><td></td><td></td><td>0.064</td></tr><tr><td></td><td></td><td>0.067</td><td>血管紧张素Ⅱ*</td></tr><tr><td></td><td></td><td>0.078</td><td>胆固酸COMMON PLASMACHOLESTERIN</td></tr><tr><td></td><td></td><td></td><td>0.088</td></tr><tr><td></td><td>0.089</td><td>血管紧张素I*</td><td></td></tr><tr><td>0.137 0.065 0.079</td><td>血钾PLASMA POTASSIUM</td><td></td><td></td></tr><tr><td></td><td></td><td>PERIPHERIC BLOOD LEUCOCYTES</td><td></td></tr><tr><td></td><td>0.082</td><td>尿中蛋白质PROTEIN INURINE</td><td></td></tr><tr><td></td><td></td><td></td><td>0.083</td></tr><tr><td></td><td>0.087</td><td>分段的中性粒细胞SEGMENTEDNEUTROPHILS</td><td></td></tr><tr><td></td><td></td><td>0.089</td><td>嗜碱性粒细胞BASOPHILS</td></tr><tr><td></td><td></td><td></td><td>0.092</td></tr><tr><td></td><td></td><td></td><td>0.099</td></tr><tr><td></td><td></td><td>0.107</td><td>BETA球蛋白*</td></tr><tr><td></td><td></td><td>0.110</td><td>单核细胞MONOCYTES</td></tr><tr><td></td><td></td><td>0.111</td><td>免疫球蛋白G*</td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td>0.114</td></tr><tr><td></td><td></td><td>0.117</td><td>血清蛋白SERUMPROTEIN</td></tr><tr><td></td><td></td><td>0.174</td><td>锂*</td></tr><tr><td></td><td></td><td></td><td>0.063</td></tr><tr><td></td><td></td><td></td><td>0.066 0.067</td></tr><tr><td></td><td></td><td>肿瘤标志物MELANOGENE在尿*</td><td></td></tr><tr><td></td><td></td><td>0.072</td><td>醛固酮尿*</td></tr><tr><td></td><td></td><td></td><td>0.074</td></tr><tr><td></td><td></td><td></td><td>0.075</td></tr><tr><td></td><td></td><td>0.076</td><td>ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM</td></tr><tr><td></td><td></td><td>0.076</td><td>肌酸磷 酸酵素COMMONCREATINPHOSPHOKINASE</td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td>0.079</td></tr><tr><td></td><td></td><td></td><td>0.080</td></tr><tr><td></td><td></td><td></td><td>0.082</td></tr><tr><td></td><td></td><td>0.083</td><td>RHEUMOFACTOR*</td></tr><tr><td></td><td></td><td></td><td>0.084</td></tr><tr><td></td><td>0.085 0.085</td><td>血清淀粉酵素SERUMALPHAAMYLASE</td><td></td></tr><tr><td></td><td>游离胆固醇FREEPLASMACHOLESTERIN 肿瘤标志物胸苷激酶</td><td></td><td></td></tr><tr><td></td><td>0.086</td><td>糖苷*</td><td></td></tr><tr><td></td><td></td><td>0.086</td><td>AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE 血清中的氨基酸NITROGENOFAMINOACIDSINSERUM</td></tr><tr><td></td><td></td><td>0.086</td><td></td></tr><tr><td></td><td></td><td>0.086</td><td>抗链球菌溶血素</td></tr><tr><td></td><td></td><td>0.088</td><td>铁蛋白* 醛固酮血*</td></tr><tr><td></td><td></td><td></td><td>0.089</td></tr><tr><td></td><td></td><td>0.091</td><td>红细胞沉降率(ESR)</td></tr><tr><td></td><td></td><td>0.091</td><td>酸性磷酸酵素ACIDPHOSPHATASE</td></tr><tr><td></td><td></td><td>0.093</td><td>嗜中性粒细胞STABNEUTROPHILS</td></tr><tr><td></td><td></td><td></td><td>0.093</td></tr><tr><td></td><td></td><td></td><td>0.093</td></tr><tr><td></td><td></td><td>0.095</td><td>尿肌配URINECREATININE</td></tr><tr><td></td><td></td><td>0.096</td><td></td></tr><tr><td></td><td></td><td>0.096</td><td>葡萄糖浆*</td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td>0.097</td></tr><tr><td></td><td></td><td>0.097</td><td>甲状腺素结合球蛋白</td></tr><tr><td></td><td></td><td>0.098</td><td>ALPHA2球蛋白</td></tr><tr><td></td><td></td><td>0.098</td><td>嗜酸性粒细胞EOSINOPHILES</td></tr><tr><td></td><td></td><td>0.098</td><td>血细胞比容，全血*</td></tr><tr><td></td><td></td><td>0.098</td><td>血组织胺BLOOD HISTAMINE</td></tr><tr><td></td><td></td><td>0.099 0.099 0.099</td><td> 维生素B1（THIAMINE）</td></tr></tbody></table></body></html>", "table_ocr_pred": {"rec_polys": [[[105, 79], [164, 79], [164, 106], [105, 106]], [[197, 79], [430, 81], [430, 106], [197, 104]], [[103, 102], [165, 102], [165, 134], [103, 134]], [[199, 106], [277, 106], [277, 133], [199, 133]], [[201, 133], [468, 133], [468, 156], [201, 156]], [[105, 158], [169, 158], [169, 183], [105, 183]], [[201, 158], [501, 158], [501, 181], [201, 181]], [[105, 183], [169, 183], [169, 208], [105, 208]], [[201, 183], [430, 183], [430, 208], [201, 208]], [[105, 208], [169, 208], [169, 235], [105, 235]], [[199, 208], [272, 208], [272, 235], [199, 235]], [[105, 233], [167, 233], [167, 260], [105, 260]], [[197, 233], [328, 229], [329, 260], [198, 264]], [[105, 258], [169, 258], [169, 285], [105, 285]], [[199, 258], [562, 258], [562, 281], [199, 281]], [[105, 283], [169, 283], [169, 310], [105, 310]], [[199, 285], [734, 285], [734, 310], [199, 310]], [[105, 310], [167, 310], [167, 335], [105, 335]], [[199, 310], [318, 310], [318, 335], [199, 335]], [[105, 335], [167, 335], [167, 362], [105, 362]], [[199, 337], [436, 337], [436, 360], [199, 360]], [[105, 360], [164, 360], [164, 387], [105, 387]], [[199, 362], [418, 362], [418, 387], [199, 387]], [[105, 387], [162, 387], [162, 412], [105, 412]], [[199, 387], [482, 387], [482, 410], [199, 410]], [[105, 412], [162, 412], [162, 439], [105, 439]], [[201, 412], [450, 412], [450, 437], [201, 437]], [[105, 437], [162, 437], [162, 464], [105, 464]], [[201, 439], [400, 439], [400, 462], [201, 462]], [[105, 462], [162, 462], [162, 489], [105, 489]], [[199, 464], [574, 464], [574, 487], [199, 487]], [[105, 487], [162, 487], [162, 514], [105, 514]], [[199, 487], [407, 487], [407, 511], [199, 511]], [[105, 512], [162, 512], [162, 539], [105, 539]], [[197, 514], [410, 512], [411, 537], [197, 539]], [[105, 535], [162, 540], [160, 569], [102, 564]], [[199, 541], [443, 541], [443, 564], [199, 564]], [[105, 564], [162, 564], [162, 591], [105, 591]], [[199, 564], [311, 564], [311, 589], [199, 589]], [[105, 591], [162, 591], [162, 616], [105, 616]], [[197, 591], [382, 591], [382, 615], [197, 615]], [[105, 616], [160, 616], [160, 641], [105, 641]], [[199, 615], [315, 615], [315, 640], [199, 640]], [[105, 641], [162, 641], [162, 668], [105, 668]], [[199, 641], [316, 641], [316, 666], [199, 666]], [[105, 666], [162, 666], [162, 693], [105, 693]], [[199, 668], [412, 668], [412, 693], [199, 693]], [[105, 692], [162, 692], [162, 718], [105, 718]], [[196, 690], [231, 690], [231, 720], [196, 720]], [[105, 717], [164, 717], [164, 744], [105, 744]], [[197, 717], [391, 719], [391, 744], [197, 742]], [[105, 742], [164, 742], [164, 769], [105, 769]], [[199, 744], [340, 744], [340, 769], [199, 769]], [[105, 769], [162, 769], [162, 796], [105, 796]], [[199, 770], [453, 770], [453, 794], [199, 794]], [[105, 794], [164, 794], [164, 821], [105, 821]], [[197, 794], [288, 794], [288, 822], [197, 822]], [[105, 819], [164, 819], [164, 846], [105, 846]], [[199, 821], [450, 821], [450, 846], [199, 846]], [[105, 846], [164, 846], [164, 871], [105, 871]], [[201, 847], [455, 847], [455, 871], [201, 871]], [[105, 871], [164, 871], [164, 898], [105, 898]], [[199, 873], [695, 873], [695, 896], [199, 896]], [[105, 896], [164, 896], [164, 923], [105, 923]], [[199, 898], [613, 898], [613, 921], [199, 921]], [[105, 921], [164, 921], [164, 948], [105, 948]], [[197, 923], [362, 921], [363, 946], [198, 948]], [[105, 946], [164, 946], [164, 973], [105, 973]], [[199, 948], [466, 948], [466, 973], [199, 973]], [[105, 971], [164, 971], [164, 998], [105, 998]], [[199, 975], [484, 975], [484, 998], [199, 998]], [[105, 998], [164, 998], [164, 1025], [105, 1025]], [[195, 998], [354, 996], [354, 1021], [196, 1023]], [[105, 1023], [164, 1023], [164, 1050], [105, 1050]], [[194, 1019], [254, 1019], [254, 1054], [194, 1054]], [[105, 1050], [164, 1050], [164, 1077], [105, 1077]], [[201, 1052], [510, 1052], [510, 1075], [201, 1075]], [[105, 1075], [164, 1075], [164, 1100], [105, 1100]], [[199, 1077], [531, 1077], [531, 1100], [199, 1100]], [[105, 1100], [164, 1100], [164, 1127], [105, 1127]], [[197, 1102], [370, 1102], [370, 1127], [197, 1127]], [[105, 1125], [164, 1125], [164, 1152], [105, 1152]], [[196, 1125], [251, 1125], [251, 1154], [196, 1154]], [[105, 1150], [164, 1150], [164, 1177], [105, 1177]], [[197, 1150], [738, 1152], [738, 1177], [197, 1175]], [[105, 1175], [164, 1175], [164, 1202], [105, 1202]], [[199, 1177], [649, 1177], [649, 1202], [199, 1202]], [[105, 1200], [164, 1200], [164, 1227], [105, 1227]], [[197, 1204], [338, 1204], [338, 1229], [197, 1229]], [[105, 1227], [164, 1227], [164, 1254], [105, 1254]], [[196, 1227], [268, 1227], [268, 1254], [196, 1254]], [[105, 1252], [164, 1252], [164, 1279], [105, 1279]], [[197, 1252], [288, 1252], [288, 1281], [197, 1281]], [[105, 1279], [162, 1279], [162, 1304], [105, 1304]], [[197, 1279], [370, 1279], [370, 1304], [197, 1304]], [[105, 1304], [162, 1304], [162, 1329], [105, 1329]], [[197, 1304], [484, 1304], [484, 1328], [197, 1328]], [[105, 1329], [164, 1329], [164, 1356], [105, 1356]], [[197, 1331], [482, 1331], [482, 1354], [197, 1354]], [[105, 1354], [164, 1354], [164, 1381], [105, 1381]], [[199, 1356], [368, 1356], [368, 1381], [199, 1381]], [[105, 1380], [164, 1380], [164, 1406], [105, 1406]], [[196, 1378], [272, 1378], [272, 1410], [196, 1410]], [[105, 1405], [164, 1405], [164, 1432], [105, 1432]], [[199, 1406], [418, 1406], [418, 1432], [199, 1432]], [[105, 1432], [164, 1432], [164, 1458], [105, 1458]], [[193, 1430], [271, 1426], [273, 1460], [195, 1464]], [[105, 1457], [164, 1457], [164, 1484], [105, 1484]], [[197, 1457], [288, 1457], [288, 1485], [197, 1485]], [[105, 1482], [164, 1482], [164, 1509], [105, 1509]], [[197, 1480], [324, 1480], [324, 1510], [197, 1510]], [[105, 1504], [164, 1509], [162, 1536], [102, 1531]], [[199, 1509], [375, 1509], [375, 1534], [199, 1534]], [[105, 1534], [164, 1534], [164, 1561], [105, 1561]], [[199, 1534], [329, 1534], [329, 1559], [199, 1559]], [[105, 1559], [164, 1559], [164, 1586], [105, 1586]], [[199, 1561], [437, 1561], [437, 1584], [199, 1584]], [[105, 1584], [164, 1584], [164, 1611], [105, 1611]], [[199, 1586], [357, 1586], [357, 1611], [199, 1611]], [[105, 1609], [165, 1609], [165, 1636], [105, 1636]], [[201, 1611], [436, 1611], [436, 1636], [201, 1636]], [[105, 1636], [164, 1636], [164, 1663], [105, 1663]], [[199, 1638], [393, 1638], [393, 1661], [199, 1661]], [[105, 1661], [164, 1661], [164, 1688], [105, 1688]], [[201, 1664], [334, 1664], [334, 1684], [201, 1684]], [[103, 1684], [167, 1684], [167, 1716], [103, 1716]], [[197, 1686], [304, 1686], [304, 1716], [197, 1716]]], "rec_texts": ["0.000", "经厚在第2暖推水平横截面", "3.896", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.049", "C反应蛋白C-REACTIVEPROTEIN", "0.058", "血尿酸SERUMURICACID", "0.064", "脂肪酶*", "0.067", "血管紧张素Ⅱ*", "0.078", "胆固酸COMMON PLASMACHOLESTERIN", "0.088", "血浆丰配化脂肪酸NONETHERIZED FATTYACIDSOF PLASMA", "0.089", "血管紧张素I*", "0.137", "血钾PLASMA POTASSIUM", "0.065", "血清蛋白SERUM ALBUMEN", "0.079", "PERIPHERIC BLOOD LEUCOCYTES", "0.082", "尿中蛋白质PROTEIN INURINE", "0.083", "血红蛋白HAEMOGLOBIN", "0.087", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.089", "嗜碱性粒细胞BASOPHILS", "0.092", "血红血球ERYTHROCYTES", "0.099", "尿白血球URINELEUCOCYTES", "0.107", "BETA球蛋白*", "0.110", "单核细胞MONOCYTES", "0.111", "免疫球蛋白G*", "0.114", "免疫球蛋白M*", "0.117", "血清蛋白SERUMPROTEIN", "0.174", "锂*", "0.063", "17-血浆氧皮质类固醇类", "0.066", "17-尿中酮类固醇", "0.067", "肿瘤标志物MELANOGENE在尿*", "0.072", "醛固酮尿*", "0.074", "血清溶菌酵SERUMLYSOZYME", "0.075", "血清补体SERUM COMPLEMENT", "0.076", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.076", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.079", "血糖BLOOD SUGAR", "0.080", "尿中肾上腺素URINEADRENALIN", "0.082", "血浆磷脂PLASMAPHOSPHOTIDES", "0.083", "RHEUMOFACTOR*", "0.084", "肾素*", "0.085", "血清淀粉酵素SERUMALPHAAMYLASE", "0.085", "游离胆固醇FREEPLASMACHOLESTERIN", "0.086", "肿瘤标 志物胸苷激酶", "0.086", "糖苷*", "0.086", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.086", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.086", "抗链球菌溶血素", "0.088", "铁蛋白*", "0.089", "醛固酮血*", "0.091", "红细胞沉降率(ESR)", "0.091", "酸性磷酸酵素ACIDPHOSPHATASE", "0.093", "嗜中性粒细胞STABNEUTROPHILS", "0.093", "血尿素BLOODUREA", "0.093", "胆汁酸*", "0.095", "尿肌配URINECREATININE", "0.096", "催乳素*", "0.096", "葡萄糖浆*", "0.097", "甲状腺球蛋白*", "0.097", "甲状腺素结合球蛋白", "0.098", "ALPHA2球蛋白", "0.098", "嗜酸性粒细胞EOSINOPHILES", "0.098", "血细胞比容，全血*", "0.098", "血组织胺BLOOD HISTAMINE", "0.099", "维 生素B1（THIAMINE）", "0.099", "糖基化血红蛋白", "0.099", "胰高血糖素*"], "rec_scores": [0.9992481470108032, 0.8813357353210449, 0.9996616244316101, 0.9983981251716614, 0.9640860557556152, 0.9999086260795593, 0.9874621033668518, 0.9999423027038574, 0.9979388117790222, 0.9999420046806335, 0.9784912467002869, 0.9998849630355835, 0.9394027590751648, 0.999956488609314, 0.941057562828064, 0.9999154806137085, 0.9386655688285828, 0.9998348355293274, 0.9409072995185852, 0.9998716115951538, 0.9822050929069519, 0.9998233914375305, 0.9814016819000244, 0.9996112585067749, 0.97152179479599, 0.9997431039810181, 0.976643443107605, 0.9997656941413879, 0.9981578588485718, 0.9997803568840027, 0.9966168403625488, 0.99980628490448, 0.9983251690864563, 0.9997758865356445, 0.9867699146270752, 0.9997366070747375, 0.9928971529006958, 0.9995795488357544, 0.9590877294540405, 0.9992464780807495, 0.9883661866188049, 0.9985781908035278, 0.9826647639274597, 0.9312297105789185, 0.980752170085907, 0.9994608163833618, 0.9972257614135742, 0.9991631507873535, 0.8812094926834106, 0.9998235702514648, 0.9876624941825867, 0.999659538269043, 0.9848162531852722, 0.9997014999389648, 0.9690084457397461, 0.9996992349624634, 0.9791863560676575, 0.9997485876083374, 0.9938246607780457, 0.9996604919433594, 0.9743426442146301, 0.9996326565742493, 0.9959184527397156, 0.9997272491455078, 0.9964339137077332, 0.9997580647468567, 0.9660680294036865, 0.9996770620346069, 0.9974356889724731, 0.9997841715812683, 0.9813632965087891, 0.9997931718826294, 0.9969139099121094, 0.9997332692146301, 0.9378030896186829, 0.9997544288635254, 0.9968136548995972, 0.9996091723442078, 0.9958581328392029, 0.9997441172599792, 0.978713870048523, 0.999790370464325, 0.9471707344055176, 0.9997652173042297, 0.9939124584197998, 0.9997861981391907, 0.9972487092018127, 0.9996930360794067, 0.9974395036697388, 0.999783992767334, 0.9549832344055176, 0.9997812509536743, 0.9852700233459473, 0.9995215535163879, 0.9253935217857361, 0.9995633363723755, 0.9960333704948425, 0.9998371005058289, 0.9951410889625549, 0.9997886419296265, 0.9944164752960205, 0.9996522068977356, 0.9588331580162048, 0.9997732043266296, 0.9675629138946533, 0.9996237754821777, 0.8078666925430298, 0.999738335609436, 0.9789459109306335, 0.9998100399971008, 0.9713653326034546, 0.999829113483429, 0.9810749888420105, 0.9996970891952515, 0.9868845343589783, 0.9997416734695435, 0.9977938532829285, 0.9998056292533875, 0.9600962400436401, 0.9997445344924927, 0.9672539234161377, 0.9996654391288757, 0.9473199248313904, 0.9997137188911438, 0.9949651956558228, 0.9996391534805298, 0.9498085379600525], "rec_boxes": [[105, 79, 164, 106], [197, 79, 430, 106], [103, 102, 165, 134], [199, 106, 277, 133], [201, 133, 468, 156], [105, 158, 169, 183], [201, 158, 501, 181], [105, 183, 169, 208], [201, 183, 430, 208], [105, 208, 169, 235], [199, 208, 272, 235], [105, 233, 167, 260], [197, 229, 329, 264], [105, 258, 169, 285], [199, 258, 562, 281], [105, 283, 169, 310], [199, 285, 734, 310], [105, 310, 167, 335], [199, 310, 318, 335], [105, 335, 167, 362], [199, 337, 436, 360], [105, 360, 164, 387], [199, 362, 418, 387], [105, 387, 162, 412], [199, 387, 482, 410], [105, 412, 162, 439], [201, 412, 450, 437], [105, 437, 162, 464], [201, 439, 400, 462], [105, 462, 162, 489], [199, 464, 574, 487], [105, 487, 162, 514], [199, 487, 407, 511], [105, 512, 162, 539], [197, 512, 411, 539], [102, 535, 162, 569], [199, 541, 443, 564], [105, 564, 162, 591], [199, 564, 311, 589], [105, 591, 162, 616], [197, 591, 382, 615], [105, 616, 160, 641], [199, 615, 315, 640], [105, 641, 162, 668], [199, 641, 316, 666], [105, 666, 162, 693], [199, 668, 412, 693], [105, 692, 162, 718], [196, 690, 231, 720], [105, 717, 164, 744], [197, 717, 391, 744], [105, 742, 164, 769], [199, 744, 340, 769], [105, 769, 162, 796], [199, 770, 453, 794], [105, 794, 164, 821], [197, 794, 288, 822], [105, 819, 164, 846], [199, 821, 450, 846], [105, 846, 164, 871], [201, 847, 455, 871], [105, 871, 164, 898], [199, 873, 695, 896], [105, 896, 164, 923], [199, 898, 613, 921], [105, 921, 164, 948], [197, 921, 363, 948], [105, 946, 164, 973], [199, 948, 466, 973], [105, 971, 164, 998], [199, 975, 484, 998], [105, 998, 164, 1025], [195, 996, 354, 1023], [105, 1023, 164, 1050], [194, 1019, 254, 1054], [105, 1050, 164, 1077], [201, 1052, 510, 1075], [105, 1075, 164, 1100], [199, 1077, 531, 1100], [105, 1100, 164, 1127], [197, 1102, 370, 1127], [105, 1125, 164, 1152], [196, 1125, 251, 1154], [105, 1150, 164, 1177], [197, 1150, 738, 1177], [105, 1175, 164, 1202], [199, 1177, 649, 1202], [105, 1200, 164, 1227], [197, 1204, 338, 1229], [105, 1227, 164, 1254], [196, 1227, 268, 1254], [105, 1252, 164, 1279], [197, 1252, 288, 1281], [105, 1279, 162, 1304], [197, 1279, 370, 1304], [105, 1304, 162, 1329], [197, 1304, 484, 1328], [105, 1329, 164, 1356], [197, 1331, 482, 1354], [105, 1354, 164, 1381], [199, 1356, 368, 1381], [105, 1380, 164, 1406], [196, 1378, 272, 1410], [105, 1405, 164, 1432], [199, 1406, 418, 1432], [105, 1432, 164, 1458], [193, 1426, 273, 1464], [105, 1457, 164, 1484], [197, 1457, 288, 1485], [105, 1482, 164, 1509], [197, 1480, 324, 1510], [102, 1504, 164, 1536], [199, 1509, 375, 1534], [105, 1534, 164, 1561], [199, 1534, 329, 1559], [105, 1559, 164, 1586], [199, 1561, 437, 1584], [105, 1584, 164, 1611], [199, 1586, 357, 1611], [105, 1609, 165, 1636], [201, 1611, 436, 1636], [105, 1636, 164, 1663], [199, 1638, 393, 1661], [105, 1661, 164, 1688], [201, 1664, 334, 1684], [103, 1684, 167, 1716], [197, 1686, 304, 1716]]}}]}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//66a4ab1e-891f-4ff3-a5e1-c3143565da0f/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-22T01%3A07%3A33Z%2F-1%2F%2F0a73eaee3f0cce7e709ffd78ee6400a4d508cedda689540b16d296dc5ff62504", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//66a4ab1e-891f-4ff3-a5e1-c3143565da0f/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-22T01%3A07%3A33Z%2F-1%2F%2Fe488e956fd6f478826a7e0d25a3b76531d63e9ce9aeb666698828fa6ba78e3dc", "table_cell_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//66a4ab1e-891f-4ff3-a5e1-c3143565da0f/table_cell_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-22T01%3A07%3A33Z%2F-1%2F%2F99cfd9dfa9c0a4b01621f54b8830a5579164d363df882113f74e8f6b15ea3e6b"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//66a4ab1e-891f-4ff3-a5e1-c3143565da0f/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-22T01%3A07%3A33Z%2F-1%2F%2F6918fc179476f13229d9190a27c43e25d8e61512c84697781589fa9fbf337bf2"}], "dataInfo": {"width": 967, "height": 1720, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}