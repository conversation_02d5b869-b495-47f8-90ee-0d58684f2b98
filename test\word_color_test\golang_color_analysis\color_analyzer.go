package main

import (
	"fmt"
	"math"
	"strconv"
	"strings"
)

// convertToIntSlice 将[][2]int转换为[][]int
func convertToIntSlice(coords [][2]int) [][]int {
	result := make([][]int, len(coords))
	for i, coord := range coords {
		result[i] = []int{coord[0], coord[1]}
	}
	return result
}

// IsColorInRange 检查RGB值是否在指定颜色范围内
func (oca *OptimizedColorAnalyzer) IsColorInRange(rgb RGB, colorName string) bool {
	r, g, b := rgb.R, rgb.G, rgb.B

	switch colorName {
	case "红色":
		// 红色判断：基于实际测量范围 RGB: (255,0,0) 到 RGB: (255,160,128)
		// 更新的红色分类规则，扩大识别范围
		return (r >= 235 && g <= 160 && b <= 140 && r > g && r > b) ||
			(r >= 240 && g <= 180 && b <= 150 && r >= g+50)

	case "橘色":
		// 橘色判断：基于实际测量范围 RGB: (235,146,53) 到 RGB: (255,231,220)
		return (235 <= r && r <= 255) && (146 <= g && g <= 231) && (53 <= b && b <= 220) &&
			(g >= b) && (float64(r) >= float64(g)*0.8) // 确保红色通道占主导，绿色通道次之

	case "绿色":
		// 绿色判断：基于实际测量范围 RGB: (120,180,120) 到 RGB: (220,240,220)
		// 绿色分类规则：绿色通道占主导，红蓝通道相对较低
		return (120 <= r && r <= 220) && (150 <= g && g <= 240) && (100 <= b && b <= 220) &&
			(g >= r) && (g >= b) && (g >= r+20) // 确保绿色通道明显占主导

	case "蓝色":
		// 蓝色判断：基于实际测量范围 RGB: (21,21,170) 到 RGB: (110,166,255)
		// 更新的蓝色分类规则，扩大识别范围
		return (53 <= r && r <= 200) && (130 <= g && g <= 200) && (170 <= b && b <= 255) &&
			(b >= r) && (b >= g) // 确保蓝色通道占主导

	case "白色":
		return r >= 240 && g >= 240 && b >= 240

	case "黑色":
		return r <= 50 && g <= 50 && b <= 50
	}

	return false
}

// ClassifyColorByStandards 根据标准颜色范围分类颜色
func (oca *OptimizedColorAnalyzer) ClassifyColorByStandards(rgb RGB) string {
	// 按优先级检查颜色（红色 > 橘色 > 绿色 > 蓝色）
	priorityOrder := []string{"红色", "橘色", "绿色", "蓝色"}

	for _, colorName := range priorityOrder {
		if oca.IsColorInRange(rgb, colorName) {
			return colorName
		}
	}

	return oca.FuzzyColorClassification(rgb)
}

// FuzzyColorClassification 模糊颜色分类（用于不在标准范围内的颜色）
func (oca *OptimizedColorAnalyzer) FuzzyColorClassification(rgb RGB) string {
	r, g, b := float64(rgb.R), float64(rgb.G), float64(rgb.B)

	// 只检测四种目标颜色：红色、橘色、绿色、蓝色
	targetColors := []string{"红色", "橘色", "绿色", "蓝色"}

	// 计算与各标准颜色的距离
	minDistance := math.Inf(1)
	closestColor := "蓝色" // 默认为蓝色（正常值）

	for _, colorName := range targetColors {
		if standard, exists := oca.colorStandards[colorName]; exists {
			stdR, stdG, stdB := float64(standard.StandardRGB[0]), float64(standard.StandardRGB[1]), float64(standard.StandardRGB[2])
			distance := math.Sqrt(math.Pow(r-stdR, 2) + math.Pow(g-stdG, 2) + math.Pow(b-stdB, 2))

			if distance < minDistance {
				minDistance = distance
				closestColor = colorName
			}
		}
	}

	// 应用距离阈值判断
	if minDistance > oca.distanceThreshold {
		fmt.Printf("[模糊分类] RGB(%d,%d,%d) 距离最近颜色 %s 的距离为 %.1f，超过阈值 %.0f，默认为蓝色\n",
			rgb.R, rgb.G, rgb.B, closestColor, minDistance, oca.distanceThreshold)
		return "蓝色"
	}

	fmt.Printf("[模糊分类] RGB(%d,%d,%d) 归类为 %s，距离: %.1f\n", rgb.R, rgb.G, rgb.B, closestColor, minDistance)
	return closestColor
}

// GetPixelColor 获取指定坐标的像素颜色（使用加权多点采样）
// 优化版本：扩展采样范围到9x9，更好地处理边缘噪声
func (oca *OptimizedColorAnalyzer) GetPixelColor(x, y int, sampleSize int) RGB {
	if sampleSize == 0 {
		sampleSize = 9 // 从5x5扩展到9x9采样
	}

	bounds := oca.image.Bounds()
	width, height := bounds.Dx(), bounds.Dy()

	// 确保坐标在图像范围内
	if x < 0 {
		x = 0
	}
	if x >= width {
		x = width - 1
	}
	if y < 0 {
		y = 0
	}
	if y >= height {
		y = height - 1
	}

	// 扩大采样范围，使用改进的加权平均算法
	var totalR, totalG, totalB, totalWeight float64
	centerWeight := 8.0 // 增加中心点权重以适应更大的采样范围
	maxDistance := float64(sampleSize) / 2.0

	for dx := -sampleSize / 2; dx <= sampleSize/2; dx++ {
		for dy := -sampleSize / 2; dy <= sampleSize/2; dy++ {
			px := x + dx
			py := y + dy

			// 确保采样点在图像范围内
			if px < 0 {
				px = 0
			}
			if px >= width {
				px = width - 1
			}
			if py < 0 {
				py = 0
			}
			if py >= height {
				py = height - 1
			}

			c := oca.image.At(px, py)
			r, g, b, _ := c.RGBA()

			// 转换为8位颜色值
			r8, g8, b8 := uint8(r>>8), uint8(g>>8), uint8(b>>8)

			// 改进的权重计算：使用高斯权重分布，更好地处理边缘噪声
			distance := math.Sqrt(float64(dx*dx + dy*dy)) // 使用欧几里得距离
			var weight float64
			if distance == 0 {
				weight = centerWeight
			} else {
				// 高斯权重分布：距离越远权重衰减越快
				sigma := maxDistance / 3.0 // 标准差设为最大距离的1/3
				weight = centerWeight * math.Exp(-0.5*math.Pow(distance/sigma, 2))
				// 确保最小权重不低于0.1
				weight = math.Max(0.1, weight)
			}

			totalR += float64(r8) * weight
			totalG += float64(g8) * weight
			totalB += float64(b8) * weight
			totalWeight += weight
		}
	}

	// 计算加权平均颜色
	avgR := int(totalR / totalWeight)
	avgG := int(totalG / totalWeight)
	avgB := int(totalB / totalWeight)

	fmt.Printf("[采样优化] 坐标(%d,%d) 采样范围%dx%d 结果RGB(%d,%d,%d)\n",
		x, y, sampleSize, sampleSize, avgR, avgG, avgB)

	return RGB{R: avgR, G: avgG, B: avgB}
}

// ExtractNumericTextPairs 根据精细化策略提取数字-文字键值对
func (oca *OptimizedColorAnalyzer) ExtractNumericTextPairs() ([]NumericTextPair, string, error) {
	fmt.Println("[提取] 开始提取数字-文字键值对")

	// 导航到 overall_ocr_res 部分
	if len(oca.ocrData.Result.TableRecResults) == 0 {
		return nil, "", fmt.Errorf("未找到 tableRecResults 数据")
	}

	overallOCRRes := oca.ocrData.Result.TableRecResults[0].PrunedResult.OverallOCRRes
	recTexts := overallOCRRes.RecTexts
	recPolys := overallOCRRes.RecPolys

	if len(recTexts) == 0 || len(recPolys) == 0 {
		return nil, "", fmt.Errorf("rec_texts 或 rec_polys 为空")
	}

	fmt.Printf("[提取] 找到 %d 个文本项\n", len(recTexts))

	// 识别数字索引
	var numericIndices []int
	for i, text := range recTexts {
		if _, err := strconv.ParseFloat(text, 64); err == nil {
			numericIndices = append(numericIndices, i)
		}
	}

	fmt.Printf("[提取] 找到 %d 个数字\n", len(numericIndices))

	// 构建数字-文字键值对
	var pairs []NumericTextPair
	var targetOrgan string

	for _, numIdx := range numericIndices {
		numericText := recTexts[numIdx]
		numericValue, err := strconv.ParseFloat(numericText, 64)
		if err != nil {
			continue
		}

		// 计算数字的坐标
		numPoly := recPolys[numIdx]
		if len(numPoly) < 4 {
			continue
		}

		var xCoords, yCoords []float64
		for _, point := range numPoly {
			if len(point) >= 2 {
				xCoords = append(xCoords, float64(point[0]))
				yCoords = append(yCoords, float64(point[1]))
			}
		}

		if len(xCoords) == 0 || len(yCoords) == 0 {
			continue
		}

		// 计算中心点
		var sumX, sumY float64
		for _, x := range xCoords {
			sumX += x
		}
		for _, y := range yCoords {
			sumY += y
		}
		numCenterX := int(sumX / float64(len(xCoords)))
		numCenterY := int(sumY / float64(len(yCoords)))

		// 检查是否为标志性的"0.000"
		if numericText == "0.000" {
			// 查找其后的文字元素作为目标器官
			if numIdx+1 < len(recTexts) {
				targetOrgan = recTexts[numIdx+1]
				fmt.Printf("[识别] 发现目标器官: %s\n", targetOrgan)
			}
			continue
		}

		// 查找紧邻的文字元素
		var textElements []string
		var textCoordinates [][2]int

		// 检查后续元素
		for j := numIdx + 1; j < len(recTexts) && j < numIdx+3; j++ {
			// 确保不是数字
			if _, err := strconv.ParseFloat(recTexts[j], 64); err != nil {
				textElements = append(textElements, recTexts[j])

				// 计算文字的坐标
				textPoly := recPolys[j]
				if len(textPoly) >= 4 {
					var textXCoords, textYCoords []float64
					for _, point := range textPoly {
						if len(point) >= 2 {
							textXCoords = append(textXCoords, float64(point[0]))
							textYCoords = append(textYCoords, float64(point[1]))
						}
					}

					if len(textXCoords) > 0 && len(textYCoords) > 0 {
						var sumTextX, sumTextY float64
						for _, x := range textXCoords {
							sumTextX += x
						}
						for _, y := range textYCoords {
							sumTextY += y
						}
						textCenterX := int(sumTextX / float64(len(textXCoords)))
						textCenterY := int(sumTextY / float64(len(textYCoords)))
						textCoordinates = append(textCoordinates, [2]int{textCenterX, textCenterY})
					} else {
						textCoordinates = append(textCoordinates, [2]int{0, 0})
					}
				} else {
					textCoordinates = append(textCoordinates, [2]int{0, 0})
				}
			}
		}

		// 检查特殊情况和过滤
		if len(textElements) >= 2 {
			hasOptimization := false
			hasVirtualMode := false
			for _, text := range textElements {
				if strings.Contains(text, "优化配置") {
					hasOptimization = true
				}
				if strings.Contains(text, "虚拟模式-健康问题发展趋势列表：") {
					hasVirtualMode = true
				}
			}
			if hasOptimization && hasVirtualMode {
				fmt.Printf("[过滤] 跳过特殊标识: %s -> %v\n", numericText, textElements)
				continue
			}
		}

		// 过滤掉特定的无关文字
		if len(textElements) == 1 && strings.Contains(textElements[0], "按照标准图谱相似度递减列表：") {
			fmt.Printf("[过滤] 跳过无关文字: %s -> %s\n", numericText, textElements[0])
			continue
		}

		// 如果没有找到文字元素，跳过
		if len(textElements) == 0 {
			fmt.Printf("[过滤] 数字 %s 后无文字元素，跳过\n", numericText)
			continue
		}

		// 创建键值对
		pair := NumericTextPair{
			NumericValue:       numericText,
			FloatValue:         numericValue,
			NumericCoordinates: []int{numCenterX, numCenterY},
			NumericPolygon:     numPoly,
			TextElements:       textElements,
			TextCoordinates:    convertToIntSlice(textCoordinates),
		}

		pairs = append(pairs, pair)
		fmt.Printf("[配对] %s -> %v\n", numericText, textElements)
	}

	fmt.Printf("[提取] 成功创建 %d 个数字-文字键值对\n", len(pairs))
	return pairs, targetOrgan, nil
}

// ApplyColorPriorityRules 应用颜色优先级规则
func (oca *OptimizedColorAnalyzer) ApplyColorPriorityRules(numColor string, textColors []TextColor) string {
	// 新规则：红色>橘色>蓝色，绿色>蓝色
	// 只要数字或文本识别中出现了高优先级颜色，就定义这一行为该颜色分类

	// 收集所有识别到的颜色（数字颜色 + 文本颜色）
	allColors := []string{numColor}
	for _, textColor := range textColors {
		allColors = append(allColors, textColor.ColorName)
	}

	// 按照优先级规则进行判断
	// 优先级1：红色最高优先级
	for _, color := range allColors {
		if color == "红色" {
			return "红色"
		}
	}

	// 优先级2：橘色次高优先级
	for _, color := range allColors {
		if color == "橘色" {
			return "橘色"
		}
	}

	// 优先级3：绿色优于蓝色
	for _, color := range allColors {
		if color == "绿色" {
			return "绿色"
		}
	}

	// 优先级4：蓝色
	for _, color := range allColors {
		if color == "蓝色" {
			return "蓝色"
		}
	}

	// 如果没有识别到标准颜色，返回第一个非未知颜色
	for _, color := range allColors {
		if color != "未知颜色" {
			return color
		}
	}

	// 默认返回蓝色
	return "蓝色"
}
