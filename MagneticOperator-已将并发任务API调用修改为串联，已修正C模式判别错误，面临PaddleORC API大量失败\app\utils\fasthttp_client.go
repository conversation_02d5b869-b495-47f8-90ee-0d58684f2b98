package utils

import (
	json "github.com/goccy/go-json"
	"fmt"
	"math"
	"time"

	"github.com/valyala/fasthttp"
)

// 网络重试配置常量
const (
	MaxRetries    = 3                // 最大重试次数
	BaseDelay     = 1 * time.Second  // 基础延迟时间
	MaxDelay      = 30 * time.Second // 最大延迟时间
	BackoffFactor = 2.0              // 指数退避因子
)

// FastHTTPClient 基于fasthttp的HTTP客户端
type FastHTTPClient struct {
	client *fasthttp.Client
}

// NewFastHTTPClient 创建新的FastHTTP客户端
func NewFastHTTPClient(timeout time.Duration) *FastHTTPClient {
	return &FastHTTPClient{
		client: &fasthttp.Client{
			ReadTimeout:  timeout,
			WriteTimeout: timeout,
		},
	}
}

// DoWithRetry 执行HTTP请求并在失败时重试
func (f *FastHTTPClient) DoWithRetry(method, url string, body []byte, headers map[string]string) (*fasthttp.Response, error) {
	var lastErr error

	for attempt := 0; attempt <= MaxRetries; attempt++ {
		// 如果不是第一次尝试，等待一段时间
		if attempt > 0 {
			delay := time.Duration(math.Pow(BackoffFactor, float64(attempt-1))) * BaseDelay
			if delay > MaxDelay {
				delay = MaxDelay
			}
			time.Sleep(delay)
		}

		// 创建请求和响应对象
		req := fasthttp.AcquireRequest()
		resp := fasthttp.AcquireResponse()
		defer fasthttp.ReleaseRequest(req)

		// 设置请求方法和URL
		req.SetRequestURI(url)
		req.Header.SetMethod(method)

		// 设置请求体
		if body != nil {
			req.SetBody(body)
		}

		// 设置请求头
		for key, value := range headers {
			req.Header.Set(key, value)
		}

		// 执行请求
		err := f.client.Do(req, resp)
		if err == nil {
			// 成功，返回响应（注意：调用者需要释放响应）
			return resp, nil
		}

		// 记录错误
		lastErr = err
		fasthttp.ReleaseResponse(resp)
	}

	return nil, fmt.Errorf("请求失败，已重试%d次: %w", MaxRetries, lastErr)
}

// Get 执行GET请求
func (f *FastHTTPClient) Get(url string, headers map[string]string) (*fasthttp.Response, error) {
	return f.DoWithRetry("GET", url, nil, headers)
}

// Post 执行POST请求
func (f *FastHTTPClient) Post(url string, body []byte, headers map[string]string) (*fasthttp.Response, error) {
	return f.DoWithRetry("POST", url, body, headers)
}

// PostJSON 执行POST JSON请求
func (f *FastHTTPClient) PostJSON(url string, data interface{}, headers map[string]string) (*fasthttp.Response, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("JSON序列化失败: %w", err)
	}

	if headers == nil {
		headers = make(map[string]string)
	}
	headers["Content-Type"] = "application/json"

	return f.Post(url, jsonData, headers)
}

// Head 执行HEAD请求
func (f *FastHTTPClient) Head(url string, headers map[string]string) (*fasthttp.Response, error) {
	return f.DoWithRetry("HEAD", url, nil, headers)
}

// ResponseToBytes 将响应体转换为字节数组
func ResponseToBytes(resp *fasthttp.Response) []byte {
	body := make([]byte, len(resp.Body()))
	copy(body, resp.Body())
	return body
}

// ResponseToString 将响应体转换为字符串
func ResponseToString(resp *fasthttp.Response) string {
	return string(resp.Body())
}

// ResponseToJSON 将响应体解析为JSON对象
func ResponseToJSON(resp *fasthttp.Response, v interface{}) error {
	return json.Unmarshal(resp.Body(), v)
}