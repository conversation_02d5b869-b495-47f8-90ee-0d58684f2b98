<template>
  <div class="error-boundary">
    <!-- 正常渲染子组件 -->
    <slot v-if="!hasError" />
    
    <!-- 错误状态显示 -->
    <div v-else class="error-container">
      <div class="error-content">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="#ff4757"/>
          </svg>
        </div>
        
        <h3 class="error-title">{{ errorTitle }}</h3>
        <p class="error-message">{{ errorMessage }}</p>
        
        <!-- 错误详情（开发模式） -->
        <details v-if="showDetails && errorDetails" class="error-details">
          <summary>错误详情</summary>
          <pre class="error-stack">{{ errorDetails }}</pre>
        </details>
        
        <!-- 操作按钮 -->
        <div class="error-actions">
          <button 
            class="btn btn-primary" 
            @click="handleRetry"
            :disabled="retrying"
          >
            <span v-if="retrying">重试中...</span>
            <span v-else>重试</span>
          </button>
          
          <button 
            class="btn btn-secondary" 
            @click="handleReload"
          >
            刷新页面
          </button>
          
          <button 
            v-if="showReportButton"
            class="btn btn-outline" 
            @click="handleReport"
          >
            报告问题
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import errorHandler from '../utils/errorHandler.js'

export default {
  name: 'ErrorBoundary',
  
  props: {
    // 自定义错误标题
    fallbackTitle: {
      type: String,
      default: '出现了一个错误'
    },
    
    // 自定义错误消息
    fallbackMessage: {
      type: String,
      default: '抱歉，组件渲染时出现了错误。请尝试刷新页面或联系技术支持。'
    },
    
    // 是否显示错误详情
    showDetails: {
      type: Boolean,
      default: process.env.NODE_ENV === 'development'
    },
    
    // 是否显示报告按钮
    showReportButton: {
      type: Boolean,
      default: true
    },
    
    // 是否自动重试
    autoRetry: {
      type: Boolean,
      default: false
    },
    
    // 自动重试次数
    maxRetries: {
      type: Number,
      default: 3
    },
    
    // 重试延迟（毫秒）
    retryDelay: {
      type: Number,
      default: 1000
    }
  },
  
  data() {
    return {
      hasError: false,
      error: null,
      errorInfo: null,
      retrying: false,
      retryCount: 0
    }
  },
  
  computed: {
    errorTitle() {
      if (this.error && this.error.userMessage) {
        return this.error.title || this.fallbackTitle
      }
      return this.fallbackTitle
    },
    
    errorMessage() {
      if (this.error && this.error.userMessage) {
        return this.error.userMessage
      }
      return this.fallbackMessage
    },
    
    errorDetails() {
      if (!this.error) return null
      
      let details = ''
      if (this.error.stack) {
        details += `Stack Trace:\n${this.error.stack}\n\n`
      }
      if (this.error.operation) {
        details += `Operation: ${this.error.operation}\n`
      }
      if (this.error.timestamp) {
        details += `Timestamp: ${new Date(this.error.timestamp).toLocaleString()}\n`
      }
      if (this.error.userAgent) {
        details += `User Agent: ${this.error.userAgent}\n`
      }
      
      return details || this.error.toString()
    }
  },
  
  errorCaptured(err, instance, info) {
    console.error('ErrorBoundary captured error:', err, info)
    
    // 处理错误
    const processedError = errorHandler.handleError(err, 'component-render', {
      showToUser: false, // 不显示toast，由ErrorBoundary处理UI
      logError: true
    })
    
    this.hasError = true
    this.error = processedError
    this.errorInfo = info
    
    // 发送错误事件
    this.$emit('error', {
      error: processedError,
      errorInfo: info,
      component: instance
    })
    
    // 自动重试
    if (this.autoRetry && this.retryCount < this.maxRetries) {
      this.scheduleRetry()
    }
    
    // 阻止错误继续向上传播
    return false
  },
  
  methods: {
    async handleRetry() {
      if (this.retrying) return
      
      this.retrying = true
      this.retryCount++
      
      try {
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, this.retryDelay))
        
        // 重置错误状态
        this.resetError()
        
        // 发送重试事件
        this.$emit('retry', {
          attempt: this.retryCount,
          error: this.error
        })
        
      } catch (retryError) {
        console.error('Retry failed:', retryError)
        errorHandler.handleError(retryError, 'error-boundary-retry')
      } finally {
        this.retrying = false
      }
    },
    
    handleReload() {
      // 发送重载事件
      this.$emit('reload')
      
      // 刷新页面
      window.location.reload()
    },
    
    handleReport() {
      // 发送报告事件
      this.$emit('report', {
        error: this.error,
        errorInfo: this.errorInfo
      })
      
      // 可以在这里集成错误报告服务
      errorHandler.reportError(this.error, {
        component: this.$options.name,
        errorInfo: this.errorInfo
      })
    },
    
    resetError() {
      this.hasError = false
      this.error = null
      this.errorInfo = null
    },
    
    scheduleRetry() {
      setTimeout(() => {
        this.handleRetry()
      }, this.retryDelay)
    }
  },
  
  // 监听路由变化，重置错误状态
  watch: {
    '$route'() {
      if (this.hasError) {
        this.resetError()
        this.retryCount = 0
      }
    }
  }
}
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.error-content {
  text-align: center;
  max-width: 500px;
}

.error-icon {
  margin-bottom: 1rem;
}

.error-title {
  color: #dc3545;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.error-message {
  color: #6c757d;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.error-details {
  text-align: left;
  margin-bottom: 1.5rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.error-details summary {
  padding: 0.75rem;
  background-color: #e9ecef;
  cursor: pointer;
  font-weight: 500;
}

.error-details summary:hover {
  background-color: #dee2e6;
}

.error-stack {
  padding: 1rem;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #495057;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}

.error-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-outline {
  background-color: transparent;
  color: #007bff;
  border: 1px solid #007bff;
}

.btn-outline:hover {
  background-color: #007bff;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-container {
    padding: 1rem;
    min-height: 200px;
  }
  
  .error-content {
    max-width: 100%;
  }
  
  .error-title {
    font-size: 1.25rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 200px;
  }
}
</style>