package main

import (
	"fmt"
	"log"

	"MagneticOperator/app/models"
	"MagneticOperator/app/services"
)

func main() {
	fmt.Println("=== 火山引擎OCR SDK测试 ===")

	// 创建测试配置（请替换为实际的AccessKey和SecretKey）
	config := &models.VolcEngineOCRConfig{
		APIURL:          "https://visual.volcengineapi.com",
		AccessKeyID:     "your_access_key_id",     // 请替换为实际的AccessKeyID
		SecretAccessKey: "your_secret_access_key", // 请替换为实际的SecretAccessKey
	}

	// 创建OCR提供者
	provider := services.NewVolcEngineOCRProvider(config, nil)

	// 检查当前使用的客户端类型
	fmt.Printf("当前使用官方SDK: %t\n", provider.IsUsingSDK())

	// 验证配置
	if err := provider.ValidateConfig(); err != nil {
		log.Printf("配置验证失败: %v", err)
		return
	}

	fmt.Println("配置验证通过")

	// 测试切换客户端
	fmt.Println("\n=== 测试客户端切换 ===")
	provider.SetUseSDK(false)
	fmt.Printf("切换后使用官方SDK: %t\n", provider.IsUsingSDK())

	provider.SetUseSDK(true)
	fmt.Printf("切换后使用官方SDK: %t\n", provider.IsUsingSDK())

	// 如果有测试图片，可以取消注释下面的代码进行实际测试
	/*
	testImagePath := "test_image.png" // 替换为实际的测试图片路径
	if _, err := os.Stat(testImagePath); err == nil {
		fmt.Printf("\n=== 测试OCR识别: %s ===\n", testImagePath)
		result, err := provider.ProcessImage(context.Background(), testImagePath)
		if err != nil {
			log.Printf("OCR处理失败: %v", err)
		} else {
			fmt.Printf("识别结果:\n")
			fmt.Printf("器官名称: %s\n", result.OrganName)
			fmt.Printf("置信度: %.2f\n", result.Confidence)
			fmt.Printf("识别文本数量: %d\n", len(result.KeyValuePairs))
		}
	} else {
		fmt.Printf("测试图片不存在: %s\n", testImagePath)
	}
	*/

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("注意: 要进行实际OCR测试，请:")
	fmt.Println("1. 在配置中填入正确的AccessKeyID和SecretAccessKey")
	fmt.Println("2. 取消注释测试代码并提供测试图片路径")

	// 清理资源
	provider.Close()
}