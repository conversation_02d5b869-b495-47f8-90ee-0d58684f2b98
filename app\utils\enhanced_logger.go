package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// EnhancedLogger is an advanced logger with module support and performance metrics.

type EnhancedLogger struct {
	mu          sync.RWMutex
	logger      *zap.Logger
	sugar       *zap.SugaredLogger
	level       zap.AtomicLevel
	moduleName  string
	metrics     map[string]*PerformanceMetric
	enableDebug bool
	logFile     *os.File
}

// PerformanceMetric stores performance data for a specific operation.
type PerformanceMetric struct {
	Name        string
	StartTime   time.Time
	EndTime     time.Time
	Duration    time.Duration
	CallCount   int64
	TotalTime   time.Duration
	AverageTime time.Duration
	MaxTime     time.Duration
	MinTime     time.Duration
}

// NewEnhancedLogger creates a new enhanced logger instance.
func NewEnhancedLogger(moduleName string, logLevel zapcore.Level, logDir string) (*EnhancedLogger, error) {
	if err := os.Mkdir<PERSON>ll(logDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %v", err)
	}

	logFileName := fmt.Sprintf("%s_%s.log", moduleName, time.Now().Format("2006-01-02"))
	logFilePath := filepath.Join(logDir, logFileName)

	logFile, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return nil, fmt.Errorf("failed to create log file: %v", err)
	}

	level := zap.NewAtomicLevelAt(logLevel)

	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig),
		zapcore.AddSync(logFile),
		level,
	)

	logger := zap.New(core, zap.AddCaller(), zap.Fields(zap.String("module", moduleName)))

 	return &EnhancedLogger{
		logger:      logger,
		sugar:       logger.Sugar(),
		level:       level,
		moduleName:  moduleName,
		metrics:     make(map[string]*PerformanceMetric),
		enableDebug: logLevel <= zapcore.DebugLevel,
		logFile:     logFile,
	}, nil
}

// SetLevel changes the logger's level.
func (el *EnhancedLogger) SetLevel(level zapcore.Level) {
	el.level.SetLevel(level)
}

// GetLevel returns the current logger level.
func (el *EnhancedLogger) GetLevel() zapcore.Level {
	return el.level.Level()
}

// Debug logs a debug message.
func (el *EnhancedLogger) Debug(msg string, keysAndValues ...interface{}) {
	el.sugar.Debugw(msg, keysAndValues...)
}

// Info logs an info message.
func (el *EnhancedLogger) Info(msg string, keysAndValues ...interface{}) {
	el.sugar.Infow(msg, keysAndValues...)
}

// Warn logs a warning message.
func (el *EnhancedLogger) Warn(msg string, keysAndValues ...interface{}) {
	el.sugar.Warnw(msg, keysAndValues...)
}

// Error logs an error message.
func (el *EnhancedLogger) Error(msg string, keysAndValues ...interface{}) {
	el.sugar.Errorw(msg, keysAndValues...)
}

// Fatal logs a fatal message and exits.
func (el *EnhancedLogger) Fatal(msg string, keysAndValues ...interface{}) {
	el.sugar.Fatalw(msg, keysAndValues...)
}

// StartTimer starts a performance timer.
func (el *EnhancedLogger) StartTimer(name string) {
	el.mu.Lock()
	defer el.mu.Unlock()

	metric, exists := el.metrics[name]
	if !exists {
		metric = &PerformanceMetric{
			Name:    name,
			MinTime: time.Duration(^uint64(0) >> 1), // Max duration
		}
		el.metrics[name] = metric
	}

	metric.StartTime = time.Now()
}

// EndTimer stops a performance timer and returns the duration.
func (el *EnhancedLogger) EndTimer(name string) time.Duration {
	el.mu.Lock()
	defer el.mu.Unlock()

	metric, exists := el.metrics[name]
	if !exists {
		return 0
	}

	metric.EndTime = time.Now()
	metric.Duration = metric.EndTime.Sub(metric.StartTime)
	metric.CallCount++
	metric.TotalTime += metric.Duration
	metric.AverageTime = time.Duration(int64(metric.TotalTime) / metric.CallCount)

	if metric.Duration > metric.MaxTime {
		metric.MaxTime = metric.Duration
	}
	if metric.Duration < metric.MinTime {
		metric.MinTime = metric.Duration
	}

	el.Debug("性能计时 [%s]: %v (调用次数: %d, 平均: %v, 最大: %v, 最小: %v)",
		name, metric.Duration, metric.CallCount, metric.AverageTime, metric.MaxTime, metric.MinTime)

	return metric.Duration
}

// LogPerformanceMetrics 记录所有性能指标
func (el *EnhancedLogger) LogPerformanceMetrics() {
	el.mu.RLock()
	defer el.mu.RUnlock()

	if len(el.metrics) == 0 {
		el.Info("没有性能指标数据")
		return
	}

	el.Info("=== 性能指标报告 ===")
	for name, metric := range el.metrics {
		el.Info("指标: %s | 调用次数: %d | 总时间: %v | 平均: %v | 最大: %v | 最小: %v",
			name, metric.CallCount, metric.TotalTime, metric.AverageTime, metric.MaxTime, metric.MinTime)
	}
	el.Info("=== 性能指标报告结束 ===")
}

// ClearMetrics 清除所有性能指标
func (el *EnhancedLogger) ClearMetrics() {
	el.mu.Lock()
	defer el.mu.Unlock()
	el.metrics = make(map[string]*PerformanceMetric)
}

// LogOCRDebug OCR专用调试日志
func (el *EnhancedLogger) LogOCRDebug(stage string, textCount int, polygonCount int, organName string) {
	if !el.enableDebug {
		return
	}

	el.Debug("[OCR-%s] 文本数量=%d, 多边形数量=%d, 器官=%s", stage, textCount, polygonCount, organName)
}

// LogPairingDebug 配对专用调试日志
func (el *EnhancedLogger) LogPairingDebug(numericValue string, numCoords [2]int, textValue string, textCoords [2]int, distance float64, threshold float64, success bool) {
	if !el.enableDebug {
		return
	}

	status := "失败"
	if success {
		status = "成功"
	}

	el.Debug("[配对-%s] 数字'%s'(%d,%d) <-> 文字'%s'(%d,%d) | 距离=%.1f, 阈值=%.1f",
		status, numericValue, numCoords[0], numCoords[1], textValue, textCoords[0], textCoords[1], distance, threshold)
}

// Close 关闭日志记录器
func (el *EnhancedLogger) Close() error {
	el.mu.Lock()
	defer el.mu.Unlock()

	if el.logFile != nil {
		return el.logFile.Close()
	}
	return nil
}
