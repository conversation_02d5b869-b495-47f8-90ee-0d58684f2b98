package main

import (
	"fmt"
	"log"
	"sync"
	"testing"
	"time"

	"MagneticOperator/app/services"
)

// MockApp 模拟App接口用于测试
type MockApp struct{}

func (m *MockApp) ShowSuccessNotification(title, message string, duration int) {
	fmt.Printf("[Mock Toast] %s: %s (duration: %dms)\n", title, message, duration)
}

// TestConcurrentOCRProcessor 测试并发OCR处理器
func TestConcurrentOCRProcessor(t *testing.T) {
	fmt.Println("=== 测试并发OCR处理器 ===")

	// 创建模拟配置服务
	configService := createMockConfigService()
	if configService == nil {
		t.Skip("跳过测试：需要真实的配置服务")
		return
	}

	// 创建OCR服务
	ocrService := services.NewOCRService(configService, nil)

	// 创建并发OCR处理器
	processor := services.NewConcurrentOCRProcessor(ocrService, 3, 50)

	// 启动处理器
	if err := processor.Start(); err != nil {
		t.Fatalf("启动处理器失败: %v", err)
	}
	defer processor.Stop()

	// 创建测试任务
	tasks := createTestTasks(5)

	// 提交任务
	var wg sync.WaitGroup
	for _, task := range tasks {
		wg.Add(1)
		go func(t *services.ScreenshotTask) {
			defer wg.Done()
			if err := processor.SubmitTask(t); err != nil {
				fmt.Printf("提交任务失败: %v\n", err)
			}
		}(task)
	}

	// 等待任务提交完成
	wg.Wait()

	// 等待处理完成
	time.Sleep(10 * time.Second)

	// 检查统计信息
	stats := processor.GetStats()
	fmt.Printf("处理统计: 提交=%d, 完成=%d, 失败=%d, 成功率=%.1f%%\n",
		stats.TotalSubmitted, stats.TotalCompleted, stats.TotalFailed, stats.SuccessRate*100)

	if stats.TotalSubmitted != int64(len(tasks)) {
		t.Errorf("期望提交%d个任务，实际提交%d个", len(tasks), stats.TotalSubmitted)
	}
}

// TestScreenshotRoundManager 测试轮次管理器
func TestScreenshotRoundManager(t *testing.T) {
	fmt.Println("=== 测试轮次管理器 ===")

	// 创建模拟服务
	configService := createMockConfigService()
	if configService == nil {
		t.Skip("跳过测试：需要真实的配置服务")
		return
	}

	screenshotService := services.NewScreenshotService(configService)
	ocrProcessor := services.NewConcurrentOCRProcessor(
		services.NewOCRService(configService, nil), 2, 20)

	// 创建mock app接口
	mockApp := &MockApp{}

	// 创建轮次管理器
	roundManager := services.NewScreenshotRoundManager(
		ocrProcessor, screenshotService, configService, mockApp)

	// 启动管理器
	if err := roundManager.Start(); err != nil {
		t.Fatalf("启动轮次管理器失败: %v", err)
	}
	defer roundManager.Stop()

	// 测试开始新轮次
	userName := "测试用户"
	round, err := roundManager.StartNewRound(userName)
	if err != nil {
		t.Fatalf("开始新轮次失败: %v", err)
	}

	fmt.Printf("轮次%d已开始，用户: %s\n", round.RoundNumber, round.UserName)

	// 模拟截图（注意：这里不会真正截图，只是测试流程）
	modes := []string{"B01", "B02"}
	for _, mode := range modes {
		// 这里应该模拟截图，但由于测试环境限制，我们跳过实际截图
		fmt.Printf("模拟截图: 轮次%d, 模式%s\n", round.RoundNumber, mode)
	}

	// 检查轮次状态
	time.Sleep(2 * time.Second)
	retrievedRound, exists := roundManager.GetRound(1)
	if !exists {
		t.Errorf("轮次1不存在")
	}

	if retrievedRound.UserName != userName {
		t.Errorf("期望用户名%s，实际%s", userName, retrievedRound.UserName)
	}

	fmt.Printf("轮次状态: %v\n", retrievedRound.Status)
}

// TestIntegratedScreenshotService 测试集成截图服务
func TestIntegratedScreenshotService(t *testing.T) {
	fmt.Println("=== 测试集成截图服务 ===")

	// 创建模拟配置服务
	configService := createMockConfigService()
	if configService == nil {
		t.Skip("跳过测试：需要真实的配置服务")
		return
	}

	// 创建集成服务
	integratedService := services.NewIntegratedScreenshotService(configService)

	// 初始化服务
	if err := integratedService.Initialize(); err != nil {
		t.Fatalf("初始化集成服务失败: %v", err)
	}
	defer integratedService.Shutdown()

	// 设置最大轮次数
	integratedService.SetMaxRounds(3)

	// 测试服务状态
	if !integratedService.IsRunning() {
		t.Errorf("服务应该处于运行状态")
	}

	// 测试开始轮次
	userName := "集成测试用户"
	round, err := integratedService.StartNewRound(userName)
	if err != nil {
		t.Fatalf("开始新轮次失败: %v", err)
	}

	fmt.Printf("集成服务轮次%d已开始\n", round.RoundNumber)

	// 测试获取进度
	progress := integratedService.GetOverallProgress()
	fmt.Printf("整体进度: %+v\n", progress)

	// 测试获取统计
	stats := integratedService.GetOCRProcessorStats()
	fmt.Printf("OCR统计: 提交=%d, 完成=%d\n", stats.TotalSubmitted, stats.TotalCompleted)
}

// BenchmarkConcurrentOCRProcessor 性能测试
func BenchmarkConcurrentOCRProcessor(b *testing.B) {
	configService := createMockConfigService()
	if configService == nil {
		b.Skip("跳过基准测试：需要真实的配置服务")
		return
	}

	ocrService := services.NewOCRService(configService, nil)
	processor := services.NewConcurrentOCRProcessor(ocrService, 5, 100)

	if err := processor.Start(); err != nil {
		b.Fatalf("启动处理器失败: %v", err)
	}
	defer processor.Stop()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		task := &services.ScreenshotTask{
			ID:           fmt.Sprintf("bench_task_%d", i),
			ImagePath:    "test_image.png",
			UserName:     "bench_user",
			RoundNumber:  1,
			ScreenNumber: 1,
			Timestamp:    time.Now(),
			Mode:         "B01",
		}

		if err := processor.SubmitTask(task); err != nil {
			b.Errorf("提交任务失败: %v", err)
		}
	}
}

// createMockConfigService 创建模拟配置服务
func createMockConfigService() *services.ConfigService {
	// 这里应该返回真实的配置服务
	// 为了测试，我们返回nil，实际使用时需要提供真实的配置
	return nil
}

// createTestTasks 创建测试任务
func createTestTasks(count int) []*services.ScreenshotTask {
	tasks := make([]*services.ScreenshotTask, count)
	for i := 0; i < count; i++ {
		tasks[i] = &services.ScreenshotTask{
			ID:           fmt.Sprintf("test_task_%d", i),
			ImagePath:    fmt.Sprintf("test_image_%d.png", i),
			UserName:     "test_user",
			RoundNumber:  1,
			ScreenNumber: i + 1,
			Timestamp:    time.Now(),
			Mode:         "B01",
		}
	}
	return tasks
}

// 运行所有测试的主函数
func main() {
	fmt.Println("开始运行并发OCR系统测试...")

	// 注意：这些测试需要真实的配置和OCR服务
	// 在实际环境中运行时，请确保配置正确

	// 运行基本功能测试
	t := &testing.T{}

	fmt.Println("\n1. 测试并发OCR处理器")
	TestConcurrentOCRProcessor(t)

	fmt.Println("\n2. 测试轮次管理器")
	TestScreenshotRoundManager(t)

	fmt.Println("\n3. 测试集成截图服务")
	TestIntegratedScreenshotService(t)

	fmt.Println("\n所有测试完成！")
}

// 性能测试示例
func runPerformanceTest() {
	fmt.Println("=== 性能测试 ===")

	// 模拟10轮并发处理的性能
	startTime := time.Now()

	// 模拟传统方式：串行处理
	fmt.Println("模拟传统串行处理...")
	traditionalTime := simulateTraditionalProcessing(10, 2) // 10轮，每轮2个截图
	fmt.Printf("传统方式总耗时: %v\n", traditionalTime)

	// 模拟并发方式：并行处理
	fmt.Println("模拟并发并行处理...")
	concurrentTime := simulateConcurrentProcessing(10, 2) // 10轮，每轮2个截图
	fmt.Printf("并发方式总耗时: %v\n", concurrentTime)

	// 计算性能提升
	if traditionalTime > 0 && concurrentTime > 0 {
		improvement := float64(traditionalTime) / float64(concurrentTime)
		fmt.Printf("性能提升: %.1fx\n", improvement)
	}

	totalTime := time.Since(startTime)
	fmt.Printf("性能测试总耗时: %v\n", totalTime)
}

// simulateTraditionalProcessing 模拟传统串行处理
func simulateTraditionalProcessing(rounds, screenshotsPerRound int) time.Duration {
	startTime := time.Now()

	for round := 1; round <= rounds; round++ {
		for screenshot := 1; screenshot <= screenshotsPerRound; screenshot++ {
			// 模拟截图时间（1秒）
			time.Sleep(100 * time.Millisecond)
			// 模拟OCR处理时间（25秒）
			time.Sleep(500 * time.Millisecond) // 缩短时间用于测试
		}
	}

	return time.Since(startTime)
}

// simulateConcurrentProcessing 模拟并发并行处理
func simulateConcurrentProcessing(rounds, screenshotsPerRound int) time.Duration {
	startTime := time.Now()

	// 用于等待所有OCR处理完成
	var wg sync.WaitGroup

	for round := 1; round <= rounds; round++ {
		for screenshot := 1; screenshot <= screenshotsPerRound; screenshot++ {
			// 模拟截图时间（1秒）
			time.Sleep(100 * time.Millisecond)

			// 异步提交OCR处理
			wg.Add(1)
			go func() {
				defer wg.Done()
				// 模拟OCR处理时间（25秒）
				time.Sleep(500 * time.Millisecond) // 缩短时间用于测试
			}()
		}

		// 模拟用户操作间隔
		time.Sleep(50 * time.Millisecond)
	}

	// 等待所有OCR处理完成
	wg.Wait()

	return time.Since(startTime)
}

// init 初始化测试环境
func init() {
	// 设置日志输出
	log.SetFlags(log.LstdFlags | log.Lshortfile)
}
