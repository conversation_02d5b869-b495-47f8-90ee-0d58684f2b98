module MagneticOperator

go 1.23.0

toolchain go1.24.2

require (
	github.com/disintegration/imaging v1.6.2
	github.com/fsnotify/fsnotify v1.8.0
	github.com/goccy/go-json v0.10.5
	github.com/kbinani/screenshot v0.0.0-20210720154843-7d3a670d8329
	github.com/mozillazg/go-pinyin v0.20.0
	github.com/panjf2000/ants/v2 v2.11.3
	github.com/skip2/go-qrcode v0.0.0-20200617195104-da1b6568686e
	github.com/spf13/viper v1.20.1
	github.com/valyala/fasthttp v1.62.0
	github.com/wailsapp/wails/v2 v2.10.1
	go.uber.org/zap v1.27.0
	golang.org/x/time v0.8.0
	gopkg.in/yaml.v2 v2.4.0
)

require (
	github.com/andy<PERSON>holm/brotli v1.1.1 // indirect
	github.com/bep/debounce v1.2.1 // indirect
	github.com/gen2brain/shm v0.0.0-20200228170931-49f9650110c5 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/go-viper/mapstructure/v2 v2.2.1 // indirect
	github.com/godbus/dbus/v5 v5.1.0 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/jchv/go-winloader v0.0.0-20210711035445-715c2860da7e // indirect
	github.com/jezek/xgb v0.0.0-20210312150743-0e0f116e1240 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/labstack/echo/v4 v4.13.3 // indirect
	github.com/labstack/gommon v0.4.2 // indirect
	github.com/leaanthony/go-ansi-parser v1.6.1 // indirect
	github.com/leaanthony/gosod v1.0.4 // indirect
	github.com/leaanthony/slicer v1.6.0 // indirect
	github.com/leaanthony/u v1.1.1 // indirect
	github.com/lxn/win v0.0.0-20210218163916-a377121e959e // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/pelletier/go-toml/v2 v2.2.3 // indirect
	github.com/pkg/browser v0.0.0-20240102092130-5ac0b6a4141c // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/sagikazarmark/locafero v0.7.0 // indirect
	github.com/samber/lo v1.49.1 // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/spf13/afero v1.12.0 // indirect
	github.com/spf13/cast v1.7.1 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/tkrajina/go-reflector v0.5.8 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasttemplate v1.2.2 // indirect
	github.com/wailsapp/go-webview2 v1.0.19 // indirect
	github.com/wailsapp/mimetype v1.4.1 // indirect
	go.uber.org/multierr v1.10.0 // indirect
	golang.org/x/crypto v0.38.0 // indirect
	golang.org/x/image v0.12.0 // indirect
	golang.org/x/net v0.40.0 // indirect
	golang.org/x/sync v0.14.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.25.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

//replace github.com/wailsapp/wails/v2 => C:\Users\<USER>\go\pkg\mod\github.com\wailsapp\wails\v2@v2.8.1
//replace github.com/wailsapp/wails/v2 => ../wails/v2
//replace lumino.org/x/hotkey => ../hotkey
//replace github.com/kbinani/screenshot => ../screenshot
//replace github.com/skip2/go-qrcode => ../go-qrcode
//replace github.com/lxn/walk => ../walk
//replace github.com/lxn/win => ../win
//replace github.com/shirou/gopsutil/v3 => ../gopsutil
//replace gopkg.in/ini.v1 => ../ini
//replace github.com/go-ole/go-ole => ../go-ole
//replace github.com/PuerkitoBio/goquery => ../goquery
//replace github.com/axgle/mahonia => ../mahonia
//replace github.com/robfig/cron/v3 => ../cron
//replace github.com/pkg/errors => ../errors

//replace github.com/wailsapp/wails/v2 => C:\Users\<USER>\go\pkg\mod\github.com\wailsapp\wails\v2@v2.8.1
