const fs = require('fs');

try {
  const content = fs.readFileSync('App.vue', 'utf8');
  const lines = content.split(/\r?\n/);
  
  console.log('Looking for style tags:');
  
  // 找到所有 <style> 开始标签
  lines.forEach((line, index) => {
    if (line.includes('<style')) {
      console.log(`Line ${index + 1}: <style> tag found: ${line.trim()}`);
    }
  });
  
  console.log('\nLooking for </style> closing tags:');
  
  // 找到所有 </style> 结束标签
  lines.forEach((line, index) => {
    if (line.includes('</style>')) {
      console.log(`Line ${index + 1}: </style> tag found: ${line.trim()}`);
    }
  });
  
} catch (error) {
  console.error('Error reading file:', error.message);
}