# 环境配置管理

本项目支持多环境配置管理，可以根据不同的运行环境（开发、生产）使用不同的配置参数。

## 配置文件结构

### 基础配置文件
- `config/app_config.json` - 主配置文件，包含应用的基本配置信息

### 环境配置文件
- `config/development.json` - 开发环境配置
- `config/production.json` - 生产环境配置

## 环境配置文件格式

```json
{
  "environment": "development",
  "debug": true,
  "api_keys": {
    "ocr": {
      "api_url": "http://localhost:8080/ocr",
      "table_api_url": "http://localhost:8080/table-ocr",
      "token": "dev-ocr-token"
    },
    "coze": {
      "api_url": "http://localhost:8080/coze",
      "token": "dev-coze-token",
      "workflow_id_post_pic": "dev-workflow-pic",
      "workflow_id_post_registration": "dev-workflow-reg",
      "workflow_id_user_info": "dev-workflow-user",
      "space_id": "dev-space-id",
      "app_id": "dev-app-id"
    },
    "cloud_function": {
      "registrations_url": "http://localhost:8080/registrations/getRegistrationsBySiteAndDevice",
      "screenshot_records_url": "http://localhost:8080/screenshot-records/createOrUpdateScreenshotRecord",
      "siteInfoByDeviceMAC_url": "http://localhost:8080/hc-actions/getSiteInfoByDeviceMAC"
    }
  },
  "color_detection": {
    "debug_mode": true,
    "save_debug_files": true
  }
}
```

## 使用方法

### 1. 通过环境变量设置

设置 `APP_ENV` 环境变量来指定运行环境：

```bash
# Windows
set APP_ENV=development
MagneticOperator.exe

# 或者
set APP_ENV=production
MagneticOperator.exe
```

### 2. 使用启动脚本

项目提供了预配置的启动脚本：

- `start-dev.bat` - 开发环境启动脚本
- `start-prod.bat` - 生产环境启动脚本

### 3. 默认行为

如果未设置 `APP_ENV` 环境变量，系统将默认使用 `development` 环境。

## 配置合并规则

1. 首先加载基础配置文件 `config/app_config.json`
2. 然后加载对应环境的配置文件（如 `config/development.json`）
3. 环境配置中的以下字段会覆盖基础配置：
   - `api_keys` - API密钥配置
   - `color_detection` - 颜色检测配置
   - `debug` - 调试模式
   - `environment` - 环境标识

## 代码中的使用

### 获取环境信息

```go
// 获取当前环境
env := configService.GetEnvironment()

// 判断是否为生产环境
if configService.IsProduction() {
    // 生产环境逻辑
}

// 判断是否为开发环境
if configService.IsDevelopment() {
    // 开发环境逻辑
}

// 判断是否为调试模式
if configService.IsDebugMode() {
    // 调试模式逻辑
}
```

### 创建环境配置服务

```go
// 使用默认环境（从环境变量获取）
configService := services.NewConfigService(ctx)

// 指定特定环境
configService := services.NewConfigServiceWithEnv(ctx, "production")
```

## 最佳实践

1. **敏感信息管理**：生产环境的API密钥等敏感信息应该通过安全的方式管理，不要直接写在配置文件中

2. **环境隔离**：确保开发环境和生产环境使用不同的API端点和数据库

3. **调试配置**：开发环境可以启用更详细的日志和调试功能

4. **配置验证**：在应用启动时验证配置的完整性和正确性

## 故障排除

1. **配置文件不存在**：如果指定环境的配置文件不存在，系统会自动回退到 `development` 环境

2. **配置格式错误**：检查JSON格式是否正确，特别注意逗号和引号

3. **环境变量未生效**：确保在启动应用之前设置了 `APP_ENV` 环境变量

4. **权限问题**：确保应用有读取配置文件的权限