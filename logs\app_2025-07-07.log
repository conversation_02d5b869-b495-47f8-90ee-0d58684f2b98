{"level":"INFO","timestamp":"2025-07-07T00:12:03.222+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.222+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.222+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.222+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.222+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.331+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.331+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.331+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":10508}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.333+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.333+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.333+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.333+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.333+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.374+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.374+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.374+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.374+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.374+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.609+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":1720}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.628+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.629+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.629+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.629+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.629+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.638+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.638+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.638+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.638+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.638+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.921+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":11432}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.960+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.960+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.960+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.960+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.960+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.970+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.971+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.971+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.971+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.971+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.971+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.976+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.978+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.979+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.979+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.979+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.979+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.979+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.979+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.985+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.985+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.985+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.985+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.726+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.787+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.794+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.794+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.795+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.804+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.806+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.806+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.822+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:17.961+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:17.961+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:17.961+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:22.334+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:22.334+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:12:22.341+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.130+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.130+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.130+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.131+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.131+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.131+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.131+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.131+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.131+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T00:12:25.534+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.257+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.291+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.291+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.291+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.291+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.291+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.292+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.292+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.292+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.292+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:17:13.984+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T00:20:31.982+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:31.987+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:31.989+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:31.992+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:31.997+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.002+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.005+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.006+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.007+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.007+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.011+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.011+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.011+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.011+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.012+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.012+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.012+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.013+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.013+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.015+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.015+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.016+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.016+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.016+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.016+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.017+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.017+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.017+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.024+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.025+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.025+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.025+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.028+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.028+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.028+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.650+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.650+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.650+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.662+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.662+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.662+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.667+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.667+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.667+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.673+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.673+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.673+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.674+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.674+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.674+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.676+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.676+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.676+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.306+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.306+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.308+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.315+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.316+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.316+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.318+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.319+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.339+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.339+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.339+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.340+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.342+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.342+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.345+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.346+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.346+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.346+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.347+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.348+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.503+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.520+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.533+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.550+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.572+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.574+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.601+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.713+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.743+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.751+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.757+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.765+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.803+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.858+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.218+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22404}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.227+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.227+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.227+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.227+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.227+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.237+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.237+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.237+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.237+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.237+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.350+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.352+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.354+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.355+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.356+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.356+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.356+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.356+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.356+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.357+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.357+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.357+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.362+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.363+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.363+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.558+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.559+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.559+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.559+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.559+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.559+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.743+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.743+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.745+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.792+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.793+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.793+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.794+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.794+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.795+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.974+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.976+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.029+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.157+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.181+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.249+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.953+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-07T00:12:12.922+0800"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"11432"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":11432}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":11432}
{"level":"WARN","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":11432,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.955+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":11432}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.955+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.955+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.955+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":20784}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.013+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.013+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.013+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.013+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.013+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.443+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.445+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.446+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.446+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.446+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.446+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.454+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.455+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.456+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.456+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.456+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.457+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.457+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.457+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.457+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.457+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.463+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.463+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.463+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.463+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.653+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.715+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.721+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.722+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.722+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.730+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.734+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.735+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.753+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.942+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.942+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.942+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.150+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.150+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.150+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.150+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.150+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.150+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.151+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.151+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.151+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.184+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.184+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.187+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.226+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.227+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.227+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.227+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.228+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.228+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.228+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.228+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.228+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.393+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.563+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:38.462+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.156+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.160+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.160+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.160+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.163+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.820+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.820+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.820+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:58.472+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:58.472+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:25:58.476+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:58.681+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:58.852+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.133+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.138+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.139+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.139+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.145+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.420+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.420+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.420+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.546+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.550+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.550+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.550+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.552+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.812+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.812+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.812+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.322+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.339+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.339+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.340+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.340+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.340+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.340+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.340+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.340+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.374+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.374+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.375+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.375+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.376+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":16768}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.620+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.620+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.620+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.620+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.620+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.629+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.629+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.629+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.629+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.629+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.200+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":16824}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.219+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.219+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.219+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.219+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.219+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.227+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.227+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.227+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.227+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.227+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.529+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.529+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.529+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.529+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.529+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.529+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.530+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.530+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22340}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.556+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.556+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.556+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.556+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.556+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.958+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.958+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.959+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.959+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.959+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.959+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.965+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.967+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.967+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.967+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.967+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.967+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.968+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.968+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.968+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.968+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.968+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.968+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.970+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.970+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.970+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.970+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.970+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.974+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.974+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.974+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.974+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.975+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.975+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.975+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.537+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.604+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.610+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.611+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.611+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.621+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.624+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.624+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.642+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.717+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.717+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.717+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.717+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.717+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.718+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.718+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.718+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.718+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.798+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.798+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.798+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.798+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.798+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.799+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.799+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.799+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.799+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:30.264+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:30.264+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:30.264+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:30.887+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:30.887+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:32:30.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:31.079+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:31.290+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:04.056+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:04.056+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:04.056+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:04.056+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:06.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:06.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:06.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:06.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:40.650+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:40.650+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:40.923+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:37:28.974+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T00:42:28.974+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T00:47:28.974+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T00:52:28.974+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T00:57:28.974+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T01:02:28.974+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.707+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22356}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.727+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.727+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.727+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.727+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.727+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.748+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.749+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.749+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.749+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.749+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.874+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.876+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.879+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.882+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.882+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.882+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.884+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.884+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.884+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.886+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.886+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.886+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.892+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.233+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.234+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.234+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.234+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.234+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-07T00:32:28.530+0800"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"22340"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":22340}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":22340}
{"level":"WARN","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":22340,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":22340}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.236+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":21252}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.280+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.281+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.281+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.281+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.281+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.693+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.694+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.695+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.695+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.696+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.696+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.702+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.704+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.711+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.711+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.712+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.713+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.713+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.713+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.915+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.983+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.992+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.992+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.992+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T01:05:03.002+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T01:05:03.007+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T01:05:03.008+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T01:05:03.025+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.747+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.747+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.747+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.747+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.747+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.747+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.748+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.748+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.748+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.921+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.921+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.921+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.030+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.031+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.031+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.031+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.031+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.031+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.031+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.032+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.032+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.669+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.669+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.675+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:11.140+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:13.638+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":19748}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.044+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.044+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.044+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.044+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.044+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.052+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.052+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.052+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.052+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.052+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.804+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.828+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.828+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.829+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.829+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.829+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.829+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.829+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.829+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.843+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.843+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.844+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.844+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.844+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22836}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.296+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.296+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.296+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.296+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.296+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.319+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.319+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.319+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.319+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.320+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.524+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.524+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.524+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.524+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.524+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.525+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.525+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.525+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22200}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.568+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.568+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.568+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.568+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.568+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.994+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.994+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.995+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.995+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.995+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.996+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.007+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.009+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.009+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.009+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.009+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.009+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.016+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.016+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.016+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.016+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.016+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.017+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.017+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.625+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.693+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.719+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.719+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.719+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.737+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.788+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.788+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.797+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.132+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.132+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.132+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.132+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.132+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.132+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.133+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.133+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.133+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.699+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.699+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.699+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.794+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.794+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.798+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.929+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:56.840+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:59.228+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:09:01.584+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:09:01.584+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:09:04.056+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:11:02.139+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:11:02.139+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:11:05.063+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.714+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.742+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.742+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.743+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.743+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.743+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.743+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.743+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.743+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.759+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.759+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.759+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.759+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.760+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
