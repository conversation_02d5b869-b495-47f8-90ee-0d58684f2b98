const fs = require('fs');

try {
  const content = fs.readFileSync('App.vue', 'utf8');
  const lines = content.split(/\r?\n/);
  
  console.log('Checking JavaScript syntax around line 1190...');
  
  // 查看第1190行周围的代码
  for (let i = 1180; i <= 1200; i++) {
    if (lines[i-1]) {
      console.log(`${i}: ${lines[i-1]}`);
    }
  }
  
  console.log('\nLooking for potential syntax issues:');
  
  // 检查可能的语法问题
  lines.forEach((line, index) => {
    const lineNum = index + 1;
    
    // 检查方法定义后是否缺少逗号
    if (line.trim() === '}' && lineNum < lines.length - 1) {
      const nextLine = lines[index + 1];
      const lineAfterNext = lines[index + 2];
      
      // 如果下一行是空行，再下一行是方法定义，可能缺少逗号
      if (nextLine && nextLine.trim() === '' && lineAfterNext && 
          (lineAfterNext.trim().match(/^\w+\s*\(/) || lineAfterNext.trim().match(/^async\s+\w+\s*\(/))) {
        console.log(`Potential missing comma after line ${lineNum}: ${line.trim()}`);
        console.log(`  Next method starts at line ${lineNum + 2}: ${lineAfterNext.trim()}`);
      }
    }
    
    // 检查其他可能的语法问题
    if (line.includes('}{')) {
      console.log(`Potential syntax issue at line ${lineNum}: ${line.trim()}`);
    }
  });
  
} catch (error) {
  console.error('Error reading file:', error.message);
}