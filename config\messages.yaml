# 用户交互消息配置文件
# 用于配置Toast通知和页面状态显示的所有消息内容

# 操作状态消息 (currentOperationStatus)
operation_status:
  # 初始状态
  initial: "等待您选择目标器官/部位，进行B02生化平衡分析采样..."
  
  # B02模式完成后的状态
  b02_completed:
    template: "B02生化平衡分析已加入队列，等待C03病理形态学分析启动..."
  
  # C03模式完成后的状态 (轮次1-9)
  c03_completed_continue:
    template: "C03病理形态学分析已加入队列，第{round}轮检测结果采样完成。开始第{next_round}轮采样，等待您选择目标器官/部位，进行B02生化平衡分析采样..."
  
  # C03模式完成后的状态 (第10轮)
  c03_completed_final:
    template: "C03病理形态学分析已加入队列，第10轮检测结果采样完成。开始在后台进行AI大模型深度研究并生成健康报告，您可以开始下一位受检者检测工作..."

# Toast通知消息
toast_messages:
  # 成功消息
  success:
    screenshot_b02_success: "B02生化平衡分析截图成功"
    screenshot_c03_success: "C03病理形态学分析截图成功"
    round_completed: "第{round}轮检测完成"
    all_rounds_completed: "所有10轮检测完成，开始生成健康报告"
    ocr_processing_success: "OCR识别处理成功"
    color_detection_success: "颜色检测分析完成"
    
  # 进度消息
  progress:
    ocr_processing: "正在进行OCR文字识别..."
    color_detection: "正在进行颜色检测分析..."
    data_extraction: "正在提取检测数据..."
    updating_records: "正在更新检测记录..."
    
  # 错误消息
  error:
    screenshot_failed: "截图操作失败，请重试"
    ocr_failed: "OCR识别失败，请重试"
    color_detection_failed: "颜色检测失败，请重试"
    data_save_failed: "数据保存失败，请重试"
    
  # 警告消息
  warning:
    duplicate_organ: "检测到重复器官：{organ_name}，是否继续？"
    round_incomplete: "当前轮次未完成，请先完成C03模式"

# 按钮文本配置
button_texts:
  screenshot_b02: "B02生化平衡分析截图"
  screenshot_c03: "C03病理形态学分析截图"
  continue_detection: "继续检测"
  start_next_round: "开始下一轮"
  generate_report: "生成健康报告"

# 进度条配置
progress_bar:
  colors:
    b02_mode: "#2196f3"  # 蓝色
    c03_mode: "#4caf50"  # 绿色
  
  # 进度计算：每轮包含B02和C03两个步骤，共20个步骤
  steps_per_round: 2
  total_rounds: 10
  total_steps: 20

# 模式显示名称
mode_names:
  B: "B02生化平衡分析"
  C: "C03病理形态学分析"
  B02: "B02生化平衡分析"
  C03: "C03病理形态学分析"

# 轮次状态描述
round_status:
  waiting_b02: "等待B02生化平衡分析"
  waiting_c03: "等待C03病理形态学分析"
  b02_completed: "B02分析完成"
  c03_completed: "C03分析完成"
  round_completed: "轮次完成"
