# 错误处理优化方案

## 概述

本文档提供了磁共振操作系统的全面错误处理优化方案，旨在提高系统的稳定性、可维护性和用户体验。

## 1. 后端Go代码错误处理优化

### 1.1 统一错误类型定义

创建统一的错误类型和错误码系统：

```go
// app/utils/errors.go
package utils

import (
	"fmt"
	"net/http"
)

// ErrorCode 错误码类型
type ErrorCode int

const (
	// 系统级错误 (1000-1999)
	ErrSystemInternal ErrorCode = 1000
	ErrSystemTimeout  ErrorCode = 1001
	ErrSystemOverload ErrorCode = 1002

	// 业务级错误 (2000-2999)
	ErrBusinessValidation ErrorCode = 2000
	ErrBusinessNotFound   ErrorCode = 2001
	ErrBusinessConflict   ErrorCode = 2002

	// 截图相关错误 (3000-3099)
	ErrScreenshotCapture ErrorCode = 3000
	ErrScreenshotSave    ErrorCode = 3001
	ErrScreenshotProcess ErrorCode = 3002

	// OCR相关错误 (3100-3199)
	ErrOCRProcess    ErrorCode = 3100
	ErrOCRTimeout    ErrorCode = 3101
	ErrOCRInvalidImg ErrorCode = 3102

	// 网络相关错误 (4000-4099)
	ErrNetworkTimeout    ErrorCode = 4000
	ErrNetworkConnection ErrorCode = 4001
	ErrNetworkAPI        ErrorCode = 4002
)

// AppError 应用错误结构
type AppError struct {
	Code       ErrorCode `json:"code"`
	Message    string    `json:"message"`
	Details    string    `json:"details,omitempty"`
	Cause      error     `json:"-"`
	StackTrace string    `json:"stack_trace,omitempty"`
	Timestamp  int64     `json:"timestamp"`
	UserID     string    `json:"user_id,omitempty"`
	Operation  string    `json:"operation,omitempty"`
}

// Error 实现error接口
func (e *AppError) Error() string {
	return fmt.Sprintf("[%d] %s: %s", e.Code, e.Message, e.Details)
}

// NewAppError 创建新的应用错误
func NewAppError(code ErrorCode, message, details string, cause error) *AppError {
	return &AppError{
		Code:      code,
		Message:   message,
		Details:   details,
		Cause:     cause,
		Timestamp: time.Now().Unix(),
	}
}

// WithOperation 添加操作信息
func (e *AppError) WithOperation(operation string) *AppError {
	e.Operation = operation
	return e
}

// WithUser 添加用户信息
func (e *AppError) WithUser(userID string) *AppError {
	e.UserID = userID
	return e
}

// IsRetryable 判断错误是否可重试
func (e *AppError) IsRetryable() bool {
	retryableCodes := []ErrorCode{
		ErrSystemTimeout,
		ErrNetworkTimeout,
		ErrNetworkConnection,
		ErrOCRTimeout,
	}
	
	for _, code := range retryableCodes {
		if e.Code == code {
			return true
		}
	}
	return false
}

// GetHTTPStatus 获取对应的HTTP状态码
func (e *AppError) GetHTTPStatus() int {
	switch e.Code {
	case ErrBusinessNotFound:
		return http.StatusNotFound
	case ErrBusinessValidation:
		return http.StatusBadRequest
	case ErrBusinessConflict:
		return http.StatusConflict
	case ErrSystemTimeout, ErrNetworkTimeout:
		return http.StatusRequestTimeout
	default:
		return http.StatusInternalServerError
	}
}
```

### 1.2 错误处理中间件

```go
// app/middleware/error_handler.go
package middleware

import (
	"context"
	"fmt"
	"runtime/debug"
	"time"
	"MagneticOperator/app/utils"
)

// ErrorHandler 错误处理中间件
type ErrorHandler struct {
	logger *utils.EnhancedLogger
}

// NewErrorHandler 创建错误处理中间件
func NewErrorHandler(logger *utils.EnhancedLogger) *ErrorHandler {
	return &ErrorHandler{logger: logger}
}

// HandleError 处理错误
func (eh *ErrorHandler) HandleError(ctx context.Context, err error, operation string) *utils.AppError {
	if err == nil {
		return nil
	}

	// 如果已经是AppError，直接返回
	if appErr, ok := err.(*utils.AppError); ok {
		eh.logError(appErr)
		return appErr
	}

	// 包装为AppError
	appErr := eh.wrapError(err, operation)
	eh.logError(appErr)
	return appErr
}

// wrapError 包装普通错误为AppError
func (eh *ErrorHandler) wrapError(err error, operation string) *utils.AppError {
	// 根据错误类型判断错误码
	code := eh.determineErrorCode(err)
	
	appErr := utils.NewAppError(
		code,
		"操作失败",
		err.Error(),
		err,
	).WithOperation(operation)

	// 添加堆栈信息（仅在开发环境）
	if utils.IsDevelopment() {
		appErr.StackTrace = string(debug.Stack())
	}

	return appErr
}

// determineErrorCode 根据错误内容确定错误码
func (eh *ErrorHandler) determineErrorCode(err error) utils.ErrorCode {
	errorMsg := err.Error()
	
	// 网络相关错误
	if contains(errorMsg, "timeout", "deadline") {
		return utils.ErrNetworkTimeout
	}
	if contains(errorMsg, "connection", "network") {
		return utils.ErrNetworkConnection
	}
	
	// OCR相关错误
	if contains(errorMsg, "ocr", "识别") {
		return utils.ErrOCRProcess
	}
	
	// 截图相关错误
	if contains(errorMsg, "screenshot", "截图") {
		return utils.ErrScreenshotCapture
	}
	
	// 默认系统内部错误
	return utils.ErrSystemInternal
}

// logError 记录错误日志
func (eh *ErrorHandler) logError(appErr *utils.AppError) {
	fields := []interface{}{
		"error_code", appErr.Code,
		"message", appErr.Message,
		"details", appErr.Details,
		"operation", appErr.Operation,
		"user_id", appErr.UserID,
		"timestamp", appErr.Timestamp,
	}
	
	if appErr.Cause != nil {
		fields = append(fields, "cause", appErr.Cause.Error())
	}
	
	eh.logger.Error("应用错误", fields...)
}

// contains 检查字符串是否包含任一关键词
func contains(str string, keywords ...string) bool {
	str = strings.ToLower(str)
	for _, keyword := range keywords {
		if strings.Contains(str, strings.ToLower(keyword)) {
			return true
		}
	}
	return false
}
```

### 1.3 重试机制优化

```go
// app/utils/retry.go
package utils

import (
	"context"
	"fmt"
	"math"
	"time"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts   int           `json:"max_attempts"`
	BaseDelay     time.Duration `json:"base_delay"`
	MaxDelay      time.Duration `json:"max_delay"`
	BackoffFactor float64       `json:"backoff_factor"`
	Jitter        bool          `json:"jitter"`
}

// DefaultRetryConfig 默认重试配置
var DefaultRetryConfig = RetryConfig{
	MaxAttempts:   3,
	BaseDelay:     100 * time.Millisecond,
	MaxDelay:      5 * time.Second,
	BackoffFactor: 2.0,
	Jitter:        true,
}

// RetryableFunc 可重试的函数类型
type RetryableFunc func() error

// RetryWithConfig 使用配置进行重试
func RetryWithConfig(ctx context.Context, config RetryConfig, fn RetryableFunc) error {
	var lastErr error
	
	for attempt := 0; attempt < config.MaxAttempts; attempt++ {
		// 执行函数
		err := fn()
		if err == nil {
			return nil
		}
		
		lastErr = err
		
		// 检查是否可重试
		if appErr, ok := err.(*AppError); ok && !appErr.IsRetryable() {
			return err
		}
		
		// 最后一次尝试，不再等待
		if attempt == config.MaxAttempts-1 {
			break
		}
		
		// 计算延迟时间
		delay := calculateDelay(config, attempt)
		
		// 等待重试
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// 继续重试
		}
	}
	
	return fmt.Errorf("重试%d次后仍然失败: %w", config.MaxAttempts, lastErr)
}

// calculateDelay 计算延迟时间
func calculateDelay(config RetryConfig, attempt int) time.Duration {
	delay := time.Duration(float64(config.BaseDelay) * math.Pow(config.BackoffFactor, float64(attempt)))
	
	if delay > config.MaxDelay {
		delay = config.MaxDelay
	}
	
	// 添加抖动
	if config.Jitter {
		jitter := time.Duration(rand.Float64() * float64(delay) * 0.1)
		delay += jitter
	}
	
	return delay
}

// Retry 使用默认配置重试
func Retry(ctx context.Context, fn RetryableFunc) error {
	return RetryWithConfig(ctx, DefaultRetryConfig, fn)
}
```

### 1.4 并发截图错误处理优化

// 在 app/services/concurrent_screenshot_methods.go 中添加错误处理

// TakeConcurrentScreenshot 并发截图方法（优化版）
func (a *App) TakeConcurrentScreenshot(roundNumber int, mode string) (map[string]interface{}, error) {
	ctx := context.Background()
	errorHandler := middleware.NewErrorHandler(utils.Logger)
	
	utils.LogInfo("开始并发截图", zap.Int("roundNumber", roundNumber), zap.String("mode", mode))
	
	// 参数验证
	if roundNumber <= 0 {
		err := utils.NewAppError(
			utils.ErrBusinessValidation,
			"参数错误",
			"轮次编号必须大于0",
			nil,
		).WithOperation("TakeConcurrentScreenshot")
		a.sendTaskErrorEvent("concurrent-screenshot", err.Error())
		return nil, err
	}
	
	if mode != "B" && mode != "C" {
		err := utils.NewAppError(
			utils.ErrBusinessValidation,
			"参数错误",
			"模式必须是B或C",
			nil,
		).WithOperation("TakeConcurrentScreenshot")
		a.sendTaskErrorEvent("concurrent-screenshot", err.Error())
		return nil, err
	}
	
	// 记录开始时间
	startTime := time.Now()
	
	// 发送开始事件
	modeDisplay := "B02生化分析"
	if mode == "C" || mode == "C03" {
		modeDisplay = "C03病理分析"
	}
	
	a.sendProgressEvent(0, mode, fmt.Sprintf("开始第%d轮%s截图...", roundNumber, modeDisplay), roundNumber)
	
	// 使用重试机制执行截图处理
	err := utils.RetryWithConfig(ctx, utils.RetryConfig{
		MaxAttempts:   3,
		BaseDelay:     200 * time.Millisecond,
		MaxDelay:      2 * time.Second,
		BackoffFactor: 1.5,
		Jitter:        true,
	}, func() error {
		return a.processScreenshotSteps(roundNumber, mode, modeDisplay)
	})
	
	if err != nil {
		appErr := errorHandler.HandleError(ctx, err, "TakeConcurrentScreenshot")
		a.sendTaskErrorEvent("concurrent-screenshot", appErr.Error())
		return nil, appErr
	}
	
	// 计算处理时间
	duration := time.Since(startTime)
	
	// 构建结果
	result := map[string]interface{}{
		"success":      true,
		"round_number": roundNumber,
		"mode":         mode,
		"user_name":    "测试用户",
		"status":       "completed",
		"message":      fmt.Sprintf("第%d轮%s截图已完成", roundNumber, modeDisplay),
		"duration":     duration.Milliseconds(),
		"timestamp":    time.Now().Unix(),
	}
	
	// 发送完成事件
	a.sendTaskCompletedEvent("concurrent-screenshot", 
		fmt.Sprintf("第%d轮%s截图处理完成", roundNumber, modeDisplay), 
		duration, result)
	
	utils.LogInfo("并发截图完成", zap.Int("roundNumber", roundNumber), zap.Duration("duration", duration))
	
	return result, nil
}

// processScreenshotSteps 处理截图步骤
func (a *App) processScreenshotSteps(roundNumber int, mode, modeDisplay string) error {
	// 模拟截图处理过程
	for i := 1; i <= 5; i++ {
		progress := i * 20 // 每步20%
		message := fmt.Sprintf("第%d轮%s截图处理中... (%d/5)", roundNumber, modeDisplay, i)
		
		a.sendProgressEvent(progress, mode, message, roundNumber)
		
		// 模拟可能的错误
		if i == 3 && rand.Float32() < 0.1 { // 10%概率出错
			return utils.NewAppError(
				utils.ErrScreenshotProcess,
				"截图处理失败",
				"模拟的处理错误",
				nil,
			)
		}
		
		// 模拟处理时间
		time.Sleep(200 * time.Millisecond)
	}
	
	// 模拟OCR处理
	return a.processOCRSteps()
}

// processOCRSteps 处理OCR步骤
func (a *App) processOCRSteps() error {
	organNames := []string{"肝脏", "心脏", "肺部", "肾脏", "脾脏"}
	for i, organName := range organNames {
		confidence := 75 + (i * 5) // 模拟置信度递增
		progress := (i + 1) * 20
		message := fmt.Sprintf("OCR识别: %s (置信度: %d%%)", organName, confidence)
		
		a.sendOCRProgressEvent(organName, confidence, progress, message)
		
		// 模拟OCR可能的错误
		if confidence < 80 && rand.Float32() < 0.05 { // 低置信度时5%概率出错
			return utils.NewAppError(
				utils.ErrOCRProcess,
				"OCR识别失败",
				fmt.Sprintf("器官%s识别置信度过低: %d%%", organName, confidence),
				nil,
			)
		}
		
		// 模拟OCR处理时间
		time.Sleep(150 * time.Millisecond)
	}
	
	return nil
}
```

## 2. 前端错误处理优化

### 2.1 统一错误处理服务

```javascript
// frontend/src/utils/errorHandler.js

class ErrorHandler {
  constructor() {
    this.errorTypes = {
      NETWORK: 'network',
      VALIDATION: 'validation',
      BUSINESS: 'business',
      SYSTEM: 'system',
      PERMISSION: 'permission'
    }
    
    this.errorCodes = {
      // 网络错误
      NETWORK_TIMEOUT: 4000,
      NETWORK_CONNECTION: 4001,
      NETWORK_API: 4002,
      
      // 业务错误
      BUSINESS_VALIDATION: 2000,
      BUSINESS_NOT_FOUND: 2001,
      BUSINESS_CONFLICT: 2002,
      
      // 系统错误
      SYSTEM_INTERNAL: 1000,
      SYSTEM_TIMEOUT: 1001,
      SYSTEM_OVERLOAD: 1002
    }
    
    this.retryableErrors = [
      this.errorCodes.NETWORK_TIMEOUT,
      this.errorCodes.NETWORK_CONNECTION,
      this.errorCodes.SYSTEM_TIMEOUT
    ]
  }

  /**
   * 处理错误
   * @param {Error|Object} error 错误对象
   * @param {string} context 错误上下文
   * @param {Object} options 处理选项
   */
  handleError(error, context = '', options = {}) {
    const errorInfo = this.parseError(error, context)
    
    // 记录错误
    this.logError(errorInfo)
    
    // 显示用户友好的错误信息
    if (options.showToUser !== false) {
      this.showUserError(errorInfo, options)
    }
    
    // 发送错误事件
    this.emitErrorEvent(errorInfo)
    
    return errorInfo
  }

  /**
   * 解析错误
   */
  parseError(error, context) {
    const errorInfo = {
      id: this.generateErrorId(),
      timestamp: Date.now(),
      context,
      type: this.errorTypes.SYSTEM,
      code: this.errorCodes.SYSTEM_INTERNAL,
      message: '未知错误',
      details: '',
      originalError: error,
      isRetryable: false,
      userMessage: '操作失败，请稍后重试'
    }

    if (error) {
      // 处理后端返回的结构化错误
      if (error.code && error.message) {
        errorInfo.code = error.code
        errorInfo.message = error.message
        errorInfo.details = error.details || ''
        errorInfo.type = this.determineErrorType(error.code)
        errorInfo.isRetryable = this.retryableErrors.includes(error.code)
        errorInfo.userMessage = this.getUserMessage(error.code, error.message)
      }
      // 处理网络错误
      else if (error.name === 'NetworkError' || error.message?.includes('fetch')) {
        errorInfo.type = this.errorTypes.NETWORK
        errorInfo.code = this.errorCodes.NETWORK_CONNECTION
        errorInfo.message = '网络连接失败'
        errorInfo.isRetryable = true
        errorInfo.userMessage = '网络连接失败，请检查网络设置后重试'
      }
      // 处理超时错误
      else if (error.name === 'TimeoutError' || error.message?.includes('timeout')) {
        errorInfo.type = this.errorTypes.NETWORK
        errorInfo.code = this.errorCodes.NETWORK_TIMEOUT
        errorInfo.message = '请求超时'
        errorInfo.isRetryable = true
        errorInfo.userMessage = '请求超时，请稍后重试'
      }
      // 处理验证错误
      else if (error.name === 'ValidationError') {
        errorInfo.type = this.errorTypes.VALIDATION
        errorInfo.code = this.errorCodes.BUSINESS_VALIDATION
        errorInfo.message = error.message || '数据验证失败'
        errorInfo.userMessage = `输入数据有误：${error.message}`
      }
      // 处理普通错误
      else {
        errorInfo.message = error.message || error.toString()
        errorInfo.details = error.stack || ''
      }
    }

    return errorInfo
  }

  /**
   * 确定错误类型
   */
  determineErrorType(code) {
    if (code >= 4000 && code < 5000) return this.errorTypes.NETWORK
    if (code >= 2000 && code < 3000) return this.errorTypes.BUSINESS
    if (code >= 1000 && code < 2000) return this.errorTypes.SYSTEM
    return this.errorTypes.SYSTEM
  }

  /**
   * 获取用户友好的错误信息
   */
  getUserMessage(code, message) {
    const userMessages = {
      [this.errorCodes.NETWORK_TIMEOUT]: '网络请求超时，请稍后重试',
      [this.errorCodes.NETWORK_CONNECTION]: '网络连接失败，请检查网络设置',
      [this.errorCodes.BUSINESS_VALIDATION]: '输入的数据格式不正确',
      [this.errorCodes.BUSINESS_NOT_FOUND]: '请求的资源不存在',
      [this.errorCodes.BUSINESS_CONFLICT]: '操作冲突，请刷新后重试',
      [this.errorCodes.SYSTEM_INTERNAL]: '系统内部错误，请联系技术支持',
      [this.errorCodes.SYSTEM_OVERLOAD]: '系统繁忙，请稍后重试'
    }
    
    return userMessages[code] || message || '操作失败，请稍后重试'
  }

  /**
   * 记录错误日志
   */
  logError(errorInfo) {
    const logData = {
      id: errorInfo.id,
      timestamp: errorInfo.timestamp,
      context: errorInfo.context,
      type: errorInfo.type,
      code: errorInfo.code,
      message: errorInfo.message,
      details: errorInfo.details,
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    // 开发环境详细日志
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 Error [${errorInfo.type}:${errorInfo.code}]`)
      console.error('Error Info:', logData)
      if (errorInfo.originalError) {
        console.error('Original Error:', errorInfo.originalError)
      }
      console.groupEnd()
    }

    // 生产环境简化日志
    else {
      console.error(`[${errorInfo.id}] ${errorInfo.type}:${errorInfo.code} - ${errorInfo.message}`)
    }

    // 发送到日志服务（如果需要）
    this.sendToLogService(logData)
  }

  /**
   * 显示用户错误信息
   */
  showUserError(errorInfo, options = {}) {
    const { notificationManager } = window.app || {}
    
    if (notificationManager) {
      const config = {
        title: this.getErrorTitle(errorInfo.type),
        message: errorInfo.userMessage,
        type: 'error',
        duration: options.duration || 5000,
        actions: []
      }

      // 添加重试按钮
      if (errorInfo.isRetryable && options.onRetry) {
        config.actions.push({
          text: '重试',
          action: options.onRetry
        })
      }

      // 添加详情按钮
      if (process.env.NODE_ENV === 'development') {
        config.actions.push({
          text: '详情',
          action: () => this.showErrorDetails(errorInfo)
        })
      }

      notificationManager.showNotification(config)
    }
  }

  /**
   * 获取错误标题
   */
  getErrorTitle(type) {
    const titles = {
      [this.errorTypes.NETWORK]: '网络错误',
      [this.errorTypes.VALIDATION]: '输入错误',
      [this.errorTypes.BUSINESS]: '业务错误',
      [this.errorTypes.SYSTEM]: '系统错误',
      [this.errorTypes.PERMISSION]: '权限错误'
    }
    
    return titles[type] || '操作失败'
  }

  /**
   * 显示错误详情
   */
  showErrorDetails(errorInfo) {
    const details = {
      错误ID: errorInfo.id,
      时间: new Date(errorInfo.timestamp).toLocaleString(),
      类型: errorInfo.type,
      代码: errorInfo.code,
      消息: errorInfo.message,
      上下文: errorInfo.context,
      详情: errorInfo.details
    }

    console.table(details)
    
    // 可以显示模态框或其他UI组件
    window.dispatchEvent(new CustomEvent('show-error-details', {
      detail: errorInfo
    }))
  }

  /**
   * 发送错误事件
   */
  emitErrorEvent(errorInfo) {
    window.dispatchEvent(new CustomEvent('app-error', {
      detail: errorInfo
    }))
  }

  /**
   * 发送到日志服务
   */
  async sendToLogService(logData) {
    try {
      // 这里可以发送到远程日志服务
      // await fetch('/api/logs', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(logData)
      // })
    } catch (error) {
      console.warn('发送日志失败:', error)
    }
  }

  /**
   * 生成错误ID
   */
  generateErrorId() {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 重试机制
   */
  async retry(fn, options = {}) {
    const {
      maxAttempts = 3,
      delay = 1000,
      backoff = 1.5,
      onRetry = null
    } = options

    let lastError
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        
        if (attempt === maxAttempts) {
          break
        }

        const errorInfo = this.parseError(error, 'retry')
        if (!errorInfo.isRetryable) {
          break
        }

        if (onRetry) {
          onRetry(attempt, error)
        }

        const waitTime = delay * Math.pow(backoff, attempt - 1)
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }
    
    throw lastError
  }
}

// 创建全局实例
const errorHandler = new ErrorHandler()

// 全局错误处理
window.addEventListener('error', (event) => {
  errorHandler.handleError(event.error, 'global-error')
})

window.addEventListener('unhandledrejection', (event) => {
  errorHandler.handleError(event.reason, 'unhandled-promise')
})

export default errorHandler
```

### 2.2 异步操作错误处理装饰器

```javascript
// frontend/src/utils/asyncErrorHandler.js

import errorHandler from './errorHandler.js'

/**
 * 异步操作错误处理装饰器
 */
export function withErrorHandling(context = '', options = {}) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function(...args) {
      try {
        const result = await originalMethod.apply(this, args)
        return result
      } catch (error) {
        const errorInfo = errorHandler.handleError(error, `${context}.${propertyKey}`, options)
        
        // 如果设置了重试，尝试重试
        if (options.retry && errorInfo.isRetryable) {
          return await errorHandler.retry(
            () => originalMethod.apply(this, args),
            options.retry
          )
        }
        
        throw errorInfo
      }
    }
    
    return descriptor
  }
}

/**
 * 简化的错误处理函数
 */
export async function safeAsync(fn, context = '', options = {}) {
  try {
    return await fn()
  } catch (error) {
    const errorInfo = errorHandler.handleError(error, context, options)
    
    if (options.retry && errorInfo.isRetryable) {
      return await errorHandler.retry(fn, options.retry)
    }
    
    if (options.throwError !== false) {
      throw errorInfo
    }
    
    return options.defaultValue
  }
}

### 2.3 组件错误边界

```javascript
// frontend/src/components/ErrorBoundary.vue

<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-content">
      <div class="error-icon">⚠️</div>
      <h3 class="error-title">{{ errorTitle }}</h3>
      <p class="error-message">{{ errorMessage }}</p>
      
      <div class="error-actions">
        <button @click="retry" class="btn btn-primary">
          重试
        </button>
        <button @click="reportError" class="btn btn-secondary">
          报告问题
        </button>
        <button v-if="showDetails" @click="toggleDetails" class="btn btn-text">
          {{ showErrorDetails ? '隐藏' : '显示' }}详情
        </button>
      </div>
      
      <div v-if="showErrorDetails" class="error-details">
        <pre>{{ errorDetails }}</pre>
      </div>
    </div>
  </div>
  
  <slot v-else />
</template>

<script>
import errorHandler from '../utils/errorHandler.js'

export default {
  name: 'ErrorBoundary',
  
  props: {
    fallbackTitle: {
      type: String,
      default: '组件加载失败'
    },
    fallbackMessage: {
      type: String,
      default: '组件遇到了一个错误，请尝试刷新页面'
    },
    showDetails: {
      type: Boolean,
      default: process.env.NODE_ENV === 'development'
    }
  },
  
  data() {
    return {
      hasError: false,
      errorInfo: null,
      showErrorDetails: false
    }
  },
  
  computed: {
    errorTitle() {
      return this.errorInfo?.userMessage || this.fallbackTitle
    },
    
    errorMessage() {
      return this.errorInfo?.message || this.fallbackMessage
    },
    
    errorDetails() {
      if (!this.errorInfo) return ''
      
      return JSON.stringify({
        id: this.errorInfo.id,
        type: this.errorInfo.type,
        code: this.errorInfo.code,
        message: this.errorInfo.message,
        details: this.errorInfo.details,
        timestamp: new Date(this.errorInfo.timestamp).toISOString()
      }, null, 2)
    }
  },
  
  errorCaptured(error, instance, info) {
    this.handleError(error, info)
    return false // 阻止错误继续传播
  },
  
  methods: {
    handleError(error, info = '') {
      this.hasError = true
      this.errorInfo = errorHandler.handleError(error, `component-error:${info}`, {
        showToUser: false // 由组件自己显示错误
      })
    },
    
    retry() {
      this.hasError = false
      this.errorInfo = null
      this.showErrorDetails = false
      
      // 触发重试事件
      this.$emit('retry')
      
      // 强制重新渲染
      this.$forceUpdate()
    },
    
    reportError() {
      if (this.errorInfo) {
        // 发送错误报告
        window.dispatchEvent(new CustomEvent('report-error', {
          detail: this.errorInfo
        }))
      }
    },
    
    toggleDetails() {
      this.showErrorDetails = !this.showErrorDetails
    }
  }
}
</script>

<style scoped>
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 20px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin: 10px 0;
}

.error-content {
  text-align: center;
  max-width: 500px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-title {
  color: #dc2626;
  margin-bottom: 8px;
  font-size: 18px;
  font-weight: 600;
}

.error-message {
  color: #6b7280;
  margin-bottom: 20px;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 16px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-text {
  background-color: transparent;
  color: #6b7280;
  text-decoration: underline;
}

.error-details {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 12px;
  text-align: left;
  max-height: 200px;
  overflow-y: auto;
}

.error-details pre {
  margin: 0;
  font-size: 12px;
  color: #374151;
  white-space: pre-wrap;
  word-break: break-word;
}
</style>
```

## 3. 错误监控和报告

### 3.1 错误收集服务

```javascript
// frontend/src/utils/errorReporting.js

class ErrorReporting {
  constructor() {
    this.isEnabled = process.env.NODE_ENV === 'production'
    this.endpoint = '/api/errors'
    this.queue = []
    this.isOnline = navigator.onLine
    this.maxQueueSize = 100
    
    this.setupEventListeners()
  }

  setupEventListeners() {
    // 监听网络状态
    window.addEventListener('online', () => {
      this.isOnline = true
      this.flushQueue()
    })
    
    window.addEventListener('offline', () => {
      this.isOnline = false
    })
    
    // 监听应用错误事件
    window.addEventListener('app-error', (event) => {
      this.reportError(event.detail)
    })
    
    // 页面卸载时发送剩余错误
    window.addEventListener('beforeunload', () => {
      this.flushQueue(true)
    })
  }

  /**
   * 报告错误
   */
  async reportError(errorInfo) {
    if (!this.isEnabled) {
      console.log('错误报告已禁用:', errorInfo)
      return
    }

    const report = this.createErrorReport(errorInfo)
    
    if (this.isOnline) {
      try {
        await this.sendReport(report)
      } catch (error) {
        console.warn('发送错误报告失败:', error)
        this.queueReport(report)
      }
    } else {
      this.queueReport(report)
    }
  }

  /**
   * 创建错误报告
   */
  createErrorReport(errorInfo) {
    return {
      id: errorInfo.id,
      timestamp: errorInfo.timestamp,
      type: errorInfo.type,
      code: errorInfo.code,
      message: errorInfo.message,
      details: errorInfo.details,
      context: errorInfo.context,
      
      // 环境信息
      userAgent: navigator.userAgent,
      url: window.location.href,
      referrer: document.referrer,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      
      // 应用信息
      appVersion: process.env.VUE_APP_VERSION || 'unknown',
      buildTime: process.env.VUE_APP_BUILD_TIME || 'unknown',
      
      // 用户信息（如果有）
      userId: this.getUserId(),
      sessionId: this.getSessionId()
    }
  }

  /**
   * 发送报告
   */
  async sendReport(report) {
    const response = await fetch(this.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(report)
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
  }

  /**
   * 队列报告
   */
  queueReport(report) {
    if (this.queue.length >= this.maxQueueSize) {
      this.queue.shift() // 移除最旧的报告
    }
    
    this.queue.push(report)
    this.saveQueueToStorage()
  }

  /**
   * 刷新队列
   */
  async flushQueue(sync = false) {
    if (this.queue.length === 0) return
    
    const reports = [...this.queue]
    this.queue = []
    
    for (const report of reports) {
      try {
        if (sync) {
          // 同步发送（页面卸载时）
          navigator.sendBeacon(this.endpoint, JSON.stringify(report))
        } else {
          await this.sendReport(report)
        }
      } catch (error) {
        console.warn('发送队列中的错误报告失败:', error)
        this.queueReport(report) // 重新加入队列
      }
    }
    
    this.saveQueueToStorage()
  }

  /**
   * 保存队列到本地存储
   */
  saveQueueToStorage() {
    try {
      localStorage.setItem('error-queue', JSON.stringify(this.queue))
    } catch (error) {
      console.warn('保存错误队列失败:', error)
    }
  }

  /**
   * 从本地存储加载队列
   */
  loadQueueFromStorage() {
    try {
      const stored = localStorage.getItem('error-queue')
      if (stored) {
        this.queue = JSON.parse(stored)
      }
    } catch (error) {
      console.warn('加载错误队列失败:', error)
      this.queue = []
    }
  }

  /**
   * 获取用户ID
   */
  getUserId() {
    // 从应用状态或存储中获取用户ID
    return window.app?.currentUser?.id || 'anonymous'
  }

  /**
   * 获取会话ID
   */
  getSessionId() {
    let sessionId = sessionStorage.getItem('session-id')
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('session-id', sessionId)
    }
    return sessionId
  }

  /**
   * 启用错误报告
   */
  enable() {
    this.isEnabled = true
    this.loadQueueFromStorage()
    if (this.isOnline) {
      this.flushQueue()
    }
  }

  /**
   * 禁用错误报告
   */
  disable() {
    this.isEnabled = false
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      enabled: this.isEnabled,
      queueSize: this.queue.length,
      isOnline: this.isOnline
    }
  }
}

// 创建全局实例
const errorReporting = new ErrorReporting()

export default errorReporting
```

## 4. 使用示例和最佳实践

### 4.1 在组件中使用错误处理

```javascript
// 在 ScreenshotPanel.vue 中使用优化的错误处理

import { withErrorHandling, safeAsync } from '../utils/asyncErrorHandler.js'
import errorHandler from '../utils/errorHandler.js'

export default {
  name: 'ScreenshotPanel',
  
  methods: {
    // 使用装饰器
    @withErrorHandling('ScreenshotPanel', {
      retry: { maxAttempts: 3, delay: 1000 },
      onRetry: (attempt) => {
        console.log(`重试截图，第${attempt}次尝试`)
      }
    })
    async testConcurrentScreenshots() {
      const result = await window.go.main.App.TakeConcurrentScreenshot(
        this.currentRound, 
        this.selectedMode
      )
      
      if (result.success) {
        this.showNotification('并发截图成功', result.message)
        this.addToRecentScreenshots(result)
      } else {
        throw new Error(result.error || '截图失败')
      }
    },
    
    // 使用 safeAsync
    async loadRecentScreenshots() {
      const screenshots = await safeAsync(
        () => this.getStoredScreenshots(),
        'ScreenshotPanel.loadRecentScreenshots',
        {
          defaultValue: [],
          showToUser: false // 加载失败不显示给用户
        }
      )
      
      this.recentScreenshots = screenshots
    },
    
    // 手动错误处理
    async takeScreenshot() {
      try {
        const result = await window.go.main.App.TakeScreenshot(
          this.selectedMode,
          this.currentPatient?.name || '测试用户'
        )
        
        this.showNotification('截图成功', '截图已保存')
        return result
      } catch (error) {
        // 使用统一错误处理
        errorHandler.handleError(error, 'ScreenshotPanel.takeScreenshot', {
          onRetry: () => this.takeScreenshot()
        })
      }
    }
  }
}
```

### 4.2 在Store中使用错误处理

```javascript
// 在 patient.js store 中使用

import { safeAsync } from '../utils/asyncErrorHandler.js'
import errorHandler from '../utils/errorHandler.js'

export const usePatientStore = defineStore('patient', {
  state: () => ({
    patients: [],
    loading: false,
    error: null
  }),
  
  actions: {
    async refreshRegistrations() {
      if (this.isRefreshing) return
      
      this.isRefreshing = true
      this.error = null
      
      try {
        const result = await errorHandler.retry(
          () => window.go.main.App.GetWaitingPatients(),
          {
            maxAttempts: 3,
            delay: 1000,
            onRetry: (attempt) => {
              console.log(`重试获取患者列表，第${attempt}次尝试`)
            }
          }
        )
        
        if (result.success) {
          this.patients = result.data || []
          return { success: true }
        } else {
          throw new Error(result.message || '获取患者列表失败')
        }
      } catch (error) {
        this.error = errorHandler.handleError(error, 'PatientStore.refreshRegistrations')
        return { success: false, message: this.error.userMessage }
      } finally {
        this.isRefreshing = false
      }
    }
  }
})
```

## 5. 配置和部署

### 5.1 错误处理配置

```javascript
// frontend/src/config/errorConfig.js

export const ERROR_CONFIG = {
  // 是否启用错误报告
  enableReporting: process.env.NODE_ENV === 'production',
  
  // 错误报告端点
  reportingEndpoint: process.env.VUE_APP_ERROR_ENDPOINT || '/api/errors',
  
  // 重试配置
  retry: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2
  },
  
  // 日志级别
  logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'error',
  
  // 错误类型配置
  errorTypes: {
    showStackTrace: process.env.NODE_ENV === 'development',
    showErrorDetails: process.env.NODE_ENV === 'development',
    autoRetry: true
  },
  
  // 通知配置
  notifications: {
    showUserErrors: true,
    defaultDuration: 5000,
    maxNotifications: 5
  }
}
```

### 5.2 初始化错误处理

```javascript
// 在 main.js 中初始化

import { createApp } from 'vue'
import App from './App.vue'
import errorHandler from './utils/errorHandler.js'
import errorReporting from './utils/errorReporting.js'
import { ERROR_CONFIG } from './config/errorConfig.js'

const app = createApp(App)

// 配置错误处理
if (ERROR_CONFIG.enableReporting) {
  errorReporting.enable()
}

// 全局错误处理
app.config.errorHandler = (error, instance, info) => {
  errorHandler.handleError(error, `vue-error:${info}`)
}

// 挂载到全局
app.config.globalProperties.$errorHandler = errorHandler
window.app = window.app || {}
window.app.errorHandler = errorHandler

app.mount('#app')
```

## 6. 总结

这个错误处理优化方案提供了：

1. **统一的错误类型和错误码系统**
2. **智能重试机制**
3. **用户友好的错误提示**
4. **详细的错误日志记录**
5. **错误监控和报告**
6. **组件级错误边界**
7. **异步操作错误处理装饰器**
8. **可配置的错误处理策略**

通过实施这些优化，系统将具备更强的容错能力和更好的用户体验。