<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>OCR功能测试</h1>
    
    <div class="test-section">
        <h2>测试现有截图</h2>
        <p>测试图片路径: <code>pic/temp/王明阳+250707004/temp_screenshot_auto_王明阳_20250707_102655.png</code></p>
        <button onclick="testExistingImage()">测试OCR识别</button>
        <div id="result1" class="result" style="display: none;"></div>
    </div>

    <script>
        // 测试现有图片的OCR功能
        async function testExistingImage() {
            const resultDiv = document.getElementById('result1');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在处理OCR请求...';

            try {
                const imagePath = 'pic/temp/王明阳+250707004/temp_screenshot_auto_王明阳_20250707_102655.png';
                
                // 调用Wails绑定的TestOCR方法
                const result = await window.go.main.App.TestOCR(imagePath);
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `OCR识别成功！\n\n` +
                    `器官名称: ${result.organ_name}\n` +
                    `置信度: ${result.confidence}\n` +
                    `图片路径: ${result.image_path}\n\n` +
                    `识别内容:\n${result.full_text}\n\n` +
                    `键值对:\n${JSON.stringify(result.key_value_pairs, null, 2)}`;
                
                console.log('OCR测试成功:', result);
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `OCR识别失败: ${error.message || error}`;
                console.error('OCR测试失败:', error);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('OCR测试页面已加载');
            
            // 检查Wails绑定是否可用
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.TestOCR) {
                console.log('Wails OCR绑定可用');
            } else {
                console.error('Wails OCR绑定不可用');
                document.body.innerHTML = '<h1>错误</h1><p>Wails绑定不可用，请确保在Wails应用中打开此页面。</p>';
            }
        });
    </script>
</body>
</html>
