// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
// 详细JSDoc语法见 https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
module.exports = {

	async add_onsite_registration(params = {}) {
		const dbJQL = uniCloud.databaseForJQL({
			clientInfo: this.getClientInfo()
		});
	
		console.log('add_onsite_registration中：', params);

    try {

        const result = await dbJQL.collection("hc-onsite-registration").add(params);
        console.log(`[hc-actions-CloudObj][add_onsite_registration][add(params)] result:`, result);
        if (result.id) { // add 操作成功，result 会包含新文档的 id
            return {
                errCode: 0,
                errMsg: '挂号信息添加成功。',
                id: result.id, // 返回新文档的ID
                affectedDocs: result.affectedDocs // 通常是 1
            };
        } else {
             return {
                 errCode: 'ADD_FAILED',
                 errMsg: '挂号信息添加失败，未创建任何记录。',
                 result: result // 返回原始结果供调试
             };
        }
    } catch (e) {
        console.error(`[hc-actions-CloudObj][add_onsite_registration] Error adding document:`, e);
		
        let errMsg = '数据库操作失败。';
        if (e.message) {
            errMsg = e.message;
        }
        if (e.code) {
             errMsg = `数据库错误: ${e.code} - ${e.message}`;
        }
        // 特别处理 TOKEN_INVALID_ANONYMOUS_USER 错误，给出更明确的提示
        if (e.code === 'TOKEN_INVALID_ANONYMOUS_USER' || (e.errMsg && e.errMsg.includes('TOKEN_INVALID_ANONYMOUS_USER'))) {
            errMsg = '用户未登录或登录状态已失效，请重新登录后再试。';
        }
        return {
            errCode: e.code || 'DATABASE_ERROR',
            errMsg: errMsg
        };
    }
	},

	async getSiteNameBySiteID(params = {}) {
		const dbJQL = uniCloud.databaseForJQL({
			clientInfo:this.getClientInfo()
		});
		
		const { siteID } = params;

		try {
			const result = await dbJQL.collection('hc-check-organization')
				.where({ site_id: siteID })
				.field('site_name')
				.get({
					getOne: true
				});

			if (!result.data) {
				return {
					errCode: 'SITE_NOT_FOUND',
					errMsg: '未找到对应的网点信息。'
				};
			}

			return {
				errCode: 0,
				errMsg: '查询成功。',
				siteName: result.data.site_name
			};
		} catch (e) {
			console.error(`[hc-actions-CloudObj][getSiteNameBySiteID] Error querying site ${siteID}:`, e);
			let errMsg = '数据库操作失败。';
			if (e.message) {
				errMsg = e.message;
			}
			if (e.code) {
				errMsg = `数据库错误: ${e.code} - ${e.message}`;
			}
			return {
				errCode: 'DATABASE_ERROR',
				errMsg: errMsg
			};
		}
	},

	// 获取最新报到记录
	async getLatestRegistration(params = {}) {
		const dbJQL = uniCloud.databaseForJQL({
			clientInfo:this.getClientInfo()
		});
		const { user_id } = params;
		
		try {
			const res = await dbJQL.collection('hc-onsite-registration').where({
				user_id: user_id
			}).orderBy('registration_time', 'desc').limit(1).get();
		
			if (res.data.length > 0) {
				return { errCode: 0, data: res.data[0] };
			} else {
				return { errCode: 0, data: null, errMsg: '未找到报到记录' };
			}
		} catch (e) {
			console.error(`[hc-actions-CloudObj][getLatestRegistration] Error querying latest registration for user ${user_id}:`, e);
			let errMsg = '数据库操作失败。';
			if (e.message) {
				errMsg = e.message;
			}
			if (e.code) {
				errMsg = `数据库错误: ${e.code} - ${e.message}`;
			}
			// 特别处理 TOKEN_INVALID_ANONYMOUS_USER 错误
			if (e.code === 'TOKEN_INVALID_ANONYMOUS_USER' || (e.errMsg && e.errMsg.includes('TOKEN_INVALID_ANONYMOUS_USER'))) {
				errMsg = '用户未登录或登录状态已失效，请重新登录后再试。';
			}
			return {
				errCode: e.code || 'DATABASE_ERROR',
				errMsg: errMsg
			};
		}
	},
	
	// 获取历史报到记录列表
	async getRegistrationHistory(params = {}) {
		const dbJQL = uniCloud.databaseForJQL({
			clientInfo: this.getClientInfo()
		});
		const { user_id, page = 1, pageSize = 10 } = params;
		const offset = (page - 1) * pageSize;

		try {
			const res = await dbJQL.collection('hc-onsite-registration')
				.where({ user_id: user_id })
				.orderBy('registration_time', 'desc')
				.skip(offset)
				.limit(pageSize)
				.get();

			const totalRes = await dbJQL.collection('hc-onsite-registration')
				.where({ user_id: user_id })
				.count();
			const total = totalRes.total;

			return {
				errCode: 0,
				errMsg: '查询成功',
				data: res.data,
				total,
				hasMore: total > page * pageSize
			};
		} catch (e) {
			console.error(`[hc-actions-CloudObj][getRegistrationHistory] Error querying history for user ${user_id}:`, e);
			let errMsg = '数据库操作失败。';
			if (e.message) {
				errMsg = e.message;
			}
			if (e.code) {
				errMsg = `数据库错误: ${e.code} - ${e.message}`;;
			}
			return {
				errCode: e.code || 'DATABASE_ERROR',
				errMsg: errMsg
			};
		}
	},

	/**
	 * 通过设备MAC地址获取站点机构信息
	 * @param {Object} params - 参数对象
	 * @param {string} params.mac_address - 设备MAC地址
	 * @returns {Promise<Object>} 包含设备信息和站点机构信息的对象
	 */
	async getSiteInfoByDeviceMAC(params = {}) {
		const dbJQL = uniCloud.databaseForJQL({
			clientInfo: this.getClientInfo()
		});
		
		// 添加详细的调试日志
		console.log('[getSiteInfoByDeviceMAC] 原始参数:', JSON.stringify(params));
		
		// 对于URL化的云对象，需要检查是否是HTTP请求
		let actualParams = params;
		
		// 获取HTTP信息进行调试
		try {
			const httpInfo = this.getHttpInfo();
			console.log('[getSiteInfoByDeviceMAC] HTTP信息:', JSON.stringify({
				method: httpInfo?.method,
				queryStringParameters: httpInfo?.queryStringParameters,
				headers: httpInfo?.headers,
				body: httpInfo?.body
			}));
			
			// 如果是HTTP GET请求，尝试从查询参数获取
			if (httpInfo && httpInfo.queryStringParameters) {
				actualParams = { ...params, ...httpInfo.queryStringParameters };
				console.log('[getSiteInfoByDeviceMAC] 合并GET参数后:', JSON.stringify(actualParams));
			}
			
			// 如果是HTTP POST请求，尝试从body获取
			if (httpInfo && httpInfo.body) {
				try {
					const bodyData = JSON.parse(httpInfo.body);
					actualParams = { ...actualParams, ...bodyData };
					console.log('[getSiteInfoByDeviceMAC] 合并POST参数后:', JSON.stringify(actualParams));
				} catch (parseError) {
					console.log('[getSiteInfoByDeviceMAC] 解析HTTP body失败:', parseError.message);
					console.log('[getSiteInfoByDeviceMAC] 原始body:', httpInfo.body);
				}
			}
		} catch (httpError) {
			console.log('[getSiteInfoByDeviceMAC] 获取HTTP信息失败:', httpError.message);
			console.log('[getSiteInfoByDeviceMAC] 使用原始参数:', JSON.stringify(params));
		}
		
		// 尝试多种可能的参数名称
		let mac_address = actualParams.mac_address || actualParams.macAddress || actualParams.MAC_ADDRESS || actualParams.deviceMAC;
		
		console.log('[getSiteInfoByDeviceMAC] 最终提取的MAC地址:', mac_address);
		console.log('[getSiteInfoByDeviceMAC] 最终参数:', JSON.stringify(actualParams));
		if (!mac_address) {
			return {
				errCode: 'PARAM_ERROR',
				errMsg: '设备MAC地址不能为空'
			};
		}

		try {
			// // 设置操作角色为admin，跳过schema权限验证
			await dbJQL.setUser({
				role: ['admin']
			});
			
			// 使用JQL联表查询：设备信息 -关联关系 -> 机构站点信息
			// 临时表field方法内需要包含关联字段，否则无法建立关联关系
			// collection中写多个表名时，只要第一个表是主表，剩余表均与主表做关联查询。第3个表只能与主表联查，不能与第2个表联查。
			const device = await dbJQL.collection('hc-device-info')
				.where({ 'mac_address': mac_address })
				.field('organization_id, device_no, mac_address, manufacturer')
				.getTemp()
			const site = await dbJQL.collection('hc-check-organization')
				.field('_id, site_id, site_name, site_type, address, contact_person, contact_phone')
				.getTemp()
			const res = await dbJQL.collection(device, site)
				.get()

			// 检查查询结果
			if (!res.data || res.data.length === 0) {
				return {
					errCode: "DEVICE_NOT_FOUND",
					errMsg: "未找到对应MAC地址的设备信息"
				};
			}

			// 获取第一条记录（设备和站点的关联数据）
			const siteData = res.data[0];
			
			// 构造返回的站点信息
			// const siteInfo = {
			// 	site_id: siteData.site_id,
			// 	site_name: siteData.site_name,
			// 	site_type: siteData.site_type
			// };

			return {
				errCode: "0",
				errMsg: "success",
				site_info: siteData
				// 'site_info_res.data[0]': res.data[0]

			};

		} catch (e) {
			console.error(`[hc-actions-CloudObj][getSiteInfoByDeviceMAC] Error querying device ${mac_address}:`, e);
			let errMsg = '数据库操作失败。';
			if (e.message) {
				errMsg = e.message;
			}
			if (e.code) {
				errMsg = `数据库错误: ${e.code} - ${e.message}`;
			}
			// 特别处理 TOKEN_INVALID_ANONYMOUS_USER 错误
			if (e.code === 'TOKEN_INVALID_ANONYMOUS_USER' || (e.errMsg && e.errMsg.includes('TOKEN_INVALID_ANONYMOUS_USER'))) {
				errMsg = '用户未登录或登录状态已失效，请重新登录后再试。';
			}
			return {
				errCode: e.code || 'DATABASE_ERROR',
				errMsg: errMsg
			};
		}
	}
	
	
	// async getSiteInfoByDeviceMAC(params = {}) {
	// 	const dbJQL = uniCloud.databaseForJQL({
	// 		clientInfo: this.getClientInfo()
	// 	});
		
	// 	// 对于URL化的云对象，需要检查是否是HTTP请求
	// 	let actualParams = params;
		
	// 	// 如果是通过HTTP URL方式调用，需要从HTTP信息中获取参数
	// 	try {
	// 		const httpInfo = this.getHttpInfo();
	// 		if (httpInfo && httpInfo.body) {
	// 			// 解析POST请求的body数据
	// 			const bodyData = JSON.parse(httpInfo.body);
	// 			actualParams = { ...params, ...bodyData };
	// 			console.log('[getSiteInfoByDeviceMAC] HTTP请求参数:', actualParams);
	// 		}
	// 	} catch (httpError) {
	// 		// 如果不是HTTP请求或解析失败，使用原始params
	// 		console.log('[getSiteInfoByDeviceMAC] 使用原始参数:', params);
	// 	}
		
	// 	const { mac_address } = actualParams;

	// 	if (!mac_address) {
	// 		return {
	// 			errCode: 'PARAM_ERROR',
	// 			errMsg: '设备MAC地址不能为空'
	// 		};
	// 	}

	// 	try {

	// 		// 使用JQL联表查询：设备信息 -> 设备关联关系 -> 机构站点信息
	// 		// 临时表field方法内需要包含关联字段，否则无法建立关联关系
	// 		// collection中写多个表名时，只要第一个表是主表，剩余表均与主表做关联查询。第3个表只能与主表联查，不能与第2个表联查。
	// 		const device = await dbJQL.collection('hc-device-info').field('_id, device_no, mac_address').getTemp()
	// 		// const device = await dbJQL.collection('hc-device-info').field('_id, device_no, mac_address').get()
	// 		const deviceRelation = await dbJQL.collection('hc-site-device-relation').field('_id, device_id, organization_id').getTemp()
	// 		// const deviceRelation = await dbJQL.collection('hc-site-device-relation').field('_id, device_id, organization_id').get()
	// 		const site = await dbJQL.collection('hc-check-organization').field('_id, site_id, site_name, site_type, address, contact_person, contact_phone').getTemp()
	// 		// const site = await dbJQL.collection('hc-check-organization').field('_id, site_id, site_name, site_type, address, contact_person, contact_phone').get()
	// 		const res = await dbJQL.collection(deviceRelation, site, device)
	// 			// .where({ 'mac_address': mac_address })
	// 			// .field('_id, site_id, site_name, site_type, address, contact_person, contact_phone')
	// 			.get()
				


	// 		const result = {
	// 			// device: device,
	// 			// deviceRelation: deviceRelation,
	// 			// site: site,
	// 			// input_params: actualParams,
	// 			res: res,
	// 			mac_address: mac_address
	// 		}
	// 		return result 

	// 		if (!result.data) {
	// 			return {
	// 				errCode: 'DEVICE_NOT_FOUND',
	// 				errMsg: '未找到对应MAC地址的设备信息'
	// 			};
	// 		}

	// 		const siteData = result.data;
			

	// 		// 构造返回的站点信息
	// 		const siteInfo = {
	// 			site_id: siteData.site_id,
	// 			site_name: siteData.site_name,
	// 			site_type: siteData.site_type,
	// 			// parent_org: parentOrgName || siteData.org_name,
	// 			location: {
	// 				// province: siteData.province,
	// 				// city: siteData.city,
	// 				// district: siteData.district,
	// 				address: siteData.address
	// 			},
	// 			contact: {
	// 				manager: siteData.contact_person,
	// 				phone: siteData.contact_phone
	// 			}
	// 		};

	// 		return {
	// 			errCode: 0,
	// 			errMsg: '查询成功',
	// 			site_info: siteInfo,
	// 		};

	// 	} catch (e) {
	// 		console.error(`[hc-actions-CloudObj][getSiteInfoByDeviceMAC] Error querying device ${mac_address}:`, e);
	// 		let errMsg = '数据库操作失败。';
	// 		if (e.message) {
	// 			errMsg = e.message;
	// 		}
	// 		if (e.code) {
	// 			errMsg = `数据库错误: ${e.code} - ${e.message}`;
	// 		}
	// 		// 特别处理 TOKEN_INVALID_ANONYMOUS_USER 错误
	// 		if (e.code === 'TOKEN_INVALID_ANONYMOUS_USER' || (e.errMsg && e.errMsg.includes('TOKEN_INVALID_ANONYMOUS_USER'))) {
	// 			errMsg = '用户未登录或登录状态已失效，请重新登录后再试。';
	// 		}
	// 		return {
	// 			errCode: e.code || 'DATABASE_ERROR',
	// 			errMsg: errMsg
	// 		};
	// 	}
	// }
}
