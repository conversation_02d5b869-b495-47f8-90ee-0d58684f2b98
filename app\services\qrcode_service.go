package services

import (
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"strings"

	"github.com/skip2/go-qrcode"
)

// QRCodeService 二维码服务
type QRCodeService struct {
	configService *ConfigService
}

// NewQRCodeService 创建新的二维码服务
func NewQRCodeService(configService *ConfigService) *QRCodeService {
	return &QRCodeService{
		configService: configService,
	}
}

// needsRegeneration 检查二维码是否需要重新生成
func (qs *QRCodeService) needsRegeneration() bool {
	_, filePath, err := qs.GetQRCodeInfo()
	if err != nil {
		return true
	}

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return true
	}

	// 检查站点信息是否有变化
	return qs.configService.IsSiteInfoChanged()
}

// GenerateCustomAppQRCodeWithCache 生成自定义小程序二维码（带缓存机制）
func (qs *QRCodeService) GenerateCustomAppQRCodeWithCache() ([]byte, string, error) {
	// 如果不需要重新生成，直接返回现有文件
	if !qs.needsRegeneration() {
		_, filePath, err := qs.GetQRCodeInfo()
		if err == nil {
			if data, err := os.ReadFile(filePath); err == nil {
				return data, filePath, nil
			}
		}
	}

	// 需要重新生成时才调用原有逻辑
	return qs.GenerateCustomAppQRCode()
}

// GenerateCustomAppQRCode 生成自定义小程序二维码
func (qs *QRCodeService) GenerateCustomAppQRCode() ([]byte, string, error) {
	config := qs.configService.GetConfig()
	if config == nil {
		return nil, "", fmt.Errorf("配置未加载")
	}

	miniAppID := config.MpAppInfo.AppID
	miniAppPagePath := config.MpAppInfo.TargetPage

	if miniAppID == "" {
		return nil, "", fmt.Errorf("Mini Program AppID is not configured")
	}
	if miniAppPagePath == "" {
		return nil, "", fmt.Errorf("Mini Program target page is not configured")
	}

	siteID := config.SiteInfo.SiteID
	if siteID == "" {
		siteID = "UNKNOWN_SITE_ID"
	}

	macAddress := config.DeviceInfo.MACAddress
	if macAddress == "" {
		macAddress = "UNKNOWN_MAC_ADDRESS"
	}

	// 为小程序路径构建查询参数
	queryParams := url.Values{}
	queryParams.Add("siteID", siteID)
	queryParams.Add("macAddress", macAddress)

	// 构建二维码的完整数据字符串（微信小程序URL Scheme）
	dataString := fmt.Sprintf("weixin://dl/business/?appid=%s&path=%s&query=%s",
		miniAppID,
		miniAppPagePath,
		queryParams.Encode(),
	)

	// 使用go-qrcode生成二维码图片
	qrCode, err := qrcode.Encode(dataString, qrcode.Medium, 256)
	if err != nil {
		return nil, "", fmt.Errorf("生成二维码失败: %w", err)
	}

	// 生成文件名：siteID+macAddress(去掉冒号)
	fileName := fmt.Sprintf("%s_%s.png", siteID, strings.ReplaceAll(macAddress, ":", ""))
	filePath := filepath.Join("pic", fileName)

	// 确保目录存在
	if err := os.MkdirAll("pic", 0755); err != nil {
		return nil, "", fmt.Errorf("创建目录失败: %v", err)
	}

	// 保存二维码图片到本地
	err = os.WriteFile(filePath, qrCode, 0644)
	if err != nil {
		return nil, "", fmt.Errorf("保存二维码文件失败: %w", err)
	}

	return qrCode, filePath, nil
}

// GetQRCodeInfo 获取二维码信息
func (qs *QRCodeService) GetQRCodeInfo() (string, string, error) {
	config := qs.configService.GetConfig()
	if config == nil {
		return "", "", fmt.Errorf("配置未加载")
	}

	siteID := config.SiteInfo.SiteID
	macAddress := config.DeviceInfo.MACAddress

	if siteID == "" {
		siteID = "UNKNOWN_SITE_ID"
	}
	if macAddress == "" {
		macAddress = "UNKNOWN_MAC_ADDRESS"
	}

	fileName := fmt.Sprintf("%s_%s.png", siteID, strings.ReplaceAll(macAddress, ":", ""))
	filePath := filepath.Join("pic", fileName)

	return fileName, filePath, nil
}
