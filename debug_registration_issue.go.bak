package main

import (
	"context"
	"encoding/json"
	"log"
	"strings"
	"time"

	"MagneticOperator/app/models"
	"MagneticOperator/app/services"
	"MagneticOperator/app/utils"

	"github.com/valyala/fasthttp"
)

// 诊断工具：检查受检者列表加载问题
func main() {
	log.Println("=== 受检者列表诊断工具 ===")

	// 1. 加载配置
	configService := services.NewConfigService(context.Background())
	config, err := configService.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}
	if config == nil {
		log.Fatalf("配置为空")
	}

	log.Printf("当前配置:")
	log.Printf("  站点ID: %s", config.SiteInfo.SiteID)
	log.Printf("  站点名称: %s", config.SiteInfo.SiteName)
	log.Printf("  设备MAC: %s", config.DeviceInfo.MACAddress)
	log.Printf("  处理后设备号: %s", strings.ReplaceAll(config.DeviceInfo.MACAddress, ":", ""))
	log.Printf("  API URL: %s", config.APIKeys.CloudFunction.RegistrationsURL)

	// 2. 测试多个日期的数据
	testDates := []string{
		"2025-07-06", // 用户报告的日期
		"2025-07-05", // 前一天
		"2025-07-07", // 后一天
		time.Now().Format("2006-01-02"), // 今天
	}

	apiService := services.NewAPIService(configService)

	for _, date := range testDates {
		log.Printf("\n=== 测试日期: %s ===", date)
		registrations, err := apiService.GetRegistrations(date)
		if err != nil {
			log.Printf("  错误: %v", err)
			continue
		}
		log.Printf("  找到 %d 条记录", len(registrations))
		for i, reg := range registrations {
			log.Printf("    [%d] 用户ID: %s, 报到号: %s, 姓名: %s, 报到时间: %d",
				i+1, reg.UserID, reg.RegistrationNumber, reg.Name, reg.RegistrationTime)
		}
	}

	// 3. 测试原始API调用（不经过业务逻辑处理）
	log.Printf("\n=== 原始API测试 ===")
	testRawAPI(config, "2025-07-06")

	// 4. 测试不同的查询参数组合
	log.Printf("\n=== 参数变化测试 ===")
	testDifferentParams(config)
}

// 测试原始API调用
func testRawAPI(config *models.AppConfig, date string) {
	deviceNo := strings.ReplaceAll(config.DeviceInfo.MACAddress, ":", "")
	requestBody := map[string]interface{}{
		"site_id":   config.SiteInfo.SiteID,
		"device_no": deviceNo,
		"page":      1,
		"pageSize":  20,
		"date":      date,
	}

	jsonData, _ := json.Marshal(requestBody)
	log.Printf("请求参数: %s", string(jsonData))

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	client := utils.NewFastHTTPClient(30 * time.Second)
	resp, err := client.DoWithRetry("POST", config.APIKeys.CloudFunction.RegistrationsURL, jsonData, headers)
	if err != nil {
		log.Printf("请求失败: %v", err)
		return
	}
	defer fasthttp.ReleaseResponse(resp)

	respBody := resp.Body()
	log.Printf("响应状态码: %d", resp.StatusCode())
	log.Printf("响应内容: %s", string(respBody))

	// 解析响应查看详细信息
	var result map[string]interface{}
	if err := json.Unmarshal(respBody, &result); err == nil {
		if data, ok := result["data"].([]interface{}); ok {
			log.Printf("数据条数: %d", len(data))
		}
		if total, ok := result["total"].(float64); ok {
			log.Printf("总记录数: %.0f", total)
		}
		if queryCondition, ok := result["queryCondition"].(map[string]interface{}); ok {
			log.Printf("查询条件: %+v", queryCondition)
		}
	}
}

// 测试不同参数组合
func testDifferentParams(config *models.AppConfig) {
	// 测试不同的站点ID
	testSiteIDs := []string{
		config.SiteInfo.SiteID,
		"YL-BJ-TZ-002", // 可能的其他站点
		"TEST-SITE",    // 测试站点
	}

	// 测试不同的设备号格式
	originalMAC := config.DeviceInfo.MACAddress
	testDeviceNos := []string{
		strings.ReplaceAll(originalMAC, ":", ""),           // 当前格式
		strings.ReplaceAll(originalMAC, ":", "-"),          // 用-分隔
		originalMAC,                                        // 原始格式
		strings.ToUpper(strings.ReplaceAll(originalMAC, ":", "")), // 大写
	}

	for _, siteID := range testSiteIDs {
		for _, deviceNo := range testDeviceNos {
			log.Printf("测试组合: site_id=%s, device_no=%s", siteID, deviceNo)
			testSpecificParams(config, siteID, deviceNo, "2025-07-06")
		}
	}
}

// 测试特定参数组合
func testSpecificParams(config *models.AppConfig, siteID, deviceNo, date string) {
	requestBody := map[string]interface{}{
		"site_id":   siteID,
		"device_no": deviceNo,
		"page":      1,
		"pageSize":  20,
		"date":      date,
	}

	jsonData, _ := json.Marshal(requestBody)
	headers := map[string]string{
		"Content-Type": "application/json",
	}

	client := utils.NewFastHTTPClient(10 * time.Second)
	resp, err := client.DoWithRetry("POST", config.APIKeys.CloudFunction.RegistrationsURL, jsonData, headers)
	if err != nil {
		log.Printf("  请求失败: %v", err)
		return
	}
	defer fasthttp.ReleaseResponse(resp)

	respBody := resp.Body()
	var result map[string]interface{}
	if err := json.Unmarshal(respBody, &result); err == nil {
		if data, ok := result["data"].([]interface{}); ok {
			if len(data) > 0 {
				log.Printf("  ✅ 找到 %d 条记录!", len(data))
				return
			}
		}
	}
	log.Printf("  ❌ 无记录")
}