(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))i(n);new MutationObserver(n=>{for(const o of n)if(o.type==="childList")for(const r of o.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&i(r)}).observe(document,{childList:!0,subtree:!0});function s(n){const o={};return n.integrity&&(o.integrity=n.integrity),n.referrerpolicy&&(o.referrerPolicy=n.referrerpolicy),n.crossorigin==="use-credentials"?o.credentials="include":n.crossorigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function i(n){if(n.ep)return;n.ep=!0;const o=s(n);fetch(n.href,o)}})();/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Ii(t){const e=Object.create(null);for(const s of t.split(","))e[s]=1;return s=>s in e}const X={},Oe=[],kt=()=>{},Or=()=>!1,Ls=t=>t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&(t.charCodeAt(2)>122||t.charCodeAt(2)<97),Oi=t=>t.startsWith("onUpdate:"),ut=Object.assign,xi=(t,e)=>{const s=t.indexOf(e);s>-1&&t.splice(s,1)},xr=Object.prototype.hasOwnProperty,q=(t,e)=>xr.call(t,e),M=Array.isArray,xe=t=>as(t)==="[object Map]",qn=t=>as(t)==="[object Set]",rn=t=>as(t)==="[object Date]",B=t=>typeof t=="function",lt=t=>typeof t=="string",$t=t=>typeof t=="symbol",tt=t=>t!==null&&typeof t=="object",Yn=t=>(tt(t)||B(t))&&B(t.then)&&B(t.catch),Jn=Object.prototype.toString,as=t=>Jn.call(t),Mr=t=>as(t).slice(8,-1),Zn=t=>as(t)==="[object Object]",Mi=t=>lt(t)&&t!=="NaN"&&t[0]!=="-"&&""+parseInt(t,10)===t,Ve=Ii(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ks=t=>{const e=Object.create(null);return s=>e[s]||(e[s]=t(s))},Dr=/-(\w)/g,xt=ks(t=>t.replace(Dr,(e,s)=>s?s.toUpperCase():"")),Lr=/\B([A-Z])/g,ve=ks(t=>t.replace(Lr,"-$1").toLowerCase()),Us=ks(t=>t.charAt(0).toUpperCase()+t.slice(1)),Js=ks(t=>t?`on${Us(t)}`:""),ae=(t,e)=>!Object.is(t,e),ws=(t,...e)=>{for(let s=0;s<t.length;s++)t[s](...e)},Xn=(t,e,s,i=!1)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,writable:i,value:s})},ui=t=>{const e=parseFloat(t);return isNaN(e)?t:e},kr=t=>{const e=lt(t)?Number(t):NaN;return isNaN(e)?t:e};let an;const $s=()=>an||(an=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function _e(t){if(M(t)){const e={};for(let s=0;s<t.length;s++){const i=t[s],n=lt(i)?Fr(i):_e(i);if(n)for(const o in n)e[o]=n[o]}return e}else if(lt(t)||tt(t))return t}const Ur=/;(?![^(]*\))/g,$r=/:([^]+)/,Br=/\/\*[^]*?\*\//g;function Fr(t){const e={};return t.replace(Br,"").split(Ur).forEach(s=>{if(s){const i=s.split($r);i.length>1&&(e[i[0].trim()]=i[1].trim())}}),e}function Gt(t){let e="";if(lt(t))e=t;else if(M(t))for(let s=0;s<t.length;s++){const i=Gt(t[s]);i&&(e+=i+" ")}else if(tt(t))for(const s in t)t[s]&&(e+=s+" ");return e.trim()}const Wr="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Hr=Ii(Wr);function to(t){return!!t||t===""}function jr(t,e){if(t.length!==e.length)return!1;let s=!0;for(let i=0;s&&i<t.length;i++)s=Cs(t[i],e[i]);return s}function Cs(t,e){if(t===e)return!0;let s=rn(t),i=rn(e);if(s||i)return s&&i?t.getTime()===e.getTime():!1;if(s=$t(t),i=$t(e),s||i)return t===e;if(s=M(t),i=M(e),s||i)return s&&i?jr(t,e):!1;if(s=tt(t),i=tt(e),s||i){if(!s||!i)return!1;const n=Object.keys(t).length,o=Object.keys(e).length;if(n!==o)return!1;for(const r in t){const a=t.hasOwnProperty(r),l=e.hasOwnProperty(r);if(a&&!l||!a&&l||!Cs(t[r],e[r]))return!1}}return String(t)===String(e)}const eo=t=>!!(t&&t.__v_isRef===!0),R=t=>lt(t)?t:t==null?"":M(t)||tt(t)&&(t.toString===Jn||!B(t.toString))?eo(t)?R(t.value):JSON.stringify(t,so,2):String(t),so=(t,e)=>eo(e)?so(t,e.value):xe(e)?{[`Map(${e.size})`]:[...e.entries()].reduce((s,[i,n],o)=>(s[Zs(i,o)+" =>"]=n,s),{})}:qn(e)?{[`Set(${e.size})`]:[...e.values()].map(s=>Zs(s))}:$t(e)?Zs(e):tt(e)&&!M(e)&&!Zn(e)?String(e):e,Zs=(t,e="")=>{var s;return $t(t)?`Symbol(${(s=t.description)!=null?s:e})`:t};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let _t;class io{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=_t,!e&&_t&&(this.index=(_t.scopes||(_t.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let e,s;if(this.scopes)for(e=0,s=this.scopes.length;e<s;e++)this.scopes[e].pause();for(e=0,s=this.effects.length;e<s;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let e,s;if(this.scopes)for(e=0,s=this.scopes.length;e<s;e++)this.scopes[e].resume();for(e=0,s=this.effects.length;e<s;e++)this.effects[e].resume()}}run(e){if(this._active){const s=_t;try{return _t=this,e()}finally{_t=s}}}on(){++this._on===1&&(this.prevScope=_t,_t=this)}off(){this._on>0&&--this._on===0&&(_t=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function no(t){return new io(t)}function oo(){return _t}function zr(t,e=!1){_t&&_t.cleanups.push(t)}let it;const Xs=new WeakSet;class ro{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,_t&&_t.active&&_t.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Xs.has(this)&&(Xs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||lo(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,ln(this),co(this);const e=it,s=Ut;it=this,Ut=!0;try{return this.fn()}finally{uo(this),it=e,Ut=s,this.flags&=-3}}stop(){if(this.flags&1){for(let e=this.deps;e;e=e.nextDep)ki(e);this.deps=this.depsTail=void 0,ln(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Xs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){fi(this)&&this.run()}get dirty(){return fi(this)}}let ao=0,Ge,Ke;function lo(t,e=!1){if(t.flags|=8,e){t.next=Ke,Ke=t;return}t.next=Ge,Ge=t}function Di(){ao++}function Li(){if(--ao>0)return;if(Ke){let e=Ke;for(Ke=void 0;e;){const s=e.next;e.next=void 0,e.flags&=-9,e=s}}let t;for(;Ge;){let e=Ge;for(Ge=void 0;e;){const s=e.next;if(e.next=void 0,e.flags&=-9,e.flags&1)try{e.trigger()}catch(i){t||(t=i)}e=s}}if(t)throw t}function co(t){for(let e=t.deps;e;e=e.nextDep)e.version=-1,e.prevActiveLink=e.dep.activeLink,e.dep.activeLink=e}function uo(t){let e,s=t.depsTail,i=s;for(;i;){const n=i.prevDep;i.version===-1?(i===s&&(s=n),ki(i),Vr(i)):e=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=n}t.deps=e,t.depsTail=s}function fi(t){for(let e=t.deps;e;e=e.nextDep)if(e.dep.version!==e.version||e.dep.computed&&(fo(e.dep.computed)||e.dep.version!==e.version))return!0;return!!t._dirty}function fo(t){if(t.flags&4&&!(t.flags&16)||(t.flags&=-17,t.globalVersion===Xe)||(t.globalVersion=Xe,!t.isSSR&&t.flags&128&&(!t.deps&&!t._dirty||!fi(t))))return;t.flags|=2;const e=t.dep,s=it,i=Ut;it=t,Ut=!0;try{co(t);const n=t.fn(t._value);(e.version===0||ae(n,t._value))&&(t.flags|=128,t._value=n,e.version++)}catch(n){throw e.version++,n}finally{it=s,Ut=i,uo(t),t.flags&=-3}}function ki(t,e=!1){const{dep:s,prevSub:i,nextSub:n}=t;if(i&&(i.nextSub=n,t.prevSub=void 0),n&&(n.prevSub=i,t.nextSub=void 0),s.subs===t&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let o=s.computed.deps;o;o=o.nextDep)ki(o,!0)}!e&&!--s.sc&&s.map&&s.map.delete(s.key)}function Vr(t){const{prevDep:e,nextDep:s}=t;e&&(e.nextDep=s,t.prevDep=void 0),s&&(s.prevDep=e,t.nextDep=void 0)}let Ut=!0;const ho=[];function Xt(){ho.push(Ut),Ut=!1}function te(){const t=ho.pop();Ut=t===void 0?!0:t}function ln(t){const{cleanup:e}=t;if(t.cleanup=void 0,e){const s=it;it=void 0;try{e()}finally{it=s}}}let Xe=0;class Gr{constructor(e,s){this.sub=e,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ui{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(e){if(!it||!Ut||it===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==it)s=this.activeLink=new Gr(it,this),it.deps?(s.prevDep=it.depsTail,it.depsTail.nextDep=s,it.depsTail=s):it.deps=it.depsTail=s,po(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=it.depsTail,s.nextDep=void 0,it.depsTail.nextDep=s,it.depsTail=s,it.deps===s&&(it.deps=i)}return s}trigger(e){this.version++,Xe++,this.notify(e)}notify(e){Di();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{Li()}}}function po(t){if(t.dep.sc++,t.sub.flags&4){const e=t.dep.computed;if(e&&!t.dep.subs){e.flags|=20;for(let i=e.deps;i;i=i.nextDep)po(i)}const s=t.dep.subs;s!==t&&(t.prevSub=s,s&&(s.nextSub=t)),t.dep.subs=t}}const Es=new WeakMap,we=Symbol(""),di=Symbol(""),ts=Symbol("");function Tt(t,e,s){if(Ut&&it){let i=Es.get(t);i||Es.set(t,i=new Map);let n=i.get(s);n||(i.set(s,n=new Ui),n.map=i,n.key=s),n.track()}}function Yt(t,e,s,i,n,o){const r=Es.get(t);if(!r){Xe++;return}const a=l=>{l&&l.trigger()};if(Di(),e==="clear")r.forEach(a);else{const l=M(t),h=l&&Mi(s);if(l&&s==="length"){const u=Number(i);r.forEach((p,y)=>{(y==="length"||y===ts||!$t(y)&&y>=u)&&a(p)})}else switch((s!==void 0||r.has(void 0))&&a(r.get(s)),h&&a(r.get(ts)),e){case"add":l?h&&a(r.get("length")):(a(r.get(we)),xe(t)&&a(r.get(di)));break;case"delete":l||(a(r.get(we)),xe(t)&&a(r.get(di)));break;case"set":xe(t)&&a(r.get(we));break}}Li()}function Kr(t,e){const s=Es.get(t);return s&&s.get(e)}function Ee(t){const e=G(t);return e===t?e:(Tt(e,"iterate",ts),Ot(t)?e:e.map(yt))}function Bs(t){return Tt(t=G(t),"iterate",ts),t}const Qr={__proto__:null,[Symbol.iterator](){return ti(this,Symbol.iterator,yt)},concat(...t){return Ee(this).concat(...t.map(e=>M(e)?Ee(e):e))},entries(){return ti(this,"entries",t=>(t[1]=yt(t[1]),t))},every(t,e){return Qt(this,"every",t,e,void 0,arguments)},filter(t,e){return Qt(this,"filter",t,e,s=>s.map(yt),arguments)},find(t,e){return Qt(this,"find",t,e,yt,arguments)},findIndex(t,e){return Qt(this,"findIndex",t,e,void 0,arguments)},findLast(t,e){return Qt(this,"findLast",t,e,yt,arguments)},findLastIndex(t,e){return Qt(this,"findLastIndex",t,e,void 0,arguments)},forEach(t,e){return Qt(this,"forEach",t,e,void 0,arguments)},includes(...t){return ei(this,"includes",t)},indexOf(...t){return ei(this,"indexOf",t)},join(t){return Ee(this).join(t)},lastIndexOf(...t){return ei(this,"lastIndexOf",t)},map(t,e){return Qt(this,"map",t,e,void 0,arguments)},pop(){return Be(this,"pop")},push(...t){return Be(this,"push",t)},reduce(t,...e){return cn(this,"reduce",t,e)},reduceRight(t,...e){return cn(this,"reduceRight",t,e)},shift(){return Be(this,"shift")},some(t,e){return Qt(this,"some",t,e,void 0,arguments)},splice(...t){return Be(this,"splice",t)},toReversed(){return Ee(this).toReversed()},toSorted(t){return Ee(this).toSorted(t)},toSpliced(...t){return Ee(this).toSpliced(...t)},unshift(...t){return Be(this,"unshift",t)},values(){return ti(this,"values",yt)}};function ti(t,e,s){const i=Bs(t),n=i[e]();return i!==t&&!Ot(t)&&(n._next=n.next,n.next=()=>{const o=n._next();return o.value&&(o.value=s(o.value)),o}),n}const qr=Array.prototype;function Qt(t,e,s,i,n,o){const r=Bs(t),a=r!==t&&!Ot(t),l=r[e];if(l!==qr[e]){const p=l.apply(t,o);return a?yt(p):p}let h=s;r!==t&&(a?h=function(p,y){return s.call(this,yt(p),y,t)}:s.length>2&&(h=function(p,y){return s.call(this,p,y,t)}));const u=l.call(r,h,i);return a&&n?n(u):u}function cn(t,e,s,i){const n=Bs(t);let o=s;return n!==t&&(Ot(t)?s.length>3&&(o=function(r,a,l){return s.call(this,r,a,l,t)}):o=function(r,a,l){return s.call(this,r,yt(a),l,t)}),n[e](o,...i)}function ei(t,e,s){const i=G(t);Tt(i,"iterate",ts);const n=i[e](...s);return(n===-1||n===!1)&&Fi(s[0])?(s[0]=G(s[0]),i[e](...s)):n}function Be(t,e,s=[]){Xt(),Di();const i=G(t)[e].apply(t,s);return Li(),te(),i}const Yr=Ii("__proto__,__v_isRef,__isVue"),go=new Set(Object.getOwnPropertyNames(Symbol).filter(t=>t!=="arguments"&&t!=="caller").map(t=>Symbol[t]).filter($t));function Jr(t){$t(t)||(t=String(t));const e=G(this);return Tt(e,"has",t),e.hasOwnProperty(t)}class mo{constructor(e=!1,s=!1){this._isReadonly=e,this._isShallow=s}get(e,s,i){if(s==="__v_skip")return e.__v_skip;const n=this._isReadonly,o=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return o;if(s==="__v_raw")return i===(n?o?aa:_o:o?So:yo).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(i)?e:void 0;const r=M(e);if(!n){let l;if(r&&(l=Qr[s]))return l;if(s==="hasOwnProperty")return Jr}const a=Reflect.get(e,s,at(e)?e:i);return($t(s)?go.has(s):Yr(s))||(n||Tt(e,"get",s),o)?a:at(a)?r&&Mi(s)?a:a.value:tt(a)?n?To(a):Fs(a):a}}class wo extends mo{constructor(e=!1){super(!1,e)}set(e,s,i,n){let o=e[s];if(!this._isShallow){const l=ce(o);if(!Ot(i)&&!ce(i)&&(o=G(o),i=G(i)),!M(e)&&at(o)&&!at(i))return l?!1:(o.value=i,!0)}const r=M(e)&&Mi(s)?Number(s)<e.length:q(e,s),a=Reflect.set(e,s,i,at(e)?e:n);return e===G(n)&&(r?ae(i,o)&&Yt(e,"set",s,i):Yt(e,"add",s,i)),a}deleteProperty(e,s){const i=q(e,s);e[s];const n=Reflect.deleteProperty(e,s);return n&&i&&Yt(e,"delete",s,void 0),n}has(e,s){const i=Reflect.has(e,s);return(!$t(s)||!go.has(s))&&Tt(e,"has",s),i}ownKeys(e){return Tt(e,"iterate",M(e)?"length":we),Reflect.ownKeys(e)}}class Zr extends mo{constructor(e=!1){super(!0,e)}set(e,s){return!0}deleteProperty(e,s){return!0}}const Xr=new wo,ta=new Zr,ea=new wo(!0);const hi=t=>t,ds=t=>Reflect.getPrototypeOf(t);function sa(t,e,s){return function(...i){const n=this.__v_raw,o=G(n),r=xe(o),a=t==="entries"||t===Symbol.iterator&&r,l=t==="keys"&&r,h=n[t](...i),u=s?hi:e?bs:yt;return!e&&Tt(o,"iterate",l?di:we),{next(){const{value:p,done:y}=h.next();return y?{value:p,done:y}:{value:a?[u(p[0]),u(p[1])]:u(p),done:y}},[Symbol.iterator](){return this}}}}function hs(t){return function(...e){return t==="delete"?!1:t==="clear"?void 0:this}}function ia(t,e){const s={get(n){const o=this.__v_raw,r=G(o),a=G(n);t||(ae(n,a)&&Tt(r,"get",n),Tt(r,"get",a));const{has:l}=ds(r),h=e?hi:t?bs:yt;if(l.call(r,n))return h(o.get(n));if(l.call(r,a))return h(o.get(a));o!==r&&o.get(n)},get size(){const n=this.__v_raw;return!t&&Tt(G(n),"iterate",we),Reflect.get(n,"size",n)},has(n){const o=this.__v_raw,r=G(o),a=G(n);return t||(ae(n,a)&&Tt(r,"has",n),Tt(r,"has",a)),n===a?o.has(n):o.has(n)||o.has(a)},forEach(n,o){const r=this,a=r.__v_raw,l=G(a),h=e?hi:t?bs:yt;return!t&&Tt(l,"iterate",we),a.forEach((u,p)=>n.call(o,h(u),h(p),r))}};return ut(s,t?{add:hs("add"),set:hs("set"),delete:hs("delete"),clear:hs("clear")}:{add(n){!e&&!Ot(n)&&!ce(n)&&(n=G(n));const o=G(this);return ds(o).has.call(o,n)||(o.add(n),Yt(o,"add",n,n)),this},set(n,o){!e&&!Ot(o)&&!ce(o)&&(o=G(o));const r=G(this),{has:a,get:l}=ds(r);let h=a.call(r,n);h||(n=G(n),h=a.call(r,n));const u=l.call(r,n);return r.set(n,o),h?ae(o,u)&&Yt(r,"set",n,o):Yt(r,"add",n,o),this},delete(n){const o=G(this),{has:r,get:a}=ds(o);let l=r.call(o,n);l||(n=G(n),l=r.call(o,n)),a&&a.call(o,n);const h=o.delete(n);return l&&Yt(o,"delete",n,void 0),h},clear(){const n=G(this),o=n.size!==0,r=n.clear();return o&&Yt(n,"clear",void 0,void 0),r}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=sa(n,t,e)}),s}function $i(t,e){const s=ia(t,e);return(i,n,o)=>n==="__v_isReactive"?!t:n==="__v_isReadonly"?t:n==="__v_raw"?i:Reflect.get(q(s,n)&&n in i?s:i,n,o)}const na={get:$i(!1,!1)},oa={get:$i(!1,!0)},ra={get:$i(!0,!1)};const yo=new WeakMap,So=new WeakMap,_o=new WeakMap,aa=new WeakMap;function la(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ca(t){return t.__v_skip||!Object.isExtensible(t)?0:la(Mr(t))}function Fs(t){return ce(t)?t:Bi(t,!1,Xr,na,yo)}function ua(t){return Bi(t,!1,ea,oa,So)}function To(t){return Bi(t,!0,ta,ra,_o)}function Bi(t,e,s,i,n){if(!tt(t)||t.__v_raw&&!(e&&t.__v_isReactive))return t;const o=ca(t);if(o===0)return t;const r=n.get(t);if(r)return r;const a=new Proxy(t,o===2?i:s);return n.set(t,a),a}function le(t){return ce(t)?le(t.__v_raw):!!(t&&t.__v_isReactive)}function ce(t){return!!(t&&t.__v_isReadonly)}function Ot(t){return!!(t&&t.__v_isShallow)}function Fi(t){return t?!!t.__v_raw:!1}function G(t){const e=t&&t.__v_raw;return e?G(e):t}function Wi(t){return!q(t,"__v_skip")&&Object.isExtensible(t)&&Xn(t,"__v_skip",!0),t}const yt=t=>tt(t)?Fs(t):t,bs=t=>tt(t)?To(t):t;function at(t){return t?t.__v_isRef===!0:!1}function Hi(t){return fa(t,!1)}function fa(t,e){return at(t)?t:new da(t,e)}class da{constructor(e,s){this.dep=new Ui,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?e:G(e),this._value=s?e:yt(e),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(e){const s=this._rawValue,i=this.__v_isShallow||Ot(e)||ce(e);e=i?e:G(e),ae(e,s)&&(this._rawValue=e,this._value=i?e:yt(e),this.dep.trigger())}}function ha(t){return at(t)?t.value:t}const pa={get:(t,e,s)=>e==="__v_raw"?t:ha(Reflect.get(t,e,s)),set:(t,e,s,i)=>{const n=t[e];return at(n)&&!at(s)?(n.value=s,!0):Reflect.set(t,e,s,i)}};function vo(t){return le(t)?t:new Proxy(t,pa)}function ga(t){const e=M(t)?new Array(t.length):{};for(const s in t)e[s]=wa(t,s);return e}class ma{constructor(e,s,i){this._object=e,this._key=s,this._defaultValue=i,this.__v_isRef=!0,this._value=void 0}get value(){const e=this._object[this._key];return this._value=e===void 0?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return Kr(G(this._object),this._key)}}function wa(t,e,s){const i=t[e];return at(i)?i:new ma(t,e,s)}class ya{constructor(e,s,i){this.fn=e,this.setter=s,this._value=void 0,this.dep=new Ui(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Xe-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&it!==this)return lo(this,!0),!0}get value(){const e=this.dep.track();return fo(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}function Sa(t,e,s=!1){let i,n;return B(t)?i=t:(i=t.get,n=t.set),new ya(i,n,s)}const ps={},Ns=new WeakMap;let ge;function _a(t,e=!1,s=ge){if(s){let i=Ns.get(s);i||Ns.set(s,i=[]),i.push(t)}}function Ta(t,e,s=X){const{immediate:i,deep:n,once:o,scheduler:r,augmentJob:a,call:l}=s,h=O=>n?O:Ot(O)||n===!1||n===0?Jt(O,1):Jt(O);let u,p,y,g,A=!1,$=!1;if(at(t)?(p=()=>t.value,A=Ot(t)):le(t)?(p=()=>h(t),A=!0):M(t)?($=!0,A=t.some(O=>le(O)||Ot(O)),p=()=>t.map(O=>{if(at(O))return O.value;if(le(O))return h(O);if(B(O))return l?l(O,2):O()})):B(t)?e?p=l?()=>l(t,2):t:p=()=>{if(y){Xt();try{y()}finally{te()}}const O=ge;ge=u;try{return l?l(t,3,[g]):t(g)}finally{ge=O}}:p=kt,e&&n){const O=p,H=n===!0?1/0:n;p=()=>Jt(O(),H)}const ot=oo(),z=()=>{u.stop(),ot&&ot.active&&xi(ot.effects,u)};if(o&&e){const O=e;e=(...H)=>{O(...H),z()}}let Y=$?new Array(t.length).fill(ps):ps;const K=O=>{if(!(!(u.flags&1)||!u.dirty&&!O))if(e){const H=u.run();if(n||A||($?H.some((ft,rt)=>ae(ft,Y[rt])):ae(H,Y))){y&&y();const ft=ge;ge=u;try{const rt=[H,Y===ps?void 0:$&&Y[0]===ps?[]:Y,g];Y=H,l?l(e,3,rt):e(...rt)}finally{ge=ft}}}else u.run()};return a&&a(K),u=new ro(p),u.scheduler=r?()=>r(K,!1):K,g=O=>_a(O,!1,u),y=u.onStop=()=>{const O=Ns.get(u);if(O){if(l)l(O,4);else for(const H of O)H();Ns.delete(u)}},e?i?K(!0):Y=u.run():r?r(K.bind(null,!0),!0):u.run(),z.pause=u.pause.bind(u),z.resume=u.resume.bind(u),z.stop=z,z}function Jt(t,e=1/0,s){if(e<=0||!tt(t)||t.__v_skip||(s=s||new Set,s.has(t)))return t;if(s.add(t),e--,at(t))Jt(t.value,e,s);else if(M(t))for(let i=0;i<t.length;i++)Jt(t[i],e,s);else if(qn(t)||xe(t))t.forEach(i=>{Jt(i,e,s)});else if(Zn(t)){for(const i in t)Jt(t[i],e,s);for(const i of Object.getOwnPropertySymbols(t))Object.prototype.propertyIsEnumerable.call(t,i)&&Jt(t[i],e,s)}return t}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function ls(t,e,s,i){try{return i?t(...i):t()}catch(n){Ws(n,e,s)}}function Bt(t,e,s,i){if(B(t)){const n=ls(t,e,s,i);return n&&Yn(n)&&n.catch(o=>{Ws(o,e,s)}),n}if(M(t)){const n=[];for(let o=0;o<t.length;o++)n.push(Bt(t[o],e,s,i));return n}}function Ws(t,e,s,i=!0){const n=e?e.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:r}=e&&e.appContext.config||X;if(e){let a=e.parent;const l=e.proxy,h=`https://vuejs.org/error-reference/#runtime-${s}`;for(;a;){const u=a.ec;if(u){for(let p=0;p<u.length;p++)if(u[p](t,l,h)===!1)return}a=a.parent}if(o){Xt(),ls(o,null,10,[t,l,h]),te();return}}va(t,s,n,i,r)}function va(t,e,s,i=!0,n=!1){if(n)throw t;console.error(t)}const Ct=[];let zt=-1;const Me=[];let oe=null,Re=0;const Co=Promise.resolve();let Ps=null;function Eo(t){const e=Ps||Co;return t?e.then(this?t.bind(this):t):e}function Ca(t){let e=zt+1,s=Ct.length;for(;e<s;){const i=e+s>>>1,n=Ct[i],o=es(n);o<t||o===t&&n.flags&2?e=i+1:s=i}return e}function ji(t){if(!(t.flags&1)){const e=es(t),s=Ct[Ct.length-1];!s||!(t.flags&2)&&e>=es(s)?Ct.push(t):Ct.splice(Ca(e),0,t),t.flags|=1,bo()}}function bo(){Ps||(Ps=Co.then(Po))}function Ea(t){M(t)?Me.push(...t):oe&&t.id===-1?oe.splice(Re+1,0,t):t.flags&1||(Me.push(t),t.flags|=1),bo()}function un(t,e,s=zt+1){for(;s<Ct.length;s++){const i=Ct[s];if(i&&i.flags&2){if(t&&i.id!==t.uid)continue;Ct.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function No(t){if(Me.length){const e=[...new Set(Me)].sort((s,i)=>es(s)-es(i));if(Me.length=0,oe){oe.push(...e);return}for(oe=e,Re=0;Re<oe.length;Re++){const s=oe[Re];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}oe=null,Re=0}}const es=t=>t.id==null?t.flags&2?-1:1/0:t.id;function Po(t){const e=kt;try{for(zt=0;zt<Ct.length;zt++){const s=Ct[zt];s&&!(s.flags&8)&&(s.flags&4&&(s.flags&=-2),ls(s,s.i,s.i?15:14),s.flags&4||(s.flags&=-2))}}finally{for(;zt<Ct.length;zt++){const s=Ct[zt];s&&(s.flags&=-2)}zt=-1,Ct.length=0,No(),Ps=null,(Ct.length||Me.length)&&Po()}}let ht=null,Ro=null;function Rs(t){const e=ht;return ht=t,Ro=t&&t.type.__scopeId||null,e}function Ao(t,e=ht,s){if(!e||t._n)return t;const i=(...n)=>{i._d&&Sn(-1);const o=Rs(e);let r;try{r=t(...n)}finally{Rs(o),i._d&&Sn(1)}return r};return i._n=!0,i._c=!0,i._d=!0,i}function be(t,e){if(ht===null)return t;const s=Vs(ht),i=t.dirs||(t.dirs=[]);for(let n=0;n<e.length;n++){let[o,r,a,l=X]=e[n];o&&(B(o)&&(o={mounted:o,updated:o}),o.deep&&Jt(r),i.push({dir:o,instance:s,value:r,oldValue:void 0,arg:a,modifiers:l}))}return t}function de(t,e,s,i){const n=t.dirs,o=e&&e.dirs;for(let r=0;r<n.length;r++){const a=n[r];o&&(a.oldValue=o[r].value);let l=a.dir[i];l&&(Xt(),Bt(l,s,8,[t.el,a,t,e]),te())}}const ba=Symbol("_vte"),Na=t=>t.__isTeleport,Ne=Symbol("_leaveCb"),gs=Symbol("_enterCb");function Pa(){const t={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Do(()=>{t.isMounted=!0}),ko(()=>{t.isUnmounting=!0}),t}const At=[Function,Array],Ra={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:At,onEnter:At,onAfterEnter:At,onEnterCancelled:At,onBeforeLeave:At,onLeave:At,onAfterLeave:At,onLeaveCancelled:At,onBeforeAppear:At,onAppear:At,onAfterAppear:At,onAppearCancelled:At};function Aa(t,e){const{leavingVNodes:s}=t;let i=s.get(e.type);return i||(i=Object.create(null),s.set(e.type,i)),i}function pi(t,e,s,i,n){const{appear:o,mode:r,persisted:a=!1,onBeforeEnter:l,onEnter:h,onAfterEnter:u,onEnterCancelled:p,onBeforeLeave:y,onLeave:g,onAfterLeave:A,onLeaveCancelled:$,onBeforeAppear:ot,onAppear:z,onAfterAppear:Y,onAppearCancelled:K}=e,O=String(t.key),H=Aa(s,t),ft=(N,F)=>{N&&Bt(N,i,9,F)},rt=(N,F)=>{const Q=F[1];ft(N,F),M(N)?N.every(P=>P.length<=1)&&Q():N.length<=1&&Q()},W={mode:r,persisted:a,beforeEnter(N){let F=l;if(!s.isMounted)if(o)F=ot||l;else return;N[Ne]&&N[Ne](!0);const Q=H[O];Q&&Ae(t,Q)&&Q.el[Ne]&&Q.el[Ne](),ft(F,[N])},enter(N){let F=h,Q=u,P=p;if(!s.isMounted)if(o)F=z||h,Q=Y||u,P=K||p;else return;let Z=!1;const pt=N[gs]=Mt=>{Z||(Z=!0,Mt?ft(P,[N]):ft(Q,[N]),W.delayedLeave&&W.delayedLeave(),N[gs]=void 0)};F?rt(F,[N,pt]):pt()},leave(N,F){const Q=String(t.key);if(N[gs]&&N[gs](!0),s.isUnmounting)return F();ft(y,[N]);let P=!1;const Z=N[Ne]=pt=>{P||(P=!0,F(),pt?ft($,[N]):ft(A,[N]),N[Ne]=void 0,H[Q]===t&&delete H[Q])};H[Q]=t,g?rt(g,[N,Z]):Z()},clone(N){const F=pi(N,e,s,i,n);return n&&n(F),F}};return W}function ss(t,e){t.shapeFlag&6&&t.component?(t.transition=e,ss(t.component.subTree,e)):t.shapeFlag&128?(t.ssContent.transition=e.clone(t.ssContent),t.ssFallback.transition=e.clone(t.ssFallback)):t.transition=e}function Io(t,e=!1,s){let i=[],n=0;for(let o=0;o<t.length;o++){let r=t[o];const a=s==null?r.key:String(s)+String(r.key!=null?r.key:o);r.type===dt?(r.patchFlag&128&&n++,i=i.concat(Io(r.children,e,a))):(e||r.type!==Kt)&&i.push(a!=null?Te(r,{key:a}):r)}if(n>1)for(let o=0;o<i.length;o++)i[o].patchFlag=-2;return i}function Oo(t){t.ids=[t.ids[0]+t.ids[2]+++"-",0,0]}function As(t,e,s,i,n=!1){if(M(t)){t.forEach((A,$)=>As(A,e&&(M(e)?e[$]:e),s,i,n));return}if(De(i)&&!n){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&As(t,e,s,i.component.subTree);return}const o=i.shapeFlag&4?Vs(i.component):i.el,r=n?null:o,{i:a,r:l}=t,h=e&&e.r,u=a.refs===X?a.refs={}:a.refs,p=a.setupState,y=G(p),g=p===X?()=>!1:A=>q(y,A);if(h!=null&&h!==l&&(lt(h)?(u[h]=null,g(h)&&(p[h]=null)):at(h)&&(h.value=null)),B(l))ls(l,a,12,[r,u]);else{const A=lt(l),$=at(l);if(A||$){const ot=()=>{if(t.f){const z=A?g(l)?p[l]:u[l]:l.value;n?M(z)&&xi(z,o):M(z)?z.includes(o)||z.push(o):A?(u[l]=[o],g(l)&&(p[l]=u[l])):(l.value=[o],t.k&&(u[t.k]=l.value))}else A?(u[l]=r,g(l)&&(p[l]=r)):$&&(l.value=r,t.k&&(u[t.k]=r))};r?(ot.id=-1,Pt(ot,s)):ot()}}}$s().requestIdleCallback;$s().cancelIdleCallback;const De=t=>!!t.type.__asyncLoader,xo=t=>t.type.__isKeepAlive;function Ia(t,e){Mo(t,"a",e)}function Oa(t,e){Mo(t,"da",e)}function Mo(t,e,s=mt){const i=t.__wdc||(t.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return t()});if(Hs(e,i,s),s){let n=s.parent;for(;n&&n.parent;)xo(n.parent.vnode)&&xa(i,e,s,n),n=n.parent}}function xa(t,e,s,i){const n=Hs(e,t,i,!0);Uo(()=>{xi(i[e],n)},s)}function Hs(t,e,s=mt,i=!1){if(s){const n=s[t]||(s[t]=[]),o=e.__weh||(e.__weh=(...r)=>{Xt();const a=cs(s),l=Bt(e,s,t,r);return a(),te(),l});return i?n.unshift(o):n.push(o),o}}const ee=t=>(e,s=mt)=>{(!os||t==="sp")&&Hs(t,(...i)=>e(...i),s)},Ma=ee("bm"),Do=ee("m"),Da=ee("bu"),Lo=ee("u"),ko=ee("bum"),Uo=ee("um"),La=ee("sp"),ka=ee("rtg"),Ua=ee("rtc");function $a(t,e=mt){Hs("ec",t,e)}const $o="components";function gi(t,e){return Fa($o,t,!0,e)||t}const Ba=Symbol.for("v-ndc");function Fa(t,e,s=!0,i=!1){const n=ht||mt;if(n){const o=n.type;if(t===$o){const a=Ol(o,!1);if(a&&(a===e||a===xt(e)||a===Us(xt(e))))return o}const r=fn(n[t]||o[t],e)||fn(n.appContext[t],e);return!r&&i?o:r}}function fn(t,e){return t&&(t[e]||t[xt(e)]||t[Us(xt(e))])}function Qe(t,e,s,i){let n;const o=s&&s[i],r=M(t);if(r||lt(t)){const a=r&&le(t);let l=!1,h=!1;a&&(l=!Ot(t),h=ce(t),t=Bs(t)),n=new Array(t.length);for(let u=0,p=t.length;u<p;u++)n[u]=e(l?h?bs(yt(t[u])):yt(t[u]):t[u],u,void 0,o&&o[u])}else if(typeof t=="number"){n=new Array(t);for(let a=0;a<t;a++)n[a]=e(a+1,a,void 0,o&&o[a])}else if(tt(t))if(t[Symbol.iterator])n=Array.from(t,(a,l)=>e(a,l,void 0,o&&o[l]));else{const a=Object.keys(t);n=new Array(a.length);for(let l=0,h=a.length;l<h;l++){const u=a[l];n[l]=e(t[u],u,l,o&&o[l])}}else n=[];return s&&(s[i]=n),n}function Wa(t,e,s={},i,n){if(ht.ce||ht.parent&&De(ht.parent)&&ht.parent.ce)return e!=="default"&&(s.name=e),D(),_i(dt,null,[ct("slot",s,i&&i())],64);let o=t[e];o&&o._c&&(o._d=!1),D();const r=o&&Bo(o(s)),a=s.key||r&&r.key,l=_i(dt,{key:(a&&!$t(a)?a:`_${e}`)+(!r&&i?"_fb":"")},r||(i?i():[]),r&&t._===1?64:-2);return!n&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),o&&o._c&&(o._d=!0),l}function Bo(t){return t.some(e=>ns(e)?!(e.type===Kt||e.type===dt&&!Bo(e.children)):!0)?t:null}const mi=t=>t?nr(t)?Vs(t):mi(t.parent):null,qe=ut(Object.create(null),{$:t=>t,$el:t=>t.vnode.el,$data:t=>t.data,$props:t=>t.props,$attrs:t=>t.attrs,$slots:t=>t.slots,$refs:t=>t.refs,$parent:t=>mi(t.parent),$root:t=>mi(t.root),$host:t=>t.ce,$emit:t=>t.emit,$options:t=>zi(t),$forceUpdate:t=>t.f||(t.f=()=>{ji(t.update)}),$nextTick:t=>t.n||(t.n=Eo.bind(t.proxy)),$watch:t=>ul.bind(t)}),si=(t,e)=>t!==X&&!t.__isScriptSetup&&q(t,e),Ha={get({_:t},e){if(e==="__v_skip")return!0;const{ctx:s,setupState:i,data:n,props:o,accessCache:r,type:a,appContext:l}=t;let h;if(e[0]!=="$"){const g=r[e];if(g!==void 0)switch(g){case 1:return i[e];case 2:return n[e];case 4:return s[e];case 3:return o[e]}else{if(si(i,e))return r[e]=1,i[e];if(n!==X&&q(n,e))return r[e]=2,n[e];if((h=t.propsOptions[0])&&q(h,e))return r[e]=3,o[e];if(s!==X&&q(s,e))return r[e]=4,s[e];wi&&(r[e]=0)}}const u=qe[e];let p,y;if(u)return e==="$attrs"&&Tt(t.attrs,"get",""),u(t);if((p=a.__cssModules)&&(p=p[e]))return p;if(s!==X&&q(s,e))return r[e]=4,s[e];if(y=l.config.globalProperties,q(y,e))return y[e]},set({_:t},e,s){const{data:i,setupState:n,ctx:o}=t;return si(n,e)?(n[e]=s,!0):i!==X&&q(i,e)?(i[e]=s,!0):q(t.props,e)||e[0]==="$"&&e.slice(1)in t?!1:(o[e]=s,!0)},has({_:{data:t,setupState:e,accessCache:s,ctx:i,appContext:n,propsOptions:o}},r){let a;return!!s[r]||t!==X&&q(t,r)||si(e,r)||(a=o[0])&&q(a,r)||q(i,r)||q(qe,r)||q(n.config.globalProperties,r)},defineProperty(t,e,s){return s.get!=null?t._.accessCache[e]=0:q(s,"value")&&this.set(t,e,s.value,null),Reflect.defineProperty(t,e,s)}};function dn(t){return M(t)?t.reduce((e,s)=>(e[s]=null,e),{}):t}let wi=!0;function ja(t){const e=zi(t),s=t.proxy,i=t.ctx;wi=!1,e.beforeCreate&&hn(e.beforeCreate,t,"bc");const{data:n,computed:o,methods:r,watch:a,provide:l,inject:h,created:u,beforeMount:p,mounted:y,beforeUpdate:g,updated:A,activated:$,deactivated:ot,beforeDestroy:z,beforeUnmount:Y,destroyed:K,unmounted:O,render:H,renderTracked:ft,renderTriggered:rt,errorCaptured:W,serverPrefetch:N,expose:F,inheritAttrs:Q,components:P,directives:Z,filters:pt}=e;if(h&&za(h,i,null),r)for(const j in r){const et=r[j];B(et)&&(i[j]=et.bind(s))}if(n){const j=n.call(s,s);tt(j)&&(t.data=Fs(j))}if(wi=!0,o)for(const j in o){const et=o[j],ue=B(et)?et.bind(s,s):B(et.get)?et.get.bind(s,s):kt,us=!B(et)&&B(et.set)?et.set.bind(s):kt,fe=rr({get:ue,set:us});Object.defineProperty(i,j,{enumerable:!0,configurable:!0,get:()=>fe.value,set:Ft=>fe.value=Ft})}if(a)for(const j in a)Fo(a[j],i,s,j);if(l){const j=B(l)?l.call(s):l;Reflect.ownKeys(j).forEach(et=>{Ya(et,j[et])})}u&&hn(u,t,"c");function nt(j,et){M(et)?et.forEach(ue=>j(ue.bind(s))):et&&j(et.bind(s))}if(nt(Ma,p),nt(Do,y),nt(Da,g),nt(Lo,A),nt(Ia,$),nt(Oa,ot),nt($a,W),nt(Ua,ft),nt(ka,rt),nt(ko,Y),nt(Uo,O),nt(La,N),M(F))if(F.length){const j=t.exposed||(t.exposed={});F.forEach(et=>{Object.defineProperty(j,et,{get:()=>s[et],set:ue=>s[et]=ue})})}else t.exposed||(t.exposed={});H&&t.render===kt&&(t.render=H),Q!=null&&(t.inheritAttrs=Q),P&&(t.components=P),Z&&(t.directives=Z),N&&Oo(t)}function za(t,e,s=kt){M(t)&&(t=yi(t));for(const i in t){const n=t[i];let o;tt(n)?"default"in n?o=Ye(n.from||i,n.default,!0):o=Ye(n.from||i):o=Ye(n),at(o)?Object.defineProperty(e,i,{enumerable:!0,configurable:!0,get:()=>o.value,set:r=>o.value=r}):e[i]=o}}function hn(t,e,s){Bt(M(t)?t.map(i=>i.bind(e.proxy)):t.bind(e.proxy),e,s)}function Fo(t,e,s,i){let n=i.includes(".")?Xo(s,i):()=>s[i];if(lt(t)){const o=e[t];B(o)&&ys(n,o)}else if(B(t))ys(n,t.bind(s));else if(tt(t))if(M(t))t.forEach(o=>Fo(o,e,s,i));else{const o=B(t.handler)?t.handler.bind(s):e[t.handler];B(o)&&ys(n,o,t)}}function zi(t){const e=t.type,{mixins:s,extends:i}=e,{mixins:n,optionsCache:o,config:{optionMergeStrategies:r}}=t.appContext,a=o.get(e);let l;return a?l=a:!n.length&&!s&&!i?l=e:(l={},n.length&&n.forEach(h=>Is(l,h,r,!0)),Is(l,e,r)),tt(e)&&o.set(e,l),l}function Is(t,e,s,i=!1){const{mixins:n,extends:o}=e;o&&Is(t,o,s,!0),n&&n.forEach(r=>Is(t,r,s,!0));for(const r in e)if(!(i&&r==="expose")){const a=Va[r]||s&&s[r];t[r]=a?a(t[r],e[r]):e[r]}return t}const Va={data:pn,props:gn,emits:gn,methods:je,computed:je,beforeCreate:vt,created:vt,beforeMount:vt,mounted:vt,beforeUpdate:vt,updated:vt,beforeDestroy:vt,beforeUnmount:vt,destroyed:vt,unmounted:vt,activated:vt,deactivated:vt,errorCaptured:vt,serverPrefetch:vt,components:je,directives:je,watch:Ka,provide:pn,inject:Ga};function pn(t,e){return e?t?function(){return ut(B(t)?t.call(this,this):t,B(e)?e.call(this,this):e)}:e:t}function Ga(t,e){return je(yi(t),yi(e))}function yi(t){if(M(t)){const e={};for(let s=0;s<t.length;s++)e[t[s]]=t[s];return e}return t}function vt(t,e){return t?[...new Set([].concat(t,e))]:e}function je(t,e){return t?ut(Object.create(null),t,e):e}function gn(t,e){return t?M(t)&&M(e)?[...new Set([...t,...e])]:ut(Object.create(null),dn(t),dn(e!=null?e:{})):e}function Ka(t,e){if(!t)return e;if(!e)return t;const s=ut(Object.create(null),t);for(const i in e)s[i]=vt(t[i],e[i]);return s}function Wo(){return{app:null,config:{isNativeTag:Or,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Qa=0;function qa(t,e){return function(i,n=null){B(i)||(i=ut({},i)),n!=null&&!tt(n)&&(n=null);const o=Wo(),r=new WeakSet,a=[];let l=!1;const h=o.app={_uid:Qa++,_component:i,_props:n,_container:null,_context:o,_instance:null,version:Ml,get config(){return o.config},set config(u){},use(u,...p){return r.has(u)||(u&&B(u.install)?(r.add(u),u.install(h,...p)):B(u)&&(r.add(u),u(h,...p))),h},mixin(u){return o.mixins.includes(u)||o.mixins.push(u),h},component(u,p){return p?(o.components[u]=p,h):o.components[u]},directive(u,p){return p?(o.directives[u]=p,h):o.directives[u]},mount(u,p,y){if(!l){const g=h._ceVNode||ct(i,n);return g.appContext=o,y===!0?y="svg":y===!1&&(y=void 0),p&&e?e(g,u):t(g,u,y),l=!0,h._container=u,u.__vue_app__=h,Vs(g.component)}},onUnmount(u){a.push(u)},unmount(){l&&(Bt(a,h._instance,16),t(null,h._container),delete h._container.__vue_app__)},provide(u,p){return o.provides[u]=p,h},runWithContext(u){const p=ye;ye=h;try{return u()}finally{ye=p}}};return h}}let ye=null;function Ya(t,e){if(mt){let s=mt.provides;const i=mt.parent&&mt.parent.provides;i===s&&(s=mt.provides=Object.create(i)),s[t]=e}}function Ye(t,e,s=!1){const i=mt||ht;if(i||ye){let n=ye?ye._context.provides:i?i.parent==null||i.ce?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(n&&t in n)return n[t];if(arguments.length>1)return s&&B(e)?e.call(i&&i.proxy):e}}function Ja(){return!!(mt||ht||ye)}const Ho={},jo=()=>Object.create(Ho),zo=t=>Object.getPrototypeOf(t)===Ho;function Za(t,e,s,i=!1){const n={},o=jo();t.propsDefaults=Object.create(null),Vo(t,e,n,o);for(const r in t.propsOptions[0])r in n||(n[r]=void 0);s?t.props=i?n:ua(n):t.type.props?t.props=n:t.props=o,t.attrs=o}function Xa(t,e,s,i){const{props:n,attrs:o,vnode:{patchFlag:r}}=t,a=G(n),[l]=t.propsOptions;let h=!1;if((i||r>0)&&!(r&16)){if(r&8){const u=t.vnode.dynamicProps;for(let p=0;p<u.length;p++){let y=u[p];if(js(t.emitsOptions,y))continue;const g=e[y];if(l)if(q(o,y))g!==o[y]&&(o[y]=g,h=!0);else{const A=xt(y);n[A]=Si(l,a,A,g,t,!1)}else g!==o[y]&&(o[y]=g,h=!0)}}}else{Vo(t,e,n,o)&&(h=!0);let u;for(const p in a)(!e||!q(e,p)&&((u=ve(p))===p||!q(e,u)))&&(l?s&&(s[p]!==void 0||s[u]!==void 0)&&(n[p]=Si(l,a,p,void 0,t,!0)):delete n[p]);if(o!==a)for(const p in o)(!e||!q(e,p)&&!0)&&(delete o[p],h=!0)}h&&Yt(t.attrs,"set","")}function Vo(t,e,s,i){const[n,o]=t.propsOptions;let r=!1,a;if(e)for(let l in e){if(Ve(l))continue;const h=e[l];let u;n&&q(n,u=xt(l))?!o||!o.includes(u)?s[u]=h:(a||(a={}))[u]=h:js(t.emitsOptions,l)||(!(l in i)||h!==i[l])&&(i[l]=h,r=!0)}if(o){const l=G(s),h=a||X;for(let u=0;u<o.length;u++){const p=o[u];s[p]=Si(n,l,p,h[p],t,!q(h,p))}}return r}function Si(t,e,s,i,n,o){const r=t[s];if(r!=null){const a=q(r,"default");if(a&&i===void 0){const l=r.default;if(r.type!==Function&&!r.skipFactory&&B(l)){const{propsDefaults:h}=n;if(s in h)i=h[s];else{const u=cs(n);i=h[s]=l.call(null,e),u()}}else i=l;n.ce&&n.ce._setProp(s,i)}r[0]&&(o&&!a?i=!1:r[1]&&(i===""||i===ve(s))&&(i=!0))}return i}const tl=new WeakMap;function Go(t,e,s=!1){const i=s?tl:e.propsCache,n=i.get(t);if(n)return n;const o=t.props,r={},a=[];let l=!1;if(!B(t)){const u=p=>{l=!0;const[y,g]=Go(p,e,!0);ut(r,y),g&&a.push(...g)};!s&&e.mixins.length&&e.mixins.forEach(u),t.extends&&u(t.extends),t.mixins&&t.mixins.forEach(u)}if(!o&&!l)return tt(t)&&i.set(t,Oe),Oe;if(M(o))for(let u=0;u<o.length;u++){const p=xt(o[u]);mn(p)&&(r[p]=X)}else if(o)for(const u in o){const p=xt(u);if(mn(p)){const y=o[u],g=r[p]=M(y)||B(y)?{type:y}:ut({},y),A=g.type;let $=!1,ot=!0;if(M(A))for(let z=0;z<A.length;++z){const Y=A[z],K=B(Y)&&Y.name;if(K==="Boolean"){$=!0;break}else K==="String"&&(ot=!1)}else $=B(A)&&A.name==="Boolean";g[0]=$,g[1]=ot,($||q(g,"default"))&&a.push(p)}}const h=[r,a];return tt(t)&&i.set(t,h),h}function mn(t){return t[0]!=="$"&&!Ve(t)}const Vi=t=>t[0]==="_"||t==="$stable",Gi=t=>M(t)?t.map(Vt):[Vt(t)],el=(t,e,s)=>{if(e._n)return e;const i=Ao((...n)=>Gi(e(...n)),s);return i._c=!1,i},Ko=(t,e,s)=>{const i=t._ctx;for(const n in t){if(Vi(n))continue;const o=t[n];if(B(o))e[n]=el(n,o,i);else if(o!=null){const r=Gi(o);e[n]=()=>r}}},Qo=(t,e)=>{const s=Gi(e);t.slots.default=()=>s},qo=(t,e,s)=>{for(const i in e)(s||!Vi(i))&&(t[i]=e[i])},sl=(t,e,s)=>{const i=t.slots=jo();if(t.vnode.shapeFlag&32){const n=e._;n?(qo(i,e,s),s&&Xn(i,"_",n,!0)):Ko(e,i)}else e&&Qo(t,e)},il=(t,e,s)=>{const{vnode:i,slots:n}=t;let o=!0,r=X;if(i.shapeFlag&32){const a=e._;a?s&&a===1?o=!1:qo(n,e,s):(o=!e.$stable,Ko(e,n)),r=e}else e&&(Qo(t,e),r={default:1});if(o)for(const a in n)!Vi(a)&&r[a]==null&&delete n[a]},Pt=wl;function nl(t){return ol(t)}function ol(t,e){const s=$s();s.__VUE__=!0;const{insert:i,remove:n,patchProp:o,createElement:r,createText:a,createComment:l,setText:h,setElementText:u,parentNode:p,nextSibling:y,setScopeId:g=kt,insertStaticContent:A}=t,$=(c,f,m,_=null,w=null,S=null,E=void 0,C=null,v=!!f.dynamicChildren)=>{if(c===f)return;c&&!Ae(c,f)&&(_=fs(c),Ft(c,w,S,!0),c=null),f.patchFlag===-2&&(v=!1,f.dynamicChildren=null);const{type:T,ref:x,shapeFlag:b}=f;switch(T){case zs:ot(c,f,m,_);break;case Kt:z(c,f,m,_);break;case Ss:c==null&&Y(f,m,_,E);break;case dt:P(c,f,m,_,w,S,E,C,v);break;default:b&1?H(c,f,m,_,w,S,E,C,v):b&6?Z(c,f,m,_,w,S,E,C,v):(b&64||b&128)&&T.process(c,f,m,_,w,S,E,C,v,Ce)}x!=null&&w&&As(x,c&&c.ref,S,f||c,!f)},ot=(c,f,m,_)=>{if(c==null)i(f.el=a(f.children),m,_);else{const w=f.el=c.el;f.children!==c.children&&h(w,f.children)}},z=(c,f,m,_)=>{c==null?i(f.el=l(f.children||""),m,_):f.el=c.el},Y=(c,f,m,_)=>{[c.el,c.anchor]=A(c.children,f,m,_,c.el,c.anchor)},K=({el:c,anchor:f},m,_)=>{let w;for(;c&&c!==f;)w=y(c),i(c,m,_),c=w;i(f,m,_)},O=({el:c,anchor:f})=>{let m;for(;c&&c!==f;)m=y(c),n(c),c=m;n(f)},H=(c,f,m,_,w,S,E,C,v)=>{f.type==="svg"?E="svg":f.type==="math"&&(E="mathml"),c==null?ft(f,m,_,w,S,E,C,v):N(c,f,w,S,E,C,v)},ft=(c,f,m,_,w,S,E,C)=>{let v,T;const{props:x,shapeFlag:b,transition:I,dirs:k}=c;if(v=c.el=r(c.type,S,x&&x.is,x),b&8?u(v,c.children):b&16&&W(c.children,v,null,_,w,ii(c,S),E,C),k&&de(c,null,_,"created"),rt(v,c,c.scopeId,E,_),x){for(const st in x)st!=="value"&&!Ve(st)&&o(v,st,null,x[st],S,_);"value"in x&&o(v,"value",null,x.value,S),(T=x.onVnodeBeforeMount)&&Ht(T,_,c)}k&&de(c,null,_,"beforeMount");const V=rl(w,I);V&&I.beforeEnter(v),i(v,f,m),((T=x&&x.onVnodeMounted)||V||k)&&Pt(()=>{T&&Ht(T,_,c),V&&I.enter(v),k&&de(c,null,_,"mounted")},w)},rt=(c,f,m,_,w)=>{if(m&&g(c,m),_)for(let S=0;S<_.length;S++)g(c,_[S]);if(w){let S=w.subTree;if(f===S||er(S.type)&&(S.ssContent===f||S.ssFallback===f)){const E=w.vnode;rt(c,E,E.scopeId,E.slotScopeIds,w.parent)}}},W=(c,f,m,_,w,S,E,C,v=0)=>{for(let T=v;T<c.length;T++){const x=c[T]=C?re(c[T]):Vt(c[T]);$(null,x,f,m,_,w,S,E,C)}},N=(c,f,m,_,w,S,E)=>{const C=f.el=c.el;let{patchFlag:v,dynamicChildren:T,dirs:x}=f;v|=c.patchFlag&16;const b=c.props||X,I=f.props||X;let k;if(m&&he(m,!1),(k=I.onVnodeBeforeUpdate)&&Ht(k,m,f,c),x&&de(f,c,m,"beforeUpdate"),m&&he(m,!0),(b.innerHTML&&I.innerHTML==null||b.textContent&&I.textContent==null)&&u(C,""),T?F(c.dynamicChildren,T,C,m,_,ii(f,w),S):E||et(c,f,C,null,m,_,ii(f,w),S,!1),v>0){if(v&16)Q(C,b,I,m,w);else if(v&2&&b.class!==I.class&&o(C,"class",null,I.class,w),v&4&&o(C,"style",b.style,I.style,w),v&8){const V=f.dynamicProps;for(let st=0;st<V.length;st++){const J=V[st],Et=b[J],St=I[J];(St!==Et||J==="value")&&o(C,J,Et,St,w,m)}}v&1&&c.children!==f.children&&u(C,f.children)}else!E&&T==null&&Q(C,b,I,m,w);((k=I.onVnodeUpdated)||x)&&Pt(()=>{k&&Ht(k,m,f,c),x&&de(f,c,m,"updated")},_)},F=(c,f,m,_,w,S,E)=>{for(let C=0;C<f.length;C++){const v=c[C],T=f[C],x=v.el&&(v.type===dt||!Ae(v,T)||v.shapeFlag&198)?p(v.el):m;$(v,T,x,null,_,w,S,E,!0)}},Q=(c,f,m,_,w)=>{if(f!==m){if(f!==X)for(const S in f)!Ve(S)&&!(S in m)&&o(c,S,f[S],null,w,_);for(const S in m){if(Ve(S))continue;const E=m[S],C=f[S];E!==C&&S!=="value"&&o(c,S,C,E,w,_)}"value"in m&&o(c,"value",f.value,m.value,w)}},P=(c,f,m,_,w,S,E,C,v)=>{const T=f.el=c?c.el:a(""),x=f.anchor=c?c.anchor:a("");let{patchFlag:b,dynamicChildren:I,slotScopeIds:k}=f;k&&(C=C?C.concat(k):k),c==null?(i(T,m,_),i(x,m,_),W(f.children||[],m,x,w,S,E,C,v)):b>0&&b&64&&I&&c.dynamicChildren?(F(c.dynamicChildren,I,m,w,S,E,C),(f.key!=null||w&&f===w.subTree)&&Yo(c,f,!0)):et(c,f,m,x,w,S,E,C,v)},Z=(c,f,m,_,w,S,E,C,v)=>{f.slotScopeIds=C,c==null?f.shapeFlag&512?w.ctx.activate(f,m,_,E,v):pt(f,m,_,w,S,E,v):Mt(c,f,v)},pt=(c,f,m,_,w,S,E)=>{const C=c.component=bl(c,_,w);if(xo(c)&&(C.ctx.renderer=Ce),Pl(C,!1,E),C.asyncDep){if(w&&w.registerDep(C,nt,E),!c.el){const v=C.subTree=ct(Kt);z(null,v,f,m)}}else nt(C,c,f,m,w,S,E)},Mt=(c,f,m)=>{const _=f.component=c.component;if(gl(c,f,m))if(_.asyncDep&&!_.asyncResolved){j(_,f,m);return}else _.next=f,_.update();else f.el=c.el,_.vnode=f},nt=(c,f,m,_,w,S,E)=>{const C=()=>{if(c.isMounted){let{next:b,bu:I,u:k,parent:V,vnode:st}=c;{const bt=Jo(c);if(bt){b&&(b.el=st.el,j(c,b,E)),bt.asyncDep.then(()=>{c.isUnmounted||C()});return}}let J=b,Et;he(c,!1),b?(b.el=st.el,j(c,b,E)):b=st,I&&ws(I),(Et=b.props&&b.props.onVnodeBeforeUpdate)&&Ht(Et,V,b,st),he(c,!0);const St=ni(c),Dt=c.subTree;c.subTree=St,$(Dt,St,p(Dt.el),fs(Dt),c,w,S),b.el=St.el,J===null&&ml(c,St.el),k&&Pt(k,w),(Et=b.props&&b.props.onVnodeUpdated)&&Pt(()=>Ht(Et,V,b,st),w)}else{let b;const{el:I,props:k}=f,{bm:V,m:st,parent:J,root:Et,type:St}=c,Dt=De(f);if(he(c,!1),V&&ws(V),!Dt&&(b=k&&k.onVnodeBeforeMount)&&Ht(b,J,f),he(c,!0),I&&Ys){const bt=()=>{c.subTree=ni(c),Ys(I,c.subTree,c,w,null)};Dt&&St.__asyncHydrate?St.__asyncHydrate(I,c,bt):bt()}else{Et.ce&&Et.ce._injectChildStyle(St);const bt=c.subTree=ni(c);$(null,bt,m,_,c,w,S),f.el=bt.el}if(st&&Pt(st,w),!Dt&&(b=k&&k.onVnodeMounted)){const bt=f;Pt(()=>Ht(b,J,bt),w)}(f.shapeFlag&256||J&&De(J.vnode)&&J.vnode.shapeFlag&256)&&c.a&&Pt(c.a,w),c.isMounted=!0,f=m=_=null}};c.scope.on();const v=c.effect=new ro(C);c.scope.off();const T=c.update=v.run.bind(v),x=c.job=v.runIfDirty.bind(v);x.i=c,x.id=c.uid,v.scheduler=()=>ji(x),he(c,!0),T()},j=(c,f,m)=>{f.component=c;const _=c.vnode.props;c.vnode=f,c.next=null,Xa(c,f.props,_,m),il(c,f.children,m),Xt(),un(c),te()},et=(c,f,m,_,w,S,E,C,v=!1)=>{const T=c&&c.children,x=c?c.shapeFlag:0,b=f.children,{patchFlag:I,shapeFlag:k}=f;if(I>0){if(I&128){us(T,b,m,_,w,S,E,C,v);return}else if(I&256){ue(T,b,m,_,w,S,E,C,v);return}}k&8?(x&16&&Ue(T,w,S),b!==T&&u(m,b)):x&16?k&16?us(T,b,m,_,w,S,E,C,v):Ue(T,w,S,!0):(x&8&&u(m,""),k&16&&W(b,m,_,w,S,E,C,v))},ue=(c,f,m,_,w,S,E,C,v)=>{c=c||Oe,f=f||Oe;const T=c.length,x=f.length,b=Math.min(T,x);let I;for(I=0;I<b;I++){const k=f[I]=v?re(f[I]):Vt(f[I]);$(c[I],k,m,null,w,S,E,C,v)}T>x?Ue(c,w,S,!0,!1,b):W(f,m,_,w,S,E,C,v,b)},us=(c,f,m,_,w,S,E,C,v)=>{let T=0;const x=f.length;let b=c.length-1,I=x-1;for(;T<=b&&T<=I;){const k=c[T],V=f[T]=v?re(f[T]):Vt(f[T]);if(Ae(k,V))$(k,V,m,null,w,S,E,C,v);else break;T++}for(;T<=b&&T<=I;){const k=c[b],V=f[I]=v?re(f[I]):Vt(f[I]);if(Ae(k,V))$(k,V,m,null,w,S,E,C,v);else break;b--,I--}if(T>b){if(T<=I){const k=I+1,V=k<x?f[k].el:_;for(;T<=I;)$(null,f[T]=v?re(f[T]):Vt(f[T]),m,V,w,S,E,C,v),T++}}else if(T>I)for(;T<=b;)Ft(c[T],w,S,!0),T++;else{const k=T,V=T,st=new Map;for(T=V;T<=I;T++){const Nt=f[T]=v?re(f[T]):Vt(f[T]);Nt.key!=null&&st.set(Nt.key,T)}let J,Et=0;const St=I-V+1;let Dt=!1,bt=0;const $e=new Array(St);for(T=0;T<St;T++)$e[T]=0;for(T=k;T<=b;T++){const Nt=c[T];if(Et>=St){Ft(Nt,w,S,!0);continue}let Wt;if(Nt.key!=null)Wt=st.get(Nt.key);else for(J=V;J<=I;J++)if($e[J-V]===0&&Ae(Nt,f[J])){Wt=J;break}Wt===void 0?Ft(Nt,w,S,!0):($e[Wt-V]=T+1,Wt>=bt?bt=Wt:Dt=!0,$(Nt,f[Wt],m,null,w,S,E,C,v),Et++)}const nn=Dt?al($e):Oe;for(J=nn.length-1,T=St-1;T>=0;T--){const Nt=V+T,Wt=f[Nt],on=Nt+1<x?f[Nt+1].el:_;$e[T]===0?$(null,Wt,m,on,w,S,E,C,v):Dt&&(J<0||T!==nn[J]?fe(Wt,m,on,2):J--)}}},fe=(c,f,m,_,w=null)=>{const{el:S,type:E,transition:C,children:v,shapeFlag:T}=c;if(T&6){fe(c.component.subTree,f,m,_);return}if(T&128){c.suspense.move(f,m,_);return}if(T&64){E.move(c,f,m,Ce);return}if(E===dt){i(S,f,m);for(let b=0;b<v.length;b++)fe(v[b],f,m,_);i(c.anchor,f,m);return}if(E===Ss){K(c,f,m);return}if(_!==2&&T&1&&C)if(_===0)C.beforeEnter(S),i(S,f,m),Pt(()=>C.enter(S),w);else{const{leave:b,delayLeave:I,afterLeave:k}=C,V=()=>{c.ctx.isUnmounted?n(S):i(S,f,m)},st=()=>{b(S,()=>{V(),k&&k()})};I?I(S,V,st):st()}else i(S,f,m)},Ft=(c,f,m,_=!1,w=!1)=>{const{type:S,props:E,ref:C,children:v,dynamicChildren:T,shapeFlag:x,patchFlag:b,dirs:I,cacheIndex:k}=c;if(b===-2&&(w=!1),C!=null&&(Xt(),As(C,null,m,c,!0),te()),k!=null&&(f.renderCache[k]=void 0),x&256){f.ctx.deactivate(c);return}const V=x&1&&I,st=!De(c);let J;if(st&&(J=E&&E.onVnodeBeforeUnmount)&&Ht(J,f,c),x&6)Ir(c.component,m,_);else{if(x&128){c.suspense.unmount(m,_);return}V&&de(c,null,f,"beforeUnmount"),x&64?c.type.remove(c,f,m,Ce,_):T&&!T.hasOnce&&(S!==dt||b>0&&b&64)?Ue(T,f,m,!1,!0):(S===dt&&b&384||!w&&x&16)&&Ue(v,f,m),_&&en(c)}(st&&(J=E&&E.onVnodeUnmounted)||V)&&Pt(()=>{J&&Ht(J,f,c),V&&de(c,null,f,"unmounted")},m)},en=c=>{const{type:f,el:m,anchor:_,transition:w}=c;if(f===dt){Ar(m,_);return}if(f===Ss){O(c);return}const S=()=>{n(m),w&&!w.persisted&&w.afterLeave&&w.afterLeave()};if(c.shapeFlag&1&&w&&!w.persisted){const{leave:E,delayLeave:C}=w,v=()=>E(m,S);C?C(c.el,S,v):v()}else S()},Ar=(c,f)=>{let m;for(;c!==f;)m=y(c),n(c),c=m;n(f)},Ir=(c,f,m)=>{const{bum:_,scope:w,job:S,subTree:E,um:C,m:v,a:T,parent:x,slots:{__:b}}=c;wn(v),wn(T),_&&ws(_),x&&M(b)&&b.forEach(I=>{x.renderCache[I]=void 0}),w.stop(),S&&(S.flags|=8,Ft(E,c,f,m)),C&&Pt(C,f),Pt(()=>{c.isUnmounted=!0},f),f&&f.pendingBranch&&!f.isUnmounted&&c.asyncDep&&!c.asyncResolved&&c.suspenseId===f.pendingId&&(f.deps--,f.deps===0&&f.resolve())},Ue=(c,f,m,_=!1,w=!1,S=0)=>{for(let E=S;E<c.length;E++)Ft(c[E],f,m,_,w)},fs=c=>{if(c.shapeFlag&6)return fs(c.component.subTree);if(c.shapeFlag&128)return c.suspense.next();const f=y(c.anchor||c.el),m=f&&f[ba];return m?y(m):f};let Qs=!1;const sn=(c,f,m)=>{c==null?f._vnode&&Ft(f._vnode,null,null,!0):$(f._vnode||null,c,f,null,null,null,m),f._vnode=c,Qs||(Qs=!0,un(),No(),Qs=!1)},Ce={p:$,um:Ft,m:fe,r:en,mt:pt,mc:W,pc:et,pbc:F,n:fs,o:t};let qs,Ys;return e&&([qs,Ys]=e(Ce)),{render:sn,hydrate:qs,createApp:qa(sn,qs)}}function ii({type:t,props:e},s){return s==="svg"&&t==="foreignObject"||s==="mathml"&&t==="annotation-xml"&&e&&e.encoding&&e.encoding.includes("html")?void 0:s}function he({effect:t,job:e},s){s?(t.flags|=32,e.flags|=4):(t.flags&=-33,e.flags&=-5)}function rl(t,e){return(!t||t&&!t.pendingBranch)&&e&&!e.persisted}function Yo(t,e,s=!1){const i=t.children,n=e.children;if(M(i)&&M(n))for(let o=0;o<i.length;o++){const r=i[o];let a=n[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=n[o]=re(n[o]),a.el=r.el),!s&&a.patchFlag!==-2&&Yo(r,a)),a.type===zs&&(a.el=r.el),a.type===Kt&&!a.el&&(a.el=r.el)}}function al(t){const e=t.slice(),s=[0];let i,n,o,r,a;const l=t.length;for(i=0;i<l;i++){const h=t[i];if(h!==0){if(n=s[s.length-1],t[n]<h){e[i]=n,s.push(i);continue}for(o=0,r=s.length-1;o<r;)a=o+r>>1,t[s[a]]<h?o=a+1:r=a;h<t[s[o]]&&(o>0&&(e[i]=s[o-1]),s[o]=i)}}for(o=s.length,r=s[o-1];o-- >0;)s[o]=r,r=e[r];return s}function Jo(t){const e=t.subTree.component;if(e)return e.asyncDep&&!e.asyncResolved?e:Jo(e)}function wn(t){if(t)for(let e=0;e<t.length;e++)t[e].flags|=8}const ll=Symbol.for("v-scx"),cl=()=>Ye(ll);function ys(t,e,s){return Zo(t,e,s)}function Zo(t,e,s=X){const{immediate:i,deep:n,flush:o,once:r}=s,a=ut({},s),l=e&&i||!e&&o!=="post";let h;if(os){if(o==="sync"){const g=cl();h=g.__watcherHandles||(g.__watcherHandles=[])}else if(!l){const g=()=>{};return g.stop=kt,g.resume=kt,g.pause=kt,g}}const u=mt;a.call=(g,A,$)=>Bt(g,u,A,$);let p=!1;o==="post"?a.scheduler=g=>{Pt(g,u&&u.suspense)}:o!=="sync"&&(p=!0,a.scheduler=(g,A)=>{A?g():ji(g)}),a.augmentJob=g=>{e&&(g.flags|=4),p&&(g.flags|=2,u&&(g.id=u.uid,g.i=u))};const y=Ta(t,e,a);return os&&(h?h.push(y):l&&y()),y}function ul(t,e,s){const i=this.proxy,n=lt(t)?t.includes(".")?Xo(i,t):()=>i[t]:t.bind(i,i);let o;B(e)?o=e:(o=e.handler,s=e);const r=cs(this),a=Zo(n,o.bind(i),s);return r(),a}function Xo(t,e){const s=e.split(".");return()=>{let i=t;for(let n=0;n<s.length&&i;n++)i=i[s[n]];return i}}const fl=(t,e)=>e==="modelValue"||e==="model-value"?t.modelModifiers:t[`${e}Modifiers`]||t[`${xt(e)}Modifiers`]||t[`${ve(e)}Modifiers`];function dl(t,e,...s){if(t.isUnmounted)return;const i=t.vnode.props||X;let n=s;const o=e.startsWith("update:"),r=o&&fl(i,e.slice(7));r&&(r.trim&&(n=s.map(u=>lt(u)?u.trim():u)),r.number&&(n=s.map(ui)));let a,l=i[a=Js(e)]||i[a=Js(xt(e))];!l&&o&&(l=i[a=Js(ve(e))]),l&&Bt(l,t,6,n);const h=i[a+"Once"];if(h){if(!t.emitted)t.emitted={};else if(t.emitted[a])return;t.emitted[a]=!0,Bt(h,t,6,n)}}function tr(t,e,s=!1){const i=e.emitsCache,n=i.get(t);if(n!==void 0)return n;const o=t.emits;let r={},a=!1;if(!B(t)){const l=h=>{const u=tr(h,e,!0);u&&(a=!0,ut(r,u))};!s&&e.mixins.length&&e.mixins.forEach(l),t.extends&&l(t.extends),t.mixins&&t.mixins.forEach(l)}return!o&&!a?(tt(t)&&i.set(t,null),null):(M(o)?o.forEach(l=>r[l]=null):ut(r,o),tt(t)&&i.set(t,r),r)}function js(t,e){return!t||!Ls(e)?!1:(e=e.slice(2).replace(/Once$/,""),q(t,e[0].toLowerCase()+e.slice(1))||q(t,ve(e))||q(t,e))}function ni(t){const{type:e,vnode:s,proxy:i,withProxy:n,propsOptions:[o],slots:r,attrs:a,emit:l,render:h,renderCache:u,props:p,data:y,setupState:g,ctx:A,inheritAttrs:$}=t,ot=Rs(t);let z,Y;try{if(s.shapeFlag&4){const O=n||i,H=O;z=Vt(h.call(H,O,u,p,g,y,A)),Y=a}else{const O=e;z=Vt(O.length>1?O(p,{attrs:a,slots:r,emit:l}):O(p,null)),Y=e.props?a:hl(a)}}catch(O){Je.length=0,Ws(O,t,1),z=ct(Kt)}let K=z;if(Y&&$!==!1){const O=Object.keys(Y),{shapeFlag:H}=K;O.length&&H&7&&(o&&O.some(Oi)&&(Y=pl(Y,o)),K=Te(K,Y,!1,!0))}return s.dirs&&(K=Te(K,null,!1,!0),K.dirs=K.dirs?K.dirs.concat(s.dirs):s.dirs),s.transition&&ss(K,s.transition),z=K,Rs(ot),z}const hl=t=>{let e;for(const s in t)(s==="class"||s==="style"||Ls(s))&&((e||(e={}))[s]=t[s]);return e},pl=(t,e)=>{const s={};for(const i in t)(!Oi(i)||!(i.slice(9)in e))&&(s[i]=t[i]);return s};function gl(t,e,s){const{props:i,children:n,component:o}=t,{props:r,children:a,patchFlag:l}=e,h=o.emitsOptions;if(e.dirs||e.transition)return!0;if(s&&l>=0){if(l&1024)return!0;if(l&16)return i?yn(i,r,h):!!r;if(l&8){const u=e.dynamicProps;for(let p=0;p<u.length;p++){const y=u[p];if(r[y]!==i[y]&&!js(h,y))return!0}}}else return(n||a)&&(!a||!a.$stable)?!0:i===r?!1:i?r?yn(i,r,h):!0:!!r;return!1}function yn(t,e,s){const i=Object.keys(e);if(i.length!==Object.keys(t).length)return!0;for(let n=0;n<i.length;n++){const o=i[n];if(e[o]!==t[o]&&!js(s,o))return!0}return!1}function ml({vnode:t,parent:e},s){for(;e;){const i=e.subTree;if(i.suspense&&i.suspense.activeBranch===t&&(i.el=t.el),i===t)(t=e.vnode).el=s,e=e.parent;else break}}const er=t=>t.__isSuspense;function wl(t,e){e&&e.pendingBranch?M(t)?e.effects.push(...t):e.effects.push(t):Ea(t)}const dt=Symbol.for("v-fgt"),zs=Symbol.for("v-txt"),Kt=Symbol.for("v-cmt"),Ss=Symbol.for("v-stc"),Je=[];let Rt=null;function D(t=!1){Je.push(Rt=t?null:[])}function yl(){Je.pop(),Rt=Je[Je.length-1]||null}let is=1;function Sn(t,e=!1){is+=t,t<0&&Rt&&e&&(Rt.hasOnce=!0)}function sr(t){return t.dynamicChildren=is>0?Rt||Oe:null,yl(),is>0&&Rt&&Rt.push(t),t}function L(t,e,s,i,n,o){return sr(d(t,e,s,i,n,o,!0))}function _i(t,e,s,i,n){return sr(ct(t,e,s,i,n,!0))}function ns(t){return t?t.__v_isVNode===!0:!1}function Ae(t,e){return t.type===e.type&&t.key===e.key}const ir=({key:t})=>t!=null?t:null,_s=({ref:t,ref_key:e,ref_for:s})=>(typeof t=="number"&&(t=""+t),t!=null?lt(t)||at(t)||B(t)?{i:ht,r:t,k:e,f:!!s}:t:null);function d(t,e=null,s=null,i=0,n=null,o=t===dt?0:1,r=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:t,props:e,key:e&&ir(e),ref:e&&_s(e),scopeId:Ro,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:i,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:ht};return a?(Ki(l,s),o&128&&t.normalize(l)):s&&(l.shapeFlag|=lt(s)?8:16),is>0&&!r&&Rt&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&Rt.push(l),l}const ct=Sl;function Sl(t,e=null,s=null,i=0,n=null,o=!1){if((!t||t===Ba)&&(t=Kt),ns(t)){const a=Te(t,e,!0);return s&&Ki(a,s),is>0&&!o&&Rt&&(a.shapeFlag&6?Rt[Rt.indexOf(t)]=a:Rt.push(a)),a.patchFlag=-2,a}if(xl(t)&&(t=t.__vccOpts),e){e=_l(e);let{class:a,style:l}=e;a&&!lt(a)&&(e.class=Gt(a)),tt(l)&&(Fi(l)&&!M(l)&&(l=ut({},l)),e.style=_e(l))}const r=lt(t)?1:er(t)?128:Na(t)?64:tt(t)?4:B(t)?2:0;return d(t,e,s,i,n,r,o,!0)}function _l(t){return t?Fi(t)||zo(t)?ut({},t):t:null}function Te(t,e,s=!1,i=!1){const{props:n,ref:o,patchFlag:r,children:a,transition:l}=t,h=e?vl(n||{},e):n,u={__v_isVNode:!0,__v_skip:!0,type:t.type,props:h,key:h&&ir(h),ref:e&&e.ref?s&&o?M(o)?o.concat(_s(e)):[o,_s(e)]:_s(e):o,scopeId:t.scopeId,slotScopeIds:t.slotScopeIds,children:a,target:t.target,targetStart:t.targetStart,targetAnchor:t.targetAnchor,staticCount:t.staticCount,shapeFlag:t.shapeFlag,patchFlag:e&&t.type!==dt?r===-1?16:r|16:r,dynamicProps:t.dynamicProps,dynamicChildren:t.dynamicChildren,appContext:t.appContext,dirs:t.dirs,transition:l,component:t.component,suspense:t.suspense,ssContent:t.ssContent&&Te(t.ssContent),ssFallback:t.ssFallback&&Te(t.ssFallback),el:t.el,anchor:t.anchor,ctx:t.ctx,ce:t.ce};return l&&i&&ss(u,l.clone(u)),u}function Zt(t=" ",e=0){return ct(zs,null,t,e)}function Tl(t,e){const s=ct(Ss,null,t);return s.staticCount=e,s}function wt(t="",e=!1){return e?(D(),_i(Kt,null,t)):ct(Kt,null,t)}function Vt(t){return t==null||typeof t=="boolean"?ct(Kt):M(t)?ct(dt,null,t.slice()):ns(t)?re(t):ct(zs,null,String(t))}function re(t){return t.el===null&&t.patchFlag!==-1||t.memo?t:Te(t)}function Ki(t,e){let s=0;const{shapeFlag:i}=t;if(e==null)e=null;else if(M(e))s=16;else if(typeof e=="object")if(i&65){const n=e.default;n&&(n._c&&(n._d=!1),Ki(t,n()),n._c&&(n._d=!0));return}else{s=32;const n=e._;!n&&!zo(e)?e._ctx=ht:n===3&&ht&&(ht.slots._===1?e._=1:(e._=2,t.patchFlag|=1024))}else B(e)?(e={default:e,_ctx:ht},s=32):(e=String(e),i&64?(s=16,e=[Zt(e)]):s=8);t.children=e,t.shapeFlag|=s}function vl(...t){const e={};for(let s=0;s<t.length;s++){const i=t[s];for(const n in i)if(n==="class")e.class!==i.class&&(e.class=Gt([e.class,i.class]));else if(n==="style")e.style=_e([e.style,i.style]);else if(Ls(n)){const o=e[n],r=i[n];r&&o!==r&&!(M(o)&&o.includes(r))&&(e[n]=o?[].concat(o,r):r)}else n!==""&&(e[n]=i[n])}return e}function Ht(t,e,s,i=null){Bt(t,e,7,[s,i])}const Cl=Wo();let El=0;function bl(t,e,s){const i=t.type,n=(e?e.appContext:t.appContext)||Cl,o={uid:El++,vnode:t,type:i,parent:e,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new io(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:e?e.provides:Object.create(n.provides),ids:e?e.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Go(i,n),emitsOptions:tr(i,n),emit:null,emitted:null,propsDefaults:X,inheritAttrs:i.inheritAttrs,ctx:X,data:X,props:X,attrs:X,slots:X,refs:X,setupState:X,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=e?e.root:o,o.emit=dl.bind(null,o),t.ce&&t.ce(o),o}let mt=null;const Nl=()=>mt||ht;let Os,Ti;{const t=$s(),e=(s,i)=>{let n;return(n=t[s])||(n=t[s]=[]),n.push(i),o=>{n.length>1?n.forEach(r=>r(o)):n[0](o)}};Os=e("__VUE_INSTANCE_SETTERS__",s=>mt=s),Ti=e("__VUE_SSR_SETTERS__",s=>os=s)}const cs=t=>{const e=mt;return Os(t),t.scope.on(),()=>{t.scope.off(),Os(e)}},_n=()=>{mt&&mt.scope.off(),Os(null)};function nr(t){return t.vnode.shapeFlag&4}let os=!1;function Pl(t,e=!1,s=!1){e&&Ti(e);const{props:i,children:n}=t.vnode,o=nr(t);Za(t,i,o,e),sl(t,n,s||e);const r=o?Rl(t,e):void 0;return e&&Ti(!1),r}function Rl(t,e){const s=t.type;t.accessCache=Object.create(null),t.proxy=new Proxy(t.ctx,Ha);const{setup:i}=s;if(i){Xt();const n=t.setupContext=i.length>1?Il(t):null,o=cs(t),r=ls(i,t,0,[t.props,n]),a=Yn(r);if(te(),o(),(a||t.sp)&&!De(t)&&Oo(t),a){if(r.then(_n,_n),e)return r.then(l=>{Tn(t,l,e)}).catch(l=>{Ws(l,t,0)});t.asyncDep=r}else Tn(t,r,e)}else or(t,e)}function Tn(t,e,s){B(e)?t.type.__ssrInlineRender?t.ssrRender=e:t.render=e:tt(e)&&(t.setupState=vo(e)),or(t,s)}let vn;function or(t,e,s){const i=t.type;if(!t.render){if(!e&&vn&&!i.render){const n=i.template||zi(t).template;if(n){const{isCustomElement:o,compilerOptions:r}=t.appContext.config,{delimiters:a,compilerOptions:l}=i,h=ut(ut({isCustomElement:o,delimiters:a},r),l);i.render=vn(n,h)}}t.render=i.render||kt}{const n=cs(t);Xt();try{ja(t)}finally{te(),n()}}}const Al={get(t,e){return Tt(t,"get",""),t[e]}};function Il(t){const e=s=>{t.exposed=s||{}};return{attrs:new Proxy(t.attrs,Al),slots:t.slots,emit:t.emit,expose:e}}function Vs(t){return t.exposed?t.exposeProxy||(t.exposeProxy=new Proxy(vo(Wi(t.exposed)),{get(e,s){if(s in e)return e[s];if(s in qe)return qe[s](t)},has(e,s){return s in e||s in qe}})):t.proxy}function Ol(t,e=!0){return B(t)?t.displayName||t.name:t.name||e&&t.__name}function xl(t){return B(t)&&"__vccOpts"in t}const rr=(t,e)=>Sa(t,e,os);function Cn(t,e,s){const i=arguments.length;return i===2?tt(e)&&!M(e)?ns(e)?ct(t,null,[e]):ct(t,e):ct(t,null,e):(i>3?s=Array.prototype.slice.call(arguments,2):i===3&&ns(s)&&(s=[s]),ct(t,e,s))}const Ml="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let vi;const En=typeof window<"u"&&window.trustedTypes;if(En)try{vi=En.createPolicy("vue",{createHTML:t=>t})}catch{}const ar=vi?t=>vi.createHTML(t):t=>t,Dl="http://www.w3.org/2000/svg",Ll="http://www.w3.org/1998/Math/MathML",qt=typeof document<"u"?document:null,bn=qt&&qt.createElement("template"),kl={insert:(t,e,s)=>{e.insertBefore(t,s||null)},remove:t=>{const e=t.parentNode;e&&e.removeChild(t)},createElement:(t,e,s,i)=>{const n=e==="svg"?qt.createElementNS(Dl,t):e==="mathml"?qt.createElementNS(Ll,t):s?qt.createElement(t,{is:s}):qt.createElement(t);return t==="select"&&i&&i.multiple!=null&&n.setAttribute("multiple",i.multiple),n},createText:t=>qt.createTextNode(t),createComment:t=>qt.createComment(t),setText:(t,e)=>{t.nodeValue=e},setElementText:(t,e)=>{t.textContent=e},parentNode:t=>t.parentNode,nextSibling:t=>t.nextSibling,querySelector:t=>qt.querySelector(t),setScopeId(t,e){t.setAttribute(e,"")},insertStaticContent(t,e,s,i,n,o){const r=s?s.previousSibling:e.lastChild;if(n&&(n===o||n.nextSibling))for(;e.insertBefore(n.cloneNode(!0),s),!(n===o||!(n=n.nextSibling)););else{bn.innerHTML=ar(i==="svg"?`<svg>${t}</svg>`:i==="mathml"?`<math>${t}</math>`:t);const a=bn.content;if(i==="svg"||i==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}e.insertBefore(a,s)}return[r?r.nextSibling:e.firstChild,s?s.previousSibling:e.lastChild]}},se="transition",Fe="animation",ke=Symbol("_vtc"),lr={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Ul=ut({},Ra,lr),pe=(t,e=[])=>{M(t)?t.forEach(s=>s(...e)):t&&t(...e)},Nn=t=>t?M(t)?t.some(e=>e.length>1):t.length>1:!1;function $l(t){const e={};for(const P in t)P in lr||(e[P]=t[P]);if(t.css===!1)return e;const{name:s="v",type:i,duration:n,enterFromClass:o=`${s}-enter-from`,enterActiveClass:r=`${s}-enter-active`,enterToClass:a=`${s}-enter-to`,appearFromClass:l=o,appearActiveClass:h=r,appearToClass:u=a,leaveFromClass:p=`${s}-leave-from`,leaveActiveClass:y=`${s}-leave-active`,leaveToClass:g=`${s}-leave-to`}=t,A=Bl(n),$=A&&A[0],ot=A&&A[1],{onBeforeEnter:z,onEnter:Y,onEnterCancelled:K,onLeave:O,onLeaveCancelled:H,onBeforeAppear:ft=z,onAppear:rt=Y,onAppearCancelled:W=K}=e,N=(P,Z,pt,Mt)=>{P._enterCancelled=Mt,ie(P,Z?u:a),ie(P,Z?h:r),pt&&pt()},F=(P,Z)=>{P._isLeaving=!1,ie(P,p),ie(P,g),ie(P,y),Z&&Z()},Q=P=>(Z,pt)=>{const Mt=P?rt:Y,nt=()=>N(Z,P,pt);pe(Mt,[Z,nt]),Pn(()=>{ie(Z,P?l:o),jt(Z,P?u:a),Nn(Mt)||Rn(Z,i,$,nt)})};return ut(e,{onBeforeEnter(P){pe(z,[P]),jt(P,o),jt(P,r)},onBeforeAppear(P){pe(ft,[P]),jt(P,l),jt(P,h)},onEnter:Q(!1),onAppear:Q(!0),onLeave(P,Z){P._isLeaving=!0;const pt=()=>F(P,Z);jt(P,p),P._enterCancelled?(jt(P,y),Ci()):(Ci(),jt(P,y)),Pn(()=>{!P._isLeaving||(ie(P,p),jt(P,g),Nn(O)||Rn(P,i,ot,pt))}),pe(O,[P,pt])},onEnterCancelled(P){N(P,!1,void 0,!0),pe(K,[P])},onAppearCancelled(P){N(P,!0,void 0,!0),pe(W,[P])},onLeaveCancelled(P){F(P),pe(H,[P])}})}function Bl(t){if(t==null)return null;if(tt(t))return[oi(t.enter),oi(t.leave)];{const e=oi(t);return[e,e]}}function oi(t){return kr(t)}function jt(t,e){e.split(/\s+/).forEach(s=>s&&t.classList.add(s)),(t[ke]||(t[ke]=new Set)).add(e)}function ie(t,e){e.split(/\s+/).forEach(i=>i&&t.classList.remove(i));const s=t[ke];s&&(s.delete(e),s.size||(t[ke]=void 0))}function Pn(t){requestAnimationFrame(()=>{requestAnimationFrame(t)})}let Fl=0;function Rn(t,e,s,i){const n=t._endId=++Fl,o=()=>{n===t._endId&&i()};if(s!=null)return setTimeout(o,s);const{type:r,timeout:a,propCount:l}=cr(t,e);if(!r)return i();const h=r+"end";let u=0;const p=()=>{t.removeEventListener(h,y),o()},y=g=>{g.target===t&&++u>=l&&p()};setTimeout(()=>{u<l&&p()},a+1),t.addEventListener(h,y)}function cr(t,e){const s=window.getComputedStyle(t),i=A=>(s[A]||"").split(", "),n=i(`${se}Delay`),o=i(`${se}Duration`),r=An(n,o),a=i(`${Fe}Delay`),l=i(`${Fe}Duration`),h=An(a,l);let u=null,p=0,y=0;e===se?r>0&&(u=se,p=r,y=o.length):e===Fe?h>0&&(u=Fe,p=h,y=l.length):(p=Math.max(r,h),u=p>0?r>h?se:Fe:null,y=u?u===se?o.length:l.length:0);const g=u===se&&/\b(transform|all)(,|$)/.test(i(`${se}Property`).toString());return{type:u,timeout:p,propCount:y,hasTransform:g}}function An(t,e){for(;t.length<e.length;)t=t.concat(t);return Math.max(...e.map((s,i)=>In(s)+In(t[i])))}function In(t){return t==="auto"?0:Number(t.slice(0,-1).replace(",","."))*1e3}function Ci(){return document.body.offsetHeight}function Wl(t,e,s){const i=t[ke];i&&(e=(e?[e,...i]:[...i]).join(" ")),e==null?t.removeAttribute("class"):s?t.setAttribute("class",e):t.className=e}const xs=Symbol("_vod"),ur=Symbol("_vsh"),ri={beforeMount(t,{value:e},{transition:s}){t[xs]=t.style.display==="none"?"":t.style.display,s&&e?s.beforeEnter(t):We(t,e)},mounted(t,{value:e},{transition:s}){s&&e&&s.enter(t)},updated(t,{value:e,oldValue:s},{transition:i}){!e!=!s&&(i?e?(i.beforeEnter(t),We(t,!0),i.enter(t)):i.leave(t,()=>{We(t,!1)}):We(t,e))},beforeUnmount(t,{value:e}){We(t,e)}};function We(t,e){t.style.display=e?t[xs]:"none",t[ur]=!e}const Hl=Symbol(""),jl=/(^|;)\s*display\s*:/;function zl(t,e,s){const i=t.style,n=lt(s);let o=!1;if(s&&!n){if(e)if(lt(e))for(const r of e.split(";")){const a=r.slice(0,r.indexOf(":")).trim();s[a]==null&&Ts(i,a,"")}else for(const r in e)s[r]==null&&Ts(i,r,"");for(const r in s)r==="display"&&(o=!0),Ts(i,r,s[r])}else if(n){if(e!==s){const r=i[Hl];r&&(s+=";"+r),i.cssText=s,o=jl.test(s)}}else e&&t.removeAttribute("style");xs in t&&(t[xs]=o?i.display:"",t[ur]&&(i.display="none"))}const On=/\s*!important$/;function Ts(t,e,s){if(M(s))s.forEach(i=>Ts(t,e,i));else if(s==null&&(s=""),e.startsWith("--"))t.setProperty(e,s);else{const i=Vl(t,e);On.test(s)?t.setProperty(ve(i),s.replace(On,""),"important"):t[i]=s}}const xn=["Webkit","Moz","ms"],ai={};function Vl(t,e){const s=ai[e];if(s)return s;let i=xt(e);if(i!=="filter"&&i in t)return ai[e]=i;i=Us(i);for(let n=0;n<xn.length;n++){const o=xn[n]+i;if(o in t)return ai[e]=o}return e}const Mn="http://www.w3.org/1999/xlink";function Dn(t,e,s,i,n,o=Hr(e)){i&&e.startsWith("xlink:")?s==null?t.removeAttributeNS(Mn,e.slice(6,e.length)):t.setAttributeNS(Mn,e,s):s==null||o&&!to(s)?t.removeAttribute(e):t.setAttribute(e,o?"":$t(s)?String(s):s)}function Ln(t,e,s,i,n){if(e==="innerHTML"||e==="textContent"){s!=null&&(t[e]=e==="innerHTML"?ar(s):s);return}const o=t.tagName;if(e==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?t.getAttribute("value")||"":t.value,l=s==null?t.type==="checkbox"?"on":"":String(s);(a!==l||!("_value"in t))&&(t.value=l),s==null&&t.removeAttribute(e),t._value=s;return}let r=!1;if(s===""||s==null){const a=typeof t[e];a==="boolean"?s=to(s):s==null&&a==="string"?(s="",r=!0):a==="number"&&(s=0,r=!0)}try{t[e]=s}catch{}r&&t.removeAttribute(n||e)}function me(t,e,s,i){t.addEventListener(e,s,i)}function Gl(t,e,s,i){t.removeEventListener(e,s,i)}const kn=Symbol("_vei");function Kl(t,e,s,i,n=null){const o=t[kn]||(t[kn]={}),r=o[e];if(i&&r)r.value=i;else{const[a,l]=Ql(e);if(i){const h=o[e]=Jl(i,n);me(t,a,h,l)}else r&&(Gl(t,a,r,l),o[e]=void 0)}}const Un=/(?:Once|Passive|Capture)$/;function Ql(t){let e;if(Un.test(t)){e={};let i;for(;i=t.match(Un);)t=t.slice(0,t.length-i[0].length),e[i[0].toLowerCase()]=!0}return[t[2]===":"?t.slice(3):ve(t.slice(2)),e]}let li=0;const ql=Promise.resolve(),Yl=()=>li||(ql.then(()=>li=0),li=Date.now());function Jl(t,e){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;Bt(Zl(i,s.value),e,5,[i])};return s.value=t,s.attached=Yl(),s}function Zl(t,e){if(M(e)){const s=t.stopImmediatePropagation;return t.stopImmediatePropagation=()=>{s.call(t),t._stopped=!0},e.map(i=>n=>!n._stopped&&i&&i(n))}else return e}const $n=t=>t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&t.charCodeAt(2)>96&&t.charCodeAt(2)<123,Xl=(t,e,s,i,n,o)=>{const r=n==="svg";e==="class"?Wl(t,i,r):e==="style"?zl(t,s,i):Ls(e)?Oi(e)||Kl(t,e,s,i,o):(e[0]==="."?(e=e.slice(1),!0):e[0]==="^"?(e=e.slice(1),!1):tc(t,e,i,r))?(Ln(t,e,i),!t.tagName.includes("-")&&(e==="value"||e==="checked"||e==="selected")&&Dn(t,e,i,r,o,e!=="value")):t._isVueCE&&(/[A-Z]/.test(e)||!lt(i))?Ln(t,xt(e),i,o,e):(e==="true-value"?t._trueValue=i:e==="false-value"&&(t._falseValue=i),Dn(t,e,i,r))};function tc(t,e,s,i){if(i)return!!(e==="innerHTML"||e==="textContent"||e in t&&$n(e)&&B(s));if(e==="spellcheck"||e==="draggable"||e==="translate"||e==="autocorrect"||e==="form"||e==="list"&&t.tagName==="INPUT"||e==="type"&&t.tagName==="TEXTAREA")return!1;if(e==="width"||e==="height"){const n=t.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return $n(e)&&lt(s)?!1:e in t}const fr=new WeakMap,dr=new WeakMap,Ms=Symbol("_moveCb"),Bn=Symbol("_enterCb"),ec=t=>(delete t.props.mode,t),sc=ec({name:"TransitionGroup",props:ut({},Ul,{tag:String,moveClass:String}),setup(t,{slots:e}){const s=Nl(),i=Pa();let n,o;return Lo(()=>{if(!n.length)return;const r=t.moveClass||`${t.name||"v"}-move`;if(!ac(n[0].el,s.vnode.el,r)){n=[];return}n.forEach(nc),n.forEach(oc);const a=n.filter(rc);Ci(),a.forEach(l=>{const h=l.el,u=h.style;jt(h,r),u.transform=u.webkitTransform=u.transitionDuration="";const p=h[Ms]=y=>{y&&y.target!==h||(!y||/transform$/.test(y.propertyName))&&(h.removeEventListener("transitionend",p),h[Ms]=null,ie(h,r))};h.addEventListener("transitionend",p)}),n=[]}),()=>{const r=G(t),a=$l(r);let l=r.tag||dt;if(n=[],o)for(let h=0;h<o.length;h++){const u=o[h];u.el&&u.el instanceof Element&&(n.push(u),ss(u,pi(u,a,i,s)),fr.set(u,u.el.getBoundingClientRect()))}o=e.default?Io(e.default()):[];for(let h=0;h<o.length;h++){const u=o[h];u.key!=null&&ss(u,pi(u,a,i,s))}return ct(l,null,o)}}}),ic=sc;function nc(t){const e=t.el;e[Ms]&&e[Ms](),e[Bn]&&e[Bn]()}function oc(t){dr.set(t,t.el.getBoundingClientRect())}function rc(t){const e=fr.get(t),s=dr.get(t),i=e.left-s.left,n=e.top-s.top;if(i||n){const o=t.el.style;return o.transform=o.webkitTransform=`translate(${i}px,${n}px)`,o.transitionDuration="0s",t}}function ac(t,e,s){const i=t.cloneNode(),n=t[ke];n&&n.forEach(a=>{a.split(/\s+/).forEach(l=>l&&i.classList.remove(l))}),s.split(/\s+/).forEach(a=>a&&i.classList.add(a)),i.style.display="none";const o=e.nodeType===1?e:e.parentNode;o.appendChild(i);const{hasTransform:r}=cr(i);return o.removeChild(i),r}const Ds=t=>{const e=t.props["onUpdate:modelValue"]||!1;return M(e)?s=>ws(e,s):e};function lc(t){t.target.composing=!0}function Fn(t){const e=t.target;e.composing&&(e.composing=!1,e.dispatchEvent(new Event("input")))}const Le=Symbol("_assign"),Wn={created(t,{modifiers:{lazy:e,trim:s,number:i}},n){t[Le]=Ds(n);const o=i||n.props&&n.props.type==="number";me(t,e?"change":"input",r=>{if(r.target.composing)return;let a=t.value;s&&(a=a.trim()),o&&(a=ui(a)),t[Le](a)}),s&&me(t,"change",()=>{t.value=t.value.trim()}),e||(me(t,"compositionstart",lc),me(t,"compositionend",Fn),me(t,"change",Fn))},mounted(t,{value:e}){t.value=e==null?"":e},beforeUpdate(t,{value:e,oldValue:s,modifiers:{lazy:i,trim:n,number:o}},r){if(t[Le]=Ds(r),t.composing)return;const a=(o||t.type==="number")&&!/^0\d/.test(t.value)?ui(t.value):t.value,l=e==null?"":e;a!==l&&(document.activeElement===t&&t.type!=="range"&&(i&&e===s||n&&t.value.trim()===l)||(t.value=l))}},cc={created(t,{value:e},s){t.checked=Cs(e,s.props.value),t[Le]=Ds(s),me(t,"change",()=>{t[Le](uc(t))})},beforeUpdate(t,{value:e,oldValue:s},i){t[Le]=Ds(i),e!==s&&(t.checked=Cs(e,i.props.value))}};function uc(t){return"_value"in t?t._value:t.value}const fc=["ctrl","shift","alt","meta"],dc={stop:t=>t.stopPropagation(),prevent:t=>t.preventDefault(),self:t=>t.target!==t.currentTarget,ctrl:t=>!t.ctrlKey,shift:t=>!t.shiftKey,alt:t=>!t.altKey,meta:t=>!t.metaKey,left:t=>"button"in t&&t.button!==0,middle:t=>"button"in t&&t.button!==1,right:t=>"button"in t&&t.button!==2,exact:(t,e)=>fc.some(s=>t[`${s}Key`]&&!e.includes(s))},Hn=(t,e)=>{const s=t._withMods||(t._withMods={}),i=e.join(".");return s[i]||(s[i]=(n,...o)=>{for(let r=0;r<e.length;r++){const a=dc[e[r]];if(a&&a(n,e))return}return t(n,...o)})},hc=ut({patchProp:Xl},kl);let jn;function pc(){return jn||(jn=nl(hc))}const gc=(...t)=>{const e=pc().createApp(...t),{mount:s}=e;return e.mount=i=>{const n=wc(i);if(!n)return;const o=e._component;!B(o)&&!o.render&&!o.template&&(o.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const r=s(n,!1,mc(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),r},e};function mc(t){if(t instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&t instanceof MathMLElement)return"mathml"}function wc(t){return lt(t)?document.querySelector(t):t}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let hr;const Gs=t=>hr=t,pr=Symbol();function Ei(t){return t&&typeof t=="object"&&Object.prototype.toString.call(t)==="[object Object]"&&typeof t.toJSON!="function"}var Ze;(function(t){t.direct="direct",t.patchObject="patch object",t.patchFunction="patch function"})(Ze||(Ze={}));function yc(){const t=no(!0),e=t.run(()=>Hi({}));let s=[],i=[];const n=Wi({install(o){Gs(n),n._a=o,o.provide(pr,n),o.config.globalProperties.$pinia=n,i.forEach(r=>s.push(r)),i=[]},use(o){return this._a?s.push(o):i.push(o),this},_p:s,_a:null,_e:t,_s:new Map,state:e});return n}const gr=()=>{};function zn(t,e,s,i=gr){t.push(e);const n=()=>{const o=t.indexOf(e);o>-1&&(t.splice(o,1),i())};return!s&&oo()&&zr(n),n}function Pe(t,...e){t.slice().forEach(s=>{s(...e)})}const Sc=t=>t(),Vn=Symbol(),ci=Symbol();function bi(t,e){t instanceof Map&&e instanceof Map?e.forEach((s,i)=>t.set(i,s)):t instanceof Set&&e instanceof Set&&e.forEach(t.add,t);for(const s in e){if(!e.hasOwnProperty(s))continue;const i=e[s],n=t[s];Ei(n)&&Ei(i)&&t.hasOwnProperty(s)&&!at(i)&&!le(i)?t[s]=bi(n,i):t[s]=i}return t}const _c=Symbol();function Tc(t){return!Ei(t)||!Object.prototype.hasOwnProperty.call(t,_c)}const{assign:ne}=Object;function vc(t){return!!(at(t)&&t.effect)}function Cc(t,e,s,i){const{state:n,actions:o,getters:r}=e,a=s.state.value[t];let l;function h(){a||(s.state.value[t]=n?n():{});const u=ga(s.state.value[t]);return ne(u,o,Object.keys(r||{}).reduce((p,y)=>(p[y]=Wi(rr(()=>{Gs(s);const g=s._s.get(t);return r[y].call(g,g)})),p),{}))}return l=mr(t,h,e,s,i,!0),l}function mr(t,e,s={},i,n,o){let r;const a=ne({actions:{}},s),l={deep:!0};let h,u,p=[],y=[],g;const A=i.state.value[t];!o&&!A&&(i.state.value[t]={}),Hi({});let $;function ot(W){let N;h=u=!1,typeof W=="function"?(W(i.state.value[t]),N={type:Ze.patchFunction,storeId:t,events:g}):(bi(i.state.value[t],W),N={type:Ze.patchObject,payload:W,storeId:t,events:g});const F=$=Symbol();Eo().then(()=>{$===F&&(h=!0)}),u=!0,Pe(p,N,i.state.value[t])}const z=o?function(){const{state:N}=s,F=N?N():{};this.$patch(Q=>{ne(Q,F)})}:gr;function Y(){r.stop(),p=[],y=[],i._s.delete(t)}const K=(W,N="")=>{if(Vn in W)return W[ci]=N,W;const F=function(){Gs(i);const Q=Array.from(arguments),P=[],Z=[];function pt(j){P.push(j)}function Mt(j){Z.push(j)}Pe(y,{args:Q,name:F[ci],store:H,after:pt,onError:Mt});let nt;try{nt=W.apply(this&&this.$id===t?this:H,Q)}catch(j){throw Pe(Z,j),j}return nt instanceof Promise?nt.then(j=>(Pe(P,j),j)).catch(j=>(Pe(Z,j),Promise.reject(j))):(Pe(P,nt),nt)};return F[Vn]=!0,F[ci]=N,F},O={_p:i,$id:t,$onAction:zn.bind(null,y),$patch:ot,$reset:z,$subscribe(W,N={}){const F=zn(p,W,N.detached,()=>Q()),Q=r.run(()=>ys(()=>i.state.value[t],P=>{(N.flush==="sync"?u:h)&&W({storeId:t,type:Ze.direct,events:g},P)},ne({},l,N)));return F},$dispose:Y},H=Fs(O);i._s.set(t,H);const rt=(i._a&&i._a.runWithContext||Sc)(()=>i._e.run(()=>(r=no()).run(()=>e({action:K}))));for(const W in rt){const N=rt[W];if(at(N)&&!vc(N)||le(N))o||(A&&Tc(N)&&(at(N)?N.value=A[W]:bi(N,A[W])),i.state.value[t][W]=N);else if(typeof N=="function"){const F=K(N,W);rt[W]=F,a.actions[W]=N}}return ne(H,rt),ne(G(H),rt),Object.defineProperty(H,"$state",{get:()=>i.state.value[t],set:W=>{ot(N=>{ne(N,W)})}}),i._p.forEach(W=>{ne(H,r.run(()=>W({store:H,app:i._a,pinia:i,options:a})))}),A&&o&&s.hydrate&&s.hydrate(H.$state,A),h=!0,u=!0,H}/*! #__NO_SIDE_EFFECTS__ */function Qi(t,e,s){let i;const n=typeof e=="function";i=n?s:e;function o(r,a){const l=Ja();return r=r||(l?Ye(pr,null):null),r&&Gs(r),r=hr,r._s.has(t)||(n?mr(t,e,i,r):Cc(t,i,r)),r._s.get(t)}return o.$id=t,o}const Ec="modulepreload",bc=function(t){return"/"+t},Gn={},Kn=function(e,s,i){if(!s||s.length===0)return e();const n=document.getElementsByTagName("link");return Promise.all(s.map(o=>{if(o=bc(o),o in Gn)return;Gn[o]=!0;const r=o.endsWith(".css"),a=r?'[rel="stylesheet"]':"";if(!!i)for(let u=n.length-1;u>=0;u--){const p=n[u];if(p.href===o&&(!r||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${a}`))return;const h=document.createElement("link");if(h.rel=r?"stylesheet":Ec,r||(h.as="script",h.crossOrigin=""),h.href=o,document.head.appendChild(h),r)return new Promise((u,p)=>{h.addEventListener("load",u),h.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${o}`)))})})).then(()=>e())};function Nc(t){return window.go.main.App.AddPatient(t)}function Pc(){return window.go.main.App.AutoCollapseAfterInactivity()}function Rc(){return window.go.main.App.CaptureScreenshotByBtn()}function Ac(){return window.go.main.App.ClearCompletedFailedTasks()}function Ic(){return window.go.main.App.ClearCurrentPatient()}function Oc(){return window.go.main.App.ClearPatientList()}function xc(){return window.go.main.App.DiagnoseSystemStatus()}function Mc(t){return window.go.main.App.ExportCurrentUserCheckingInfoJSON(t)}function Dc(t){return window.go.main.App.ExtractOrganFromScreenshot(t)}function Ni(){return window.go.main.App.GenerateQRCode()}function Pi(){return window.go.main.App.GenerateRegistrationQRCode()}function Lc(){return window.go.main.App.GetCompletedPatients()}function Ri(t){return window.go.main.App.GetCompletedPatientsByDate(t)}function qi(){return window.go.main.App.GetConfig()}function kc(){return window.go.main.App.GetCurrentPatientIndex()}function Uc(){return window.go.main.App.GetCurrentPatientName()}function $c(){return window.go.main.App.GetCurrentRegistrationNumber()}function Bc(t){return window.go.main.App.GetCurrentUserCheckingInfo(t)}function Fc(){return window.go.main.App.GetFailedTasks()}function wr(){return window.go.main.App.GetModeConfig()}function Wc(){return window.go.main.App.GetPatientList()}function yr(t){return window.go.main.App.GetPendingPatients(t)}function Sr(){return window.go.main.App.GetPendingRegistrations()}function _r(t){return window.go.main.App.GetRegistrations(t)}function Tr(){return window.go.main.App.GetSiteInfo()}function Hc(){return window.go.main.App.GetTaskManagerStatus()}function jc(){return window.go.main.App.GetTodayPatientCount()}function vr(t){return window.go.main.App.GetUnanalyzedPatients(t)}function zc(t){return window.go.main.App.GetUserCheckingInfoStatus(t)}function rs(){return window.go.main.App.GetWindowState()}function Vc(t){return window.go.main.App.Greet(t)}function Gc(t){return window.go.main.App.HandleCozeLLM_AnylizeD_value(t)}function Kc(t){return window.go.main.App.HandleHotkey(t)}function ze(t){return window.go.main.App.HandleKeyboardShortcut(t)}function Qc(t,e){return window.go.main.App.MarkPatientCompleted(t,e)}function Yi(){return window.go.main.App.MinimizeWindow()}function qc(){return window.go.main.App.MoveToNextPatient()}function Yc(t){return window.go.main.App.ProcessImageWithOCR(t)}function Jc(t,e){return window.go.main.App.ProcessScreenshotWithOCR(t,e)}function Ai(t){return window.go.main.App.ProcessScreenshotWorkflow(t)}function Zc(t){return window.go.main.App.RemovePatient(t)}function Xc(t,e,s){return window.go.main.App.SendConfigurableToast(t,e,s)}function tu(t){return window.go.main.App.SendTaskCompletedNotification(t)}function Cr(t){return window.go.main.App.SetAlwaysOnTop(t)}function Ji(){return window.go.main.App.SetCompactWindow()}function Er(t){return window.go.main.App.SetCurrentPatientIndex(t)}function eu(t){return window.go.main.App.SetCurrentPatientName(t)}function Zi(){return window.go.main.App.SetExpandedWindow()}function Xi(t){return window.go.main.App.SetWindowPosition(t)}function su(t,e,s){return window.go.main.App.ShowErrorNotification(t,e,s)}function iu(t,e,s){return window.go.main.App.ShowInfoNotification(t,e,s)}function nu(t,e,s){return window.go.main.App.ShowOCRProcessNotification(t,e,s)}function ou(t,e,s,i){return window.go.main.App.ShowProgressNotification(t,e,s,i)}function ru(t,e,s){return window.go.main.App.ShowSuccessNotification(t,e,s)}function au(t,e,s,i,n,o){return window.go.main.App.ShowToastNotification(t,e,s,i,n,o)}function lu(t,e,s,i){return window.go.main.App.ShowWailsNotification(t,e,s,i)}function cu(t,e,s){return window.go.main.App.ShowWarningNotification(t,e,s)}function uu(t,e,s){return window.go.main.App.SubmitScreenshotTask(t,e,s)}function br(t){return window.go.main.App.TakeConcurrentScreenshot(t)}function fu(t,e){return window.go.main.App.TakeOCRScreenshot(t,e)}function du(t){return window.go.main.App.TestAllTasksCompletion(t)}function hu(t){return window.go.main.App.TestOCR(t)}function pu(){return window.go.main.App.ToggleWindowSize()}function tn(t){return window.go.main.App.UpdateCropSettings(t)}function gu(t){return window.go.main.App.UpdateNotificationMode(t)}function mu(t,e){return window.go.main.App.UpdateOperationStatus(t,e)}function wu(t,e,s){return window.go.main.App.UpdateProgressBar(t,e,s)}function yu(t,e,s){return window.go.main.App.UpdateProgressNotification(t,e,s)}function Su(t){return window.go.main.App.UpdateSiteInfo(t)}function _u(){return window.go.main.App.ValidateOCRSetup()}const Qn=Object.freeze(Object.defineProperty({__proto__:null,AddPatient:Nc,AutoCollapseAfterInactivity:Pc,CaptureScreenshotByBtn:Rc,ClearCompletedFailedTasks:Ac,ClearCurrentPatient:Ic,ClearPatientList:Oc,DiagnoseSystemStatus:xc,ExportCurrentUserCheckingInfoJSON:Mc,ExtractOrganFromScreenshot:Dc,GenerateQRCode:Ni,GenerateRegistrationQRCode:Pi,GetCompletedPatients:Lc,GetCompletedPatientsByDate:Ri,GetConfig:qi,GetCurrentPatientIndex:kc,GetCurrentPatientName:Uc,GetCurrentRegistrationNumber:$c,GetCurrentUserCheckingInfo:Bc,GetFailedTasks:Fc,GetModeConfig:wr,GetPatientList:Wc,GetPendingPatients:yr,GetPendingRegistrations:Sr,GetRegistrations:_r,GetSiteInfo:Tr,GetTaskManagerStatus:Hc,GetTodayPatientCount:jc,GetUnanalyzedPatients:vr,GetUserCheckingInfoStatus:zc,GetWindowState:rs,Greet:Vc,HandleCozeLLM_AnylizeD_value:Gc,HandleHotkey:Kc,HandleKeyboardShortcut:ze,MarkPatientCompleted:Qc,MinimizeWindow:Yi,MoveToNextPatient:qc,ProcessImageWithOCR:Yc,ProcessScreenshotWithOCR:Jc,ProcessScreenshotWorkflow:Ai,RemovePatient:Zc,SendConfigurableToast:Xc,SendTaskCompletedNotification:tu,SetAlwaysOnTop:Cr,SetCompactWindow:Ji,SetCurrentPatientIndex:Er,SetCurrentPatientName:eu,SetExpandedWindow:Zi,SetWindowPosition:Xi,ShowErrorNotification:su,ShowInfoNotification:iu,ShowOCRProcessNotification:nu,ShowProgressNotification:ou,ShowSuccessNotification:ru,ShowToastNotification:au,ShowWailsNotification:lu,ShowWarningNotification:cu,SubmitScreenshotTask:uu,TakeConcurrentScreenshot:br,TakeOCRScreenshot:fu,TestAllTasksCompletion:du,TestOCR:hu,ToggleWindowSize:pu,UpdateCropSettings:tn,UpdateNotificationMode:gu,UpdateOperationStatus:mu,UpdateProgressBar:wu,UpdateProgressNotification:yu,UpdateSiteInfo:Su,ValidateOCRSetup:_u},Symbol.toStringTag,{value:"Module"})),Tu=Qi("patient",{state:()=>({registrations:[],selectedPatientIndex:0,displayLimit:5,isRefreshing:!1,pollTimer:null,patients:[]}),getters:{currentPatient:t=>t.registrations.length>0&&t.selectedPatientIndex>=0&&t.selectedPatientIndex<t.registrations.length?t.registrations[t.selectedPatientIndex]:null,displayedRegistrations:t=>t.registrations.slice(0,t.displayLimit),todayPatientCount:t=>t.registrations.length,currentRegistrationNumber:t=>t.patients.length+1},actions:{async loadRegistrations(){try{this.registrations=[];const t=new Date().toISOString().split("T")[0],e=await _r(t);this.registrations=e||[],this.registrations.sort((s,i)=>new Date(i.register_time)-new Date(s.register_time)),this.displayLimit=5,this.registrations.length>0?this.selectedPatientIndex=0:this.selectedPatientIndex=-1}catch(t){throw console.error("\u52A0\u8F7D\u5019\u68C0\u8005\u5217\u8868\u5931\u8D25:",t),this.registrations=[],this.displayLimit=5,this.selectedPatientIndex=-1,t}},async refreshRegistrations(){if(!this.isRefreshing){this.isRefreshing=!0;try{return await this.loadRegistrations(),{success:!0,message:"\u5019\u68C0\u8005\u5217\u8868\u5DF2\u5237\u65B0"}}catch(t){return{success:!1,message:"\u5237\u65B0\u5931\u8D25: "+t}}finally{this.isRefreshing=!1}}},async selectPatient(t){var e;this.selectedPatientIndex=t;try{return await Er(t),{success:!0,message:`\u5DF2\u9009\u62E9\u5019\u68C0\u8005: ${(e=this.currentPatient)==null?void 0:e.name}`}}catch(s){return console.error("\u8BBE\u7F6E\u5F53\u524D\u60A3\u8005\u7D22\u5F15\u5931\u8D25:",s),{success:!1,message:"\u8BBE\u7F6E\u5F53\u524D\u60A3\u8005\u5931\u8D25"}}},loadMoreRegistrations(){this.displayLimit=Math.min(this.displayLimit+5,this.registrations.length)},async startConditionalPolling(){this.pollTimer&&clearTimeout(this.pollTimer);let t=0;const e=2,s=2e3,i=async()=>{t++;const n=this.registrations.length;try{if(await this.loadRegistrations(),this.registrations.length>n)return{success:!0,message:`\u53D1\u73B0 ${this.registrations.length-n} \u4F4D\u65B0\u5019\u68C0\u8005`};t<e&&(this.pollTimer=setTimeout(i,s))}catch(o){console.error("\u8F6E\u8BE2\u5019\u68C0\u8005\u5217\u8868\u5931\u8D25:",o),t<e&&(this.pollTimer=setTimeout(i,s))}};this.pollTimer=setTimeout(i,1e3)},stopPolling(){this.pollTimer&&(clearTimeout(this.pollTimer),this.pollTimer=null)},addPatient(t){try{return this.patients.push({id:Date.now(),...t,addedTime:new Date().toISOString()}),{success:!0,message:`\u60A3\u8005 ${t.name||"\u672A\u77E5"} \u5DF2\u6DFB\u52A0`}}catch(e){return console.error("\u6DFB\u52A0\u60A3\u8005\u5931\u8D25:",e),{success:!1,message:"\u6DFB\u52A0\u60A3\u8005\u5931\u8D25: "+e.message}}},removePatient(t){try{const e=this.patients.findIndex(s=>s.id===t);if(e>-1){const s=this.patients[e];return this.patients.splice(e,1),{success:!0,message:"\u60A3\u8005\u5DF2\u79FB\u9664",patientName:s.name||"\u672A\u77E5"}}else return{success:!1,message:"\u672A\u627E\u5230\u6307\u5B9A\u60A3\u8005"}}catch(e){return console.error("\u79FB\u9664\u60A3\u8005\u5931\u8D25:",e),{success:!1,message:"\u79FB\u9664\u60A3\u8005\u5931\u8D25: "+e.message}}},clearPatients(){try{return this.patients=[],{success:!0,message:"\u60A3\u8005\u5217\u8868\u5DF2\u6E05\u7A7A"}}catch(t){return console.error("\u6E05\u7A7A\u60A3\u8005\u5217\u8868\u5931\u8D25:",t),{success:!1,message:"\u6E05\u7A7A\u5931\u8D25: "+t.message}}},formatGender(t){return t===1?"\u7537":t===2?"\u5973":"\u672A\u77E5"},calculateAge(t){if(!t)return"";const e=new Date(t),s=new Date;let i=s.getFullYear()-e.getFullYear();const n=s.getMonth()-e.getMonth();return(n<0||n===0&&s.getDate()<e.getDate())&&i--,i+"\u5C81"},formatTime(t){if(!t)return"";try{if(t.includes("-")&&t.includes(":")){const e=t.split(" ");if(e.length===2){const s=e[0].split("-"),i=e[1].split(":");if(s.length===3&&i.length>=2)return`${s[1]}/${s[2]} ${i[0]}:${i[1]}`}}return t}catch{return t}}}}),vu=Qi("device",{state:()=>({config:{SiteInfo:{SiteName:"",SiteID:""},device_info:{mac_address:"",device_no:""}},windowState:{isExpanded:!1,position:"left"},systemStatus:"\u6B63\u5E38",modeConfig:{},useSystemNotification:!0,configWatcherTimer:null}),getters:{siteInfo:t=>!t.config||!t.config.SiteInfo||!t.config.SiteInfo.SiteName?"\u52A0\u8F7D\u4E2D...":`${t.config.SiteInfo.SiteName} (${t.config.SiteInfo.SiteID})`,deviceNumber:t=>{var e,s;return((s=(e=t.config)==null?void 0:e.device_info)==null?void 0:s.device_no)||"\u672A\u8BBE\u7F6E"},macAddress:t=>{var e,s;return((s=(e=t.config)==null?void 0:e.device_info)==null?void 0:s.mac_address)||"\u672A\u8BBE\u7F6E"}},actions:{async loadConfig(){try{return this.config=await qi(),this.config.device_info&&this.config.device_info.mac_address&&(this.config.device_info.device_no=this.config.device_info.mac_address.replace(/:/g,"")),this.config.use_system_notification!==void 0&&(this.useSystemNotification=this.config.use_system_notification),{success:!0,config:this.config}}catch(t){return console.error("\u52A0\u8F7D\u914D\u7F6E\u5931\u8D25:",t),{success:!1,error:t}}},async fetchSiteInfo(){try{const t=await Tr();return this.config.SiteInfo={SiteName:t.site_name,SiteID:t.site_id,SiteType:t.site_type,ParentOrg:t.parent_org,Location:t.location,Contact:t.contact},{success:!0,message:`\u7AD9\u70B9\u4FE1\u606F\u5DF2\u52A0\u8F7D: ${t.site_name||"\u672A\u77E5\u7AD9\u70B9"}`,siteInfo:t}}catch(t){return console.error("\u83B7\u53D6\u7AD9\u70B9\u4FE1\u606F\u5931\u8D25:",t),{success:!1,message:"\u83B7\u53D6\u7AD9\u70B9\u4FE1\u606F\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5",error:t}}},updateSiteInfo(t){return t&&typeof t=="object"?(this.config.SiteInfo={SiteName:t.site_name||t.SiteName,SiteID:t.site_id||t.SiteID,SiteType:t.site_type||t.SiteType,ParentOrg:t.parent_org||t.ParentOrg,Location:t.location||t.Location,Contact:t.contact||t.Contact},{success:!0,message:`\u7AD9\u70B9\u4FE1\u606F\u5DF2\u66F4\u65B0: ${t.site_name||t.SiteName||"\u672A\u77E5\u7AD9\u70B9"}`}):(console.warn("updateSiteInfo - newSiteInfo is not an object or is null:",t),{success:!1,message:"\u7AD9\u70B9\u4FE1\u606F\u683C\u5F0F\u9519\u8BEF"})},async initializeWindowState(){try{return this.windowState=await rs(),{success:!0,windowState:this.windowState}}catch(t){return console.error("\u521D\u59CB\u5316\u7A97\u4F53\u72B6\u6001\u5931\u8D25:",t),{success:!1,error:t}}},async toggleWindowSize(){try{this.windowState.isExpanded?await Ji():await Zi();const t=await rs();return this.windowState=t,{success:!0,windowState:t}}catch(t){return console.error("\u5207\u6362\u7A97\u4F53\u5927\u5C0F\u5931\u8D25:",t),{success:!1,message:`\u5207\u6362\u7A97\u4F53\u5927\u5C0F\u5931\u8D25: ${t}`}}},async toggleWindowPosition(){try{const t=this.windowState.position==="left"?"right":"left";return await Xi(t),this.windowState.position=t,{success:!0,position:t}}catch(t){return console.error("\u5207\u6362\u7A97\u4F53\u4F4D\u7F6E\u5931\u8D25:",t),{success:!1,message:`\u5207\u6362\u7A97\u4F53\u4F4D\u7F6E\u5931\u8D25: ${t}`}}},async minimizeWindow(){try{return await Yi(),{success:!0}}catch(t){return console.error("\u6700\u5C0F\u5316\u7A97\u4F53\u5931\u8D25:",t),{success:!1,message:`\u6700\u5C0F\u5316\u7A97\u4F53\u5931\u8D25: ${t}`}}},async setAlwaysOnTop(t=!0){try{return await Cr(t),{success:!0}}catch(e){return console.error("\u8BBE\u7F6E\u7A97\u4F53\u7F6E\u9876\u5931\u8D25:",e),{success:!1,error:e}}},async updateCropSettings(t){try{return await tn(t),await this.loadConfig(),{success:!0,message:"\u88C1\u526A\u8BBE\u7F6E\u66F4\u65B0\u6210\u529F"}}catch(e){return console.error("\u66F4\u65B0\u88C1\u526A\u8BBE\u7F6E\u5931\u8D25:",e),{success:!1,message:`\u66F4\u65B0\u88C1\u526A\u8BBE\u7F6E\u5931\u8D25: ${e}`}}},async updateNotificationMode(){try{return await window.go.main.App.UpdateNotificationMode(this.useSystemNotification),{success:!0,message:this.useSystemNotification?"\u5DF2\u5207\u6362\u5230\u7CFB\u7EDF\u7EA7\u901A\u77E5\u6A21\u5F0F":"\u5DF2\u5207\u6362\u5230\u5E94\u7528\u5185\u901A\u77E5\u6A21\u5F0F"}}catch(t){return console.error("\u66F4\u65B0\u901A\u77E5\u6A21\u5F0F\u5931\u8D25:",t),{success:!1,message:`\u66F4\u65B0\u901A\u77E5\u6A21\u5F0F\u5931\u8D25: ${t}`}}},hasConfigChanged(t){if(!this.config||!t)return!0;const e=this.config.SiteInfo||{},s=t.SiteInfo||{};return e.SiteID!==s.SiteID||e.SiteName!==s.SiteName||e.SiteType!==s.SiteType||e.ParentOrg!==s.ParentOrg},startConfigWatcher(){console.log("\u914D\u7F6E\u76D1\u542C\u5668\u5DF2\u542F\u52A8\uFF08\u4EC5\u4E8B\u4EF6\u76D1\u542C\u6A21\u5F0F\uFF09")},stopConfigWatcher(){this.configWatcherTimer&&(clearInterval(this.configWatcherTimer),this.configWatcherTimer=null)}}}),Cu=Qi("notification",{state:()=>({notification:{show:!1,message:"",type:"info",duration:3e3},notificationHistory:[],useSystemNotification:!0,notificationCount:0}),getters:{currentNotification:t=>t.notification,hasActiveNotification:t=>t.notification.show,recentNotifications:t=>t.notificationHistory.slice(-10).reverse(),unreadCount:t=>t.notificationHistory.filter(e=>!e.read).length},actions:{showNotification(t,e="info",s=3e3){return this.notification={show:!0,message:t,type:e,duration:s},this.addToHistory(t,e),s>0&&setTimeout(()=>{this.hideNotification()},s),{success:!0,message:`\u901A\u77E5\u5DF2\u663E\u793A: ${t}`}},showSuccess(t,e=3e3){return this.showNotification(t,"success",e)},showError(t,e=5e3){return this.showNotification(t,"error",e)},showWarning(t,e=4e3){return this.showNotification(t,"warning",e)},showInfo(t,e=3e3){return this.showNotification(t,"info",e)},hideNotification(){return this.notification.show=!1,{success:!0}},addToHistory(t,e){const s={id:++this.notificationCount,message:t,type:e,timestamp:new Date,read:!1};this.notificationHistory.push(s),this.notificationHistory.length>100&&(this.notificationHistory=this.notificationHistory.slice(-50))},markAsRead(t){const e=this.notificationHistory.find(s=>s.id===t);return e?(e.read=!0,{success:!0}):{success:!1,message:"\u901A\u77E5\u4E0D\u5B58\u5728"}},markAllAsRead(){return this.notificationHistory.forEach(t=>{t.read=!0}),{success:!0,message:"\u6240\u6709\u901A\u77E5\u5DF2\u6807\u8BB0\u4E3A\u5DF2\u8BFB"}},clearHistory(){return this.notificationHistory=[],this.notificationCount=0,{success:!0,message:"\u901A\u77E5\u5386\u53F2\u5DF2\u6E05\u7A7A"}},updateNotificationMode(t){return this.useSystemNotification=t,{success:!0,message:t?"\u5DF2\u5207\u6362\u5230\u7CFB\u7EDF\u7EA7\u901A\u77E5\u6A21\u5F0F":"\u5DF2\u5207\u6362\u5230\u5E94\u7528\u5185\u901A\u77E5\u6A21\u5F0F"}},handleNotificationShown(t){if(t&&t.message){const e=t.type||"info",s=t.duration||3e3;return this.showNotification(t.message,e,s),{success:!0,message:`\u5904\u7406\u540E\u7AEF\u901A\u77E5: ${t.message}`}}return{success:!1,message:"\u65E0\u6548\u7684\u901A\u77E5\u6570\u636E"}},showOperationStatus(t,e=!0){const s=e?"success":"error",i=e?2e3:4e3;return this.showNotification(t,s,i)},showLoading(t="\u52A0\u8F7D\u4E2D..."){return this.showNotification(t,"info",0)},hideLoading(){return this.hideNotification()},showNetworkError(t){const e=(t==null?void 0:t.message)||"\u7F51\u7EDC\u8FDE\u63A5\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8BBE\u7F6E";return this.showError(e,5e3)},showApiError(t,e="\u64CD\u4F5C"){var i,n;let s=`${e}\u5931\u8D25`;return(n=(i=t==null?void 0:t.response)==null?void 0:i.data)!=null&&n.message?s+=`: ${t.response.data.message}`:t!=null&&t.message&&(s+=`: ${t.message}`),this.showError(s,5e3)},showValidationError(t){if(Array.isArray(t)){const e=t.join("; ");return this.showError(`\u9A8C\u8BC1\u5931\u8D25: ${e}`,4e3)}else if(typeof t=="string")return this.showError(`\u9A8C\u8BC1\u5931\u8D25: ${t}`,4e3);return this.showError("\u6570\u636E\u9A8C\u8BC1\u5931\u8D25",4e3)},showPermissionError(t="\u6267\u884C\u6B64\u64CD\u4F5C"){return this.showError(`\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5${t}`,4e3)},showConfigUpdate(t="\u914D\u7F6E"){return this.showSuccess(`${t}\u5DF2\u66F4\u65B0`,2e3)},showDataSync(t="\u6570\u636E",e=!0){return e?this.showSuccess(`${t}\u540C\u6B65\u6210\u529F`,2e3):this.showError(`${t}\u540C\u6B65\u5931\u8D25`,4e3)}}});Hi("checking");const U={SUCCESS:"success",ERROR:"error",WARNING:"warning",INFO:"info"},Eu={TOP_RIGHT:"top-right",TOP_LEFT:"top-left",TOP_CENTER:"top-center",BOTTOM_RIGHT:"bottom-right",BOTTOM_LEFT:"bottom-left",BOTTOM_CENTER:"bottom-center"},gt={[U.SUCCESS]:5e3,[U.ERROR]:8e3,[U.WARNING]:6e3,[U.INFO]:5e3},Nr={maxToasts:5,defaultDuration:6e3,position:Eu.TOP_RIGHT,enableQueue:!0,retryAttempts:10,retryDelay:100,debug:!1,animation:{enter:"toast-enter",leave:"toast-leave",duration:400},styling:{borderRadius:"12px",backdropBlur:"20px",shadow:"0 8px 32px rgba(0, 0, 0, 0.12)",minWidth:"320px",maxWidth:"90vw"},accessibility:{announceToScreenReader:!0,focusOnShow:!1,keyboardDismiss:!0},behavior:{pauseOnHover:!0,closeOnClick:!1,showCloseButton:!0,showProgress:!1,stackNewest:"top"}},It={OPERATION_SUCCESS:{type:U.SUCCESS,title:"\u64CD\u4F5C\u6210\u529F",duration:gt[U.SUCCESS]},OPERATION_ERROR:{type:U.ERROR,title:"\u64CD\u4F5C\u5931\u8D25",duration:gt[U.ERROR]},NETWORK_ERROR:{type:U.ERROR,title:"\u7F51\u7EDC\u9519\u8BEF",message:"\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5\u540E\u91CD\u8BD5",duration:gt[U.ERROR]},PERMISSION_ERROR:{type:U.WARNING,title:"\u6743\u9650\u4E0D\u8DB3",message:"\u60A8\u6CA1\u6709\u6267\u884C\u6B64\u64CD\u4F5C\u7684\u6743\u9650",duration:gt[U.WARNING]},SAVE_SUCCESS:{type:U.SUCCESS,title:"\u4FDD\u5B58\u6210\u529F",message:"\u6570\u636E\u5DF2\u6210\u529F\u4FDD\u5B58",duration:gt[U.SUCCESS]},LOAD_ERROR:{type:U.ERROR,title:"\u52A0\u8F7D\u5931\u8D25",message:"\u6570\u636E\u52A0\u8F7D\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5",duration:gt[U.ERROR]},SYSTEM_MAINTENANCE:{type:U.INFO,title:"\u7CFB\u7EDF\u7EF4\u62A4",message:"\u7CFB\u7EDF\u6B63\u5728\u7EF4\u62A4\u4E2D\uFF0C\u90E8\u5206\u529F\u80FD\u53EF\u80FD\u53D7\u9650",duration:1e4},UPLOAD_SUCCESS:{type:U.SUCCESS,title:"\u4E0A\u4F20\u6210\u529F",duration:gt[U.SUCCESS]},UPLOAD_ERROR:{type:U.ERROR,title:"\u4E0A\u4F20\u5931\u8D25",duration:gt[U.ERROR]},CONNECTION_LOST:{type:U.WARNING,title:"\u8FDE\u63A5\u65AD\u5F00",message:"\u4E0E\u670D\u52A1\u5668\u7684\u8FDE\u63A5\u5DF2\u65AD\u5F00\uFF0C\u6B63\u5728\u5C1D\u8BD5\u91CD\u8FDE...",duration:0},CONNECTION_RESTORED:{type:U.SUCCESS,title:"\u8FDE\u63A5\u6062\u590D",message:"\u4E0E\u670D\u52A1\u5668\u7684\u8FDE\u63A5\u5DF2\u6062\u590D",duration:gt[U.SUCCESS]}},Lt={MRI_OPERATION:{SCAN_START:{type:U.INFO,title:"\u626B\u63CF\u5F00\u59CB",message:"\u78C1\u5171\u632F\u626B\u63CF\u5DF2\u5F00\u59CB\uFF0C\u8BF7\u4FDD\u6301\u9759\u6B62",duration:8e3,showProgress:!0},SCAN_COMPLETE:{type:U.SUCCESS,title:"\u626B\u63CF\u5B8C\u6210",message:"\u78C1\u5171\u632F\u626B\u63CF\u5DF2\u5B8C\u6210",duration:gt[U.SUCCESS]},SCAN_ERROR:{type:U.ERROR,title:"\u626B\u63CF\u5F02\u5E38",message:"\u626B\u63CF\u8FC7\u7A0B\u4E2D\u53D1\u751F\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u6280\u672F\u4EBA\u5458",duration:0},PATIENT_MOVEMENT:{type:U.WARNING,title:"\u68C0\u6D4B\u5230\u79FB\u52A8",message:"\u68C0\u6D4B\u5230\u60A3\u8005\u79FB\u52A8\uFF0C\u53EF\u80FD\u5F71\u54CD\u626B\u63CF\u8D28\u91CF",duration:gt[U.WARNING]}},PATIENT_MANAGEMENT:{REGISTRATION_SUCCESS:{type:U.SUCCESS,title:"\u767B\u8BB0\u6210\u529F",message:"\u60A3\u8005\u4FE1\u606F\u5DF2\u6210\u529F\u767B\u8BB0",duration:gt[U.SUCCESS]},REGISTRATION_ERROR:{type:U.ERROR,title:"\u767B\u8BB0\u5931\u8D25",duration:gt[U.ERROR]},INFO_UPDATE_SUCCESS:{type:U.SUCCESS,title:"\u4FE1\u606F\u66F4\u65B0\u6210\u529F",duration:gt[U.SUCCESS]},DUPLICATE_PATIENT:{type:U.WARNING,title:"\u60A3\u8005\u4FE1\u606F\u91CD\u590D",message:"\u68C0\u6D4B\u5230\u91CD\u590D\u7684\u60A3\u8005\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4",duration:gt[U.WARNING]}},DEVICE_STATUS:{DEVICE_READY:{type:U.SUCCESS,title:"\u8BBE\u5907\u5C31\u7EEA",message:"\u78C1\u5171\u632F\u8BBE\u5907\u5DF2\u51C6\u5907\u5C31\u7EEA",duration:gt[U.SUCCESS]},DEVICE_ERROR:{type:U.ERROR,title:"\u8BBE\u5907\u6545\u969C",message:"\u8BBE\u5907\u68C0\u6D4B\u5230\u6545\u969C\uFF0C\u8BF7\u8054\u7CFB\u7EF4\u62A4\u4EBA\u5458",duration:0},DEVICE_MAINTENANCE:{type:U.INFO,title:"\u8BBE\u5907\u7EF4\u62A4",message:"\u8BBE\u5907\u6B63\u5728\u8FDB\u884C\u4F8B\u884C\u7EF4\u62A4",duration:gt[U.INFO]},CALIBRATION_COMPLETE:{type:U.SUCCESS,title:"\u6821\u51C6\u5B8C\u6210",message:"\u8BBE\u5907\u6821\u51C6\u5DF2\u5B8C\u6210",duration:gt[U.SUCCESS]}}};function Pr(t,e={}){if(typeof t=="string"){const s=It[t]||Lt.MRI_OPERATION[t]||Lt.PATIENT_MANAGEMENT[t]||Lt.DEVICE_STATUS[t];return s?{...s,...e}:(console.warn(`Toast\u6A21\u677F '${t}' \u4E0D\u5B58\u5728`),{...It.OPERATION_SUCCESS,...e})}return{...t,...e}}const Ks=(t,e)=>{const s=t.__vccOpts||t;for(const[i,n]of e)s[i]=n;return s},bu={name:"ToastIcon",props:{type:{type:String,default:"info",validator:t=>["success","error","warning","info","loading"].includes(t)}}},Nu={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Pu={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Ru={key:2,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Au={key:3,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Iu={key:4,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",class:"animate-spin"},Ou={key:5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"};function xu(t,e,s,i,n,o){return D(),L("div",{class:Gt(["toast-icon",`toast-icon--${s.type}`])},[s.type==="success"?(D(),L("svg",Nu,e[0]||(e[0]=[d("path",{d:"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):s.type==="error"?(D(),L("svg",Pu,e[1]||(e[1]=[d("path",{d:"M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):s.type==="warning"?(D(),L("svg",Ru,e[2]||(e[2]=[d("path",{d:"M12 9V13M12 17H12.01M10.29 3.86L1.82 18A2 2 0 0 0 3.24 21H20.76A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):s.type==="info"?(D(),L("svg",Au,e[3]||(e[3]=[d("path",{d:"M12 16V12M12 8H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):s.type==="loading"?(D(),L("svg",Iu,e[4]||(e[4]=[d("path",{d:"M21 12A9 9 0 1 1 12 3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(D(),L("svg",Ou,e[5]||(e[5]=[d("path",{d:"M13 16H12V12H11M12 8H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))],2)}const Mu=Ks(bu,[["render",xu],["__scopeId","data-v-01501397"]]);const Du={render(){return Cn("svg",{viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg"},[Cn("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"})])}},Lu={name:"ToastNotification",components:{ToastIcon:Mu,CloseIcon:Du},props:{config:{type:Object,default:()=>({})}},data(){return{toasts:[],toastQueue:[],nextId:1,wailsRuntimeStatus:"checking",eventListenerStatus:"not-set",eventListenerCleanup:null,retryTimeouts:[],taskManagerStatus:{active_tasks:[],stats:{total_submitted:0,active_count:0,completed_tasks:0,failed_tasks:0,cancelled_tasks:0,queue_size:0}},taskStatusTimer:null}},computed:{mergedConfig(){return{...Nr,...this.config}},isDevelopment(){return!1}},mounted(){this.log("\u7EC4\u4EF6\u5DF2\u6302\u8F7D"),this.setupEventListener(),this.processQueue()},beforeUnmount(){this.cleanup(),this.stopTaskStatusUpdates()},methods:{log(t,e=null){(this.mergedConfig.debug||this.isDevelopment)&&console.log(`[ToastNotification] ${t}`,e||"")},error(t,e=null){console.error(`[ToastNotification] ${t}`,e||"")},setupEventListener(t=0){const e=this.mergedConfig.retryAttempts,s=this.mergedConfig.retryDelay;try{if(window.runtime&&window.runtime.EventsOn)this.wailsRuntimeStatus="available",this.log("Wails runtime \u53EF\u7528\uFF0C\u8BBE\u7F6E showToastNotification \u4E8B\u4EF6\u76D1\u542C\u5668"),window.runtime.EventsOn("showToastNotification",this.handleBackendNotification),this.eventListenerStatus="active",this.log("\u4E8B\u4EF6\u76D1\u542C\u5668\u8BBE\u7F6E\u5B8C\u6210"),this.clearRetryTimeouts();else if(t<e){this.wailsRuntimeStatus="retrying",this.log(`Wails runtime \u5C1A\u672A\u53EF\u7528\uFF0C${s}ms\u540E\u91CD\u8BD5 (${t+1}/${e})`);const i=setTimeout(()=>{this.setupEventListener(t+1)},s);this.retryTimeouts.push(i)}else this.wailsRuntimeStatus="failed",this.eventListenerStatus="failed",this.error("Wails runtime \u5728\u6700\u5927\u91CD\u8BD5\u6B21\u6570\u540E\u4ECD\u4E0D\u53EF\u7528\uFF01"),this.showToast({title:"\u7CFB\u7EDF\u8B66\u544A",message:"Toast\u901A\u77E5\u7CFB\u7EDF\u521D\u59CB\u5316\u5931\u8D25\uFF0C\u90E8\u5206\u529F\u80FD\u53EF\u80FD\u53D7\u9650",type:"warning",duration:8e3})}catch(i){this.error("\u8BBE\u7F6E\u4E8B\u4EF6\u76D1\u542C\u5668\u65F6\u53D1\u751F\u9519\u8BEF:",i),this.wailsRuntimeStatus="error",this.eventListenerStatus="error"}},clearRetryTimeouts(){this.retryTimeouts.forEach(t=>clearTimeout(t)),this.retryTimeouts=[]},handleBackendNotification(t){try{if(this.log("\u6536\u5230\u540E\u7AEF\u901A\u77E5\u4E8B\u4EF6:",t),!t||typeof t!="object"){this.error("\u6536\u5230\u65E0\u6548\u7684\u540E\u7AEF\u901A\u77E5\u6570\u636E:",t);return}const{title:e,message:s,type:i,duration:n,showProgress:o,progress:r}=t;this.log("\u89E3\u6790\u540E\u7684\u6570\u636E:",{title:e,message:s,type:i,duration:n,showProgress:o,progress:r}),this.showToast({title:e||"\u901A\u77E5",message:s||"",type:i||"info",duration:n!==void 0?n:this.mergedConfig.defaultDuration,showProgress:o||!1,progress:r||0})}catch(e){this.error("\u5904\u7406\u540E\u7AEF\u901A\u77E5\u65F6\u53D1\u751F\u9519\u8BEF:",e)}},showToast(t){try{if(console.log("[ToastNotification] showToast\u88AB\u8C03\u7528\uFF0C\u53C2\u6570:",t),console.log("[ToastNotification] \u5F53\u524DmergedConfig:",this.mergedConfig),console.log("[ToastNotification] \u5F53\u524Dtoasts\u6570\u7EC4\u957F\u5EA6:",this.toasts.length),this.log("showToast \u88AB\u8C03\u7528\uFF0C\u53C2\u6570:",t),!t||typeof t!="object")return this.error("showToast \u6536\u5230\u65E0\u6548\u53C2\u6570:",t),null;const e={id:this.nextId++,title:t.title||"\u901A\u77E5",message:t.message||"",type:t.type||"info",duration:t.duration!==void 0?t.duration:this.mergedConfig.defaultDuration,showProgress:t.showProgress||!1,progress:t.progress||0,closable:t.closable!==!1,timestamp:Date.now()};return console.log("[ToastNotification] \u521B\u5EFA\u7684Toast\u5BF9\u8C61:",e),this.log("\u521B\u5EFA\u7684Toast\u5BF9\u8C61:",e),this.mergedConfig.enableQueue&&this.toasts.length>=this.mergedConfig.maxToasts?(console.log("[ToastNotification] \u8FBE\u5230\u6700\u5927\u663E\u793A\u6570\u91CF\uFF0C\u6DFB\u52A0\u5230\u961F\u5217"),this.toastQueue.push(e),this.log("Toast\u5DF2\u52A0\u5165\u961F\u5217\uFF0C\u5F53\u524D\u961F\u5217\u957F\u5EA6:",this.toastQueue.length)):(console.log("[ToastNotification] \u672A\u8FBE\u5230\u6700\u5927\u663E\u793A\u6570\u91CF\uFF0C\u76F4\u63A5\u663E\u793A"),this.addToastToDisplay(e)),console.log("[ToastNotification] showToast\u6267\u884C\u5B8C\u6210\uFF0C\u8FD4\u56DEtoast.id"),e.id}catch(e){return this.error("\u663E\u793AToast\u65F6\u53D1\u751F\u9519\u8BEF:",e),null}},addToastToDisplay(t){console.log("[ToastNotification] addToastToDisplay\u88AB\u8C03\u7528\uFF0Ctoast:",t),console.log("[ToastNotification] \u6DFB\u52A0\u524Dtoasts\u6570\u7EC4\u957F\u5EA6:",this.toasts.length),this.toasts.push(t),console.log("[ToastNotification] \u6DFB\u52A0\u540Etoasts\u6570\u7EC4\u957F\u5EA6:",this.toasts.length),console.log("[ToastNotification] \u6DFB\u52A0\u540Etoasts\u6570\u7EC4\u5185\u5BB9:",this.toasts),this.log("\u5F53\u524DToast\u5217\u8868:",this.toasts),this.$forceUpdate(),this.$emit("notification-shown",{id:t.id,message:t.message,type:t.type,timestamp:t.timestamp}),t.duration>0&&setTimeout(()=>{this.removeToast(t.id)},t.duration)},processQueue(){if(this.toastQueue.length>0&&this.toasts.length<this.mergedConfig.maxToasts){const t=this.toastQueue.shift();this.addToastToDisplay(t),setTimeout(()=>this.processQueue(),100)}},updateToastProgress(t,e,s){try{const i=this.toasts.find(n=>n.id===t);i?(i.progress=Math.min(100,Math.max(0,e)),s!==void 0&&(i.message=s),this.log(`\u66F4\u65B0Toast ${t} \u8FDB\u5EA6:`,{progress:i.progress,message:s})):this.log(`\u672A\u627E\u5230ID\u4E3A ${t} \u7684Toast`)}catch(i){this.error("\u66F4\u65B0Toast\u8FDB\u5EA6\u65F6\u53D1\u751F\u9519\u8BEF:",i)}},removeToast(t){try{const e=this.toasts.findIndex(s=>s.id===t);if(e>-1){const s=this.toasts.splice(e,1)[0];this.log("\u79FB\u9664Toast:",s),this.$emit("notification-removed",{id:s.id,type:s.type}),this.processQueue()}}catch(e){this.error("\u79FB\u9664Toast\u65F6\u53D1\u751F\u9519\u8BEF:",e)}},clearAllToasts(){try{const t=this.toasts.length;this.toasts=[],this.toastQueue=[],this.log(`\u6E05\u9664\u4E86 ${t} \u4E2AToast\u548C\u961F\u5217`),this.$emit("all-notifications-cleared")}catch(t){this.error("\u6E05\u9664\u6240\u6709Toast\u65F6\u53D1\u751F\u9519\u8BEF:",t)}},showSuccess(t,e,s){return this.showToast({title:t,message:e,type:"success",duration:s})},showError(t,e,s){return this.showToast({title:t,message:e,type:"error",duration:s})},showWarning(t,e,s){return this.showToast({title:t,message:e,type:"warning",duration:s})},showInfo(t,e,s){return this.showToast({title:t,message:e,type:"info",duration:s})},showProgress(t,e,s=0){return this.showToast({title:t,message:e,type:"info",duration:0,showProgress:!0,progress:s})},healthCheck(){return{wailsRuntimeStatus:this.wailsRuntimeStatus,eventListenerStatus:this.eventListenerStatus,activeToasts:this.toasts.length,queuedToasts:this.toastQueue.length,config:this.mergedConfig}},cleanup(){try{this.log("\u5F00\u59CB\u6E05\u7406\u8D44\u6E90"),this.clearRetryTimeouts(),this.eventListenerCleanup&&this.eventListenerCleanup(),this.toasts=[],this.toastQueue=[],this.log("\u8D44\u6E90\u6E05\u7406\u5B8C\u6210")}catch(t){this.error("\u6E05\u7406\u8D44\u6E90\u65F6\u53D1\u751F\u9519\u8BEF:",t)}},startTaskStatusUpdates(){this.isDevelopment&&this.mergedConfig.debug&&(this.log("\u542F\u52A8\u4EFB\u52A1\u72B6\u6001\u66F4\u65B0\u5B9A\u65F6\u5668"),this.updateTaskStatus(),this.taskStatusTimer=setInterval(()=>{this.updateTaskStatus()},2e3))},stopTaskStatusUpdates(){this.taskStatusTimer&&(clearInterval(this.taskStatusTimer),this.taskStatusTimer=null,this.log("\u4EFB\u52A1\u72B6\u6001\u66F4\u65B0\u5B9A\u65F6\u5668\u5DF2\u505C\u6B62"))},async updateTaskStatus(){try{if(this.log("\u5F00\u59CB\u66F4\u65B0\u4EFB\u52A1\u72B6\u6001"),window.go&&window.go.main&&window.go.main.App&&window.go.main.App.GetTaskManagerStatus){this.log("\u8C03\u7528GetTaskManagerStatus\u65B9\u6CD5");const t=await window.go.main.App.GetTaskManagerStatus();this.log("\u83B7\u53D6\u5230\u4EFB\u52A1\u72B6\u6001:",t),this.taskManagerStatus=t,this.log("\u4EFB\u52A1\u72B6\u6001\u5DF2\u66F4\u65B0:",this.taskManagerStatus)}else this.log("Wails API\u4E0D\u53EF\u7528\uFF0C\u8DF3\u8FC7\u4EFB\u52A1\u72B6\u6001\u66F4\u65B0")}catch(t){this.error("\u83B7\u53D6\u4EFB\u52A1\u72B6\u6001\u5931\u8D25:",t)}},formatTime(t){if(!t)return"N/A";try{return new Date(t).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch{return"Invalid Date"}}}},ku={class:"toast-container"},Uu={class:"toast-content"},$u={class:"toast-title"},Bu={class:"toast-message"},Fu={key:0,class:"toast-progress"},Wu={class:"progress-bar"},Hu={class:"progress-text"},ju=["onClick","aria-label"],zu={key:0,class:"debug-panel"},Vu={class:"task-manager-section"},Gu={class:"task-stats"},Ku={class:"active-tasks-section"},Qu={key:0,class:"active-tasks-container"},qu={class:"task-header"},Yu={class:"task-id"},Ju={class:"task-type"},Zu={class:"task-status"},Xu={class:"task-details"},tf={key:1,class:"no-tasks"};function ef(t,e,s,i,n,o){const r=gi("ToastIcon"),a=gi("CloseIcon");return D(),L("div",ku,[ct(ic,{name:"toast",tag:"div",class:"toast-list"},{default:Ao(()=>[(D(!0),L(dt,null,Qe(n.toasts,l=>(D(),L("div",{key:l.id,class:Gt(["toast",`toast-${l.type}`,{"toast-with-progress":l.showProgress}])},[ct(r,{type:l.type},null,8,["type"]),d("div",Uu,[d("div",$u,R(l.title),1),d("div",Bu,R(l.message),1),l.showProgress?(D(),L("div",Fu,[d("div",Wu,[d("div",{class:"progress-fill",style:_e({width:l.progress+"%"})},null,4)]),d("span",Hu,R(l.progress)+"%",1)])):wt("",!0)]),l.closable!==!1?(D(),L("button",{key:0,onClick:h=>o.removeToast(l.id),class:"toast-close","aria-label":`\u5173\u95ED${l.title}\u901A\u77E5`},[ct(a)],8,ju)):wt("",!0),l.duration>0?(D(),L("div",{key:1,class:"toast-timer",style:_e({animationDuration:l.duration+"ms"})},null,4)):wt("",!0)],2))),128))]),_:1}),o.mergedConfig.debug&&o.isDevelopment?(D(),L("div",zu,[d("div",Vu,[e[6]||(e[6]=d("h4",null,"\u4EFB\u52A1\u7BA1\u7406\u5668\u72B6\u6001",-1)),d("div",Gu,[d("p",null,"\u6D3B\u8DC3\u4EFB\u52A1: "+R(n.taskManagerStatus.stats.active_count),1),d("p",null,"\u961F\u5217\u5927\u5C0F: "+R(n.taskManagerStatus.stats.queue_size),1),d("p",null,"\u603B\u63D0\u4EA4: "+R(n.taskManagerStatus.stats.total_submitted),1),d("p",null,"\u5DF2\u5B8C\u6210: "+R(n.taskManagerStatus.stats.completed_tasks),1),d("p",null,"\u5931\u8D25\u4EFB\u52A1: "+R(n.taskManagerStatus.stats.failed_tasks),1),d("p",null,"\u5DF2\u53D6\u6D88: "+R(n.taskManagerStatus.stats.cancelled_tasks),1)]),d("div",Ku,[e[5]||(e[5]=d("h5",null,"\u6D3B\u8DC3\u4EFB\u52A1\u5217\u8868",-1)),n.taskManagerStatus.active_tasks.length>0?(D(),L("div",Qu,[(D(!0),L(dt,null,Qe(n.taskManagerStatus.active_tasks,l=>(D(),L("div",{key:l.id,class:"task-item"},[d("div",qu,[d("span",Yu,"ID: "+R(l.id),1),d("span",Ju,R(l.type),1),d("span",Zu,R(l.status),1)]),d("div",Xu,[d("p",null,[e[0]||(e[0]=d("strong",null,"\u7528\u6237:",-1)),Zt(" "+R(l.user_name||"N/A"),1)]),d("p",null,[e[1]||(e[1]=d("strong",null,"\u6A21\u5F0F:",-1)),Zt(" "+R(l.mode||"N/A"),1)]),d("p",null,[e[2]||(e[2]=d("strong",null,"\u521B\u5EFA\u65F6\u95F4:",-1)),Zt(" "+R(o.formatTime(l.created_at)),1)]),d("p",null,[e[3]||(e[3]=d("strong",null,"\u5F00\u59CB\u65F6\u95F4:",-1)),Zt(" "+R(o.formatTime(l.started_at)),1)]),d("p",null,[e[4]||(e[4]=d("strong",null,"\u8FD0\u884C\u65F6\u957F:",-1)),Zt(" "+R(l.duration),1)])])]))),128))])):(D(),L("p",tf,"\u5F53\u524D\u65E0\u6D3B\u8DC3\u4EFB\u52A1"))])])])):wt("",!0)])}const sf=Ks(Lu,[["render",ef],["__scopeId","data-v-5047fa6c"]]);class nf{constructor(){this.errorTypes={NETWORK:"network",VALIDATION:"validation",BUSINESS:"business",SYSTEM:"system",PERMISSION:"permission"},this.errorCodes={NETWORK_TIMEOUT:4e3,NETWORK_CONNECTION:4001,NETWORK_API:4002,BUSINESS_VALIDATION:2e3,BUSINESS_NOT_FOUND:2001,BUSINESS_CONFLICT:2002,SYSTEM_INTERNAL:1e3,SYSTEM_TIMEOUT:1001,SYSTEM_OVERLOAD:1002,SCREENSHOT_CAPTURE:3e3,SCREENSHOT_SAVE:3001,SCREENSHOT_PROCESS:3002,OCR_PROCESS:3100,OCR_TIMEOUT:3101,OCR_INVALID_IMG:3102},this.retryableErrors=[this.errorCodes.NETWORK_TIMEOUT,this.errorCodes.NETWORK_CONNECTION,this.errorCodes.SYSTEM_TIMEOUT,this.errorCodes.OCR_TIMEOUT]}handleError(e,s="",i={}){const n=this.parseError(e,s);return this.logError(n),i.showToUser!==!1&&this.showUserError(n,i),this.emitErrorEvent(n),n}parseError(e,s){var n,o;const i={id:this.generateErrorId(),timestamp:Date.now(),context:s,type:this.errorTypes.SYSTEM,code:this.errorCodes.SYSTEM_INTERNAL,message:"\u672A\u77E5\u9519\u8BEF",details:"",originalError:e,isRetryable:!1,userMessage:"\u64CD\u4F5C\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5"};return e&&(e.code&&e.message?(i.code=e.code,i.message=e.message,i.details=e.details||"",i.type=this.determineErrorType(e.code),i.isRetryable=this.retryableErrors.includes(e.code),i.userMessage=this.getUserMessage(e.code,e.message)):e.name==="NetworkError"||((n=e.message)==null?void 0:n.includes("fetch"))?(i.type=this.errorTypes.NETWORK,i.code=this.errorCodes.NETWORK_CONNECTION,i.message="\u7F51\u7EDC\u8FDE\u63A5\u5931\u8D25",i.isRetryable=!0,i.userMessage="\u7F51\u7EDC\u8FDE\u63A5\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8BBE\u7F6E\u540E\u91CD\u8BD5"):e.name==="TimeoutError"||((o=e.message)==null?void 0:o.includes("timeout"))?(i.type=this.errorTypes.NETWORK,i.code=this.errorCodes.NETWORK_TIMEOUT,i.message="\u8BF7\u6C42\u8D85\u65F6",i.isRetryable=!0,i.userMessage="\u8BF7\u6C42\u8D85\u65F6\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5"):e.name==="ValidationError"?(i.type=this.errorTypes.VALIDATION,i.code=this.errorCodes.BUSINESS_VALIDATION,i.message=e.message||"\u6570\u636E\u9A8C\u8BC1\u5931\u8D25",i.userMessage=`\u8F93\u5165\u6570\u636E\u6709\u8BEF\uFF1A${e.message}`):(i.message=e.message||e.toString(),i.details=e.stack||"")),i}determineErrorType(e){return e>=4e3&&e<5e3?this.errorTypes.NETWORK:e>=2e3&&e<3e3?this.errorTypes.BUSINESS:e>=1e3&&e<2e3?this.errorTypes.SYSTEM:this.errorTypes.SYSTEM}getUserMessage(e,s){return{[this.errorCodes.NETWORK_TIMEOUT]:"\u7F51\u7EDC\u8BF7\u6C42\u8D85\u65F6\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5",[this.errorCodes.NETWORK_CONNECTION]:"\u7F51\u7EDC\u8FDE\u63A5\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8BBE\u7F6E",[this.errorCodes.BUSINESS_VALIDATION]:"\u8F93\u5165\u7684\u6570\u636E\u683C\u5F0F\u4E0D\u6B63\u786E",[this.errorCodes.BUSINESS_NOT_FOUND]:"\u8BF7\u6C42\u7684\u8D44\u6E90\u4E0D\u5B58\u5728",[this.errorCodes.BUSINESS_CONFLICT]:"\u64CD\u4F5C\u51B2\u7A81\uFF0C\u8BF7\u5237\u65B0\u540E\u91CD\u8BD5",[this.errorCodes.SYSTEM_INTERNAL]:"\u7CFB\u7EDF\u5185\u90E8\u9519\u8BEF\uFF0C\u8BF7\u8054\u7CFB\u6280\u672F\u652F\u6301",[this.errorCodes.SYSTEM_OVERLOAD]:"\u7CFB\u7EDF\u7E41\u5FD9\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5",[this.errorCodes.SCREENSHOT_CAPTURE]:"\u622A\u56FE\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5",[this.errorCodes.SCREENSHOT_SAVE]:"\u622A\u56FE\u4FDD\u5B58\u5931\u8D25",[this.errorCodes.SCREENSHOT_PROCESS]:"\u622A\u56FE\u5904\u7406\u5931\u8D25",[this.errorCodes.OCR_PROCESS]:"OCR\u8BC6\u522B\u5931\u8D25",[this.errorCodes.OCR_TIMEOUT]:"OCR\u8BC6\u522B\u8D85\u65F6",[this.errorCodes.OCR_INVALID_IMG]:"\u56FE\u7247\u683C\u5F0F\u4E0D\u652F\u6301"}[e]||s||"\u64CD\u4F5C\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5"}logError(e){const s={id:e.id,timestamp:e.timestamp,context:e.context,type:e.type,code:e.code,message:e.message,details:e.details,userAgent:navigator.userAgent,url:window.location.href};console.error(`[${e.id}] ${e.type}:${e.code} - ${e.message}`),this.sendToLogService(s)}showUserError(e,s={}){const{notificationManager:i}=window.app||{};if(i){const n={title:this.getErrorTitle(e.type),message:e.userMessage,type:"error",duration:s.duration||5e3,actions:[]};e.isRetryable&&s.onRetry&&n.actions.push({text:"\u91CD\u8BD5",action:s.onRetry}),i.showNotification(n)}else console.warn("NotificationManager not available, using alert"),alert(e.userMessage)}getErrorTitle(e){return{[this.errorTypes.NETWORK]:"\u7F51\u7EDC\u9519\u8BEF",[this.errorTypes.VALIDATION]:"\u8F93\u5165\u9519\u8BEF",[this.errorTypes.BUSINESS]:"\u4E1A\u52A1\u9519\u8BEF",[this.errorTypes.SYSTEM]:"\u7CFB\u7EDF\u9519\u8BEF",[this.errorTypes.PERMISSION]:"\u6743\u9650\u9519\u8BEF"}[e]||"\u64CD\u4F5C\u5931\u8D25"}showErrorDetails(e){const s={\u9519\u8BEFID:e.id,\u65F6\u95F4:new Date(e.timestamp).toLocaleString(),\u7C7B\u578B:e.type,\u4EE3\u7801:e.code,\u6D88\u606F:e.message,\u4E0A\u4E0B\u6587:e.context,\u8BE6\u60C5:e.details};console.table(s),window.dispatchEvent(new CustomEvent("show-error-details",{detail:e}))}emitErrorEvent(e){window.dispatchEvent(new CustomEvent("app-error",{detail:e}))}async sendToLogService(e){}generateErrorId(){return`err_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}async retry(e,s={}){const{maxAttempts:i=3,delay:n=1e3,backoff:o=1.5,onRetry:r=null}=s;let a;for(let l=1;l<=i;l++)try{return await e()}catch(h){if(a=h,l===i||!this.parseError(h,"retry").isRetryable)break;r&&r(l,h);const p=n*Math.pow(o,l-1);await new Promise(y=>setTimeout(y,p))}throw a}async safeAsync(e,s="",i={}){try{return await e()}catch(n){const o=this.handleError(n,s,i);if(i.retry&&o.isRetryable)return await this.retry(e,i.retry);if(i.throwError!==!1)throw o;return i.defaultValue}}}const Se=new nf;window.addEventListener("error",t=>{Se.handleError(t.error,"global-error")});window.addEventListener("unhandledrejection",t=>{Se.handleError(t.reason,"unhandled-promise")});const of={name:"ErrorBoundary",props:{fallbackTitle:{type:String,default:"\u51FA\u73B0\u4E86\u4E00\u4E2A\u9519\u8BEF"},fallbackMessage:{type:String,default:"\u62B1\u6B49\uFF0C\u7EC4\u4EF6\u6E32\u67D3\u65F6\u51FA\u73B0\u4E86\u9519\u8BEF\u3002\u8BF7\u5C1D\u8BD5\u5237\u65B0\u9875\u9762\u6216\u8054\u7CFB\u6280\u672F\u652F\u6301\u3002"},showDetails:{type:Boolean,default:!1},showReportButton:{type:Boolean,default:!0},autoRetry:{type:Boolean,default:!1},maxRetries:{type:Number,default:3},retryDelay:{type:Number,default:1e3}},data(){return{hasError:!1,error:null,errorInfo:null,retrying:!1,retryCount:0}},computed:{errorTitle(){return this.error&&this.error.userMessage?this.error.title||this.fallbackTitle:this.fallbackTitle},errorMessage(){return this.error&&this.error.userMessage?this.error.userMessage:this.fallbackMessage},errorDetails(){if(!this.error)return null;let t="";return this.error.stack&&(t+=`Stack Trace:
${this.error.stack}

`),this.error.operation&&(t+=`Operation: ${this.error.operation}
`),this.error.timestamp&&(t+=`Timestamp: ${new Date(this.error.timestamp).toLocaleString()}
`),this.error.userAgent&&(t+=`User Agent: ${this.error.userAgent}
`),t||this.error.toString()}},errorCaptured(t,e,s){console.error("ErrorBoundary captured error:",t,s);const i=Se.handleError(t,"component-render",{showToUser:!1,logError:!0});return this.hasError=!0,this.error=i,this.errorInfo=s,this.$emit("error",{error:i,errorInfo:s,component:e}),this.autoRetry&&this.retryCount<this.maxRetries&&this.scheduleRetry(),!1},methods:{async handleRetry(){if(!this.retrying){this.retrying=!0,this.retryCount++;try{await new Promise(t=>setTimeout(t,this.retryDelay)),this.resetError(),this.$emit("retry",{attempt:this.retryCount,error:this.error})}catch(t){console.error("Retry failed:",t),Se.handleError(t,"error-boundary-retry")}finally{this.retrying=!1}}},handleReload(){this.$emit("reload"),window.location.reload()},handleReport(){this.$emit("report",{error:this.error,errorInfo:this.errorInfo}),Se.reportError(this.error,{component:this.$options.name,errorInfo:this.errorInfo})},resetError(){this.hasError=!1,this.error=null,this.errorInfo=null},scheduleRetry(){setTimeout(()=>{this.handleRetry()},this.retryDelay)}},watch:{$route(){this.hasError&&(this.resetError(),this.retryCount=0)}}},rf={class:"error-boundary"},af={key:1,class:"error-container"},lf={class:"error-content"},cf={class:"error-title"},uf={class:"error-message"},ff={key:0,class:"error-details"},df={class:"error-stack"},hf={class:"error-actions"},pf=["disabled"],gf={key:0},mf={key:1};function wf(t,e,s,i,n,o){return D(),L("div",rf,[n.hasError?(D(),L("div",af,[d("div",lf,[e[4]||(e[4]=d("div",{class:"error-icon"},[d("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[d("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z",fill:"#ff4757"})])],-1)),d("h3",cf,R(o.errorTitle),1),d("p",uf,R(o.errorMessage),1),s.showDetails&&o.errorDetails?(D(),L("details",ff,[e[3]||(e[3]=d("summary",null,"\u9519\u8BEF\u8BE6\u60C5",-1)),d("pre",df,R(o.errorDetails),1)])):wt("",!0),d("div",hf,[d("button",{class:"btn btn-primary",onClick:e[0]||(e[0]=(...r)=>o.handleRetry&&o.handleRetry(...r)),disabled:n.retrying},[n.retrying?(D(),L("span",gf,"\u91CD\u8BD5\u4E2D...")):(D(),L("span",mf,"\u91CD\u8BD5"))],8,pf),d("button",{class:"btn btn-secondary",onClick:e[1]||(e[1]=(...r)=>o.handleReload&&o.handleReload(...r))}," \u5237\u65B0\u9875\u9762 "),s.showReportButton?(D(),L("button",{key:0,class:"btn btn-outline",onClick:e[2]||(e[2]=(...r)=>o.handleReport&&o.handleReport(...r))}," \u62A5\u544A\u95EE\u9898 ")):wt("",!0)])])])):Wa(t.$slots,"default",{key:0},void 0,!0)])}const yf=Ks(of,[["render",wf],["__scopeId","data-v-cfea101e"]]);class Sf{constructor(){this.toastComponent=null,this.isInitialized=!1,this.pendingToasts=[],this.eventListeners=new Map}init(e){this.toastComponent=e,this.isInitialized=!0,this.processPendingToasts(),console.log("[ToastManager] \u5DF2\u521D\u59CB\u5316")}checkInitialized(){return!this.isInitialized||!this.toastComponent?(console.warn("[ToastManager] \u5C1A\u672A\u521D\u59CB\u5316\uFF0CToast\u5C06\u88AB\u52A0\u5165\u5F85\u5904\u7406\u961F\u5217"),!1):!0}processPendingToasts(){this.pendingToasts.length>0&&(console.log(`[ToastManager] \u5904\u7406 ${this.pendingToasts.length} \u4E2A\u5F85\u53D1\u9001\u7684Toast`),this.pendingToasts.forEach(({method:e,args:s})=>{this[e](...s)}),this.pendingToasts=[])}addPendingToast(e,s){this.pendingToasts.push({method:e,args:s})}show(e,s={}){if(!this.checkInitialized())return this.addPendingToast("show",[e,s]),null;try{const i=Pr(e,s);return this.toastComponent.showToast(i)}catch(i){return console.error("[ToastManager] \u663E\u793AToast\u65F6\u53D1\u751F\u9519\u8BEF:",i),null}}success(e,s="\u64CD\u4F5C\u6210\u529F",i={}){return this.show(It.OPERATION_SUCCESS,{title:s,message:e,...i})}error(e,s="\u64CD\u4F5C\u5931\u8D25",i={}){return this.show(It.OPERATION_ERROR,{title:s,message:e,...i})}warning(e,s="\u8B66\u544A",i={}){return this.show(It.OPERATION_ERROR,{type:"warning",title:s,message:e,...i})}info(e,s="\u63D0\u793A",i={}){return this.show(It.OPERATION_SUCCESS,{type:"info",title:s,message:e,...i})}progress(e,s="\u5904\u7406\u4E2D",i=0,n={}){return this.show({type:"info",title:s,message:e,duration:0,showProgress:!0,progress:i,...n})}updateProgress(e,s,i){if(!this.checkInitialized()){console.warn("[ToastManager] \u65E0\u6CD5\u66F4\u65B0\u8FDB\u5EA6\uFF0CToast\u7EC4\u4EF6\u672A\u521D\u59CB\u5316");return}try{this.toastComponent.updateToastProgress(e,s,i)}catch(n){console.error("[ToastManager] \u66F4\u65B0\u8FDB\u5EA6\u65F6\u53D1\u751F\u9519\u8BEF:",n)}}remove(e){if(!this.checkInitialized()){console.warn("[ToastManager] \u65E0\u6CD5\u79FB\u9664Toast\uFF0C\u7EC4\u4EF6\u672A\u521D\u59CB\u5316");return}try{this.toastComponent.removeToast(e)}catch(s){console.error("[ToastManager] \u79FB\u9664Toast\u65F6\u53D1\u751F\u9519\u8BEF:",s)}}clear(){if(!this.checkInitialized()){console.warn("[ToastManager] \u65E0\u6CD5\u6E05\u9664Toast\uFF0C\u7EC4\u4EF6\u672A\u521D\u59CB\u5316");return}try{this.toastComponent.clearAllToasts()}catch(e){console.error("[ToastManager] \u6E05\u9664\u6240\u6709Toast\u65F6\u53D1\u751F\u9519\u8BEF:",e)}}getStatus(){if(!this.checkInitialized())return{initialized:!1,error:"Toast\u7EC4\u4EF6\u672A\u521D\u59CB\u5316"};try{return{initialized:!0,...this.toastComponent.healthCheck()}}catch(e){return console.error("[ToastManager] \u83B7\u53D6\u72B6\u6001\u65F6\u53D1\u751F\u9519\u8BEF:",e),{initialized:!0,error:e.message}}}scanStart(e=""){return this.show(Lt.MRI_OPERATION.SCAN_START,{message:e?`${e} \u7684\u78C1\u5171\u632F\u626B\u63CF\u5DF2\u5F00\u59CB\uFF0C\u8BF7\u4FDD\u6301\u9759\u6B62`:"\u78C1\u5171\u632F\u626B\u63CF\u5DF2\u5F00\u59CB\uFF0C\u8BF7\u4FDD\u6301\u9759\u6B62"})}scanComplete(e=""){return this.show(Lt.MRI_OPERATION.SCAN_COMPLETE,{message:e?`${e} \u7684\u78C1\u5171\u632F\u626B\u63CF\u5DF2\u5B8C\u6210`:"\u78C1\u5171\u632F\u626B\u63CF\u5DF2\u5B8C\u6210"})}scanError(e="\u626B\u63CF\u8FC7\u7A0B\u4E2D\u53D1\u751F\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u6280\u672F\u4EBA\u5458"){return this.show(Lt.MRI_OPERATION.SCAN_ERROR,{message:e})}patientMovement(e=""){return this.show(Lt.MRI_OPERATION.PATIENT_MOVEMENT,{message:e?`\u68C0\u6D4B\u5230 ${e} \u79FB\u52A8\uFF0C\u53EF\u80FD\u5F71\u54CD\u626B\u63CF\u8D28\u91CF`:"\u68C0\u6D4B\u5230\u60A3\u8005\u79FB\u52A8\uFF0C\u53EF\u80FD\u5F71\u54CD\u626B\u63CF\u8D28\u91CF"})}registrationSuccess(e=""){return this.show(Lt.PATIENT_MANAGEMENT.REGISTRATION_SUCCESS,{message:e?`${e} \u7684\u4FE1\u606F\u5DF2\u6210\u529F\u767B\u8BB0`:"\u60A3\u8005\u4FE1\u606F\u5DF2\u6210\u529F\u767B\u8BB0"})}registrationError(e="\u60A3\u8005\u4FE1\u606F\u767B\u8BB0\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"){return this.show(Lt.PATIENT_MANAGEMENT.REGISTRATION_ERROR,{message:e})}deviceReady(){return this.show(Lt.DEVICE_STATUS.DEVICE_READY)}deviceError(e="\u8BBE\u5907\u68C0\u6D4B\u5230\u6545\u969C\uFF0C\u8BF7\u8054\u7CFB\u7EF4\u62A4\u4EBA\u5458"){return this.show(Lt.DEVICE_STATUS.DEVICE_ERROR,{message:e})}networkError(){return this.show(It.NETWORK_ERROR)}connectionLost(){return this.show(It.CONNECTION_LOST)}connectionRestored(){return this.show(It.CONNECTION_RESTORED)}on(e,s){this.eventListeners.has(e)||this.eventListeners.set(e,[]),this.eventListeners.get(e).push(s)}off(e,s){if(this.eventListeners.has(e)){const i=this.eventListeners.get(e),n=i.indexOf(s);n>-1&&i.splice(n,1)}}emit(e,s){this.eventListeners.has(e)&&this.eventListeners.get(e).forEach(i=>{try{i(s)}catch(n){console.error(`[ToastManager] \u4E8B\u4EF6\u5904\u7406\u5668\u9519\u8BEF (${e}):`,n)}})}destroy(){this.toastComponent=null,this.isInitialized=!1,this.pendingToasts=[],this.eventListeners.clear(),console.log("[ToastManager] \u5DF2\u9500\u6BC1")}}const ms=new Sf;class _f{constructor(){this.listeners=new Map,this.eventQueue=[],this.isProcessing=!1,this.retryAttempts=new Map,this.maxRetries=3,this.retryDelay=1e3,this.debugMode=!0}init(){this.log("EventManager \u521D\u59CB\u5316"),this.setupWailsEventListeners(),this.startEventProcessor()}setupWailsEventListeners(){if(!window.runtime||!window.runtime.EventsOn){this.error("Wails runtime \u4E0D\u53EF\u7528");return}window.runtime.EventsOn("concurrent-screenshot-progress",e=>{this.handleConcurrentProgress(e)}),window.runtime.EventsOn("ocr-processing-progress",e=>{this.handleOCRProgress(e)}),window.runtime.EventsOn("task-completed",e=>{this.handleTaskCompleted(e)}),window.runtime.EventsOn("task-error",e=>{this.handleTaskError(e)}),window.runtime.EventsOn("system-status-update",e=>{this.handleSystemStatusUpdate(e)}),this.log("Wails\u4E8B\u4EF6\u76D1\u542C\u5668\u8BBE\u7F6E\u5B8C\u6210")}startEventProcessor(){setInterval(()=>{this.processEventQueue()},100)}handleConcurrentProgress(e){this.log("\u6536\u5230\u5E76\u53D1\u622A\u56FE\u8FDB\u5EA6:",e);const s={type:"concurrent-progress",data:{mode:e.mode||"B",progress:e.progress||0,message:e.message||"\u6B63\u5728\u5904\u7406...",timestamp:Date.now()}};this.queueEvent(s)}handleOCRProgress(e){this.log("\u6536\u5230OCR\u5904\u7406\u8FDB\u5EA6:",e);const s={type:"ocr-progress",data:{organName:e.organName||"\u672A\u77E5\u5668\u5B98",confidence:e.confidence||0,progress:e.progress||0,message:e.message||"OCR\u5904\u7406\u4E2D...",timestamp:Date.now()}};this.queueEvent(s)}handleTaskCompleted(e){this.log("\u6536\u5230\u4EFB\u52A1\u5B8C\u6210\u4E8B\u4EF6:",e);const s={type:"task-completed",data:{taskType:e.taskType||"unknown",result:e.result||{},duration:e.duration||0,message:e.message||"\u4EFB\u52A1\u5B8C\u6210",timestamp:Date.now()}};this.queueEvent(s)}handleTaskError(e){this.error("\u6536\u5230\u4EFB\u52A1\u9519\u8BEF\u4E8B\u4EF6:",e);const s={type:"task-error",data:{taskType:e.taskType||"unknown",error:e.error||"\u672A\u77E5\u9519\u8BEF",message:e.message||"\u4EFB\u52A1\u6267\u884C\u5931\u8D25",timestamp:Date.now()}};this.queueEvent(s)}handleSystemStatusUpdate(e){this.log("\u6536\u5230\u7CFB\u7EDF\u72B6\u6001\u66F4\u65B0:",e);const s={type:"system-status",data:{status:e.status||"unknown",message:e.message||"\u7CFB\u7EDF\u72B6\u6001\u66F4\u65B0",details:e.details||{},timestamp:Date.now()}};this.queueEvent(s)}queueEvent(e){e.id=this.generateEventId(),this.eventQueue.push(e),this.log(`\u4E8B\u4EF6\u5DF2\u52A0\u5165\u961F\u5217: ${e.type} (ID: ${e.id})`)}async processEventQueue(){if(!(this.isProcessing||this.eventQueue.length===0)){this.isProcessing=!0;try{for(;this.eventQueue.length>0;){const e=this.eventQueue.shift();await this.processEvent(e)}}catch(e){this.error("\u5904\u7406\u4E8B\u4EF6\u961F\u5217\u65F6\u53D1\u751F\u9519\u8BEF:",e)}finally{this.isProcessing=!1}}}async processEvent(e){try{this.log(`\u5904\u7406\u4E8B\u4EF6: ${e.type} (ID: ${e.id})`);const s=this.listeners.get(e.type)||[];for(const i of s)try{await i(e.data)}catch(n){this.error(`\u4E8B\u4EF6\u76D1\u542C\u5668\u6267\u884C\u5931\u8D25 (${e.type}):`,n)}this.retryAttempts.delete(e.id)}catch(s){this.error(`\u5904\u7406\u4E8B\u4EF6\u5931\u8D25 (${e.type}):`,s),await this.retryEvent(e)}}async retryEvent(e){const s=this.retryAttempts.get(e.id)||0;s<this.maxRetries?(this.retryAttempts.set(e.id,s+1),setTimeout(()=>{this.eventQueue.unshift(e),this.log(`\u91CD\u8BD5\u4E8B\u4EF6: ${e.type} (ID: ${e.id}, \u5C1D\u8BD5: ${s+1}/${this.maxRetries})`)},this.retryDelay*(s+1))):(this.error(`\u4E8B\u4EF6\u5904\u7406\u5931\u8D25\uFF0C\u5DF2\u8FBE\u5230\u6700\u5927\u91CD\u8BD5\u6B21\u6570: ${e.type} (ID: ${e.id})`),this.retryAttempts.delete(e.id))}on(e,s){this.listeners.has(e)||this.listeners.set(e,[]),this.listeners.get(e).push(s),this.log(`\u6CE8\u518C\u4E8B\u4EF6\u76D1\u542C\u5668: ${e}`)}off(e,s){const i=this.listeners.get(e);if(i){const n=i.indexOf(s);n>-1&&(i.splice(n,1),this.log(`\u79FB\u9664\u4E8B\u4EF6\u76D1\u542C\u5668: ${e}`))}}generateEventId(){return`event_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}log(...e){this.debugMode&&console.log("[EventManager]",...e)}error(...e){console.error("[EventManager]",...e)}getStats(){return{queueLength:this.eventQueue.length,isProcessing:this.isProcessing,listenerCount:Array.from(this.listeners.values()).reduce((e,s)=>e+s.length,0),retryingEvents:this.retryAttempts.size}}}const Ie=new _f;class Tf{constructor(){this.toastComponent=null,this.notifications=new Map,this.progressNotifications=new Map,this.config={maxToasts:5,defaultDuration:5e3,progressUpdateInterval:100,debugMode:!0},this.stats={totalNotifications:0,activeNotifications:0,completedNotifications:0,failedNotifications:0}}init(e){this.toastComponent=e,this.setupEventListeners(),this.log("NotificationManager \u521D\u59CB\u5316\u5B8C\u6210")}setupEventListeners(){Ie.on("concurrent-progress",e=>{this.handleConcurrentProgress(e)}),Ie.on("ocr-progress",e=>{this.handleOCRProgress(e)}),Ie.on("task-completed",e=>{this.handleTaskCompleted(e)}),Ie.on("task-error",e=>{this.handleTaskError(e)}),Ie.on("system-status",e=>{this.handleSystemStatus(e)})}handleConcurrentProgress(e){const i={id:`concurrent_${e.mode}`,title:"\u5E76\u53D1\u622A\u56FE\u5904\u7406",message:`${e.mode==="B"?"B02\u751F\u5316\u5206\u6790":"C03\u75C5\u7406\u5206\u6790"} (${e.progress}%)`,type:"info",duration:0,showProgress:!0,progress:e.progress,persistent:!0};this.showProgressNotification(i),this.updateOperationStatus(e.message)}handleOCRProgress(e){const i={id:`ocr_${e.organName.replace(/\s+/g,"_")}`,title:"OCR\u8BC6\u522B\u5904\u7406",message:`${e.organName} - \u7F6E\u4FE1\u5EA6: ${e.confidence}%`,type:e.confidence>80?"success":e.confidence>60?"warning":"error",duration:3e3,showProgress:!0,progress:e.progress};this.showNotification(i)}handleTaskCompleted(e){const s={title:"\u4EFB\u52A1\u5B8C\u6210",message:`${e.taskType} \u5DF2\u5B8C\u6210 (\u8017\u65F6: ${this.formatDuration(e.duration)})`,type:"success",duration:5e3,showProgress:!1};this.showNotification(s),this.stats.completedNotifications++,this.clearProgressNotifications(e.taskType)}handleTaskError(e){const s={title:"\u4EFB\u52A1\u5931\u8D25",message:`${e.taskType}: ${e.error}`,type:"error",duration:8e3,showProgress:!1,actions:[{text:"\u91CD\u8BD5",action:()=>this.retryTask(e.taskType)},{text:"\u67E5\u770B\u8BE6\u60C5",action:()=>this.showErrorDetails(e)}]};this.showNotification(s),this.stats.failedNotifications++}handleSystemStatus(e){const s={title:"\u7CFB\u7EDF\u72B6\u6001",message:e.message,type:this.getStatusType(e.status),duration:4e3,showProgress:!1};this.showNotification(s)}showNotification(e){if(!this.toastComponent)return this.error("Toast\u7EC4\u4EF6\u672A\u521D\u59CB\u5316"),null;e.id||(e.id=this.generateNotificationId());const s={duration:this.config.defaultDuration,showProgress:!1,...e};try{const i=this.toastComponent.showToast(s);return i&&(this.notifications.set(e.id,{config:s,timestamp:Date.now(),status:"active"}),this.stats.totalNotifications++,this.stats.activeNotifications++,this.log(`\u663E\u793A\u901A\u77E5: ${s.title} (ID: ${e.id})`)),i}catch(i){return this.error("\u663E\u793A\u901A\u77E5\u5931\u8D25:",i),null}}showProgressNotification(e){const s=this.showNotification(e);return s&&e.id&&this.progressNotifications.set(e.id,{notification:s,config:e,lastUpdate:Date.now()}),s}updateProgressNotification(e,s,i){const n=this.progressNotifications.get(e);if(n&&this.toastComponent)try{this.toastComponent.updateProgress(e,s,i),n.lastUpdate=Date.now(),this.log(`\u66F4\u65B0\u8FDB\u5EA6\u901A\u77E5: ${e} (${s}%)`)}catch(o){this.error(`\u66F4\u65B0\u8FDB\u5EA6\u901A\u77E5\u5931\u8D25 (${e}):`,o)}}clearProgressNotifications(e){const s=[];for(const[i,n]of this.progressNotifications)i.includes(e)&&s.push(i);s.forEach(i=>{this.removeNotification(i),this.progressNotifications.delete(i)})}removeNotification(e){if(this.toastComponent)try{this.toastComponent.removeToast(e);const s=this.notifications.get(e);s&&(s.status="removed",this.stats.activeNotifications--),this.log(`\u79FB\u9664\u901A\u77E5: ${e}`)}catch(s){this.error(`\u79FB\u9664\u901A\u77E5\u5931\u8D25 (${e}):`,s)}}clearAllNotifications(){if(this.toastComponent)try{this.toastComponent.clearAllToasts();for(const e of this.notifications.values())e.status==="active"&&(e.status="cleared",this.stats.activeNotifications--);this.progressNotifications.clear(),this.log("\u6E05\u9664\u6240\u6709\u901A\u77E5")}catch(e){this.error("\u6E05\u9664\u6240\u6709\u901A\u77E5\u5931\u8D25:",e)}}updateOperationStatus(e){window.app&&window.app.updateOperationStatus&&window.app.updateOperationStatus(e),window.dispatchEvent(new CustomEvent("operation-status-update",{detail:{message:e,timestamp:Date.now()}}))}retryTask(e){this.log(`\u91CD\u8BD5\u4EFB\u52A1: ${e}`),window.dispatchEvent(new CustomEvent("task-retry",{detail:{taskType:e,timestamp:Date.now()}}))}showErrorDetails(e){this.log("\u663E\u793A\u9519\u8BEF\u8BE6\u60C5:",e),window.dispatchEvent(new CustomEvent("show-error-details",{detail:e}))}getStatusType(e){return{success:"success",error:"error",warning:"warning",info:"info",processing:"info",completed:"success",failed:"error"}[e]||"info"}formatDuration(e){if(e<1e3)return`${e}ms`;if(e<6e4)return`${(e/1e3).toFixed(1)}s`;{const s=Math.floor(e/6e4),i=Math.floor(e%6e4/1e3);return`${s}m ${i}s`}}generateNotificationId(){return`notification_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}log(...e){this.config.debugMode&&console.log("[NotificationManager]",...e)}error(...e){console.error("[NotificationManager]",...e)}getStats(){return{...this.stats,activeNotifications:this.stats.activeNotifications,progressNotifications:this.progressNotifications.size,totalNotifications:this.notifications.size}}getActiveNotifications(){const e=[];for(const[s,i]of this.notifications)i.status==="active"&&e.push({id:s,...i.config,timestamp:i.timestamp});return e}}const He=new Tf;class vf{constructor(){this.isInitialized=!1,this.errorQueue=[],this.config={maxQueueSize:100,reportInterval:3e4,enableConsoleCapture:!0,enableUnhandledRejection:!0,enableResourceError:!0,enablePerformanceMonitoring:!0,reportEndpoint:null,apiKey:null,userId:null,sessionId:this.generateSessionId(),environment:"production"},this.performanceData={pageLoadTime:0,resourceErrors:[],jsErrors:[],networkErrors:[]}}init(e={}){if(this.isInitialized){console.warn("ErrorMonitor already initialized");return}this.config={...this.config,...e},this.isInitialized=!0,this.setupGlobalErrorHandlers(),this.config.enablePerformanceMonitoring&&this.setupPerformanceMonitoring(),this.startReporting(),console.log("ErrorMonitor initialized",this.config)}setupGlobalErrorHandlers(){window.addEventListener("error",e=>{var s;this.captureError({type:"javascript",message:e.message,filename:e.filename,lineno:e.lineno,colno:e.colno,error:e.error,stack:(s=e.error)==null?void 0:s.stack,timestamp:Date.now(),url:window.location.href,userAgent:navigator.userAgent})}),this.config.enableUnhandledRejection&&window.addEventListener("unhandledrejection",e=>{var s,i;this.captureError({type:"unhandled-promise",message:((s=e.reason)==null?void 0:s.message)||"Unhandled Promise Rejection",reason:e.reason,stack:(i=e.reason)==null?void 0:i.stack,timestamp:Date.now(),url:window.location.href,userAgent:navigator.userAgent})}),this.config.enableResourceError&&window.addEventListener("error",e=>{e.target!==window&&this.captureError({type:"resource",message:`Failed to load resource: ${e.target.src||e.target.href}`,element:e.target.tagName,source:e.target.src||e.target.href,timestamp:Date.now(),url:window.location.href})},!0),this.config.enableConsoleCapture&&this.setupConsoleCapture()}setupConsoleCapture(){const e=console.error,s=console.warn;console.error=(...i)=>{this.captureError({type:"console-error",message:i.join(" "),args:i,timestamp:Date.now(),url:window.location.href,stack:new Error().stack}),e.apply(console,i)},console.warn=(...i)=>{this.captureError({type:"console-warn",message:i.join(" "),args:i,timestamp:Date.now(),url:window.location.href,stack:new Error().stack}),s.apply(console,i)}}setupPerformanceMonitoring(){if(window.addEventListener("load",()=>{setTimeout(()=>{const e=performance.getEntriesByType("navigation")[0];e&&(this.performanceData.pageLoadTime=e.loadEventEnd-e.fetchStart)},0)}),"PerformanceObserver"in window)try{new PerformanceObserver(s=>{for(const i of s.getEntries())(i.entryType==="measure"||i.entryType==="mark")&&this.recordPerformance(i)}).observe({entryTypes:["measure","mark"]})}catch(e){console.warn("PerformanceObserver not supported",e)}}captureError(e){const s={...e,sessionId:this.config.sessionId,userId:this.config.userId,environment:this.config.environment,timestamp:e.timestamp||Date.now(),id:this.generateErrorId()};this.errorQueue.push(s),this.errorQueue.length>this.config.maxQueueSize&&this.errorQueue.shift(),this.isCriticalError(e)&&this.reportImmediately([s]),console.log("Error captured:",s)}reportError(e,s={}){const i={type:"manual",message:e.message,stack:e.stack,name:e.name,context:s,timestamp:Date.now(),url:window.location.href,userAgent:navigator.userAgent};this.captureError(i)}recordPerformance(e){const s={name:e.name,entryType:e.entryType,startTime:e.startTime,duration:e.duration,timestamp:Date.now()};console.log("Performance recorded:",s)}startReporting(){setInterval(()=>{this.errorQueue.length>0&&this.reportErrors()},this.config.reportInterval)}async reportErrors(){if(this.errorQueue.length===0)return;const e=[...this.errorQueue];this.errorQueue=[];try{await this.sendErrorReport(e),console.log(`Reported ${e.length} errors`)}catch(s){console.error("Failed to report errors:",s),this.errorQueue.unshift(...e)}}async reportImmediately(e){try{await this.sendErrorReport(e),console.log("Critical errors reported immediately")}catch(s){console.error("Failed to report critical errors:",s)}}async sendErrorReport(e){const s={errors:e,performance:this.performanceData,metadata:{sessionId:this.config.sessionId,userId:this.config.userId,environment:this.config.environment,timestamp:Date.now(),url:window.location.href,userAgent:navigator.userAgent,viewport:{width:window.innerWidth,height:window.innerHeight},screen:{width:window.screen.width,height:window.screen.height}}};if(this.config.reportEndpoint){const i=await fetch(this.config.reportEndpoint,{method:"POST",headers:{"Content-Type":"application/json",...this.config.apiKey&&{Authorization:`Bearer ${this.config.apiKey}`}},body:JSON.stringify(s)});if(!i.ok)throw new Error(`HTTP ${i.status}: ${i.statusText}`)}else console.group("Error Report"),console.log("Errors:",e),console.log("Performance:",this.performanceData),console.log("Metadata:",s.metadata),console.groupEnd()}isCriticalError(e){return[/network error/i,/server error/i,/database/i,/authentication/i,/authorization/i,/security/i].some(i=>i.test(e.message)||i.test(e.type))}generateSessionId(){return"session_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}generateErrorId(){return"error_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}setUserId(e){this.config.userId=e}addContext(e){this.config.context={...this.config.context,...e}}clearErrorQueue(){this.errorQueue=[]}getErrorStats(){const e={total:this.errorQueue.length,byType:{},recent:this.errorQueue.slice(-10)};return this.errorQueue.forEach(s=>{e.byType[s.type]=(e.byType[s.type]||0)+1}),e}destroy(){this.isInitialized=!1,this.clearErrorQueue(),console.log("ErrorMonitor destroyed")}}const vs=new vf;typeof window<"u"&&vs.init();async function Cf(t,e="",s={}){try{return await t()}catch(i){const n=Se.handleError(i,e,s);if(s.retry&&n.isRetryable)return await Se.retry(t,s.retry);if(s.throwError!==!1)throw n;return s.defaultValue}}const Ef={name:"App",components:{ToastNotification:sf,ErrorBoundary:yf},setup(){const t=Tu(),e=vu(),s=Cu();return{patientStore:t,deviceStore:e,notificationStore:s}},data(){return{toastConfig:{...Nr,debug:!0,maxToasts:3},logs:[],currentTime:"",loading:!1,loadingMessage:"",processingDialog:{show:!1,organName:"",currentStep:0,totalSteps:10,progress:0,startTime:null,message:"\u6B63\u5728\u5206\u6790\u6570\u636E\uFF0C\u8BF7\u7A0D\u5019..."},timeInterval:null,latestQRCode:null,registrationQRCodeUrl:"",showAddPatientDialog:!1,newPatient:{name:"",registrationNumber:""},currentOperationStatus:"\u7B49\u5F85\u60A8\u9009\u62E9\u76EE\u6807\u5668\u5B98/\u90E8\u4F4D\uFF0C\u8FDB\u884CB02\u751F\u5316\u5E73\u8861\u5206\u6790\u91C7\u6837...",currentProgress:0,currentMode:"",activeTab:"pending",pendingRegistrations:[],completedPatients:[],unanalyzedPatients:[],pendingDisplayLimit:10,completedDisplayLimit:10,unanalyzedDisplayLimit:10,isRefreshing:!1,submitButtonState:"extract",captureButtonDisabled:!0}},computed:{todayPatientCount(){return this.patientStore.todayPatientCount},currentRegistrationNumber(){return this.patientStore.currentRegistrationNumber},displayedRegistrations(){return this.patientStore.displayedRegistrations},currentPatient(){return this.patientStore.currentPatient},config(){return this.deviceStore.config},patients(){return this.patientStore.patients},registrations(){return this.patientStore.registrations},notification(){return this.notificationStore.currentNotification},displayedPendingRegistrations(){return this.pendingRegistrations.slice(0,this.pendingDisplayLimit)},displayedCompletedPatients(){return this.completedPatients.slice(0,this.completedDisplayLimit)},displayedUnanalyzedPatients(){return this.unanalyzedPatients.slice(0,this.unanalyzedDisplayLimit)},selectedPatientIndex:{get(){return this.patientStore.selectedPatientIndex},set(t){this.patientStore.setSelectedPatientIndex(t)}},submitButtonText(){return this.submitButtonState==="extract"?"\u5F00\u59CB\u63D0\u53D6<br>B02/C03":"\u63D0\u4EA4\u751F\u6210<br>\u5065\u5EB7\u62A5\u544A"}},async mounted(){this.initializeErrorHandling(),await this.initializeApp(),this.startTimeUpdate(),this.setupKeyboardShortcuts(),this.setupWailsNotificationListeners(),this.initializeToastManager(),this.initializeEventAndNotificationManagers(),await this.generateRegistrationQRCode(),await this.patientStore.loadRegistrations(),await this.loadPendingRegistrations(),await this.loadCompletedPatients()},beforeUnmount(){this.timeInterval&&clearInterval(this.timeInterval),this.patientStore.stopPolling(),this.deviceStore.stopConfigWatcher()},methods:{initializeErrorHandling(){var t,e;try{vs.init({reportEndpoint:null,enableConsoleCapture:!0,enableUnhandledRejection:!0,enableResourceError:!0,enablePerformanceMonitoring:!0,environment:"production"}),(e=(t=this.config)==null?void 0:t.device_info)!=null&&e.device_no&&vs.setUserId(this.config.device_info.device_no),this.setupErrorEventListeners(),console.log("\u9519\u8BEF\u5904\u7406\u7CFB\u7EDF\u521D\u59CB\u5316\u5B8C\u6210")}catch(s){console.error("\u9519\u8BEF\u5904\u7406\u7CFB\u7EDF\u521D\u59CB\u5316\u5931\u8D25:",s)}},setupErrorEventListeners(){window.addEventListener("error-report",t=>{this.handleErrorReport(t.detail)}),this.$on("component-error",this.handleComponentError)},handleErrorReport(t){console.log("\u6536\u5230\u9519\u8BEF\u62A5\u544A:",t),t.showToUser&&this.$refs.toastNotification&&this.$refs.toastNotification.showToast({title:"\u64CD\u4F5C\u5931\u8D25",message:t.userMessage||"\u64CD\u4F5C\u8FC7\u7A0B\u4E2D\u51FA\u73B0\u9519\u8BEF\uFF0C\u8BF7\u91CD\u8BD5",type:"error",duration:5e3})},handleComponentError(t){var e,s;console.error("\u7EC4\u4EF6\u9519\u8BEF:",t),vs.reportError(t.error,{component:((s=(e=t.component)==null?void 0:e.$options)==null?void 0:s.name)||"Unknown",errorInfo:t.errorInfo})},async safeExecute(t,e="",s={}){return await Cf(t,e,{showToUser:!0,...s})},setupWailsNotificationListeners(){window.runtime.EventsOn("wails:update-progress",t=>{this.$refs.toastNotification&&this.$refs.toastNotification.updateProgress(t.id,t.progress,t.message)}),window.runtime.EventsOn("updateOperationStatus",t=>{console.log("[App] \u6536\u5230\u64CD\u4F5C\u72B6\u6001\u66F4\u65B0:",t),this.currentOperationStatus=t.status,this.currentMode=t.mode}),window.runtime.EventsOn("updateProgress",t=>{console.log("[App] \u6536\u5230\u8FDB\u5EA6\u66F4\u65B0:",t),this.currentProgress=t.progress,this.currentMode=t.mode})},async submitUserScreenshotTaskToAI(){if(this.submitButtonState==="extract")console.log("[App] \u64CD\u4F5C\u8005\u5F00\u59CB\u63D0\u53D6B02/C03\u6570\u636E"),this.submitButtonState="generate",this.captureButtonDisabled=!1,this.$refs.toastNotification&&this.$refs.toastNotification.showToast({title:"\u5F00\u59CB\u6570\u636E\u63D0\u53D6\u5206\u6790",message:"\u8BF7\u5F00\u59CB\u5BF9\u68C0\u6D4B\u7ED3\u679C\u5206\u6790\u4E2D\u7684B02/C03\u9009\u9879\uFF0C\u8FDB\u884C\u63D0\u53D6\u5206\u6790\u7684\u76F8\u5173\u64CD\u4F5C\uFF01",type:"info",duration:8e3,showProgress:!1});else if(console.log("[App] \u64CD\u4F5C\u8005\u63D0\u4EA4\u7528\u6237\u622A\u56FE\u4EFB\u52A1\u5230AI\uFF0C\u4EE5\u751F\u6210\u5065\u5EB7\u5206\u6790\u62A5\u544A"),this.submitButtonState="extract",this.captureButtonDisabled=!0,this.$refs.toastNotification){const t=this.$refs.toastNotification.showToast({title:"\u63D0\u4EA4\u68C0\u6D4B\u6570\u636E",message:"\u5F00\u59CB\u542F\u52A8AI\u5927\u6A21\u578B\u5206\u6790\u751F\u6210\u5065\u5EB7\u62A5\u544A\uFF0C\u60A8\u53EF\u4EE5\u5F00\u59CB\u4E0B\u4E00\u4F4D\u53D7\u68C0\u8005\u68C0\u6D4B\u5DE5\u4F5C...",type:"success",duration:8e3,showProgress:!1});console.log("[App] showToast\u8FD4\u56DE\u7ED3\u679C:",t)}else console.error("[App] \u672A\u627E\u5230ToastNotification\u7EC4\u4EF6\u5F15\u7528")},async captureScreenshotData(){console.log("[App] \u6293\u53D6\u7528\u6237\u622A\u56FE\u6570\u636E");try{const{ShowSuccessNotification:t}=await Kn(()=>Promise.resolve().then(()=>Qn),void 0);console.log("[App] \u8C03\u7528\u540E\u7AEFShowSuccessNotification\u65B9\u6CD5"),await t("\u6D4B\u8BD5\u540E\u7AEF\u901A\u77E5","\u8FD9\u662F\u6765\u81EA\u540E\u7AEF\u7684\u6D4B\u8BD5\u901A\u77E5\uFF0C\u7528\u4E8E\u9A8C\u8BC1\u4E8B\u4EF6\u4F20\u9012",5e3),console.log("[App] \u540E\u7AEF\u65B9\u6CD5\u8C03\u7528\u5B8C\u6210")}catch(t){console.error("[App] \u8C03\u7528\u540E\u7AEF\u901A\u77E5\u65B9\u6CD5\u5931\u8D25:",t)}},async captureScreenshotByBtn(){console.log("[App] \u5F00\u59CB\u6309\u94AE\u89E6\u53D1\u7684\u667A\u80FD\u622A\u56FE");try{this.showLoading("\u6B63\u5728\u8FDB\u884C\u68C0\u6D4B\u7ED3\u679C\u83B7\u53D6\u548C\u521D\u6B65\u5206\u6790...");const{CaptureScreenshotByBtn:t}=await Kn(()=>Promise.resolve().then(()=>Qn),void 0);console.log("[App] \u8C03\u7528\u540E\u7AEFCaptureScreenshotByBtn\u65B9\u6CD5");const e=await t();this.hideLoading(),e&&e.success?(this.showNotification(`\u68C0\u6D4B\u7ED3\u679C\u83B7\u53D6\u548C\u521D\u6B65\u5206\u6790\u5B8C\u6210\uFF01\u68C0\u6D4B\u5230${e.detected_mode}\u6A21\u5F0F
\u5668\u5B98: ${e.organ_name}`,"success"),this.submitButtonState="extract",this.addLog({time:new Date().toLocaleString(),action:"\u667A\u80FD\u622A\u56FE",mode:e.detected_mode,result:"\u6210\u529F",message:`\u5668\u5B98: ${e.organ_name}, \u7F6E\u4FE1\u5EA6: ${(e.confidence*100).toFixed(1)}%`,taskId:e.task_id}),console.log("[App] \u667A\u80FD\u622A\u56FE\u6210\u529F:",{taskId:e.task_id,detectedMode:e.detected_mode,organName:e.organ_name,confidence:e.confidence,imagePath:e.image_path})):(this.showNotification("\u68C0\u6D4B\u7ED3\u679C\u83B7\u53D6\u548C\u521D\u6B65\u5206\u6790\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5","error"),console.error("[App] \u667A\u80FD\u622A\u56FE\u5931\u8D25:",e))}catch(t){this.hideLoading(),this.showNotification(`\u68C0\u6D4B\u7ED3\u679C\u83B7\u53D6\u548C\u521D\u6B65\u5206\u6790\u5931\u8D25: ${t.message||t}`,"error"),this.addLog({time:new Date().toLocaleString(),action:"\u667A\u80FD\u622A\u56FE",mode:"\u672A\u77E5",result:"\u5931\u8D25",message:t.message||t.toString()}),console.error("[App] \u667A\u80FD\u622A\u56FE\u5F02\u5E38:",t)}},formatGender(t){return console.log("formatGender - gender:",t,"type:",typeof t),t===1?"\u7537":t===2?"\u5973":"\u672A\u77E5"},calculateAge(t){if(!t)return"";const e=new Date(t),s=new Date;let i=s.getFullYear()-e.getFullYear();const n=s.getMonth()-e.getMonth();return(n<0||n===0&&s.getDate()<e.getDate())&&i--,i+"\u5C81"},async initializeApp(){try{this.notificationStore.showLoading("\u6B63\u5728\u521D\u59CB\u5316\u5E94\u7528...");const t=await this.deviceStore.loadConfig();console.log("initializeApp - config loaded:",t);const e=await this.deviceStore.fetchSiteInfo();e.success?this.notificationStore.showSuccess(e.message):this.notificationStore.showError(e.message),window.runtime.EventsOn("siteInfoUpdated",i=>{console.log("siteInfoUpdated - newSiteInfo:",JSON.stringify(i,null,2));const n=this.deviceStore.updateSiteInfo(i);n.success?this.notificationStore.showSuccess(n.message):this.notificationStore.showError(n.message)}),window.runtime.EventsOn("showProcessingNotification",i=>{console.log("showProcessingNotification - data:",i),this.showProcessingDialog(i.organName,i.currentStep,i.totalSteps,i.message)}),window.runtime.EventsOn("updateProcessingProgress",i=>{console.log("updateProcessingProgress - data:",i),this.updateProcessingProgress(i.currentStep,i.totalSteps,i.progress)}),window.runtime.EventsOn("hideProcessingNotification",()=>{console.log("hideProcessingNotification"),this.hideProcessingDialog()});const s=await wr();console.log("Mode config loaded:",s),await this.patientStore.loadRegistrations(),await Promise.all([this.loadPendingRegistrations(),this.loadCompletedPatients(),this.loadUnanalyzedPatients()]),this.deviceStore.startConfigWatcher(),this.notificationStore.hideLoading(),this.notificationStore.showSuccess("\u5E94\u7528\u521D\u59CB\u5316\u6210\u529F")}catch(t){this.notificationStore.hideLoading(),this.notificationStore.showError(`\u521D\u59CB\u5316\u5931\u8D25: ${t}`),console.error("\u521D\u59CB\u5316\u5E94\u7528\u5931\u8D25:",t)}},async generateRegistrationQRCode(){try{const t=await Pi();t&&t.qr_code_base64?this.registrationQRCodeUrl=`data:image/png;base64,${t.qr_code_base64}`:t&&t.file_path?(console.log("\u62A5\u5230\u4E8C\u7EF4\u7801\u6587\u4EF6\u8DEF\u5F84:",t.file_path),this.notificationStore.showInfo("\u62A5\u5230\u4E8C\u7EF4\u7801\u5DF2\u751F\u6210 (\u8DEF\u5F84\u65B9\u5F0F\uFF0C\u53EF\u80FD\u65E0\u6CD5\u76F4\u63A5\u663E\u793A)")):(console.error("\u751F\u6210\u62A5\u5230\u4E8C\u7EF4\u7801\u5931\u8D25\u6216\u8FD4\u56DE\u6570\u636E\u683C\u5F0F\u4E0D\u6B63\u786E",t),this.notificationStore.showError("\u751F\u6210\u62A5\u5230\u4E8C\u7EF4\u7801\u5931\u8D25"))}catch(t){console.error("\u8C03\u7528\u751F\u6210\u62A5\u5230\u4E8C\u7EF4\u7801\u65B9\u6CD5\u5931\u8D25:",t),this.notificationStore.showError(`\u751F\u6210\u62A5\u5230\u4E8C\u7EF4\u7801\u5931\u8D25: ${t.message||t}`)}},async updateCropSettings(t){const e=await this.deviceStore.updateCropSettings(t);e.success?this.notificationStore.showSuccess(e.message):this.notificationStore.showError(e.message)},async updateNotificationMode(){const t=await this.deviceStore.updateNotificationMode();t.success?this.notificationStore.showSuccess(t.message):this.notificationStore.showError(t.message)},async handleScreenshot(t){try{if(t==="B"||t==="C"||t==="\u751F\u5316\u5E73\u8861\u5206\u6790"||t==="\u75C5\u7406\u5F62\u6001\u5B66\u5206\u6790"){this.showLoading(`\u6B63\u5728\u5904\u7406[${t}]\u622A\u56FE...`),this.showNotification(`\u5F00\u59CB\u5904\u7406${t}\u6A21\u5F0F\u622A\u56FE`,"info");try{const e=await br(t);this.hideLoading(),this.showNotification(`${t}\u6A21\u5F0F\u622A\u56FE\u5904\u7406\u5B8C\u6210`,"success"),this.addLog({time:new Date().toLocaleString(),action:"\u5E76\u53D1\u622A\u56FE",mode:t,result:"\u6210\u529F",message:e.message||"\u622A\u56FE\u4EFB\u52A1\u5DF2\u63D0\u4EA4"})}catch(e){console.warn("\u5E76\u53D1\u622A\u56FE\u5931\u8D25\uFF0C\u56DE\u9000\u5230\u4F20\u7EDF\u65B9\u5F0F:",e);const s=await Ai(t);this.hideLoading(),this.showNotification(`${t}\u6A21\u5F0F\u6570\u636E\u5904\u7406\u5B8C\u6210: ${s}`,"success"),this.addLog({time:new Date().toLocaleString(),action:"\u4F20\u7EDF\u622A\u56FE\uFF08\u56DE\u9000\uFF09",mode:t,result:"\u6210\u529F",url:s})}}else{this.showLoading(`\u6B63\u5728\u5904\u7406\u548C\u5206\u6790[${t}]\u6570\u636E...`);const e=await Ai(t);this.hideLoading(),this.showNotification(`\u6570\u636E\u5904\u7406\u5B8C\u6210: ${e}`,"success"),this.addLog({time:new Date().toLocaleString(),action:"\u6570\u636E\u5904\u7406",mode:t,result:"\u6210\u529F",url:e})}}catch(e){this.hideLoading(),this.showNotification(`\u6570\u636E\u5904\u7406\u5931\u8D25: ${e}`,"error"),this.addLog({time:new Date().toLocaleString(),action:"\u622A\u56FE\u5904\u7406",mode:t,result:"\u5931\u8D25",error:e.toString()})}},async generateQRCode(){try{this.showLoading("\u6B63\u5728\u751F\u6210\u4E8C\u7EF4\u7801..."),this.updateOperationStatus("\u6B63\u5728\u751F\u6210\u4E8C\u7EF4\u7801...");const t=await Ni();return this.latestQRCode=t,this.hideLoading(),this.showNotification("\u4E8C\u7EF4\u7801\u751F\u6210\u6210\u529F","success"),this.updateOperationStatus("\u4E8C\u7EF4\u7801\u751F\u6210\u5B8C\u6210"),t}catch(t){throw this.hideLoading(),this.showNotification(`\u751F\u6210\u4E8C\u7EF4\u7801\u5931\u8D25: ${t}`,"error"),t}},async handleAddPatient(){if(!this.newPatient.name||!this.newPatient.registrationNumber){this.showNotification("\u8BF7\u586B\u5199\u5B8C\u6574\u7684\u60A3\u8005\u4FE1\u606F","warning");return}try{await this.addPatient(this.newPatient.name),this.newPatient={name:"",registrationNumber:""},this.showAddPatientDialog=!1,this.updateActivity()}catch(t){this.showNotification(`\u6DFB\u52A0\u60A3\u8005\u5931\u8D25: ${t}`,"error")}},async addPatient(t){const e=await this.patientStore.addPatient(t);e.success?(this.notificationStore.showSuccess(e.message),this.addLog({time:new Date().toLocaleString(),action:"\u6DFB\u52A0\u60A3\u8005",user:t,result:"\u6210\u529F"})):this.notificationStore.showError(e.message)},async removePatient(t){const e=await this.patientStore.removePatient(t);e.success?(this.notificationStore.showSuccess(e.message),this.addLog({time:new Date().toLocaleString(),action:"\u79FB\u9664\u60A3\u8005",user:e.patientName||"\u672A\u77E5",result:"\u6210\u529F"})):this.notificationStore.showError(e.message)},async clearPatients(){const t=await this.patientStore.clearPatients();t.success?(this.notificationStore.showSuccess(t.message),this.addLog({time:new Date().toLocaleString(),action:"\u6E05\u7A7A\u60A3\u8005\u5217\u8868",user:"\u7CFB\u7EDF",result:"\u6210\u529F"})):this.notificationStore.showError(t.message)},switchTab(t){this.activeTab=t,t==="completed"?this.loadCompletedPatients():t==="pending"?this.loadPendingRegistrations():t==="unanalyzed"&&this.loadUnanalyzedPatients()},async loadPendingRegistrations(){try{const t=await Sr();this.pendingRegistrations=t||[],console.log("\u5F85\u68C0\u6D4B\u60A3\u8005\u5217\u8868:",this.pendingRegistrations)}catch(t){console.error("\u52A0\u8F7D\u5F85\u68C0\u6D4B\u60A3\u8005\u5217\u8868\u5931\u8D25:",t),this.pendingRegistrations=[]}},async loadCompletedPatients(t=null){try{let e;if(t)e=await Ri(t);else{const s=new Date().toISOString().split("T")[0];e=await Ri(s)}this.completedPatients=e||[],console.log("\u5DF2\u5B8C\u6210\u60A3\u8005\u5217\u8868\uFF08\u5DF2\u68C0\u6D4B\uFF09:",this.completedPatients)}catch(e){console.error("\u52A0\u8F7D\u5DF2\u5B8C\u6210\u60A3\u8005\u5217\u8868\u5931\u8D25:",e),this.completedPatients=[]}},async loadUnanalyzedPatients(t=null){try{const e=t||new Date().toISOString().split("T")[0],s=await vr(e);return this.unanalyzedPatients=s||[],console.log("\u672A\u5206\u6790\u60A3\u8005\u5217\u8868\uFF08\u5DF2\u68C0\u6D4B\u672A\u5206\u6790\uFF09:",this.unanalyzedPatients),this.unanalyzedPatients}catch(e){return console.error("\u52A0\u8F7D\u672A\u5206\u6790\u60A3\u8005\u5217\u8868\u5931\u8D25:",e),this.unanalyzedPatients=[],[]}},async refreshAllLists(){this.isRefreshing=!0;try{await Promise.all([this.loadPendingRegistrations(),this.loadCompletedPatients(),this.loadUnanalyzedPatients(),this.patientStore.loadRegistrations()]),this.notificationStore.showSuccess("\u5217\u8868\u5237\u65B0\u6210\u529F")}catch(t){console.error("\u5237\u65B0\u5217\u8868\u5931\u8D25:",t),this.notificationStore.showError("\u5237\u65B0\u5217\u8868\u5931\u8D25")}finally{this.isRefreshing=!1}},loadMorePendingRegistrations(){this.pendingDisplayLimit+=10},loadMoreCompletedPatients(){this.completedDisplayLimit+=10},loadMoreUnanalyzedPatients(){this.unanalyzedDisplayLimit+=10},selectPatient(t){this.selectedPatientIndex=t},formatCompletionTime(t){return t?new Date(t).toLocaleString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):""},formatTime(t){return t?new Date(t).toLocaleString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):""},handleProcessComplete(t){this.showNotification(`\u5904\u7406\u5B8C\u6210: ${t}`,"success")},addLog(t){this.logs.unshift(t),this.logs.length>100&&(this.logs=this.logs.slice(0,100))},showNotification(t,e="info"){this.notificationStore.showNotification(t,e),this.$refs.toastNotification&&this.$refs.toastNotification.showToast({title:this.getNotificationTitle(e),message:t,type:e,duration:this.getNotificationDuration(e),showProgress:!1})},getNotificationTitle(t){return{success:"\u64CD\u4F5C\u6210\u529F",error:"\u64CD\u4F5C\u5931\u8D25",warning:"\u8B66\u544A",info:"\u63D0\u793A"}[t]||"\u901A\u77E5"},getNotificationDuration(t){return{success:3e3,error:5e3,warning:4e3,info:3e3}[t]||3e3},showProcessingDialog(t,e=1,s=10,i="\u6B63\u5728\u5206\u6790\u6570\u636E\uFF0C\u8BF7\u7A0D\u5019..."){this.processingDialog={show:!0,organName:t||"\u672A\u77E5\u5668\u5B98",currentStep:e,totalSteps:s,progress:Math.round(e/s*100),startTime:new Date,message:i}},hideProcessingDialog(){this.processingDialog.show=!1},updateProcessingProgress(t,e,s){this.processingDialog.show&&(this.processingDialog.currentStep=t,this.processingDialog.totalSteps=e,this.processingDialog.progress=s)},startTimeUpdate(){this.updateTime(),this.timeInterval=setInterval(this.updateTime,1e3)},updateTime(){this.currentTime=new Date().toLocaleString()},setupKeyboardShortcuts(){document.addEventListener("keydown",t=>{t.key==="F12"&&(t.preventDefault(),this.handleFunctionKey(t.key)),t.ctrlKey&&t.shiftKey&&(t.preventDefault(),this.handleHotkeyCombo(t.key.toUpperCase()))})},async handleFunctionKey(t){const e={F12:()=>this.clearPatients()};if(e[t]){await e[t]();try{await ze(t)}catch(s){console.error("\u540E\u7AEF\u5FEB\u6377\u952E\u5904\u7406\u5931\u8D25:",s)}}},async handleHotkeyCombo(t){const e={A:()=>this.handleScreenshot("\u5668\u5B98\u95EE\u9898\u6765\u6E90\u5206\u6790"),B:()=>this.handleScreenshot("\u751F\u5316\u5E73\u8861\u5206\u6790"),C:()=>this.handleScreenshot("\u75C5\u7406\u5F62\u6001\u5B66\u5206\u6790")};if(e[t]){await e[t]();try{await ze(`Ctrl+Shift+${t}`)}catch(s){console.error("\u540E\u7AEF\u5FEB\u6377\u952E\u5904\u7406\u5931\u8D25:",s)}}},startConfigWatcher(){},hasConfigChanged(t){if(!this.config||!t)return!0;const e=this.config.SiteInfo||{},s=t.SiteInfo||{};return e.SiteID!==s.SiteID||e.SiteName!==s.SiteName||e.SiteType!==s.SiteType||e.ParentOrg!==s.ParentOrg},async loadRegistrations(){const t=await this.patientStore.loadRegistrations();t.success||this.notificationStore.showError(t.message)},async refreshRegistrations(){const t=await this.patientStore.refreshRegistrations();t.success?this.notificationStore.showSuccess(t.message):this.notificationStore.showError(t.message)},async selectPatient(t){const e=await this.patientStore.selectPatient(t);e.success?this.notificationStore.showInfo(e.message):this.notificationStore.showError(e.message)},async startConditionalPolling(){const t=await this.patientStore.startConditionalPolling();t.success&&t.newCount>0&&this.notificationStore.showSuccess(`\u53D1\u73B0 ${t.newCount} \u4F4D\u65B0\u5019\u68C0\u8005`)},loadMoreRegistrations(){this.patientStore.loadMoreRegistrations()},formatTime(t){if(!t)return"";try{if(t.includes("-")&&t.includes(":")){const e=t.split(" ");if(e.length===2){const s=e[0].split("-"),i=e[1].split(":");if(s.length===3&&i.length>=2)return`${s[1]}/${s[2]} ${i[0]}:${i[1]}`}}return t}catch{return t}},async generateRegistrationQRCode(){try{const t=await Pi();t&&t.qr_code_base64?this.registrationQRCodeUrl=`data:image/png;base64,${t.qr_code_base64}`:t&&t.file_path?(console.log("\u62A5\u5230\u4E8C\u7EF4\u7801\u6587\u4EF6\u8DEF\u5F84:",t.file_path),this.showNotification("\u62A5\u5230\u4E8C\u7EF4\u7801\u5DF2\u751F\u6210 (\u8DEF\u5F84\u65B9\u5F0F\uFF0C\u53EF\u80FD\u65E0\u6CD5\u76F4\u63A5\u663E\u793A)","info")):(console.error("\u751F\u6210\u62A5\u5230\u4E8C\u7EF4\u7801\u5931\u8D25\u6216\u8FD4\u56DE\u6570\u636E\u683C\u5F0F\u4E0D\u6B63\u786E",t),this.showNotification("\u751F\u6210\u62A5\u5230\u4E8C\u7EF4\u7801\u5931\u8D25","error"))}catch(t){console.error("\u8C03\u7528\u751F\u6210\u62A5\u5230\u4E8C\u7EF4\u7801\u65B9\u6CD5\u5931\u8D25:",t),this.showNotification(`\u751F\u6210\u62A5\u5230\u4E8C\u7EF4\u7801\u5931\u8D25: ${t.message||t}`,"error")}},async updateCropSettings(t){try{await tn(t),this.config=await qi(),this.config.device_info&&this.config.device_info.mac_address&&(this.config.device_info.device_no=this.config.device_info.mac_address.replace(/:/g,"")),this.showNotification("\u88C1\u526A\u8BBE\u7F6E\u66F4\u65B0\u6210\u529F","success")}catch(e){this.showNotification(`\u66F4\u65B0\u88C1\u526A\u8BBE\u7F6E\u5931\u8D25: ${e}`,"error")}},async generateQRCode(){try{this.showLoading("\u6B63\u5728\u751F\u6210\u4E8C\u7EF4\u7801...");const t=await Ni();return this.latestQRCode=t,this.hideLoading(),this.showNotification("\u4E8C\u7EF4\u7801\u751F\u6210\u6210\u529F","success"),t}catch(t){throw this.hideLoading(),this.showNotification(`\u751F\u6210\u4E8C\u7EF4\u7801\u5931\u8D25: ${t}`,"error"),t}},async toggleWindowSize(){try{this.windowState.isExpanded?await Ji():await Zi();const t=await rs();this.windowState=t,this.updateActivity()}catch(t){this.showNotification(`\u5207\u6362\u7A97\u4F53\u5927\u5C0F\u5931\u8D25: ${t}`,"error")}},async toggleWindowPosition(){try{const t=this.windowState.position==="left"?"right":"left";await Xi(t),this.windowState.position=t,this.updateActivity()}catch(t){this.showNotification(`\u5207\u6362\u7A97\u4F53\u4F4D\u7F6E\u5931\u8D25: ${t}`,"error")}},async minimizeWindow(){try{await Yi(),this.updateActivity()}catch(t){this.showNotification(`\u6700\u5C0F\u5316\u7A97\u4F53\u5931\u8D25: ${t}`,"error")}},async handleQuickScreenshot(){try{this.updateOperationStatus("\u6B63\u5728\u8FDB\u884C\u5FEB\u901F\u622A\u56FE..."),await this.handleScreenshot("default","\u5FEB\u901F\u64CD\u4F5C"),this.updateOperationStatus("\u5FEB\u901F\u622A\u56FE\u5B8C\u6210"),this.updateActivity()}catch(t){this.updateOperationStatus("\u5FEB\u901F\u622A\u56FE\u5931\u8D25"),this.showNotification(`\u5FEB\u901F\u622A\u56FE\u5931\u8D25: ${t}`,"error")}},async handleAddPatient(){if(!this.newPatient.name||!this.newPatient.registrationNumber){this.showNotification("\u8BF7\u586B\u5199\u5B8C\u6574\u7684\u60A3\u8005\u4FE1\u606F","warning");return}try{await this.addPatient(this.newPatient.name),this.newPatient={name:"",registrationNumber:""},this.showAddPatientDialog=!1,this.updateActivity()}catch(t){this.showNotification(`\u6DFB\u52A0\u60A3\u8005\u5931\u8D25: ${t}`,"error")}},updateActivity(){this.lastActivityTime=Date.now(),this.resetInactivityTimer()},resetInactivityTimer(){this.inactivityTimer&&clearTimeout(this.inactivityTimer),this.inactivityTimer=setTimeout(()=>{this.windowState.isExpanded&&(this.toggleWindowSize(),this.showNotification("\u957F\u65F6\u95F4\u65E0\u64CD\u4F5C\uFF0C\u7A97\u4F53\u5DF2\u81EA\u52A8\u6536\u7F29","info"))},5*60*1e3)},async initializeWindowState(){try{const t=await rs();this.windowState=t}catch(t){console.error("\u83B7\u53D6\u7A97\u4F53\u72B6\u6001\u5931\u8D25:",t)}},async addPatient(t){const e=await this.patientStore.addPatient(t);e.success?(this.notificationStore.showSuccess(e.message),this.addLog({time:new Date().toLocaleString(),action:"\u6DFB\u52A0\u60A3\u8005",user:t,result:"\u6210\u529F"})):this.notificationStore.showError(e.message)},async removePatient(t){const e=await this.patientStore.removePatient(t);e.success?(this.notificationStore.showSuccess(e.message),this.addLog({time:new Date().toLocaleString(),action:"\u79FB\u9664\u60A3\u8005",user:e.patientName||"\u672A\u77E5",result:"\u6210\u529F"})):this.notificationStore.showError(e.message)},async clearPatients(){const t=await this.patientStore.clearPatients();t.success?(this.notificationStore.showSuccess(t.message),this.addLog({time:new Date().toLocaleString(),action:"\u6E05\u7A7A\u60A3\u8005\u5217\u8868",user:"\u7CFB\u7EDF",result:"\u6210\u529F"})):this.notificationStore.showError(t.message)},handleProcessComplete(t){this.notificationStore.showSuccess(`\u5904\u7406\u5B8C\u6210: ${t}`)},addLog(t){this.logs.unshift(t),this.logs.length>100&&(this.logs=this.logs.slice(0,100))},showLoading(t){this.loading=!0,this.loadingMessage=t},hideLoading(){this.loading=!1,this.loadingMessage=""},startTimeUpdate(){this.updateTime(),this.timeInterval=setInterval(this.updateTime,1e3)},updateTime(){this.currentTime=new Date().toLocaleString()},setupKeyboardShortcuts(){document.addEventListener("keydown",t=>{["F9","F10","F11","F12"].includes(t.key)&&(t.preventDefault(),this.handleFunctionKey(t.key)),t.ctrlKey&&t.shiftKey&&(t.preventDefault(),this.handleHotkeyCombo(t.key.toUpperCase()))})},async handleFunctionKey(t){const e={F9:()=>this.minimizeWindow(),F10:()=>this.toggleWindowPosition(),F12:()=>this.clearPatients()};if(e[t]){await e[t]();try{await ze(t)}catch(s){console.error("\u540E\u7AEF\u5FEB\u6377\u952E\u5904\u7406\u5931\u8D25:",s)}}},async handleHotkeyCombo(t){const e={A:()=>this.handleScreenshot("\u5668\u5B98\u95EE\u9898\u6765\u6E90\u5206\u6790"),B:()=>this.handleScreenshot("\u751F\u5316\u5E73\u8861\u5206\u6790"),C:()=>this.handleScreenshot("\u75C5\u7406\u5F62\u6001\u5B66\u5206\u6790")};if(e[t]){await e[t]();try{await ze(`Ctrl+Shift+${t}`)}catch(s){console.error("\u540E\u7AEF\u5FEB\u6377\u952E\u5904\u7406\u5931\u8D25:",s)}}},startConfigWatcher(){},hasConfigChanged(t){if(!this.config||!t)return!0;const e=this.config.SiteInfo||{},s=t.SiteInfo||{};return e.SiteID!==s.SiteID||e.SiteName!==s.SiteName||e.SiteType!==s.SiteType||e.ParentOrg!==s.ParentOrg},async loadRegistrations(){try{this.registrations=[];const t=new Date().toISOString().split("T")[0],e=await yr(t);this.registrations=e||[],this.registrations.sort((s,i)=>new Date(i.register_time)-new Date(s.register_time)),this.displayLimit=5,this.registrations.length>0?this.selectedPatientIndex=0:this.selectedPatientIndex=-1,console.log("\u5019\u68C0\u8005\u5217\u8868\uFF08\u5DF2\u62A5\u5230\u672A\u68C0\u6D4B\uFF09:",this.registrations)}catch(t){console.error("\u52A0\u8F7D\u5019\u68C0\u8005\u5217\u8868\u5931\u8D25:",t),this.showNotification("\u52A0\u8F7D\u5019\u68C0\u8005\u5217\u8868\u5931\u8D25: "+t,"error"),this.registrations=[],this.displayLimit=5,this.selectedPatientIndex=-1}},async refreshRegistrations(){if(!this.isRefreshing){this.isRefreshing=!0;try{await this.loadRegistrations(),this.showNotification("\u5019\u68C0\u8005\u5217\u8868\u5DF2\u5237\u65B0","success")}catch(t){this.showNotification("\u5237\u65B0\u5931\u8D25: "+t,"error")}finally{this.isRefreshing=!1}}},formatTime(t){if(!t)return"";try{if(t.includes("-")&&t.includes(":")){const e=t.split(" ");if(e.length===2){const s=e[0].split("-"),i=e[1].split(":");if(s.length===3&&i.length>=2)return`${s[1]}/${s[2]} ${i[0]}:${i[1]}`}}return t}catch{return t}},updateOperationStatus(t){this.currentOperationStatus=t;const e=t.match(/(B02生化分析|C03病理分析)/);if(e){const s=e[1].includes("B02")?"B":"C";this.currentMode=s,this.currentProgress=50}else this.currentProgress=0,this.currentMode=""},handleNotificationShown(t){this.updateOperationStatus(t.message),this.addLog(`Toast\u901A\u77E5: ${t.message}`,"info")},handleNotificationRemoved(t){console.log("[App] Toast\u901A\u77E5\u5DF2\u79FB\u9664:",t)},handleAllNotificationsCleared(){console.log("[App] \u6240\u6709Toast\u901A\u77E5\u5DF2\u6E05\u9664"),this.updateOperationStatus("\u6240\u6709\u901A\u77E5\u5DF2\u6E05\u9664")},showBusinessToast(t,e={}){const s=Pr(t,e);return this.$refs.toastNotification?this.$refs.toastNotification.showToast(s):null},showSuccessToast(t,e="\u64CD\u4F5C\u6210\u529F"){return this.showBusinessToast(It.OPERATION_SUCCESS,{title:e,message:t})},showErrorToast(t,e="\u64CD\u4F5C\u5931\u8D25"){return this.showBusinessToast(It.OPERATION_ERROR,{title:e,message:t})},showWarningToast(t,e="\u8B66\u544A"){return this.showBusinessToast(It.OPERATION_ERROR,{type:"warning",title:e,message:t})},initializeToastManager(){try{this.$nextTick(()=>{this.$refs.toastNotification?(ms.init(this.$refs.toastNotification),console.log("[App] Toast\u7BA1\u7406\u5668\u521D\u59CB\u5316\u6210\u529F"),ms.on("toast-shown",t=>{console.log("[App] Toast\u663E\u793A:",t)}),ms.on("toast-removed",t=>{console.log("[App] Toast\u79FB\u9664:",t)}),ms.success("\u5E94\u7528\u7A0B\u5E8F\u5DF2\u542F\u52A8","\u7CFB\u7EDF\u5C31\u7EEA")):console.error("[App] Toast\u7EC4\u4EF6\u5F15\u7528\u672A\u627E\u5230")})}catch(t){console.error("[App] Toast\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5931\u8D25:",t)}},initializeEventAndNotificationManagers(){try{Ie.initialize(),console.log("[App] \u4E8B\u4EF6\u7BA1\u7406\u5668\u521D\u59CB\u5316\u6210\u529F"),this.$nextTick(()=>{this.$refs.toastNotification?(He.initialize(this.$refs.toastNotification),console.log("[App] \u901A\u77E5\u7BA1\u7406\u5668\u521D\u59CB\u5316\u6210\u529F"),He.on("progress-updated",t=>{this.updateOperationStatus(`${t.message} (${t.progress}%)`)}),He.on("task-completed",t=>{this.updateOperationStatus(`\u4EFB\u52A1\u5B8C\u6210: ${t.message}`)}),He.on("task-error",t=>{this.updateOperationStatus(`\u4EFB\u52A1\u9519\u8BEF: ${t.message}`)}),He.on("system-status",t=>{this.updateOperationStatus(t.message)})):console.error("[App] Toast\u7EC4\u4EF6\u5F15\u7528\u672A\u627E\u5230\uFF0C\u65E0\u6CD5\u521D\u59CB\u5316\u901A\u77E5\u7BA1\u7406\u5668")})}catch(t){console.error("[App] \u4E8B\u4EF6\u7BA1\u7406\u5668\u548C\u901A\u77E5\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5931\u8D25:",t)}}}},bf={id:"app",class:"compact"},Nf={class:"compact-header"},Pf={class:"title-bar"},Rf={class:"window-controls"},Af={class:"compact-status"},If={class:"qrcode-column"},Of={class:"registration-qrcode-container"},xf=["src"],Mf={key:1},Df={class:"status-column"},Lf={class:"status-item"},kf={class:"value"},Uf={class:"status-item"},$f={class:"value"},Bf={class:"status-item"},Ff={class:"value"},Wf={class:"status-item"},Hf={class:"value"},jf={class:"status-item"},zf={class:"value"},Vf={class:"compact-content"},Gf={class:"operation-status"},Kf={class:"operation-text"},Qf={class:"operation-buttons"},qf=["disabled"],Yf=["innerHTML"],Jf={class:"current-patient-section"},Zf={key:0,class:"current-patient-card"},Xf={class:"current-patient-info"},td={class:"current-patient-name"},ed={class:"current-patient-number"},sd={key:1,class:"current-patient-card empty"},id={class:"patient-tabs-container"},nd={class:"tab-navigation"},od={class:"tab-text"},rd={class:"tab-text"},ad={class:"tab-text"},ld=["disabled"],cd={class:"tab-content"},ud={class:"tab-panel"},fd={class:"patient-list"},dd=["onClick"],hd=["value"],pd={class:"patient-name"},gd={class:"patient-age"},md={class:"patient-gender"},wd={class:"patient-number"},yd={class:"patient-time"},Sd={key:1,class:"empty-state"},_d={class:"tab-panel"},Td={class:"patient-list"},vd={class:"patient-name"},Cd={class:"patient-age"},Ed={class:"patient-gender"},bd={class:"patient-number"},Nd={class:"patient-time"},Pd={class:"completion-time"},Rd={key:1,class:"empty-state"},Ad={class:"tab-panel"},Id={class:"patient-list"},Od={class:"patient-name"},xd={class:"patient-age"},Md={class:"patient-gender"},Dd={class:"patient-number"},Ld={class:"patient-time"},kd={class:"completion-time"},Ud={key:1,class:"empty-state"},$d={key:0,class:"compact-qrcode"},Bd={class:"qrcode-display"},Fd=["src"],Wd={class:"dialog-buttons"},Hd={key:1,class:"loading-overlay"},jd={key:3,class:"processing-overlay"},zd={class:"processing-dialog"},Vd={class:"processing-content"},Gd={class:"organ-info"},Kd={class:"organ-name"},Qd={class:"progress-section"},qd={class:"progress-info"},Yd={class:"step-text"},Jd={class:"progress-percent"},Zd={class:"progress-bar"},Xd={class:"processing-status"},th={class:"status-text"},eh={class:"processing-footer"};function sh(t,e,s,i,n,o){var a,l,h,u,p,y;const r=gi("ToastNotification");return D(),L("div",bf,[ct(r,{ref:"toastNotification",config:n.toastConfig,onNotificationShown:o.handleNotificationShown,onNotificationRemoved:o.handleNotificationRemoved,onAllNotificationsCleared:o.handleAllNotificationsCleared},null,8,["config","onNotificationShown","onNotificationRemoved","onAllNotificationsCleared"]),d("div",Nf,[d("div",Pf,[e[19]||(e[19]=d("span",{class:"app-title"},"AI\u78C1\u611F\u8BC4\u4F30",-1)),d("div",Rf,[d("button",{onClick:e[0]||(e[0]=(...g)=>o.minimizeWindow&&o.minimizeWindow(...g)),class:"minimize-btn",title:"\u6700\u5C0F\u5316 (F9)"},e[18]||(e[18]=[d("svg",{width:"16",height:"16",viewBox:"0 0 16 16"},[d("path",{d:"M3 8h10v1H3V8z",fill:"currentColor"})],-1)]))])]),d("div",Af,[d("div",If,[d("div",Of,[n.registrationQRCodeUrl?(D(),L("img",{key:0,src:n.registrationQRCodeUrl,alt:"\u62A5\u5230\u4E8C\u7EF4\u7801",class:"registration-qrcode-image"},null,8,xf)):(D(),L("p",Mf,"\u6B63\u5728\u751F\u6210\u62A5\u5230\u4E8C\u7EF4\u7801..."))])]),d("div",Df,[d("div",Lf,[e[20]||(e[20]=d("span",{class:"label"},"\u68C0\u6D4B\u7AD9\u70B9:",-1)),d("span",kf,R(((l=(a=o.config)==null?void 0:a.SiteInfo)==null?void 0:l.SiteName)||"\u52A0\u8F7D\u4E2D..."),1)]),d("div",Uf,[e[21]||(e[21]=d("span",{class:"label"},"\u7AD9\u70B9\u7F16\u53F7:",-1)),d("span",$f,R(((u=(h=o.config)==null?void 0:h.SiteInfo)==null?void 0:u.SiteID)||"\u52A0\u8F7D\u4E2D..."),1)]),d("div",Bf,[e[22]||(e[22]=d("span",{class:"label"},"\u8BBE\u5907\u7F16\u53F7:",-1)),d("span",Ff,R(((y=(p=o.config)==null?void 0:p.device_info)==null?void 0:y.device_no)||"\u672A\u8BBE\u7F6E"),1)]),d("div",Wf,[e[23]||(e[23]=d("span",{class:"label"},"\u4ECA\u65E5\u62A5\u5230:",-1)),d("span",Hf,R(o.todayPatientCount)+"\u4EBA",1)]),d("div",jf,[e[24]||(e[24]=d("span",{class:"label"},"\u4ECA\u65E5\u68C0\u6D4B:",-1)),d("span",zf,R(o.currentRegistrationNumber)+"\u4EBA",1)])])])]),d("div",Vf,[d("div",Gf,[d("div",{class:"progress-bar",style:_e({width:n.currentProgress+"%",backgroundColor:n.currentMode==="B"?"#2196f3":"#4caf50"})},null,4),d("span",Kf,R(n.currentOperationStatus),1),d("div",Qf,[d("button",{onClick:e[1]||(e[1]=(...g)=>o.captureScreenshotByBtn&&o.captureScreenshotByBtn(...g)),class:"operation-btn capture-btn",disabled:n.captureButtonDisabled},e[25]||(e[25]=[Zt("\u6A21\u5F0F"),d("br",null,null,-1),Zt("\u5206\u6790")]),8,qf),d("button",{onClick:e[2]||(e[2]=(...g)=>o.submitUserScreenshotTaskToAI&&o.submitUserScreenshotTaskToAI(...g)),class:"operation-btn submit-btn",innerHTML:o.submitButtonText},null,8,Yf)])]),d("div",Jf,[e[27]||(e[27]=d("div",{class:"section-header"},[d("span",null,"\u5F53\u524D\u53D7\u68C0\u8005")],-1)),o.currentPatient?(D(),L("div",Zf,[d("div",Xf,[d("span",td,R(o.currentPatient.name),1),d("span",ed,R(o.currentPatient.number),1)])])):(D(),L("div",sd,e[26]||(e[26]=[d("div",{class:"current-patient-info"},[d("span",{class:"empty-text"},"\u6682\u65E0\u53D7\u68C0\u8005")],-1)])))]),d("div",id,[d("div",nd,[d("button",{class:Gt(["tab-button",{active:n.activeTab==="pending"}]),onClick:e[3]||(e[3]=g=>o.switchTab("pending"))},[e[28]||(e[28]=d("span",{class:"tab-icon"},"\u23F3",-1)),d("span",od,"\u5F85\u68C0\u6D4B ("+R(n.pendingRegistrations.length)+")",1)],2),d("button",{class:Gt(["tab-button",{active:n.activeTab==="completed"}]),onClick:e[4]||(e[4]=g=>o.switchTab("completed"))},[e[29]||(e[29]=d("span",{class:"tab-icon"},"\u2705",-1)),d("span",rd,"\u5DF2\u5B8C\u6210 ("+R(n.completedPatients.length)+")",1)],2),d("button",{class:Gt(["tab-button",{active:n.activeTab==="unanalyzed"}]),onClick:e[5]||(e[5]=g=>o.switchTab("unanalyzed"))},[e[30]||(e[30]=d("span",{class:"tab-icon"},"\u{1F4CA}",-1)),d("span",ad,"\u5F85\u5206\u6790 ("+R(n.unanalyzedPatients.length)+")",1)],2),d("button",{onClick:e[6]||(e[6]=(...g)=>o.refreshAllLists&&o.refreshAllLists(...g)),class:"refresh-btn",disabled:n.isRefreshing},"\u{1F504}",8,ld)]),d("div",cd,[be(d("div",ud,[d("div",fd,[(D(!0),L(dt,null,Qe(o.displayedPendingRegistrations,(g,A)=>(D(),L("div",{key:"pending-"+A,class:Gt(["patient-item",{selected:o.selectedPatientIndex===A}]),onClick:$=>o.selectPatient(A)},[be(d("input",{type:"radio",name:"patient-radio",value:A,"onUpdate:modelValue":e[7]||(e[7]=$=>o.selectedPatientIndex=$),class:"patient-radio"},null,8,hd),[[cc,o.selectedPatientIndex]]),d("span",pd,R(g.name),1),d("span",gd,R(o.calculateAge(g.userInfo&&g.userInfo[0]?g.userInfo[0].birthday:"")),1),d("span",md,R(o.formatGender(g.userInfo&&g.userInfo[0]?g.userInfo[0].gender:0)),1),d("span",wd,R(g.number),1),d("span",yd,R(o.formatTime(g.register_time)),1),e[31]||(e[31]=d("span",{class:"patient-status pending"},"\u5F85\u68C0\u6D4B",-1))],10,dd))),128)),n.pendingRegistrations.length>n.pendingDisplayLimit?(D(),L("div",{key:0,class:"more-patients",onClick:e[8]||(e[8]=(...g)=>o.loadMorePendingRegistrations&&o.loadMorePendingRegistrations(...g))}," \u70B9\u51FB\u52A0\u8F7D\u66F4\u591A (\u8FD8\u6709 "+R(n.pendingRegistrations.length-n.pendingDisplayLimit)+" \u4F4D\u5019\u68C0\u8005) ",1)):wt("",!0),n.pendingRegistrations.length===0?(D(),L("div",Sd,e[32]||(e[32]=[d("span",{class:"empty-icon"},"\u{1F4CB}",-1),d("span",{class:"empty-text"},"\u6682\u65E0\u5F85\u68C0\u6D4B\u7684\u5019\u68C0\u8005",-1)]))):wt("",!0)])],512),[[ri,n.activeTab==="pending"]]),be(d("div",_d,[d("div",Td,[(D(!0),L(dt,null,Qe(o.displayedCompletedPatients,(g,A)=>(D(),L("div",{key:"completed-"+A,class:"patient-item completed"},[d("span",vd,R(g.name),1),d("span",Cd,R(o.calculateAge(g.userInfo&&g.userInfo[0]?g.userInfo[0].birthday:"")),1),d("span",Ed,R(o.formatGender(g.userInfo&&g.userInfo[0]?g.userInfo[0].gender:0)),1),d("span",bd,R(g.number),1),d("span",Nd,R(o.formatTime(g.register_time)),1),e[33]||(e[33]=d("span",{class:"patient-status completed"},"\u5DF2\u5B8C\u6210",-1)),d("span",Pd,R(o.formatCompletionTime(g.completion_time)),1)]))),128)),n.completedPatients.length>n.completedDisplayLimit?(D(),L("div",{key:0,class:"more-patients",onClick:e[9]||(e[9]=(...g)=>o.loadMoreCompletedPatients&&o.loadMoreCompletedPatients(...g))}," \u70B9\u51FB\u52A0\u8F7D\u66F4\u591A (\u8FD8\u6709 "+R(n.completedPatients.length-n.completedDisplayLimit)+" \u4F4D\u5DF2\u5B8C\u6210) ",1)):wt("",!0),n.completedPatients.length===0?(D(),L("div",Rd,e[34]||(e[34]=[d("span",{class:"empty-icon"},"\u{1F389}",-1),d("span",{class:"empty-text"},"\u4ECA\u65E5\u6682\u65E0\u5DF2\u5B8C\u6210\u68C0\u6D4B\u7684\u60A3\u8005",-1)]))):wt("",!0)])],512),[[ri,n.activeTab==="completed"]]),be(d("div",Ad,[d("div",Id,[(D(!0),L(dt,null,Qe(o.displayedUnanalyzedPatients,(g,A)=>(D(),L("div",{key:"unanalyzed-"+A,class:"patient-item unanalyzed"},[d("span",Od,R(g.name),1),d("span",xd,R(o.calculateAge(g.userInfo&&g.userInfo[0]?g.userInfo[0].birthday:"")),1),d("span",Md,R(o.formatGender(g.userInfo&&g.userInfo[0]?g.userInfo[0].gender:0)),1),d("span",Dd,R(g.number),1),d("span",Ld,R(o.formatTime(g.register_time)),1),e[35]||(e[35]=d("span",{class:"patient-status unanalyzed"},"\u5F85\u5206\u6790",-1)),d("span",kd,R(o.formatCompletionTime(g.completion_time)),1)]))),128)),n.unanalyzedPatients.length>n.unanalyzedDisplayLimit?(D(),L("div",{key:0,class:"more-patients",onClick:e[10]||(e[10]=(...g)=>o.loadMoreUnanalyzedPatients&&o.loadMoreUnanalyzedPatients(...g))}," \u70B9\u51FB\u52A0\u8F7D\u66F4\u591A (\u8FD8\u6709 "+R(n.unanalyzedPatients.length-n.unanalyzedDisplayLimit)+" \u4F4D\u5F85\u5206\u6790) ",1)):wt("",!0),n.unanalyzedPatients.length===0?(D(),L("div",Ud,e[36]||(e[36]=[d("span",{class:"empty-icon"},"\u{1F4CA}",-1),d("span",{class:"empty-text"},"\u4ECA\u65E5\u6682\u65E0\u5F85\u5206\u6790\u7684\u60A3\u8005",-1)]))):wt("",!0)])],512),[[ri,n.activeTab==="unanalyzed"]])])]),n.latestQRCode?(D(),L("div",$d,[e[37]||(e[37]=d("div",{class:"section-header"},[d("span",null,"\u5F53\u524D\u4E8C\u7EF4\u7801")],-1)),d("div",Bd,[d("img",{src:n.latestQRCode,alt:"\u60A3\u8005\u4E8C\u7EF4\u7801"},null,8,Fd)])])):wt("",!0)]),n.showAddPatientDialog?(D(),L("div",{key:0,class:"dialog-overlay",onClick:e[16]||(e[16]=g=>n.showAddPatientDialog=!1)},[d("div",{class:"dialog",onClick:e[15]||(e[15]=Hn(()=>{},["stop"]))},[e[39]||(e[39]=d("h3",null,"\u6DFB\u52A0\u60A3\u8005",-1)),d("form",{onSubmit:e[14]||(e[14]=Hn((...g)=>o.handleAddPatient&&o.handleAddPatient(...g),["prevent"]))},[be(d("input",{"onUpdate:modelValue":e[11]||(e[11]=g=>n.newPatient.name=g),placeholder:"\u60A3\u8005\u59D3\u540D",required:""},null,512),[[Wn,n.newPatient.name]]),be(d("input",{"onUpdate:modelValue":e[12]||(e[12]=g=>n.newPatient.registrationNumber=g),placeholder:"\u6302\u53F7\u53F7\u7801",required:""},null,512),[[Wn,n.newPatient.registrationNumber]]),d("div",Wd,[d("button",{type:"button",onClick:e[13]||(e[13]=g=>n.showAddPatientDialog=!1)},"\u53D6\u6D88"),e[38]||(e[38]=d("button",{type:"submit"},"\u6DFB\u52A0",-1))])],32)])])):wt("",!0),n.loading?(D(),L("div",Hd,[e[40]||(e[40]=d("div",{class:"loading-spinner"},null,-1)),d("p",null,R(n.loadingMessage),1)])):wt("",!0),o.notification.show?(D(),L("div",{key:2,class:Gt(["notification",o.notification.type])},R(o.notification.message),3)):wt("",!0),n.processingDialog.show?(D(),L("div",jd,[d("div",zd,[e[44]||(e[44]=Tl('<div class="processing-header"><div class="processing-icon"><svg class="processing-spinner" width="24" height="24" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416"><animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"></animate><animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"></animate></circle></svg></div><h3 class="processing-title">\u6B63\u5728\u5904\u7406\u5668\u5B98\u6570\u636E</h3></div>',1)),d("div",Vd,[d("div",Gd,[e[41]||(e[41]=d("span",{class:"organ-label"},"\u68C0\u6D4B\u5668\u5B98:",-1)),d("span",Kd,R(n.processingDialog.organName),1)]),d("div",Qd,[d("div",qd,[d("span",Yd,"\u6B65\u9AA4 "+R(n.processingDialog.currentStep)+" / "+R(n.processingDialog.totalSteps),1),d("span",Jd,R(n.processingDialog.progress)+"%",1)]),d("div",Zd,[d("div",{class:"progress-fill",style:_e({width:n.processingDialog.progress+"%"})},null,4)])]),d("div",Xd,[e[42]||(e[42]=d("div",{class:"status-dots"},[d("span",{class:"dot"}),d("span",{class:"dot"}),d("span",{class:"dot"})],-1)),d("span",th,R(n.processingDialog.message),1)])]),d("div",eh,[d("button",{onClick:e[17]||(e[17]=(...g)=>o.hideProcessingDialog&&o.hideProcessingDialog(...g)),class:"cancel-btn"},e[43]||(e[43]=[d("svg",{width:"16",height:"16",viewBox:"0 0 16 16"},[d("path",{d:"M8 16A8 8 0 1 1 8 0a8 8 0 0 1 0 16zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z",fill:"currentColor"})],-1),Zt(" \u53D6\u6D88\u5904\u7406 ")]))])])])):wt("",!0)])}const ih=Ks(Ef,[["render",sh]]);const Rr=gc(ih),nh=yc();Rr.use(nh);Rr.mount("#app");
