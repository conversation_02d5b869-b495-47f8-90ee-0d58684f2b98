# OCR调试日志使用指南

## 概述

为了更好地诊断OCR处理失败的问题，我们在 `extractOCRDataOptimized` 函数中添加了详细的日志记录功能。这些日志将帮助开发者了解OCR API响应的具体格式和数据提取过程中的每个步骤。

## 新增的日志功能

### 1. 响应数据基本信息
- 记录OCR API响应数据的长度
- 显示响应数据的前500个字符预览
- 在解析失败时显示完整的响应数据

### 2. 结构体解析状态
- 记录是否成功使用OptimizedOCRResponse结构体解析
- 在解析失败时自动回退到通用解析方法

### 3. rec_texts数据提取过程
详细记录在以下4个路径中查找rec_texts数据的过程：
- 路径1: `Result.TableRecResults[].PrunedResult.OverallOcrRes.RecTexts`
- 路径2: `Data.Result[].PrunedResult.RecTexts`
- 路径3: `PrunedResult.RecTexts`
- 路径4: `RecTexts`

### 4. 置信度数据提取
- 记录从不同数据源提取置信度的过程
- 支持新格式(`Data.OcrResult`)和旧格式(`Results.TextData`)的置信度数据
- 显示每个置信度值和最终的平均置信度

### 5. 数据清理和键值对提取
- 记录数据清理前后的rec_texts元素数量
- 显示清理后的前几个元素内容
- 记录提取到的键值对数量和内容（限制显示数量避免日志过长）

### 6. 器官名称提取
- 记录是否找到"0.000"标志的器官名称
- 在未找到时搜索其他可能的器官名称标志
- 显示最终的解析结果摘要

## 日志格式说明

所有新增的日志都使用 `[OCR解析]` 或 `[OCR回退解析]` 前缀，便于在日志文件中快速定位和过滤。

### 示例日志输出

```
[OCR解析] 开始解析OCR响应数据，数据长度: 1234 字节
[OCR解析] 响应数据预览: {"result":{"table_rec_results":[...]}}
[OCR解析] 结构体解析成功
[OCR解析] 开始提取rec_texts数据
[OCR解析] 检查路径1: Result.TableRecResults，数量: 1
[OCR解析] 检查TableRecResults[0].PrunedResult.OverallOcrRes.RecTexts，数量: 25
[OCR解析] 在路径1找到rec_texts数据，数量: 25
[OCR解析] 成功找到rec_texts字段，共25个元素
[OCR解析] rec_texts[0]: 检查项目
[OCR解析] rec_texts[1]: 结果
...
[OCR解析] 开始清理rec_texts数据
[OCR解析] 清理后rec_texts字段，共20个元素
[OCR解析] 开始提取键值对
[OCR解析] 提取到8个键值对
[OCR解析] 键值对[0.000]: 胃后壁
[OCR解析] 键值对[1.001]: 正常
...
[OCR解析] 开始提取器官名称
[OCR解析] 通过0.000标志找到器官名称: 胃后壁
[OCR解析] 解析完成，最终结果: 器官名称=胃后壁, 置信度=0.95, 键值对数量=8
```

## 使用方法

1. **重新编译应用程序**：
   ```bash
   go build -o MagneticOperator.exe
   ```

2. **运行应用程序并执行OCR操作**：
   - 启动应用程序
   - 执行智能截图功能
   - 观察控制台输出或查看日志文件

3. **查看日志文件**：
   - 日志文件位置: `logs/app_YYYY-MM-DD.log`
   - 使用文本编辑器或日志查看工具搜索 `[OCR解析]` 关键字

## 故障排除

### 常见问题和解决方案

1. **未找到rec_texts字段**：
   - 检查OCR API响应格式是否发生变化
   - 查看响应数据预览，确认数据结构
   - 可能需要更新OptimizedOCRResponse结构体定义

2. **置信度数据为0**：
   - 检查置信度数据源是否存在
   - 确认置信度数据的格式和类型
   - 可能需要添加新的置信度提取逻辑

3. **键值对提取失败**：
   - 查看清理后的rec_texts内容
   - 检查数据清理逻辑是否过于严格
   - 确认键值对匹配规则是否适用于当前数据格式

4. **器官名称提取失败**：
   - 确认是否存在"0.000"标志
   - 查看其他可能的器官名称标志
   - 可能需要更新器官名称提取逻辑

## 注意事项

- 详细日志会增加日志文件的大小，建议在生产环境中适当调整日志级别
- 响应数据预览限制为500个字符，完整数据仅在解析失败时显示
- 键值对和rec_texts元素的显示数量有限制，避免日志过长
- 这些日志主要用于开发和调试阶段，可以根据需要进行调整

## 后续改进建议

1. 添加日志级别控制，允许在配置文件中调整详细程度
2. 考虑将详细日志输出到单独的调试日志文件
3. 添加性能监控，记录各个解析步骤的耗时
4. 实现日志数据的结构化输出，便于自动化分析