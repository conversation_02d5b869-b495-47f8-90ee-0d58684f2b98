package services

import (
	"time"
)

// RoundService 负责处理与截图轮次相关的业务逻辑
type RoundService struct {
	roundManager *ScreenshotRoundManager
}

// NewRoundService 创建一个新的 RoundService
func NewRoundService(roundManager *ScreenshotRoundManager) *RoundService {
	return &RoundService{
		roundManager: roundManager,
	}
}

// StartNewRound 开始一个新的截图轮次
func (s *RoundService) StartNewRound(userName string) (*ScreenshotRound, error) {
	return s.roundManager.StartNewRound(userName)
}

// GetRound 获取指定轮次
func (s *RoundService) GetRound(roundNumber int) (*ScreenshotRound, bool) {
	return s.roundManager.GetRound(roundNumber)
}

// GetAllRounds 获取所有轮次
func (s *RoundService) GetAllRounds() map[int]*ScreenshotRound {
	return s.roundManager.GetAllRounds()
}

// GetCurrentRoundNumber 获取当前轮次号
func (s *RoundService) GetCurrentRoundNumber() int {
	return s.roundManager.GetCurrentRoundNumber()
}

// GetTotalRounds 获取总轮次数
func (s *RoundService) GetTotalRounds() int {
	return s.roundManager.GetTotalRounds()
}

// GetCompletedRoundsCount 获取已完成的轮次数
func (s *RoundService) GetCompletedRoundsCount() int {
	return s.roundManager.GetCompletedRoundsCount()
}

// WaitForRoundCompletion 等待指定轮次完成
func (s *RoundService) WaitForRoundCompletion(roundNumber int, timeout time.Duration) (*ScreenshotRound, error) {
	return s.roundManager.WaitForRoundCompletion(roundNumber, timeout)
}