package services

import (
	"context"
	"fmt"
	"time"

	"MagneticOperator/app/utils"
)

// ScreenshotTaskHandler 截图任务处理器
type ScreenshotTaskHandler struct {
	screenshotService *ScreenshotService
	apiService        *APIService
	app               AppInterface // 添加App接口引用
	taskType          TaskType
	timeout           time.Duration
}

// NewScreenshotTaskHandler 创建截图任务处理器
func NewScreenshotTaskHandler(screenshotService *ScreenshotService, apiService *APIService, app AppInterface, taskType TaskType) *ScreenshotTaskHandler {
	timeout := 30 * time.Second // 默认30秒超时

	// 根据任务类型调整超时时间
	switch taskType {
	case TaskTypeScreenshotA:
		timeout = 25 * time.Second // 器官问题来源分析相对简单
	case TaskTypeScreenshotB:
		timeout = 35 * time.Second // 生化平衡分析可能需要更多时间
	case TaskTypeScreenshotC:
		timeout = 40 * time.Second // 病理形态学分析最复杂
	}

	return &ScreenshotTaskHandler{
		screenshotService: screenshotService,
		apiService:        apiService,
		app:               app,
		taskType:          taskType,
		timeout:           timeout,
	}
}

// Execute 执行截图任务
func (h *ScreenshotTaskHandler) Execute(ctx context.Context, task *Task) (interface{}, error) {
	utils.LogOperation(fmt.Sprintf("执行截图任务-%s", task.Mode), task.UserName, "")

	// 直接调用App的ProcessScreenshotWorkflow方法，这样就能使用真实的OCR和API调用
	result, err := h.app.ProcessScreenshotWorkflow(task.Mode)
	if err != nil {
		return nil, fmt.Errorf("截图工作流程失败: %v", err)
	}

	// ProcessScreenshotWorkflow返回的是string类型，创建一个结果map
	finalResult := map[string]interface{}{
		"result":       result,
		"mode":         task.Mode,
		"user_name":    task.UserName,
		"round_number": task.RoundNumber,
		"task_id":      task.ID,
		"completed_at": time.Now().Format("2006-01-02 15:04:05"),
	}

	utils.LogOperation(fmt.Sprintf("截图任务完成-%s", task.Mode), task.UserName, "")
	return finalResult, nil
}

// GetType 获取任务类型
func (h *ScreenshotTaskHandler) GetType() TaskType {
	return h.taskType
}

// GetTimeout 获取超时时间
func (h *ScreenshotTaskHandler) GetTimeout() time.Duration {
	return h.timeout
}

// processImageWithOCR 处理图像OCR识别
func (h *ScreenshotTaskHandler) processImageWithOCR(ctx context.Context, imagePath string) (interface{}, error) {
	// 检查上下文
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	// 注意：这里需要通过App实例来调用OCR服务
	// 由于架构限制，暂时返回模拟结果
	// 实际实现中应该通过依赖注入获取OCR服务
	return map[string]interface{}{
		"text":       "模拟OCR识别结果",
		"confidence": 0.95,
		"image_path": imagePath,
	}, nil
}

// uploadImageToCloud 上传图片到云端
func (h *ScreenshotTaskHandler) uploadImageToCloud(ctx context.Context, imagePath, userName string) (string, error) {
	// 检查上下文
	select {
	case <-ctx.Done():
		return "", ctx.Err()
	default:
	}

	if h.apiService == nil {
		return "", fmt.Errorf("API服务未初始化")
	}

	// 注意：APIService没有UploadImage方法，需要使用其他方式
	// 暂时返回模拟URL
	picURL := fmt.Sprintf("https://example.com/images/%s_%d.png", userName, time.Now().Unix())

	return picURL, nil
}

// callCozeAPI 调用Coze API
func (h *ScreenshotTaskHandler) callCozeAPI(ctx context.Context, picURL, userName, mode string) (interface{}, error) {
	// 检查上下文
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	if h.apiService == nil {
		return nil, fmt.Errorf("API服务未初始化")
	}

	// 注意：APIService没有CallCozeAPI方法，需要使用CallCozeWorkflow
	// 暂时返回模拟结果
	result := map[string]interface{}{
		"analysis": "模拟分析结果",
		"mode":     mode,
		"pic_url":  picURL,
	}

	return result, nil
}

// OCRTaskHandler OCR任务处理器
type OCRTaskHandler struct {
	ocrService OCRInterface
	timeout    time.Duration
}

// NewOCRTaskHandler 创建OCR任务处理器
func NewOCRTaskHandler(ocrService OCRInterface) *OCRTaskHandler {
	return &OCRTaskHandler{
		ocrService: ocrService,
		timeout:    15 * time.Second, // OCR任务相对较快
	}
}

// Execute 执行OCR任务
func (h *OCRTaskHandler) Execute(ctx context.Context, task *Task) (interface{}, error) {
	if h.ocrService == nil {
		return nil, fmt.Errorf("OCR服务未初始化")
	}

	// 从任务元数据中获取图片路径
	imagePath, ok := task.Metadata["image_path"].(string)
	if !ok {
		return nil, fmt.Errorf("任务元数据中缺少图片路径")
	}

	// 检查上下文
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	// 执行OCR识别 - 使用正确的方法名
	result, err := h.ocrService.ProcessImageWithDetails(imagePath)
	if err != nil {
		return nil, fmt.Errorf("OCR处理失败: %v", err)
	}

	return result, nil
}

// GetType 获取任务类型
func (h *OCRTaskHandler) GetType() TaskType {
	return TaskTypeOCR
}

// GetTimeout 获取超时时间
func (h *OCRTaskHandler) GetTimeout() time.Duration {
	return h.timeout
}

// UploadTaskHandler 上传任务处理器
type UploadTaskHandler struct {
	apiService *APIService
	timeout    time.Duration
}

// NewUploadTaskHandler 创建上传任务处理器
func NewUploadTaskHandler(apiService *APIService) *UploadTaskHandler {
	return &UploadTaskHandler{
		apiService: apiService,
		timeout:    20 * time.Second, // 上传任务可能需要较长时间
	}
}

// Execute 执行上传任务
func (h *UploadTaskHandler) Execute(ctx context.Context, task *Task) (interface{}, error) {
	if h.apiService == nil {
		return nil, fmt.Errorf("API服务未初始化")
	}

	// 从任务元数据中获取文件路径
	filePath, ok := task.Metadata["file_path"].(string)
	if !ok {
		return nil, fmt.Errorf("任务元数据中缺少文件路径")
	}

	// 检查上下文
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	// 执行上传 - APIService没有UploadImage方法，暂时返回模拟URL
	url := fmt.Sprintf("https://example.com/uploads/%s_%d.png", task.UserName, time.Now().Unix())

	return map[string]interface{}{
		"url":       url,
		"file_path": filePath,
		"user_name": task.UserName,
	}, nil
}

// GetType 获取任务类型
func (h *UploadTaskHandler) GetType() TaskType {
	return TaskTypeUpload
}

// GetTimeout 获取超时时间
func (h *UploadTaskHandler) GetTimeout() time.Duration {
	return h.timeout
}
