{"logId": "cdb60e26-913c-4b21-b6be-206582b3cf22", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 8, "label": "table", "score": 0.9862777590751648, "coordinate": [13.798255920410156, 74.84362030029297, 767.7372436523438, 1713.0750732421875]}, {"cls_id": 9, "label": "table_title", "score": 0.6330050230026245, "coordinate": [19.603797912597656, 27.25540542602539, 520.6676025390625, 62.98589324951172]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.5154430866241455, "coordinate": [19.603797912597656, 27.25540542602539, 520.6676025390625, 62.98589324951172]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 79], [384, 79], [384, 102], [192, 102]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[194, 156], [495, 156], [495, 179], [194, 179]], [[98, 179], [161, 179], [161, 204], [98, 204]], [[194, 181], [423, 181], [423, 204], [194, 204]], [[98, 204], [161, 204], [161, 229], [98, 229]], [[192, 204], [266, 204], [266, 231], [192, 231]], [[98, 229], [162, 229], [162, 256], [98, 256]], [[192, 231], [319, 227], [320, 254], [193, 258]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [556, 257], [556, 281], [192, 281]], [[98, 281], [161, 281], [161, 306], [98, 306]], [[196, 284], [310, 284], [310, 304], [196, 304]], [[98, 306], [161, 306], [161, 332], [98, 332]], [[194, 307], [727, 307], [727, 331], [194, 331]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [428, 336], [428, 354], [196, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[194, 359], [412, 359], [412, 383], [194, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[194, 384], [393, 384], [393, 408], [194, 408]], [[98, 408], [155, 408], [155, 434], [98, 434]], [[194, 409], [443, 409], [443, 433], [194, 433]], [[98, 434], [155, 434], [155, 459], [98, 459]], [[192, 434], [474, 434], [474, 458], [192, 458]], [[98, 458], [155, 458], [155, 484], [98, 484]], [[194, 463], [397, 463], [397, 481], [194, 481]], [[98, 484], [155, 484], [155, 509], [98, 509]], [[194, 486], [404, 486], [404, 509], [194, 509]], [[98, 511], [155, 511], [155, 536], [98, 536]], [[194, 511], [567, 511], [567, 534], [194, 534]], [[98, 536], [153, 536], [153, 561], [98, 561]], [[192, 536], [310, 536], [310, 561], [192, 561]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[192, 561], [310, 561], [310, 586], [192, 586]], [[98, 586], [155, 586], [155, 611], [98, 611]], [[194, 588], [438, 588], [438, 611], [194, 611]], [[98, 611], [155, 611], [155, 638], [98, 638]], [[192, 613], [377, 613], [377, 636], [192, 636]], [[98, 636], [155, 636], [155, 663], [98, 663]], [[192, 640], [408, 640], [408, 663], [192, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[192, 663], [301, 663], [301, 688], [192, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[188, 686], [227, 686], [227, 717], [188, 717]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[190, 713], [262, 713], [262, 740], [190, 740]], [[98, 740], [157, 740], [157, 765], [98, 765]], [[192, 740], [689, 740], [689, 763], [192, 763]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[192, 765], [325, 765], [325, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [294, 792], [294, 817], [192, 817]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[190, 815], [244, 815], [244, 844], [190, 844]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[190, 842], [364, 842], [364, 867], [190, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [449, 869], [449, 892], [192, 892]], [[98, 892], [155, 892], [155, 919], [98, 919]], [[192, 894], [449, 894], [449, 917], [192, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[194, 919], [441, 919], [441, 944], [194, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [349, 946], [349, 969], [194, 969]], [[98, 969], [155, 969], [155, 996], [98, 996]], [[192, 971], [524, 971], [524, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [332, 996], [332, 1021], [192, 1021]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[194, 1022], [508, 1022], [508, 1046], [194, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[194, 1047], [606, 1047], [606, 1071], [194, 1071]], [[98, 1071], [157, 1071], [157, 1096], [98, 1096]], [[192, 1072], [733, 1072], [733, 1096], [192, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [347, 1098], [347, 1121], [192, 1121]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[192, 1123], [367, 1123], [367, 1146], [192, 1146]], [[98, 1148], [155, 1148], [155, 1173], [98, 1173]], [[192, 1149], [504, 1149], [504, 1173], [192, 1173]], [[98, 1173], [157, 1173], [157, 1198], [98, 1198]], [[194, 1173], [412, 1173], [412, 1196], [194, 1196]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [456, 1198], [456, 1223], [190, 1225]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[190, 1224], [260, 1224], [260, 1251], [190, 1251]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1251], [430, 1251], [430, 1274], [192, 1274]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[194, 1276], [482, 1276], [482, 1300], [194, 1300]], [[98, 1301], [157, 1301], [157, 1326], [98, 1326]], [[196, 1305], [639, 1305], [639, 1323], [196, 1323]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[194, 1328], [460, 1328], [460, 1351], [194, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [476, 1353], [476, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1378], [500, 1378], [500, 1401], [192, 1401]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [428, 1403], [428, 1426], [192, 1426]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[192, 1428], [327, 1428], [327, 1453], [192, 1453]], [[98, 1453], [155, 1453], [155, 1478], [98, 1478]], [[192, 1455], [356, 1455], [356, 1478], [192, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[192, 1480], [314, 1480], [314, 1505], [192, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[190, 1503], [242, 1503], [242, 1532], [190, 1532]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[190, 1530], [332, 1530], [332, 1555], [190, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[188, 1553], [264, 1553], [264, 1586], [188, 1586]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[191, 1578], [370, 1584], [369, 1611], [190, 1605]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1607], [402, 1607], [402, 1632], [192, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[192, 1634], [375, 1634], [375, 1657], [192, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1659], [478, 1659], [478, 1682], [192, 1682]], [[100, 1682], [155, 1682], [155, 1709], [100, 1709]], [[192, 1684], [286, 1684], [286, 1712], [192, 1712]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["按照标准图谱相似度递减列表：", "0.000", "厦部第1腰椎水平截面", "4.479", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.054", "C反应蛋白C-REACTIVEPROTEIN", "0.072", "血尿酸SERUMURICACID", "0.074", "脂肪酶*", "0.075", "血管紧张素Ⅱ*", "0.087", "胆固醇COMMONPLASMA CHOLESTERIN", "0.099", "血管紧张素I", "0.101", "血浆丰配化脂肪酸NONETHERIZEDFATTYACIDSOF PLASMA", "0.161", "血钾PLASMAPOTASSIUM", "0.095", "血清蛋白SERUM ALBUMEN", "0.097", "血红蛋白HAEMOGLOBIN", "0.103", "尿中蛋白质PROTEININURINE", "0.105", "PERIPHERIC BLOOD LEUCOCYTES", "0.107", "嗜碱性粒细胞BASOPHILS", "0.107", "血红血球ERYTHROCYTES", "0.114", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.131", "免疫球蛋白G*", "0.132", "免疫球蛋白M*", "0.133", "尿白血球URINELEUCOCYTES", "0.135", "单核细胞MONOCYTES", "0.143", "血清蛋白SERUMPROTEIN", "0.147", "BETA球蛋白", "0.201", "锂*", "0.092", "胆汁酸*", "0.093", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.099", "ALPHA2球蛋白*", "0.102", "胰高血糖素", "0.104", "糖苷*", "0.105", "红细胞沉降率(ESR)", "0.106", "血清补体SERUMCOMPLEMENT", "0.107", "肿瘤标志物MELANOGENE在尿*", "0.107", "血清溶菌酵SERUMLYSOZYME", "0.109", "血细胞比容，全血", "0.111", "游离胆固醇FREEPLASMACHOLESTERIN", "0.113", "糖基化血红蛋白*", "0.114", "血清铜蓝蛋白SERUMCERULOPLASMIN", "0.115", "肌酸磷酸酵素COMMON CREATINPHOSPHOKINASE", "0.115", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.116", "RHEUMOFACTOR*", "0.116", "肿瘤标志物胸苷激酶", "0.117", "血清淀粉酵素SERUMALPHAAMYLASE", "0.117", "尿肌配URINECREATININE", "0.117", "伽马球蛋白GAMMA-GLOBULINS", "0.117", "铁蛋白*", "0.118", "嗜酸性粒细胞EOSINOPHILES", "0.118", "促肾上腺皮质激素CORTICOTROPIN", "0.118", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.118", "尿中肾上腺素URINEADRENALIN", "0.118", "嗜中性粒细胞STABNEUTROPHILS", "0.119", "生长激素SOMATOTROPICHORMONE", "0.119", "血组织胺BLOODHISTAMINE", "0.119", "ALPHA1球蛋白*", "0.120", "血糖BLOOD SUGAR", "0.120", "甲状腺球蛋白*", "0.120", "肾素*", "0.120", "抗链球菌溶血素*", "0.121", "催乳素*", "0.121", "ALPHA1-抗胰蛋白酶*", "0.123", "维生素B1（THIAMINE）*", "0.126", "DELTA氨基乙酰丙酸", "0.126", "血浆磷脂PLASMAPHOSPHOTIDES", "0.127", "维生素B6*"], "rec_scores": [0.9955605864524841, 0.9992006421089172, 0.9220801591873169, 0.9988524317741394, 0.9983981251716614, 0.9665411710739136, 0.9999231100082397, 0.9886558651924133, 0.9999233484268188, 0.9981778860092163, 0.9998815655708313, 0.9787902235984802, 0.9999483227729797, 0.941586971282959, 0.9999241828918457, 0.9782752394676208, 0.999923050403595, 0.977410614490509, 0.9998477697372437, 0.9450623989105225, 0.9999014735221863, 0.9814251065254211, 0.9996863603591919, 0.9807018041610718, 0.9996916651725769, 0.9984849095344543, 0.9995733499526978, 0.9969378113746643, 0.9995217323303223, 0.9698492884635925, 0.9994786381721497, 0.9812630414962769, 0.9993487596511841, 0.9961856007575989, 0.9993133544921875, 0.993124783039093, 0.9994786381721497, 0.9875865578651428, 0.9995611310005188, 0.992206871509552, 0.9995157122612, 0.9925421476364136, 0.9995859265327454, 0.997487485408783, 0.9995468258857727, 0.9955726265907288, 0.9991366267204285, 0.9935155510902405, 0.9994516372680664, 0.9606345295906067, 0.9997864961624146, 0.9675710201263428, 0.999636173248291, 0.9947442412376404, 0.9995541572570801, 0.9422779083251953, 0.9995965957641602, 0.997287392616272, 0.9994552731513977, 0.9665315747261047, 0.9994734525680542, 0.9244000911712646, 0.9993106722831726, 0.998449444770813, 0.9994791746139526, 0.9935643672943115, 0.9994699358940125, 0.99184250831604, 0.9992948770523071, 0.9944967031478882, 0.9981158375740051, 0.9958582520484924, 0.9994648098945618, 0.9525598287582397, 0.9989427328109741, 0.9945222735404968, 0.9993501901626587, 0.9832534790039062, 0.9991741180419922, 0.9849311113357544, 0.9993810653686523, 0.9894301891326904, 0.9990262985229492, 0.9895035624504089, 0.9990242123603821, 0.998862624168396, 0.999039351940155, 0.9629787802696228, 0.9990525245666504, 0.9961625337600708, 0.999039351940155, 0.9330690503120422, 0.9993749856948853, 0.9977938532829285, 0.9994460940361023, 0.998503565788269, 0.9994057416915894, 0.9973668456077576, 0.9992116689682007, 0.9933547973632812, 0.9993749856948853, 0.997769832611084, 0.9993714094161987, 0.9966481924057007, 0.9986928701400757, 0.9920355677604675, 0.9986355900764465, 0.9741079211235046, 0.9991754293441772, 0.9675531983375549, 0.999508261680603, 0.9346173405647278, 0.9993797540664673, 0.8804211616516113, 0.9992661476135254, 0.9690635800361633, 0.9995349645614624, 0.9840336441993713, 0.9995824098587036, 0.987791121006012, 0.9994745254516602, 0.9170867204666138, 0.9990471005439758, 0.9954785704612732, 0.9991771578788757, 0.9852492809295654, 0.9992483854293823, 0.9778856635093689], "rec_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 79], [384, 79], [384, 102], [192, 102]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[194, 156], [495, 156], [495, 179], [194, 179]], [[98, 179], [161, 179], [161, 204], [98, 204]], [[194, 181], [423, 181], [423, 204], [194, 204]], [[98, 204], [161, 204], [161, 229], [98, 229]], [[192, 204], [266, 204], [266, 231], [192, 231]], [[98, 229], [162, 229], [162, 256], [98, 256]], [[192, 231], [319, 227], [320, 254], [193, 258]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [556, 257], [556, 281], [192, 281]], [[98, 281], [161, 281], [161, 306], [98, 306]], [[196, 284], [310, 284], [310, 304], [196, 304]], [[98, 306], [161, 306], [161, 332], [98, 332]], [[194, 307], [727, 307], [727, 331], [194, 331]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [428, 336], [428, 354], [196, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[194, 359], [412, 359], [412, 383], [194, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[194, 384], [393, 384], [393, 408], [194, 408]], [[98, 408], [155, 408], [155, 434], [98, 434]], [[194, 409], [443, 409], [443, 433], [194, 433]], [[98, 434], [155, 434], [155, 459], [98, 459]], [[192, 434], [474, 434], [474, 458], [192, 458]], [[98, 458], [155, 458], [155, 484], [98, 484]], [[194, 463], [397, 463], [397, 481], [194, 481]], [[98, 484], [155, 484], [155, 509], [98, 509]], [[194, 486], [404, 486], [404, 509], [194, 509]], [[98, 511], [155, 511], [155, 536], [98, 536]], [[194, 511], [567, 511], [567, 534], [194, 534]], [[98, 536], [153, 536], [153, 561], [98, 561]], [[192, 536], [310, 536], [310, 561], [192, 561]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[192, 561], [310, 561], [310, 586], [192, 586]], [[98, 586], [155, 586], [155, 611], [98, 611]], [[194, 588], [438, 588], [438, 611], [194, 611]], [[98, 611], [155, 611], [155, 638], [98, 638]], [[192, 613], [377, 613], [377, 636], [192, 636]], [[98, 636], [155, 636], [155, 663], [98, 663]], [[192, 640], [408, 640], [408, 663], [192, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[192, 663], [301, 663], [301, 688], [192, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[188, 686], [227, 686], [227, 717], [188, 717]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[190, 713], [262, 713], [262, 740], [190, 740]], [[98, 740], [157, 740], [157, 765], [98, 765]], [[192, 740], [689, 740], [689, 763], [192, 763]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[192, 765], [325, 765], [325, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [294, 792], [294, 817], [192, 817]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[190, 815], [244, 815], [244, 844], [190, 844]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[190, 842], [364, 842], [364, 867], [190, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [449, 869], [449, 892], [192, 892]], [[98, 892], [155, 892], [155, 919], [98, 919]], [[192, 894], [449, 894], [449, 917], [192, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[194, 919], [441, 919], [441, 944], [194, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [349, 946], [349, 969], [194, 969]], [[98, 969], [155, 969], [155, 996], [98, 996]], [[192, 971], [524, 971], [524, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [332, 996], [332, 1021], [192, 1021]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[194, 1022], [508, 1022], [508, 1046], [194, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[194, 1047], [606, 1047], [606, 1071], [194, 1071]], [[98, 1071], [157, 1071], [157, 1096], [98, 1096]], [[192, 1072], [733, 1072], [733, 1096], [192, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [347, 1098], [347, 1121], [192, 1121]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[192, 1123], [367, 1123], [367, 1146], [192, 1146]], [[98, 1148], [155, 1148], [155, 1173], [98, 1173]], [[192, 1149], [504, 1149], [504, 1173], [192, 1173]], [[98, 1173], [157, 1173], [157, 1198], [98, 1198]], [[194, 1173], [412, 1173], [412, 1196], [194, 1196]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [456, 1198], [456, 1223], [190, 1225]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[190, 1224], [260, 1224], [260, 1251], [190, 1251]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1251], [430, 1251], [430, 1274], [192, 1274]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[194, 1276], [482, 1276], [482, 1300], [194, 1300]], [[98, 1301], [157, 1301], [157, 1326], [98, 1326]], [[196, 1305], [639, 1305], [639, 1323], [196, 1323]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[194, 1328], [460, 1328], [460, 1351], [194, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [476, 1353], [476, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1378], [500, 1378], [500, 1401], [192, 1401]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [428, 1403], [428, 1426], [192, 1426]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[192, 1428], [327, 1428], [327, 1453], [192, 1453]], [[98, 1453], [155, 1453], [155, 1478], [98, 1478]], [[192, 1455], [356, 1455], [356, 1478], [192, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[192, 1480], [314, 1480], [314, 1505], [192, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[190, 1503], [242, 1503], [242, 1532], [190, 1532]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[190, 1530], [332, 1530], [332, 1555], [190, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[188, 1553], [264, 1553], [264, 1586], [188, 1586]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[191, 1578], [370, 1584], [369, 1611], [190, 1605]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1607], [402, 1607], [402, 1632], [192, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[192, 1634], [375, 1634], [375, 1657], [192, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1659], [478, 1659], [478, 1682], [192, 1682]], [[100, 1682], [155, 1682], [155, 1709], [100, 1709]], [[192, 1684], [286, 1684], [286, 1712], [192, 1712]]], "rec_boxes": [[20, 30, 519, 61], [98, 77, 157, 102], [192, 79, 384, 102], [98, 102, 157, 127], [192, 102, 270, 129], [194, 129, 462, 152], [98, 154, 161, 179], [194, 156, 495, 179], [98, 179, 161, 204], [194, 181, 423, 204], [98, 204, 161, 229], [192, 204, 266, 231], [98, 229, 162, 256], [192, 227, 320, 258], [98, 256, 161, 281], [192, 257, 556, 281], [98, 281, 161, 306], [196, 284, 310, 304], [98, 306, 161, 332], [194, 307, 727, 331], [98, 332, 161, 358], [196, 336, 428, 354], [98, 358, 157, 383], [194, 359, 412, 383], [98, 383, 155, 409], [194, 384, 393, 408], [98, 408, 155, 434], [194, 409, 443, 433], [98, 434, 155, 459], [192, 434, 474, 458], [98, 458, 155, 484], [194, 463, 397, 481], [98, 484, 155, 509], [194, 486, 404, 509], [98, 511, 155, 536], [194, 511, 567, 534], [98, 536, 153, 561], [192, 536, 310, 561], [98, 561, 155, 588], [192, 561, 310, 586], [98, 586, 155, 611], [194, 588, 438, 611], [98, 611, 155, 638], [192, 613, 377, 636], [98, 636, 155, 663], [192, 640, 408, 663], [98, 663, 155, 688], [192, 663, 301, 688], [98, 688, 155, 715], [188, 686, 227, 717], [98, 713, 157, 740], [190, 713, 262, 740], [98, 740, 157, 765], [192, 740, 689, 763], [98, 765, 157, 790], [192, 765, 325, 790], [98, 790, 157, 817], [192, 792, 294, 817], [98, 815, 157, 842], [190, 815, 244, 844], [98, 840, 157, 867], [190, 842, 364, 867], [98, 867, 157, 892], [192, 869, 449, 892], [98, 892, 155, 919], [192, 894, 449, 917], [98, 917, 155, 944], [194, 919, 441, 944], [98, 944, 157, 969], [194, 946, 349, 969], [98, 969, 155, 996], [192, 971, 524, 994], [98, 994, 157, 1021], [192, 996, 332, 1021], [98, 1021, 157, 1046], [194, 1022, 508, 1046], [98, 1046, 157, 1071], [194, 1047, 606, 1071], [98, 1071, 157, 1096], [192, 1072, 733, 1096], [98, 1096, 157, 1123], [192, 1098, 347, 1121], [98, 1123, 157, 1148], [192, 1123, 367, 1146], [98, 1148, 155, 1173], [192, 1149, 504, 1173], [98, 1173, 157, 1198], [194, 1173, 412, 1196], [98, 1198, 157, 1224], [190, 1198, 456, 1225], [98, 1224, 157, 1249], [190, 1224, 260, 1251], [98, 1249, 157, 1274], [192, 1251, 430, 1274], [98, 1274, 157, 1301], [194, 1276, 482, 1300], [98, 1301, 157, 1326], [196, 1305, 639, 1323], [98, 1326, 157, 1351], [194, 1328, 460, 1351], [98, 1351, 157, 1376], [192, 1353, 476, 1376], [98, 1376, 157, 1403], [192, 1378, 500, 1401], [100, 1403, 157, 1428], [192, 1403, 428, 1426], [100, 1428, 157, 1453], [192, 1428, 327, 1453], [98, 1453, 155, 1478], [192, 1455, 356, 1478], [98, 1478, 157, 1505], [192, 1480, 314, 1505], [98, 1505, 157, 1530], [190, 1503, 242, 1532], [98, 1530, 157, 1555], [190, 1530, 332, 1555], [98, 1555, 155, 1582], [188, 1553, 264, 1586], [98, 1580, 157, 1607], [190, 1578, 370, 1611], [98, 1607, 157, 1632], [192, 1607, 402, 1632], [100, 1632, 157, 1657], [192, 1634, 375, 1657], [100, 1657, 157, 1684], [192, 1659, 478, 1682], [100, 1682, 155, 1709], [192, 1684, 286, 1712]]}, "table_res_list": [{"cell_box_list": [[98.38934326171875, 77.70430493354797, 170.45783233642578, 128.70171356201172], [193.72699737548828, 76.85059428215027, 767.2275161743164, 104.46680068969727], [42.82529830932617, 103.48102951049805, 70.03959274291992, 128.7168960571289], [70.02519226074219, 103.46329879760742, 98.48847198486328, 128.74835205078125], [192.0, 102.0, 270.0, 129.0], [42.74989318847656, 128.81719970703125, 70.03010177612305, 154.3861083984375], [69.98165130615234, 128.83088302612305, 98.4515609741211, 154.4112777709961], [98.35012817382812, 128.75428771972656, 170.49657440185547, 154.2817840576172], [189.40862274169922, 127.99474334716797, 767.1019058227539, 154.66517639160156], [42.752193450927734, 154.2251205444336, 69.96818542480469, 180.07723999023438], [69.95083618164062, 154.26628875732422, 98.39305114746094, 180.1143341064453], [98.25231170654297, 154.14287567138672, 170.6739273071289, 180.12023162841797], [190.63459014892578, 154.21428680419922, 767.2615737915039, 180.20706939697266], [42.71968460083008, 179.8971405029297, 69.9099235534668, 205.69779205322266], [69.91891860961914, 179.89370727539062, 98.38609313964844, 205.74120330810547], [98.3060073852539, 179.8978729248047, 170.80513763427734, 205.84162139892578], [194.0, 181.0, 423.0, 204.0], [69.91542434692383, 205.49468231201172, 98.34019470214844, 231.03411102294922], [98.22113037109375, 205.72490692138672, 170.9144515991211, 231.15750885009766], [187.17342376708984, 205.84075164794922, 767.2988662719727, 231.16387176513672], [98.2249984741211, 231.18482208251953, 171.5014419555664, 282.1425247192383], [190.67179107666016, 231.1972885131836, 767.2709732055664, 256.84310150146484], [69.88257598876953, 256.56131744384766, 98.39161682128906, 282.24466705322266], [192.0, 257.0, 556.0, 281.0], [69.88980102539062, 282.05997467041016, 98.41197967529297, 332.6675033569336], [98.3152084350586, 281.98128509521484, 171.71353912353516, 307.71839141845703], [192.08698272705078, 281.95259857177734, 767.3753433227539, 308.2588577270508], [98.28412628173828, 307.6921920776367, 171.95813751220703, 358.1467819213867], [194.0, 307.0, 727.0, 331.0], [192.1990737915039, 332.77767181396484, 767.3260269165039, 358.38842010498047], [42.73868179321289, 357.5924301147461, 69.9483528137207, 383.20824432373047], [69.90122604370117, 357.64635467529297, 98.44293212890625, 433.78499603271484], [98.32633209228516, 357.9780807495117, 171.85710906982422, 408.74520111083984], [192.3862075805664, 358.6033248901367, 767.4806289672852, 383.70934295654297], [194.0, 384.0, 393.0, 408.0], [42.79819297790527, 408.3460922241211, 69.94474792480469, 433.7087631225586], [98.26581573486328, 408.6910934448242, 171.82202911376953, 434.2112350463867], [192.48242950439453, 409.2217025756836, 767.4581069946289, 433.92308807373047], [70.00216674804688, 433.86128997802734, 98.49282836914062, 459.4088363647461], [98.33425903320312, 434.05257415771484, 171.85636138916016, 459.7683639526367], [192.58080291748047, 434.2272262573242, 767.5381240844727, 460.1737289428711], [42.8043098449707, 459.1919479370117, 69.9962272644043, 485.10665130615234], [69.9331169128418, 459.16475677490234, 98.39507293701172, 485.02498626708984], [98.30551147460938, 459.5019760131836, 171.6613998413086, 485.3397445678711], [194.0, 463.0, 397.0, 481.0], [69.90247344970703, 485.0407943725586, 98.41001892089844, 510.58922576904297], [98.2906265258789, 485.08617401123047, 171.71866607666016, 510.7790756225586], [192.78217315673828, 485.4343490600586, 767.4811172485352, 511.25865936279297], [42.83963394165039, 510.47374725341797, 69.94028854370117, 536.095329284668], [69.89279556274414, 510.4570541381836, 98.40628814697266, 536.1512069702148], [98.24243927001953, 510.7260971069336, 171.73047637939453, 536.3817672729492], [194.0, 511.0, 567.0, 534.0], [42.892934799194336, 536.2959823608398, 70.02458190917969, 587.4418258666992], [69.98214340209961, 536.2141647338867, 98.4795913696289, 561.7484359741211], [98.32913208007812, 536.2730026245117, 171.7163314819336, 561.8932113647461], [192.60750579833984, 536.4703598022461, 767.5284194946289, 562.384147644043], [69.91874694824219, 561.5058212280273, 98.37992858886719, 587.4091110229492], [98.28050994873047, 561.6892623901367, 171.5781478881836, 587.4155197143555], [192.43915557861328, 562.5626754760742, 767.4304580688477, 588.0626754760742], [42.81326675415039, 587.4807662963867, 69.9455337524414, 612.971061706543], [69.8844223022461, 587.3629684448242, 98.37483978271484, 612.9147872924805], [98.28288269042969, 587.3278732299805, 171.6451187133789, 612.9821701049805], [194.0, 588.0, 438.0, 611.0], [42.9003963470459, 612.8492965698242, 69.93222045898438, 638.4542770385742], [69.88106536865234, 612.833122253418, 98.38102722167969, 638.4749069213867], [98.24419403076172, 612.981071472168, 171.64653778076172, 638.5246505737305], [192.47954559326172, 613.436882019043, 767.3599014282227, 638.300895690918], [42.93355751037598, 638.6603317260742, 70.0186538696289, 664.0990524291992], [69.97928619384766, 638.5160446166992, 98.4682388305664, 664.0127487182617], [98.3259048461914, 638.5914840698242, 171.58318328857422, 663.9816818237305], [192.51993560791016, 638.7656173706055, 767.3171768188477, 664.7648849487305], [42.915700912475586, 663.8574752807617, 70.0079574584961, 689.810173034668], [69.93384170532227, 663.8424606323242, 98.37134552001953, 689.7607955932617], [98.2771224975586, 663.8938522338867, 171.46637725830078, 689.8025436401367], [192.0, 663.0, 301.0, 688.0], [69.885498046875, 689.7707443237305, 98.39950561523438, 715.4014205932617], [98.36133575439453, 689.8656539916992, 171.54216766357422, 715.3779830932617], [192.54419708251953, 690.086784362793, 767.5024185180664, 715.823356628418], [42.87632751464844, 715.2405319213867, 69.93759155273438, 740.933464050293], [69.90195846557617, 715.2280807495117, 98.39867401123047, 740.9822311401367], [98.27378845214844, 715.4042892456055, 171.45012664794922, 740.921745300293], [190.0, 713.0, 262.0, 740.0], [42.90968704223633, 741.1406784057617, 70.03688430786133, 766.5815963745117], [69.9842529296875, 740.8373947143555, 98.439697265625, 766.350456237793], [98.31705474853516, 741.0429000854492, 171.3886947631836, 766.2363204956055], [192.82373809814453, 741.569450378418, 767.7455215454102, 766.7443161010742], [42.883310317993164, 766.3124923706055, 70.01898956298828, 792.275993347168], [69.94378662109375, 766.2555465698242, 98.36516571044922, 792.2419967651367], [98.28373718261719, 766.2175827026367, 171.3647232055664, 792.302116394043], [192.7393569946289, 767.1122360229492, 767.6698379516602, 792.3071212768555], [98.2828598022461, 792.0864181518555, 171.51238250732422, 843.255729675293], [192.0, 792.0, 294.0, 817.0], [42.85316467285156, 817.7234420776367, 69.96949768066406, 843.409294128418], [69.92512893676758, 817.854606628418, 98.44406127929688, 868.8652877807617], [192.77320098876953, 818.403678894043, 767.3375015258789, 843.175895690918], [98.30455780029297, 843.3344650268555, 171.5242691040039, 894.4567184448242], [192.84847259521484, 843.5678024291992, 767.3810806274414, 869.1501998901367], [69.89978790283203, 868.7431564331055, 98.39698028564453, 894.406608581543], [192.0, 869.0, 449.0, 892.0], [98.0, 894.4936447143555, 171.63794708251953, 944.0], [192.95862579345703, 894.6518478393555, 767.0495986938477, 920.1771774291992], [69.85668182373047, 919.6454391479492, 98.39997100830078, 969.7819747924805], [194.0, 919.0, 441.0, 944.0], [98.1923828125, 945.3888473510742, 171.6882095336914, 970.036979675293], [192.75399017333984, 944.9994430541992, 767.0646743774414, 970.1094284057617], [98.0, 969.0, 155.0, 996.0], [192.78783416748047, 970.1334762573242, 767.2060317993164, 996.0382614135742], [69.83544158935547, 994.976676940918, 98.34744262695312, 1045.929557800293], [98.2208023071289, 995.2153244018555, 171.62578582763672, 1020.7951583862305], [192.0, 996.0, 332.0, 1021.0], [98.27297973632812, 1020.498405456543, 171.60196685791016, 1046.3635177612305], [192.72823333740234, 1020.7838668823242, 767.3599014282227, 1046.799430847168], [69.84558486938477, 1045.7746505737305, 98.2880859375, 1071.287712097168], [98.20995330810547, 1046.2350387573242, 171.66779327392578, 1097.0903244018555], [194.0, 1047.0, 606.0, 1071.0], [192.9655990600586, 1072.040397644043, 767.6650772094727, 1097.711540222168], [69.82545471191406, 1096.8003463745117, 98.34072875976562, 1122.5937423706055], [98.24198150634766, 1096.9950485229492, 171.7596206665039, 1122.7792892456055], [192.84830474853516, 1098.307731628418, 767.7982559204102, 1122.7634201049805], [42.69569969177246, 1122.486686706543, 69.85482788085938, 1148.2375411987305], [69.83379745483398, 1122.440055847168, 98.33329772949219, 1148.215934753418], [98.29090881347656, 1122.5324630737305, 171.6467056274414, 1148.4516525268555], [192.0, 1123.0, 367.0, 1146.0], [42.713294982910156, 1148.051872253418, 69.85964965820312, 1173.6489181518555], [69.852783203125, 1148.044548034668, 98.29098510742188, 1173.6638107299805], [98.23747253417969, 1148.358757019043, 171.7901840209961, 1199.315788269043], [192.73688507080078, 1148.5764083862305, 767.4814834594727, 1173.6642990112305], [69.84775161743164, 1173.643669128418, 98.3580093383789, 1199.186393737793], [192.82953643798828, 1174.1223068237305, 767.5370254516602, 1199.8972091674805], [42.689273834228516, 1199.2631759643555, 69.88431167602539, 1225.0766525268555], [69.82415771484375, 1199.1899337768555, 98.34805297851562, 1225.015007019043], [98.23661804199219, 1199.2446212768555, 171.8595962524414, 1225.055778503418], [190.0, 1198.0, 456.0, 1225.0], [42.6854133605957, 1224.9294357299805, 69.83818817138672, 1250.618522644043], [69.81475830078125, 1224.860954284668, 98.34623718261719, 1250.609977722168], [98.31365966796875, 1224.8940353393555, 171.77791595458984, 1250.8547286987305], [192.88744354248047, 1225.447380065918, 767.5145034790039, 1251.521598815918], [42.69375228881836, 1250.4736251831055, 69.83810424804688, 1276.0478439331055], [69.83440780639648, 1250.4558029174805, 98.28738403320312, 1276.083122253418], [98.25502014160156, 1250.702751159668, 171.7962417602539, 1276.372917175293], [192.0, 1251.0, 430.0, 1274.0], [69.83354568481445, 1276.1188888549805, 98.35327911376953, 1301.687126159668], [98.28289794921875, 1276.2641525268555, 171.7837905883789, 1301.8078536987305], [192.86815643310547, 1276.5895919799805, 767.7220230102539, 1302.2685470581055], [42.68638229370117, 1301.698844909668, 69.87517547607422, 1327.5317306518555], [69.81249618530273, 1301.6455001831055, 98.345458984375, 1327.471549987793], [98.24050903320312, 1301.724967956543, 171.84745025634766, 1327.4836349487305], [192.83533477783203, 1302.384147644043, 767.7158584594727, 1327.824333190918], [42.70157241821289, 1327.420036315918, 69.8541145324707, 1353.072135925293], [69.82192611694336, 1327.3478927612305, 98.3341064453125, 1353.045768737793], [98.27717590332031, 1327.298454284668, 171.81378936767578, 1353.155876159668], [194.0, 1328.0, 460.0, 1351.0], [42.708078384399414, 1352.9694747924805, 69.87275695800781, 1378.5241622924805], [69.85559463500977, 1352.9360275268555, 98.29701232910156, 1378.534782409668], [98.22901916503906, 1353.055046081543, 171.82878875732422, 1378.598014831543], [192.78600311279297, 1353.4760665893555, 767.4352188110352, 1378.5161056518555], [69.85887908935547, 1378.5910568237305, 98.358642578125, 1404.083854675293], [98.27680969238281, 1378.5844650268555, 171.77205657958984, 1404.133903503418], [192.75647735595703, 1378.990348815918, 767.4565811157227, 1404.6074142456055], [42.678667068481445, 1404.075798034668, 69.90910720825195, 1429.9245529174805], [69.83108520507812, 1404.035514831543, 98.35237121582031, 1429.8420333862305], [98.2396469116211, 1404.0593185424805, 171.76981353759766, 1429.760368347168], [192.0, 1403.0, 428.0, 1426.0], [42.67281532287598, 1429.776969909668, 69.86800765991211, 1455.4309005737305], [69.82925796508789, 1429.7080001831055, 98.3680191040039, 1455.369743347168], [98.32628631591797, 1429.598014831543, 171.8102798461914, 1455.389518737793], [192.72303009033203, 1429.984489440918, 767.6550064086914, 1480.4238204956055], [69.85000991821289, 1455.2763595581055, 98.32161712646484, 1480.7150802612305], [98.2419662475586, 1455.3688888549805, 171.80455780029297, 1480.774284362793], [98.27979278564453, 1480.792594909668, 171.92896270751953, 1556.671257019043], [192.74906158447266, 1480.948844909668, 767.7001724243164, 1531.6599044799805], [42.67503547668457, 1505.9890060424805, 69.9333381652832, 1531.4018478393555], [69.83301162719727, 1506.011589050293, 98.3907470703125, 1581.357048034668], [192.88098907470703, 1531.9184494018555, 767.6953506469727, 1581.259391784668], [42.764455795288086, 1556.4316329956055, 69.91368103027344, 1581.3120040893555], [98.26968383789062, 1556.6494064331055, 171.87703704833984, 1607.346061706543], [69.92637252807617, 1581.4135665893555, 98.42109680175781, 1606.856315612793], [192.8985824584961, 1581.709098815918, 767.4806900024414, 1607.6245040893555], [42.76094436645508, 1606.715202331543, 69.99950790405273, 1632.578727722168], [69.88123321533203, 1606.720817565918, 98.39668273925781, 1632.5605392456055], [98.27116394042969, 1607.1384201049805, 171.66243743896484, 1632.978385925293], [192.0, 1607.0, 402.0, 1632.0], [69.86994171142578, 1632.481071472168, 98.42288208007812, 1683.1347579956055], [98.31539154052734, 1632.646354675293, 171.64798736572266, 1658.4594650268555], [193.03247833251953, 1632.866325378418, 767.1661148071289, 1658.6826095581055], [98.2878189086914, 1658.1440353393555, 171.48157501220703, 1683.9404220581055], [192.88141632080078, 1658.6130294799805, 767.0364151000977, 1683.979606628418], [100.0, 1682.0, 155.0, 1709.0], [192.0, 1684.0, 286.0, 1712.0]], "pred_html": "<html><body><table><tbody><tr><td>0.000 4.479</td><td>厦部第1腰椎水平截面</td><td></td></tr><tr><td></td><td></td><td>优化配置</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.054</td></tr><tr><td></td><td></td><td>0.072</td></tr><tr><td></td><td>0.074</td><td>脂肪酶*</td></tr><tr><td>0.075 0.087</td><td>血管紧张素Ⅱ*</td><td></td></tr><tr><td></td><td>0.099</td><td>血管紧张素I</td></tr><tr><td>0.101 0.161</td><td>血浆丰配化脂肪酸NONETHERIZEDFATTYACIDSOF PLASMA</td><td>血钾PLASMAPOTASSIUM</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.095 0.097</td></tr><tr><td></td><td>0.103</td><td>尿中蛋白质PROTEININURINE</td></tr><tr><td></td><td>0.105</td><td>PERIPHERIC BLOOD LEUCOCYTES</td></tr><tr><td></td><td></td><td>0.107</td></tr><tr><td></td><td>0.107</td><td>血红血球ERYTHROCYTES</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.114</td></tr><tr><td></td><td></td><td>0.131</td></tr><tr><td></td><td>0.132</td><td>免疫球蛋白M*</td></tr><tr><td></td><td></td><td>0.133</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.135</td></tr><tr><td></td><td></td><td>0.143</td></tr><tr><td></td><td></td><td>0.147</td></tr><tr><td></td><td>0.201</td><td>锂*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.092</td></tr><tr><td></td><td></td><td>0.093</td></tr><tr><td></td><td></td><td>0.099</td></tr><tr><td>0.102 0.104</td><td>胰高血糖素</td><td></td></tr><tr><td></td><td></td><td>糖苷*</td></tr><tr><td>0.105 0.106</td><td>红细胞沉降率(ESR)</td><td></td></tr><tr><td>0.107 0.107</td><td>肿瘤标志物MELANOGENE在尿*</td><td></td></tr><tr><td></td><td>血清溶菌酵SERUMLYSOZYME</td><td>0.109</td></tr><tr><td>0.111</td><td>游离胆固醇FREEPLASMACHOLESTERIN</td><td></td></tr><tr><td></td><td>0.113</td><td>糖基化血红蛋白*</td></tr><tr><td>0.114</td><td>血清铜蓝蛋白SERUMCERULOPLASMIN</td><td></td></tr><tr><td></td><td>0.115 0.115</td><td>肌酸磷酸酵素COMMON CREATINPHOSPHOKINASE</td></tr><tr><td></td><td>0.116</td><td>RHEUMOFACTOR*</td></tr><tr><td></td><td></td><td>0.116</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.117 0.117</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.117</td></tr><tr><td></td><td></td><td>0.117</td></tr><tr><td></td><td></td><td>0.118</td></tr><tr><td></td><td>0.118</td><td>促肾上腺皮质激素CORTICOTROPIN</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.118</td></tr><tr><td></td><td></td><td>0.118</td></tr><tr><td></td><td></td><td>0.118</td></tr><tr><td></td><td>0.119</td><td>生长激素SOMATOTROPICHORMONE</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.119</td></tr><tr><td></td><td></td><td>0.119</td></tr><tr><td></td><td>0.120</td><td>0.120 0.120 0.120</td></tr><tr><td></td><td></td><td>抗链球菌溶血素* 催乳素*</td></tr><tr><td></td><td>0.121 0.121</td><td></td></tr><tr><td></td><td>ALPHA1-抗胰蛋白酶*</td><td></td></tr><tr><td></td><td></td><td>0.123</td></tr><tr><td></td><td>0.126</td><td>DELTA氨基乙酰丙酸</td></tr><tr><td>0.126</td><td>血浆磷脂PLASMAPHOSPHOTIDES</td><td>0.127</td></tr></tbody></table></body></html>", "table_ocr_pred": {"rec_polys": [[[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 79], [384, 79], [384, 102], [192, 102]], [[98, 102], [157, 102], [157, 127], [98, 127]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 129], [462, 129], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[194, 156], [495, 156], [495, 179], [194, 179]], [[98, 179], [161, 179], [161, 204], [98, 204]], [[194, 181], [423, 181], [423, 204], [194, 204]], [[98, 204], [161, 204], [161, 229], [98, 229]], [[192, 204], [266, 204], [266, 231], [192, 231]], [[98, 229], [162, 229], [162, 256], [98, 256]], [[192, 231], [319, 227], [320, 254], [193, 258]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [556, 257], [556, 281], [192, 281]], [[98, 281], [161, 281], [161, 306], [98, 306]], [[196, 284], [310, 284], [310, 304], [196, 304]], [[98, 306], [161, 306], [161, 332], [98, 332]], [[194, 307], [727, 307], [727, 331], [194, 331]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [428, 336], [428, 354], [196, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[194, 359], [412, 359], [412, 383], [194, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[194, 384], [393, 384], [393, 408], [194, 408]], [[98, 408], [155, 408], [155, 434], [98, 434]], [[194, 409], [443, 409], [443, 433], [194, 433]], [[98, 434], [155, 434], [155, 459], [98, 459]], [[192, 434], [474, 434], [474, 458], [192, 458]], [[98, 458], [155, 458], [155, 484], [98, 484]], [[194, 463], [397, 463], [397, 481], [194, 481]], [[98, 484], [155, 484], [155, 509], [98, 509]], [[194, 486], [404, 486], [404, 509], [194, 509]], [[98, 511], [155, 511], [155, 536], [98, 536]], [[194, 511], [567, 511], [567, 534], [194, 534]], [[98, 536], [153, 536], [153, 561], [98, 561]], [[192, 536], [310, 536], [310, 561], [192, 561]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[192, 561], [310, 561], [310, 586], [192, 586]], [[98, 586], [155, 586], [155, 611], [98, 611]], [[194, 588], [438, 588], [438, 611], [194, 611]], [[98, 611], [155, 611], [155, 638], [98, 638]], [[192, 613], [377, 613], [377, 636], [192, 636]], [[98, 636], [155, 636], [155, 663], [98, 663]], [[192, 640], [408, 640], [408, 663], [192, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[192, 663], [301, 663], [301, 688], [192, 688]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[188, 686], [227, 686], [227, 717], [188, 717]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[190, 713], [262, 713], [262, 740], [190, 740]], [[98, 740], [157, 740], [157, 765], [98, 765]], [[192, 740], [689, 740], [689, 763], [192, 763]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[192, 765], [325, 765], [325, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [294, 792], [294, 817], [192, 817]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[190, 815], [244, 815], [244, 844], [190, 844]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[190, 842], [364, 842], [364, 867], [190, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [449, 869], [449, 892], [192, 892]], [[98, 892], [155, 892], [155, 919], [98, 919]], [[192, 894], [449, 894], [449, 917], [192, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[194, 919], [441, 919], [441, 944], [194, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [349, 946], [349, 969], [194, 969]], [[98, 969], [155, 969], [155, 996], [98, 996]], [[192, 971], [524, 971], [524, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [332, 996], [332, 1021], [192, 1021]], [[98, 1021], [157, 1021], [157, 1046], [98, 1046]], [[194, 1022], [508, 1022], [508, 1046], [194, 1046]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[194, 1047], [606, 1047], [606, 1071], [194, 1071]], [[98, 1071], [157, 1071], [157, 1096], [98, 1096]], [[192, 1072], [733, 1072], [733, 1096], [192, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [347, 1098], [347, 1121], [192, 1121]], [[98, 1123], [157, 1123], [157, 1148], [98, 1148]], [[192, 1123], [367, 1123], [367, 1146], [192, 1146]], [[98, 1148], [155, 1148], [155, 1173], [98, 1173]], [[192, 1149], [504, 1149], [504, 1173], [192, 1173]], [[98, 1173], [157, 1173], [157, 1198], [98, 1198]], [[194, 1173], [412, 1173], [412, 1196], [194, 1196]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [456, 1198], [456, 1223], [190, 1225]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[190, 1224], [260, 1224], [260, 1251], [190, 1251]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1251], [430, 1251], [430, 1274], [192, 1274]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[194, 1276], [482, 1276], [482, 1300], [194, 1300]], [[98, 1301], [157, 1301], [157, 1326], [98, 1326]], [[196, 1305], [639, 1305], [639, 1323], [196, 1323]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[194, 1328], [460, 1328], [460, 1351], [194, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [476, 1353], [476, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1378], [500, 1378], [500, 1401], [192, 1401]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [428, 1403], [428, 1426], [192, 1426]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[192, 1428], [327, 1428], [327, 1453], [192, 1453]], [[98, 1453], [155, 1453], [155, 1478], [98, 1478]], [[192, 1455], [356, 1455], [356, 1478], [192, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[192, 1480], [314, 1480], [314, 1505], [192, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[190, 1503], [242, 1503], [242, 1532], [190, 1532]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[190, 1530], [332, 1530], [332, 1555], [190, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[188, 1553], [264, 1553], [264, 1586], [188, 1586]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[191, 1578], [370, 1584], [369, 1611], [190, 1605]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[192, 1607], [402, 1607], [402, 1632], [192, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[192, 1634], [375, 1634], [375, 1657], [192, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1659], [478, 1659], [478, 1682], [192, 1682]], [[100, 1682], [155, 1682], [155, 1709], [100, 1709]], [[192, 1684], [286, 1684], [286, 1712], [192, 1712]]], "rec_texts": ["0.000", "厦部第1腰椎水平截面", "4.479", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.054", "C反应蛋白C-REACTIVEPROTEIN", "0.072", "血尿酸SERUMURICACID", "0.074", "脂肪酶*", "0.075", "血管紧张素Ⅱ*", "0.087", "胆固醇COMMONPLASMA CHOLESTERIN", "0.099", "血管紧张素I", "0.101", "血浆丰配化脂肪酸NONETHERIZEDFATTYACIDSOF PLASMA", "0.161", "血钾PLASMAPOTASSIUM", "0.095", "血清蛋白SERUM ALBUMEN", "0.097", "血红蛋白HAEMOGLOBIN", "0.103", "尿中蛋白质PROTEININURINE", "0.105", "PERIPHERIC BLOOD LEUCOCYTES", "0.107", "嗜碱性粒细胞BASOPHILS", "0.107", "血红血球ERYTHROCYTES", "0.114", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.131", "免疫球蛋白G*", "0.132", "免疫球蛋白M*", "0.133", "尿白血球URINELEUCOCYTES", "0.135", "单核细胞MONOCYTES", "0.143", "血清蛋白SERUMPROTEIN", "0.147", "BETA球蛋白", "0.201", "锂*", "0.092", "胆汁酸*", "0.093", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.099", "ALPHA2球蛋白*", "0.102", "胰高血糖素", "0.104", "糖苷*", "0.105", "红细胞沉降率(ESR)", "0.106", "血清补体SERUMCOMPLEMENT", "0.107", "肿瘤标志物MELANOGENE在尿*", "0.107", "血清溶菌酵SERUMLYSOZYME", "0.109", "血细胞比容，全血", "0.111", "游离胆固醇FREEPLASMACHOLESTERIN", "0.113", "糖基化血红蛋白*", "0.114", "血清铜蓝蛋白SERUMCERULOPLASMIN", "0.115", "肌酸磷酸酵素COMMON CREATINPHOSPHOKINASE", "0.115", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.116", "RHEUMOFACTOR*", "0.116", "肿瘤标志物胸苷激酶", "0.117", "血清淀粉酵素SERUMALPHAAMYLASE", "0.117", "尿肌配URINECREATININE", "0.117", "伽马球蛋白GAMMA-GLOBULINS", "0.117", "铁蛋白*", "0.118", "嗜酸性粒细胞EOSINOPHILES", "0.118", "促肾上腺皮质激素CORTICOTROPIN", "0.118", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.118", "尿中肾上腺素URINEADRENALIN", "0.118", "嗜中性粒细胞STABNEUTROPHILS", "0.119", "生长激素SOMATOTROPICHORMONE", "0.119", "血组织胺BLOODHISTAMINE", "0.119", "ALPHA1球蛋白*", "0.120", "血糖BLOOD SUGAR", "0.120", "甲状腺球蛋白*", "0.120", "肾素*", "0.120", "抗链球菌溶血素*", "0.121", "催乳素*", "0.121", "ALPHA1-抗胰蛋白酶*", "0.123", "维生素B1（THIAMINE）*", "0.126", "DELTA氨基乙酰丙酸", "0.126", "血浆磷脂PLASMAPHOSPHOTIDES", "0.127", "维生素B6*"], "rec_scores": [0.9992006421089172, 0.9220801591873169, 0.9988524317741394, 0.9983981251716614, 0.9665411710739136, 0.9999231100082397, 0.9886558651924133, 0.9999233484268188, 0.9981778860092163, 0.9998815655708313, 0.9787902235984802, 0.9999483227729797, 0.941586971282959, 0.9999241828918457, 0.9782752394676208, 0.999923050403595, 0.977410614490509, 0.9998477697372437, 0.9450623989105225, 0.9999014735221863, 0.9814251065254211, 0.9996863603591919, 0.9807018041610718, 0.9996916651725769, 0.9984849095344543, 0.9995733499526978, 0.9969378113746643, 0.9995217323303223, 0.9698492884635925, 0.9994786381721497, 0.9812630414962769, 0.9993487596511841, 0.9961856007575989, 0.9993133544921875, 0.993124783039093, 0.9994786381721497, 0.9875865578651428, 0.9995611310005188, 0.992206871509552, 0.9995157122612, 0.9925421476364136, 0.9995859265327454, 0.997487485408783, 0.9995468258857727, 0.9955726265907288, 0.9991366267204285, 0.9935155510902405, 0.9994516372680664, 0.9606345295906067, 0.9997864961624146, 0.9675710201263428, 0.999636173248291, 0.9947442412376404, 0.9995541572570801, 0.9422779083251953, 0.9995965957641602, 0.997287392616272, 0.9994552731513977, 0.9665315747261047, 0.9994734525680542, 0.9244000911712646, 0.9993106722831726, 0.998449444770813, 0.9994791746139526, 0.9935643672943115, 0.9994699358940125, 0.99184250831604, 0.9992948770523071, 0.9944967031478882, 0.9981158375740051, 0.9958582520484924, 0.9994648098945618, 0.9525598287582397, 0.9989427328109741, 0.9945222735404968, 0.9993501901626587, 0.9832534790039062, 0.9991741180419922, 0.9849311113357544, 0.9993810653686523, 0.9894301891326904, 0.9990262985229492, 0.9895035624504089, 0.9990242123603821, 0.998862624168396, 0.999039351940155, 0.9629787802696228, 0.9990525245666504, 0.9961625337600708, 0.999039351940155, 0.9330690503120422, 0.9993749856948853, 0.9977938532829285, 0.9994460940361023, 0.998503565788269, 0.9994057416915894, 0.9973668456077576, 0.9992116689682007, 0.9933547973632812, 0.9993749856948853, 0.997769832611084, 0.9993714094161987, 0.9966481924057007, 0.9986928701400757, 0.9920355677604675, 0.9986355900764465, 0.9741079211235046, 0.9991754293441772, 0.9675531983375549, 0.999508261680603, 0.9346173405647278, 0.9993797540664673, 0.8804211616516113, 0.9992661476135254, 0.9690635800361633, 0.9995349645614624, 0.9840336441993713, 0.9995824098587036, 0.987791121006012, 0.9994745254516602, 0.9170867204666138, 0.9990471005439758, 0.9954785704612732, 0.9991771578788757, 0.9852492809295654, 0.9992483854293823, 0.9778856635093689], "rec_boxes": [[98, 77, 157, 102], [192, 79, 384, 102], [98, 102, 157, 127], [192, 102, 270, 129], [194, 129, 462, 152], [98, 154, 161, 179], [194, 156, 495, 179], [98, 179, 161, 204], [194, 181, 423, 204], [98, 204, 161, 229], [192, 204, 266, 231], [98, 229, 162, 256], [192, 227, 320, 258], [98, 256, 161, 281], [192, 257, 556, 281], [98, 281, 161, 306], [196, 284, 310, 304], [98, 306, 161, 332], [194, 307, 727, 331], [98, 332, 161, 358], [196, 336, 428, 354], [98, 358, 157, 383], [194, 359, 412, 383], [98, 383, 155, 409], [194, 384, 393, 408], [98, 408, 155, 434], [194, 409, 443, 433], [98, 434, 155, 459], [192, 434, 474, 458], [98, 458, 155, 484], [194, 463, 397, 481], [98, 484, 155, 509], [194, 486, 404, 509], [98, 511, 155, 536], [194, 511, 567, 534], [98, 536, 153, 561], [192, 536, 310, 561], [98, 561, 155, 588], [192, 561, 310, 586], [98, 586, 155, 611], [194, 588, 438, 611], [98, 611, 155, 638], [192, 613, 377, 636], [98, 636, 155, 663], [192, 640, 408, 663], [98, 663, 155, 688], [192, 663, 301, 688], [98, 688, 155, 715], [188, 686, 227, 717], [98, 713, 157, 740], [190, 713, 262, 740], [98, 740, 157, 765], [192, 740, 689, 763], [98, 765, 157, 790], [192, 765, 325, 790], [98, 790, 157, 817], [192, 792, 294, 817], [98, 815, 157, 842], [190, 815, 244, 844], [98, 840, 157, 867], [190, 842, 364, 867], [98, 867, 157, 892], [192, 869, 449, 892], [98, 892, 155, 919], [192, 894, 449, 917], [98, 917, 155, 944], [194, 919, 441, 944], [98, 944, 157, 969], [194, 946, 349, 969], [98, 969, 155, 996], [192, 971, 524, 994], [98, 994, 157, 1021], [192, 996, 332, 1021], [98, 1021, 157, 1046], [194, 1022, 508, 1046], [98, 1046, 157, 1071], [194, 1047, 606, 1071], [98, 1071, 157, 1096], [192, 1072, 733, 1096], [98, 1096, 157, 1123], [192, 1098, 347, 1121], [98, 1123, 157, 1148], [192, 1123, 367, 1146], [98, 1148, 155, 1173], [192, 1149, 504, 1173], [98, 1173, 157, 1198], [194, 1173, 412, 1196], [98, 1198, 157, 1224], [190, 1198, 456, 1225], [98, 1224, 157, 1249], [190, 1224, 260, 1251], [98, 1249, 157, 1274], [192, 1251, 430, 1274], [98, 1274, 157, 1301], [194, 1276, 482, 1300], [98, 1301, 157, 1326], [196, 1305, 639, 1323], [98, 1326, 157, 1351], [194, 1328, 460, 1351], [98, 1351, 157, 1376], [192, 1353, 476, 1376], [98, 1376, 157, 1403], [192, 1378, 500, 1401], [100, 1403, 157, 1428], [192, 1403, 428, 1426], [100, 1428, 157, 1453], [192, 1428, 327, 1453], [98, 1453, 155, 1478], [192, 1455, 356, 1478], [98, 1478, 157, 1505], [192, 1480, 314, 1505], [98, 1505, 157, 1530], [190, 1503, 242, 1532], [98, 1530, 157, 1555], [190, 1530, 332, 1555], [98, 1555, 155, 1582], [188, 1553, 264, 1586], [98, 1580, 157, 1607], [190, 1578, 370, 1611], [98, 1607, 157, 1632], [192, 1607, 402, 1632], [100, 1632, 157, 1657], [192, 1634, 375, 1657], [100, 1657, 157, 1684], [192, 1659, 478, 1682], [100, 1682, 155, 1709], [192, 1684, 286, 1712]]}}]}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//cdb60e26-913c-4b21-b6be-206582b3cf22/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T00%3A06%3A47Z%2F-1%2F%2Ff41aa659bb0752819591ef1dba9889d0f447fa5922d3f2582e5f5a689af345ec", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//cdb60e26-913c-4b21-b6be-206582b3cf22/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T00%3A06%3A47Z%2F-1%2F%2F190669619ab02998766258ff44624d4f1c68df15022d9d8a3456ddaf86590b64", "table_cell_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//cdb60e26-913c-4b21-b6be-206582b3cf22/table_cell_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T00%3A06%3A47Z%2F-1%2F%2Fe86a1279e9dace5f9e469e645af5d0deea0fbd63b9c2a4ee1b574046c1b6c598"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//cdb60e26-913c-4b21-b6be-206582b3cf22/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T00%3A06%3A47Z%2F-1%2F%2Fd588c42bfe3567dd91bb4e5393157f0400d335d87e211942c35cd38a3e8e4f29"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}