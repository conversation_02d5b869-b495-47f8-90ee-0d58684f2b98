# 统一轮次管理架构设计

## 概述

本文档设计了一个统一的轮次管理架构，以 `app.go` 为核心，融合现有三种轮次管理方式的优势，实现分层架构的统一轮次管理系统。

## 现状分析

### 现有三种轮次管理方式

1. **主系统轮次管理（app.go）**
   - 优势：简单直观，状态管理清晰，UI集成度高
   - 缺点：缺乏并发保护，数据持久化不足

2. **截图轮次管理（ScreenshotRoundManager）**
   - 优势：并发安全，功能完整，专业化管理
   - 缺点：与主系统存在同步问题

3. **数据收集轮次管理（CurrentUserCheckingInfo）**
   - 优势：数据完整性好，支持统计分析
   - 缺点：轮次逻辑相对简单

## 统一架构设计

### 分层架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    业务层 (app.go)                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   UI交互管理    │  │   状态展示      │  │   通知系统      │ │
│  │  - Toast通知    │  │  - 进度条       │  │  - 事件分发     │ │
│  │  - 用户操作     │  │  - 状态同步     │  │  - 回调管理     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   管理层 (UnifiedRoundManager)               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   轮次生命周期  │  │   并发安全      │  │   状态同步      │ │
│  │  - 创建/启动    │  │  - 读写锁       │  │  - 事件驱动     │ │
│  │  - 进度跟踪     │  │  - 原子操作     │  │  - 状态一致性   │ │
│  │  - 完成检测     │  │  - 错误恢复     │  │  - 定时同步     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   数据层 (DataManager)                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   数据持久化    │  │   统计分析      │  │   完整性验证    │ │
│  │  - 轮次数据     │  │  - 器官统计     │  │  - 数据校验     │ │
│  │  - OCR结果      │  │  - 进度统计     │  │  - 一致性检查   │ │
│  │  - 用户信息     │  │  - 性能指标     │  │  - 错误检测     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件设计

#### 1. 统一轮次状态结构体

```go
// UnifiedRoundStatus 统一轮次状态结构体
type UnifiedRoundStatus struct {
    // 基本信息
    UserKey         string    `json:"user_key"`         // 用户唯一标识 (用户名_日期)
    UserName        string    `json:"user_name"`        // 用户名
    CurrentRound    int       `json:"current_round"`    // 当前轮次 (1-10)
    
    // 模式完成状态
    B02Completed    bool      `json:"b02_completed"`    // B02模式是否已完成
    C03Completed    bool      `json:"c03_completed"`    // C03模式是否已完成
    
    // 时间信息
    StartTime       time.Time `json:"start_time"`       // 轮次开始时间
    LastUpdateTime  time.Time `json:"last_update_time"` // 最后更新时间
    CompletionTime  *time.Time `json:"completion_time,omitempty"` // 轮次完成时间
    
    // 数据引用
    DataRef         *models.CurrentUserCheckingInfo `json:"-"` // 数据层引用
    ScreenshotRef   *services.ScreenshotRound       `json:"-"` // 截图管理引用
    
    // 状态管理
    Status          RoundStatusType `json:"status"`          // 轮次状态
    ErrorCount      int            `json:"error_count"`     // 错误计数
    RetryCount      int            `json:"retry_count"`     // 重试计数
    
    // 并发保护
    mu              sync.RWMutex   `json:"-"`              // 读写锁
}

// RoundStatusType 轮次状态类型
type RoundStatusType int

const (
    RoundStatusPending    RoundStatusType = iota // 等待中
    RoundStatusInProgress                        // 进行中
    RoundStatusCompleted                         // 已完成
    RoundStatusError                             // 错误状态
    RoundStatusCancelled                         // 已取消
)
```

#### 2. 统一轮次管理器

```go
// UnifiedRoundManager 统一轮次管理器
type UnifiedRoundManager struct {
    // 核心状态
    rounds          map[string]*UnifiedRoundStatus // 轮次映射
    globalMutex     sync.RWMutex                   // 全局读写锁
    
    // 服务引用
    app             *App                           // App实例引用
    dataManager     *DataManager                   // 数据管理器
    screenshotMgr   *services.ScreenshotRoundManager // 截图管理器
    
    // 事件系统
    eventBus        *EventBus                      // 事件总线
    callbacks       map[string][]RoundCallback     // 回调函数映射
    
    // 配置参数
    maxRounds       int                            // 最大轮次数
    syncInterval    time.Duration                  // 同步间隔
    
    // 运行状态
    ctx             context.Context                // 上下文
    cancel          context.CancelFunc             // 取消函数
    running         bool                           // 运行状态
}
```

#### 3. 事件驱动系统

```go
// RoundEvent 轮次事件
type RoundEvent struct {
    Type      RoundEventType `json:"type"`
    UserKey   string         `json:"user_key"`
    Round     int            `json:"round"`
    Mode      string         `json:"mode"`
    Data      interface{}    `json:"data"`
    Timestamp time.Time      `json:"timestamp"`
}

// RoundEventType 轮次事件类型
type RoundEventType string

const (
    EventRoundStarted    RoundEventType = "round_started"
    EventModeCompleted   RoundEventType = "mode_completed"
    EventRoundCompleted  RoundEventType = "round_completed"
    EventRoundError      RoundEventType = "round_error"
    EventProgressUpdate  RoundEventType = "progress_update"
    EventDataSync        RoundEventType = "data_sync"
)

// EventBus 事件总线
type EventBus struct {
    subscribers map[RoundEventType][]EventHandler
    mutex       sync.RWMutex
}

// EventHandler 事件处理器
type EventHandler func(event RoundEvent) error
```

## 实现方案

### 第一阶段：核心结构重构

1. **扩展 App 结构体**
   ```go
   type App struct {
       // ... 现有字段 ...
       
       // 统一轮次管理
       unifiedRoundManager *UnifiedRoundManager
       
       // 保持兼容性的旧字段（逐步废弃）
       roundManager map[string]*RoundStatus // 标记为 @deprecated
   }
   ```

2. **创建统一管理器**
   - 实现 `UnifiedRoundManager` 结构体
   - 集成现有三种管理方式的功能
   - 提供统一的接口

### 第二阶段：数据层整合

1. **数据管理器实现**
   ```go
   type DataManager struct {
       userCheckingInfo map[string]*models.CurrentUserCheckingInfo
       mutex           sync.RWMutex
       persistenceLayer PersistenceInterface
   }
   ```

2. **数据同步机制**
   - 实现定时同步
   - 事件驱动的即时同步
   - 数据一致性检查

### 第三阶段：事件系统集成

1. **事件总线实现**
   - 发布-订阅模式
   - 异步事件处理
   - 错误恢复机制

2. **回调系统统一**
   - 统一回调接口
   - 优先级管理
   - 异常处理

### 第四阶段：并发安全优化

1. **锁策略优化**
   - 细粒度锁设计
   - 读写锁合理使用
   - 死锁预防

2. **原子操作应用**
   - 状态更新原子化
   - 计数器原子操作
   - 无锁数据结构

## 接口设计

### 核心接口

```go
// RoundManagerInterface 轮次管理器接口
type RoundManagerInterface interface {
    // 轮次操作
    StartRound(userKey string) error
    CompleteMode(userKey string, mode string) error
    GetCurrentRound(userKey string) int
    GetRoundStatus(userKey string) *UnifiedRoundStatus
    
    // 事件管理
    Subscribe(eventType RoundEventType, handler EventHandler)
    Unsubscribe(eventType RoundEventType, handler EventHandler)
    PublishEvent(event RoundEvent)
    
    // 数据管理
    GetUserData(userKey string) *models.CurrentUserCheckingInfo
    SyncData(userKey string) error
    
    // 生命周期
    Start() error
    Stop() error
    IsRunning() bool
}
```

### 兼容性接口

```go
// 保持与现有代码的兼容性
func (a *App) markModeCompleted(userName, mode string) (int, bool, int) {
    return a.unifiedRoundManager.MarkModeCompleted(userName, mode)
}

func (a *App) getCurrentRound(userName string) int {
    return a.unifiedRoundManager.GetCurrentRound(userName)
}
```

## 迁移策略

### 渐进式迁移

1. **第一步：并行运行**
   - 新旧系统同时运行
   - 数据双写验证
   - 功能对比测试

2. **第二步：逐步切换**
   - 按功能模块切换
   - 保持向后兼容
   - 监控性能指标

3. **第三步：完全迁移**
   - 移除旧代码
   - 清理冗余接口
   - 优化性能

### 风险控制

1. **数据安全**
   - 数据备份机制
   - 回滚策略
   - 一致性检查

2. **功能验证**
   - 单元测试覆盖
   - 集成测试验证
   - 用户验收测试

## 性能优化

### 内存优化

1. **对象池使用**
   ```go
   var roundStatusPool = sync.Pool{
       New: func() interface{} {
           return &UnifiedRoundStatus{}
       },
   }
   ```

2. **缓存策略**
   - LRU缓存热点数据
   - 定期清理过期数据
   - 内存使用监控

### 并发优化

1. **无锁数据结构**
   - 原子操作替代锁
   - CAS操作应用
   - 无锁队列使用

2. **协程池管理**
   - 限制协程数量
   - 任务队列缓冲
   - 优雅关闭机制

## 监控和调试

### 指标收集

```go
// Metrics 性能指标
type Metrics struct {
    RoundCreated     int64 `json:"round_created"`
    RoundCompleted   int64 `json:"round_completed"`
    ModeCompleted    int64 `json:"mode_completed"`
    ErrorCount       int64 `json:"error_count"`
    AvgRoundDuration time.Duration `json:"avg_round_duration"`
    ConcurrentUsers  int64 `json:"concurrent_users"`
}
```

### 日志系统

```go
// 结构化日志
utils.LogInfo("轮次状态更新",
    zap.String("user_key", userKey),
    zap.Int("round", round),
    zap.String("mode", mode),
    zap.String("status", status.String()),
    zap.Duration("duration", duration),
)
```

## 总结

这个统一轮次管理架构设计具有以下优势：

1. **统一性**：以 app.go 为核心，统一管理所有轮次相关逻辑
2. **安全性**：完善的并发保护和错误处理机制
3. **可扩展性**：模块化设计，易于扩展和维护
4. **兼容性**：渐进式迁移，保持向后兼容
5. **性能**：优化的数据结构和并发策略
6. **可观测性**：完善的监控和日志系统

通过这个架构，可以有效解决现有三种轮次管理方式的问题，提供一个统一、安全、高效的轮次管理解决方案。