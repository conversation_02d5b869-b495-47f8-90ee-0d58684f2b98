package api

import (
	"MagneticOperator/app/services"
	"time"
)

// RoundStatusDTO 是用于表示轮次状态的精简数据结构
type RoundStatusDTO struct {
	RoundNumber       int           `json:"round_number"`
	Status            string        `json:"status"`
	TotalTasks        int           `json:"total_tasks"`
	CompletedTasks    int           `json:"completed_tasks"`
	Progress          float64       `json:"progress"`
	StartTime         time.Time     `json:"start_time"`
	CompletedTime     time.Time     `json:"completed_time,omitempty"`
	RemainingTime     time.Duration `json:"remaining_time,omitempty"`
}

// ServiceStatusDTO 是用于表示服务状态的精简数据结构
type ServiceStatusDTO struct {
	IsRunning      bool                      `json:"is_running"`
	CurrentRound   int                       `json:"current_round"`
	TotalRounds    int                       `json:"total_rounds"`
	OcrStats       services.ProcessorStats  `json:"ocr_stats"`
	CompletedRounds int                      `json:"completed_rounds"`
	AllRoundsStatus []RoundStatusDTO `json:"all_rounds_status"`
}