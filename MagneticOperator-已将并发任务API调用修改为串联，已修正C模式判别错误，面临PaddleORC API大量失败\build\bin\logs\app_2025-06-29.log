{"level":"INFO","timestamp":"2025-06-29T08:05:25.640+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T08:05:25.641+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T08:05:25.641+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T08:05:25.641+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T08:05:25.641+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T08:05:25.641+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T08:05:25.641+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T08:05:25.641+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":11552}
{"level":"INFO","timestamp":"2025-06-29T08:05:25.718+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T08:05:25.718+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T08:05:27.634+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T08:05:27.634+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T08:05:27.638+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T08:05:27.641+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.210+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.268+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.274+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.274+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.274+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.276+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.277+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.277+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.322+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.322+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.328+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.367+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.368+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.368+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.368+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T08:05:28.956+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T08:05:29.623+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T08:06:17.766+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T08:06:17.766+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T08:06:18.014+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T08:06:18.014+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T08:06:18.267+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T08:06:18.267+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T08:06:19.017+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T08:06:19.249+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T08:16:23.033+0800","caller":"utils/logger.go:96","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-06-29T08:16:23.033+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T08:16:23.033+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-29T08:16:23.033+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-29T08:16:23.034+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T08:16:23.034+0800","caller":"utils/logger.go:96","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.179+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.179+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.179+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.179+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.180+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.180+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.180+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.180+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":32996}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.214+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.215+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.753+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.753+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.759+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.760+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T19:24:30.760+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.289+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.350+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.356+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.356+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.356+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.359+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.361+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.361+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.393+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.393+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.399+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.456+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.457+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.457+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T19:24:31.457+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T19:24:32.046+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:24:32.743+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:25:00.991+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T19:25:00.991+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:25:01.234+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T19:25:01.234+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:25:01.462+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:25:01.462+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:25:01.906+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:25:02.123+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:26:13.193+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T19:26:13.193+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:26:13.454+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T19:26:13.454+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:26:13.705+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:26:13.705+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:26:14.103+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:26:14.304+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.429+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.430+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.431+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.435+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.435+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.435+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.436+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.436+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.436+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.437+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.441+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.451+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.451+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.451+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.452+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.454+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.454+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.454+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.454+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.455+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:55.456+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:56.105+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:56.113+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:56.122+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:56.341+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:56.769+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:56.777+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.239+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.240+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.240+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.240+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.240+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.240+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":5,"modified":"2025-06-29T19:24:30.180+0800"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.240+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"32996"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.240+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":32996}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.240+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":32996}
{"level":"WARN","timestamp":"2025-06-29T19:30:58.240+0800","caller":"utils/logger.go:103","msg":"查找进程失败","pid":32996,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.240+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":32996}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.240+0800","caller":"utils/logger.go:96","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.240+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.241+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":37752}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.324+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.324+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.837+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.837+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.844+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.846+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T19:30:58.846+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.060+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.119+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.123+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.123+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.123+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.125+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.126+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.126+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.152+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.153+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.159+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.363+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.508+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.508+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.508+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.508+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T19:30:59.590+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.257+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.258+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.260+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.262+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.263+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.263+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.263+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.263+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.263+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.264+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.264+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.264+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.267+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.267+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.267+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.267+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.269+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.269+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.269+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.269+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.270+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.918+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.920+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:36.921+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:37.102+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:37.564+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:37.639+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.770+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.772+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.773+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.776+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.776+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.776+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.777+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.777+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.777+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.777+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.777+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.777+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.780+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.781+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.781+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.781+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.781+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.781+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.783+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.783+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.783+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.983+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:42.983+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:43.016+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:43.210+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:43.227+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:43.261+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:44.948+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-29T19:36:44.983+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T19:36:44.984+0800","caller":"utils/logger.go:96","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-06-29T19:36:44.984+0800","caller":"utils/logger.go:96","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-06-29T19:36:44.984+0800","caller":"utils/logger.go:96","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-06-29T19:36:44.984+0800","caller":"utils/logger.go:96","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.017+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.017+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.018+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.919+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.919+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.919+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.919+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.919+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.919+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.919+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.919+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":38200}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.949+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T19:36:45.949+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T19:36:46.450+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T19:36:46.450+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T19:36:46.457+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T19:36:46.460+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T19:36:46.460+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.008+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.086+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.089+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.090+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.090+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.093+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.095+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.095+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.130+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.130+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.130+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.130+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.137+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.137+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.146+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.337+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:36:47.537+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:37:07.814+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T19:37:07.814+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:37:08.059+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T19:37:08.059+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:37:08.331+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:37:08.331+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:37:08.819+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:37:09.054+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.108+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.108+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.108+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.108+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.108+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.108+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":5,"modified":"2025-06-29T19:36:45.919+0800"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.109+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"38200"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.109+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":38200}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.109+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":38200}
{"level":"WARN","timestamp":"2025-06-29T19:47:31.109+0800","caller":"utils/logger.go:103","msg":"查找进程失败","pid":38200,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.109+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":38200}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.109+0800","caller":"utils/logger.go:96","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.109+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.109+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":38648}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.174+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.174+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.637+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.637+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.643+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.644+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.644+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.822+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.883+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.890+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.890+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.890+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.894+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.897+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.897+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.932+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.932+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:47:31.936+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:47:32.431+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T19:47:32.432+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T19:47:32.432+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T19:47:32.432+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T19:47:32.656+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T19:47:33.321+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:00:42.924+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:00:42.930+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:00:42.930+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:00:42.930+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:00:42.939+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:00:42.939+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:00:42.942+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:00:43.613+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:00:44.262+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:00:52.432+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:00:52.436+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:00:52.436+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:00:52.436+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:00:52.442+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:00:52.442+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:00:52.444+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:00:53.007+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:00:53.180+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:01:14.192+0800","caller":"utils/logger.go:96","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-06-29T20:01:14.192+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T20:01:14.192+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-29T20:01:14.192+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-29T20:01:14.193+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T20:01:14.193+0800","caller":"utils/logger.go:96","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.355+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.357+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.357+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.357+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.357+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.357+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.357+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.357+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":39932}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.391+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.391+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.857+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.858+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.864+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.865+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T20:12:55.865+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.365+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.425+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.429+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.429+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.429+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.432+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.434+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.434+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.454+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.454+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.461+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.555+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.555+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.555+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T20:12:56.556+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T20:12:57.088+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:12:57.778+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:13:16.582+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T20:13:16.582+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:13:17.259+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T20:13:17.259+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:13:17.448+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:13:17.448+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:13:17.879+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:13:18.152+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:14:01.702+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T20:14:01.702+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:14:01.946+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T20:14:01.946+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:14:02.192+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:14:02.192+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:14:02.528+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:14:02.765+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.029+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.031+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.032+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.036+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.036+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.036+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.037+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.037+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.037+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.038+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.038+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.038+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.044+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.044+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.045+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.045+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.045+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.046+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.046+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.046+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.048+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.738+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.738+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.741+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:28.909+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:29.331+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:29.342+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.545+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.545+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.545+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.545+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.545+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.545+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":5,"modified":"2025-06-29T20:12:55.357+0800"}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.545+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"39932"}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.545+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":39932}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.546+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":39932}
{"level":"WARN","timestamp":"2025-06-29T20:17:30.546+0800","caller":"utils/logger.go:103","msg":"查找进程失败","pid":39932,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.546+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":39932}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.546+0800","caller":"utils/logger.go:96","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.546+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.546+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":39712}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.666+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T20:17:30.666+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.101+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.101+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.107+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.109+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.109+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.284+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.344+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.347+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.347+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.347+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.351+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.353+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.354+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.377+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.378+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.382+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.584+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.797+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.819+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.819+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.819+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T20:17:31.819+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.772+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.774+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.776+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.781+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.781+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.781+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.782+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.782+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.782+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.783+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.783+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.783+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.788+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.789+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.791+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.791+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.792+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.792+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.794+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.795+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.796+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:48.995+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:49.026+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:49.044+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:49.189+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:49.221+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:49.252+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.574+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.574+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.574+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.574+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.574+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.574+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":5,"modified":"2025-06-29T20:17:30.546+0800"}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.574+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"39712"}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.574+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":39712}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.574+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":39712}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.575+0800","caller":"utils/logger.go:96","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.575+0800","caller":"utils/logger.go:96","msg":"执行命令","command":"tasklist /FI \"PID eq 39712\" /NH"}
{"level":"WARN","timestamp":"2025-06-29T20:17:56.603+0800","caller":"utils/logger.go:103","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.603+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":39712}
{"level":"WARN","timestamp":"2025-06-29T20:17:56.603+0800","caller":"utils/logger.go:103","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.606+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"ERROR","timestamp":"2025-06-29T20:17:56.606+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.707+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":39712}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.707+0800","caller":"utils/logger.go:96","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-06-29T20:17:56.707+0800","caller":"utils/logger.go:96","msg":"执行命令","command":"tasklist /FI \"PID eq 39712\" /NH"}
{"level":"WARN","timestamp":"2025-06-29T20:17:56.729+0800","caller":"utils/logger.go:103","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"WARN","timestamp":"2025-06-29T20:17:56.729+0800","caller":"utils/logger.go:103","msg":"单实例检查失败，程序退出"}
{"level":"INFO","timestamp":"2025-06-29T20:26:54.247+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T20:26:54.247+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:26:54.984+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T20:26:54.984+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:26:55.590+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:26:55.590+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:26:56.064+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:26:56.242+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:27:04.431+0800","caller":"utils/logger.go:96","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-06-29T20:27:04.431+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T20:27:04.431+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-29T20:27:04.431+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-29T20:27:04.431+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T20:27:04.431+0800","caller":"utils/logger.go:96","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.498+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.498+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.498+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.498+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.498+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.499+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.499+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.499+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":39080}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.533+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.534+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.850+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.850+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.857+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.859+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T20:27:24.859+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.343+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.403+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.407+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.407+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.407+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.411+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.411+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.411+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.438+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.439+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.443+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.799+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.800+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.800+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.800+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T20:27:25.908+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:27:26.091+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:27:32.440+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T20:27:32.440+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:27:32.656+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T20:27:32.656+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:27:32.912+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:27:32.912+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:27:33.302+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:27:33.484+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:28:28.026+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T20:28:28.026+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:28:28.314+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T20:28:28.314+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:28:28.535+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:28:28.535+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:28:28.880+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:28:29.067+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.799+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.800+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.802+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.805+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.806+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.806+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.806+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.806+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.806+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.806+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.807+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.807+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.809+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.809+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.810+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.810+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.810+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.810+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.810+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.811+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:34:59.812+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:00.449+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:00.456+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:00.458+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:00.683+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:01.128+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:01.141+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.969+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.970+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.972+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.977+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.977+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.977+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.977+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.977+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.977+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.978+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.978+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.978+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.986+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.986+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.986+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.987+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.988+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.988+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.988+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.989+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:05.990+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:06.205+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:06.227+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:06.227+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:06.392+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:06.409+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:06.455+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.469+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.469+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.469+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.469+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.470+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.470+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":5,"modified":"2025-06-29T20:27:24.499+0800"}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.470+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"39080"}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.470+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":39080}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.470+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":39080}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.470+0800","caller":"utils/logger.go:96","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.470+0800","caller":"utils/logger.go:96","msg":"执行命令","command":"tasklist /FI \"PID eq 39080\" /NH"}
{"level":"WARN","timestamp":"2025-06-29T20:35:07.495+0800","caller":"utils/logger.go:103","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.495+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":39080}
{"level":"WARN","timestamp":"2025-06-29T20:35:07.495+0800","caller":"utils/logger.go:103","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.495+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"ERROR","timestamp":"2025-06-29T20:35:07.495+0800","caller":"utils/logger.go:85","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:85\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.595+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":39080}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.595+0800","caller":"utils/logger.go:96","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-06-29T20:35:07.595+0800","caller":"utils/logger.go:96","msg":"执行命令","command":"tasklist /FI \"PID eq 39080\" /NH"}
{"level":"WARN","timestamp":"2025-06-29T20:35:07.618+0800","caller":"utils/logger.go:103","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"WARN","timestamp":"2025-06-29T20:35:07.618+0800","caller":"utils/logger.go:103","msg":"单实例检查失败，程序退出"}
{"level":"INFO","timestamp":"2025-06-29T20:35:09.087+0800","caller":"utils/logger.go:96","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-06-29T20:35:09.087+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T20:35:09.087+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-29T20:35:09.088+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T20:35:09.088+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-29T20:35:09.088+0800","caller":"utils/logger.go:96","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-06-29T20:36:15.824+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T20:36:15.824+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T20:36:15.825+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T20:36:15.825+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T20:36:15.825+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T20:36:15.825+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T20:36:15.825+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T20:36:15.825+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":41132}
{"level":"INFO","timestamp":"2025-06-29T20:36:15.962+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T20:36:15.962+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T20:36:16.412+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T20:36:16.412+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T20:36:16.418+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T20:36:16.421+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T20:36:16.421+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T20:36:16.894+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:36:16.979+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T20:36:16.983+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:36:16.983+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:36:16.983+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:36:16.994+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T20:36:17.003+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T20:36:17.003+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T20:36:17.031+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:36:17.031+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:36:17.039+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:36:17.179+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:36:17.180+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T20:36:17.180+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T20:36:17.180+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T20:36:18.292+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:36:18.477+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:36:55.870+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T20:36:55.870+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:36:56.045+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T20:36:56.045+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:36:56.287+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:36:56.287+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:36:57.034+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:36:57.280+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:37:38.628+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T20:37:38.628+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:37:38.841+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T20:37:38.841+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:37:39.031+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:37:39.031+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:37:39.414+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:37:39.659+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:38:27.968+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T20:38:27.974+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T20:38:27.976+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T20:38:27.976+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T20:38:28.008+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:38:28.008+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T20:38:28.008+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T20:38:28.020+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T20:38:28.020+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T20:38:28.021+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:03:35.384+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:03:36.071+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.293+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:96","msg":"发现现有锁文件","size":5,"modified":"2025-06-29T20:36:15.825+0800"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:96","msg":"锁文件内容","pid":"41132"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":41132}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:96","msg":"检查进程是否运行","pid":41132}
{"level":"WARN","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:103","msg":"查找进程失败","pid":41132,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:96","msg":"进程未找到，清理旧锁文件","pid":41132}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:96","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.294+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":28076}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.382+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.382+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.838+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.838+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.844+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.847+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T21:09:17.847+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.380+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.442+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.446+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.446+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.446+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.449+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.452+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.452+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.471+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.471+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.477+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.526+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.526+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.527+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T21:09:18.527+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T21:09:19.128+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:09:19.765+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:10:04.614+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:10:04.618+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:10:04.618+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:10:04.618+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:10:04.625+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:10:04.625+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:10:04.628+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:10:04.834+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:10:05.045+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:10:20.990+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:10:20.994+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:10:20.994+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:10:20.994+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:10:20.996+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:10:20.996+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:10:20.997+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:10:21.190+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:10:21.642+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:12:47.979+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-29T21:12:48.015+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T21:12:48.015+0800","caller":"utils/logger.go:96","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-06-29T21:12:48.015+0800","caller":"utils/logger.go:96","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-06-29T21:12:48.015+0800","caller":"utils/logger.go:96","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-06-29T21:12:48.015+0800","caller":"utils/logger.go:96","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-06-29T21:12:48.033+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T21:12:48.033+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-29T21:12:48.033+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.038+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.038+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.038+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.038+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.039+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.039+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.039+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.039+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":19508}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.189+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.189+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.612+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.612+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.619+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.621+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T21:13:08.621+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.115+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.176+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.179+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.179+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.179+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.182+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.184+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.184+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.210+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.210+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.217+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.346+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.346+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.346+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.346+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T21:13:09.830+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:13:10.470+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:13:22.379+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T21:13:22.379+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:13:22.605+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T21:13:22.606+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:13:22.828+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:13:22.828+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:13:23.233+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:13:23.458+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:14:58.944+0800","caller":"utils/logger.go:96","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-06-29T21:14:58.944+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T21:14:58.944+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-29T21:14:58.944+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-29T21:14:58.944+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T21:14:58.944+0800","caller":"utils/logger.go:96","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-06-29T21:15:11.413+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T21:15:11.414+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T21:15:11.414+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T21:15:11.414+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T21:15:11.414+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T21:15:11.414+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T21:15:11.414+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T21:15:11.414+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":41392}
{"level":"INFO","timestamp":"2025-06-29T21:15:11.538+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T21:15:11.538+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.133+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.133+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.139+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.142+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.142+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.634+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.694+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.700+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.700+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.700+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.702+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.704+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.704+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.729+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.730+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:15:12.738+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:15:13.041+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:15:13.041+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T21:15:13.041+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T21:15:13.041+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T21:15:13.695+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:15:13.943+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:25:58.922+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:25:58.928+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:25:58.928+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:25:58.928+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:25:58.933+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:25:58.934+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:25:58.936+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:25:59.598+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:26:00.276+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:26:22.054+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:26:22.055+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:26:22.055+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:26:22.055+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:26:22.058+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:26:22.058+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:26:22.062+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:26:22.254+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:26:22.520+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:26:43.903+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:26:43.907+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:26:43.907+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:26:43.907+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:26:43.912+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:26:43.913+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:26:43.914+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:26:44.177+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:26:44.434+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:27:38.165+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:27:38.167+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:27:38.167+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:27:38.167+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:27:38.170+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:27:38.170+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:27:38.171+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:27:38.424+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:27:38.611+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:27:50.957+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:27:50.960+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:27:50.960+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:27:50.960+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:28:03.410+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:28:03.411+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:28:03.411+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:28:03.411+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:28:03.413+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:28:03.413+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:28:03.418+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:28:03.624+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:28:04.210+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:44:05.714+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-29T21:44:05.764+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T21:44:05.764+0800","caller":"utils/logger.go:96","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-06-29T21:44:05.764+0800","caller":"utils/logger.go:96","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-06-29T21:44:05.764+0800","caller":"utils/logger.go:96","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-06-29T21:44:05.764+0800","caller":"utils/logger.go:96","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-06-29T21:44:05.781+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T21:44:05.781+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-29T21:44:05.781+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T21:45:38.291+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T21:45:38.291+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T21:45:38.291+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T21:45:38.292+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T21:45:38.292+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T21:45:38.292+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T21:45:38.292+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T21:45:38.292+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":36020}
{"level":"INFO","timestamp":"2025-06-29T21:45:38.332+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T21:45:38.332+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.295+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.295+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.301+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.303+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.303+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.874+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.940+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.944+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.945+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.945+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.948+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.950+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.950+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.975+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.975+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:45:39.982+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:45:40.023+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:45:40.023+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T21:45:40.023+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T21:45:40.023+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T21:45:40.665+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:45:41.303+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:46:14.177+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T21:46:14.177+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:46:14.404+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T21:46:14.404+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:46:14.654+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:46:14.654+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:46:15.071+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:46:15.306+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:47:17.722+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T21:47:17.722+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:47:17.962+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T21:47:17.962+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:47:18.199+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:47:18.199+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:47:18.553+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:47:18.774+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:48:37.051+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:48:37.986+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:48:41.960+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:48:42.836+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:48:46.068+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:51:03.477+0800","caller":"utils/logger.go:96","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-06-29T21:51:03.477+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T21:51:03.477+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-29T21:51:03.477+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-29T21:51:03.477+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T21:51:03.477+0800","caller":"utils/logger.go:96","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-06-29T21:51:31.928+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T21:51:31.929+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T21:51:31.929+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T21:51:31.929+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T21:51:31.929+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T21:51:31.929+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T21:51:31.929+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T21:51:31.929+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":27664}
{"level":"INFO","timestamp":"2025-06-29T21:51:32.058+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T21:51:32.058+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T21:51:32.516+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T21:51:32.516+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T21:51:32.522+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T21:51:32.523+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T21:51:32.523+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.131+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.211+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.216+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.216+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.216+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.216+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.218+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.218+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.218+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.222+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.223+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.224+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.266+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.266+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:51:33.279+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:51:34.983+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:51:35.620+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:52:41.048+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:52:41.054+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:52:41.054+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:52:41.054+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:52:41.058+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:52:41.059+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:52:41.066+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:52:41.271+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:52:41.563+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:52:45.869+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:52:45.872+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:52:45.872+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:52:45.872+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:52:45.874+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:52:45.874+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:52:45.876+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:52:46.315+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:52:46.656+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:54:12.333+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:54:12.336+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:54:12.336+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:54:12.336+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:54:12.350+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:54:12.350+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:54:12.356+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:54:12.613+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:54:12.797+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:54:28.581+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:54:28.584+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:54:28.584+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:54:28.584+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:54:28.600+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:54:28.600+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:54:28.612+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:54:28.802+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:54:29.028+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.964+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.972+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.972+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.972+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.974+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.976+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.978+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.979+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.981+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.982+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.982+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.990+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.993+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:57:49.993+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:57:50.001+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:57:50.006+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:57:50.006+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:57:50.006+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:57:50.017+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:57:50.018+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:57:50.025+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:57:50.599+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:57:50.633+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:57:50.819+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:57:50.830+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:57:50.906+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:57:51.159+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:59:18.069+0800","caller":"utils/logger.go:96","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-06-29T21:59:18.069+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T21:59:18.069+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-29T21:59:18.069+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-29T21:59:18.069+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T21:59:18.069+0800","caller":"utils/logger.go:96","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-06-29T21:59:32.854+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T21:59:32.855+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T21:59:32.855+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T21:59:32.855+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T21:59:32.855+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T21:59:32.855+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T21:59:32.855+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T21:59:32.855+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":36228}
{"level":"INFO","timestamp":"2025-06-29T21:59:32.906+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T21:59:32.906+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.435+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.435+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.444+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.446+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.446+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.766+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.766+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.782+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.782+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.782+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.789+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.789+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.793+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.839+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.842+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.842+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.843+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.846+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.848+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.849+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.874+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.874+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.889+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:59:33.994+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.115+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.116+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.116+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.119+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.126+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.305+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.308+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.459+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.462+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.463+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.463+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.467+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.467+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.477+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.717+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T21:59:34.937+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:05:03.526+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:05:03.528+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:05:03.528+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:05:03.528+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:05:03.530+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:05:03.530+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:05:03.532+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:05:04.107+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:05:04.685+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:05:10.170+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:05:10.171+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:05:10.171+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:05:10.171+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:05:10.185+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:05:10.186+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:05:10.195+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:05:10.436+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:05:10.607+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:05:20.654+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:05:20.658+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:05:20.658+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:05:20.658+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:05:20.661+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:05:20.661+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:05:20.669+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:05:20.936+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:05:21.140+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:45.809+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:07:45.816+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:07:45.816+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:07:45.816+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:07:45.817+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:07:45.821+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:45.823+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:07:45.823+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:07:45.823+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:07:45.827+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:46.422+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:46.425+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:46.425+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:46.425+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:47.078+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:47.080+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:47.080+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:07:47.087+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:47.279+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:47.294+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:47.447+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:47.549+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:47.550+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:07:47.555+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:47.728+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:07:47.960+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:05.712+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:08:05.718+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:08:05.718+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:08:05.718+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:08:05.722+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:05.943+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:06.160+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:06.392+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:06.555+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:06.555+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:08:06.561+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:06.796+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:07.021+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:25.409+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T22:08:25.409+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:25.605+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T22:08:25.605+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:25.867+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:25.867+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:26.283+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:08:26.558+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:16.941+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:09:16.943+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:09:16.943+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:09:16.943+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:09:16.946+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:17.136+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:17.312+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:17.526+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:17.749+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:17.750+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:09:17.756+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:17.998+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:18.238+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:50.028+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T22:09:50.028+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:50.240+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T22:09:50.240+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:50.478+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:50.478+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:50.913+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:51.090+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:55.222+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:55.444+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式生化平衡分析] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T22:09:55.444+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:55.690+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:55.690+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:56.116+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:09:57.349+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:10:15.976+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:10:16.228+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式生化平衡分析] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T22:10:16.228+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:10:16.456+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:10:16.456+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:10:16.928+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:10:17.090+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:10:33.749+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T22:10:33.749+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:10:34.031+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T22:10:34.031+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:10:34.321+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:10:34.321+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:10:34.757+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:10:34.969+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:13:21.943+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-29T22:13:21.962+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T22:13:21.962+0800","caller":"utils/logger.go:96","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-06-29T22:13:21.962+0800","caller":"utils/logger.go:96","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-06-29T22:13:21.962+0800","caller":"utils/logger.go:96","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-06-29T22:13:21.962+0800","caller":"utils/logger.go:96","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-06-29T22:13:21.977+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T22:13:21.977+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-29T22:13:21.977+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T22:42:37.929+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T22:42:37.929+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T22:42:37.929+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T22:42:37.929+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T22:42:37.929+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T22:42:37.930+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T22:42:37.930+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T22:42:37.930+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":29012}
{"level":"INFO","timestamp":"2025-06-29T22:42:37.963+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T22:42:37.963+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T22:42:38.644+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T22:42:38.644+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T22:42:38.655+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T22:42:38.662+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T22:42:38.662+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T22:42:38.906+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:42:38.959+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:42:38.959+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:42:38.959+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:42:38.975+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.025+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.066+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.071+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.071+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.071+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.076+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.078+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.079+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.108+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.336+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.336+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.336+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.336+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.618+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.795+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.795+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.795+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:39.851+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:40.042+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:40.042+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:42:40.048+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:40.062+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:40.250+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:40.250+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:42:40.256+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:40.284+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:40.456+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:40.513+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:42:40.642+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:43:02.994+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:43:02.997+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:43:02.997+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:43:02.997+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:43:03.002+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:43:03.236+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:43:03.491+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:43:03.715+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:43:03.986+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:43:03.986+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:43:03.994+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:43:04.234+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:43:04.471+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:44:11.230+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T22:44:11.230+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:44:11.431+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T22:44:11.431+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:44:11.609+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:44:11.609+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:44:12.092+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:44:12.288+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:44:53.697+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T22:44:53.697+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:44:53.912+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式C] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T22:44:53.912+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:44:54.523+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:44:54.523+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:44:54.901+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:44:55.134+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:45:33.119+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T22:45:33.119+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:45:33.340+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T22:45:33.340+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:45:33.533+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:45:33.534+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:45:33.986+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:45:34.171+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:46:20.384+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T22:46:20.384+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:46:20.604+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T22:46:20.604+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:46:20.805+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:46:20.805+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:46:21.336+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:46:21.551+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:46:24.283+0800","caller":"utils/logger.go:96","msg":"开始并发截图","roundNumber":1,"mode":"生化平衡分析"}
{"level":"INFO","timestamp":"2025-06-29T22:46:24.283+0800","caller":"utils/logger.go:96","msg":"并发截图完成","roundNumber":1}
{"level":"INFO","timestamp":"2025-06-29T22:46:24.293+0800","caller":"utils/logger.go:96","msg":"开始并发截图","roundNumber":0,"mode":"生化平衡分析"}
{"level":"INFO","timestamp":"2025-06-29T22:46:24.293+0800","caller":"utils/logger.go:96","msg":"并发截图完成","roundNumber":0}
{"level":"INFO","timestamp":"2025-06-29T22:46:30.651+0800","caller":"utils/logger.go:96","msg":"开始并发截图","roundNumber":1,"mode":"病理形态学分析"}
{"level":"INFO","timestamp":"2025-06-29T22:46:30.651+0800","caller":"utils/logger.go:96","msg":"并发截图完成","roundNumber":1}
{"level":"INFO","timestamp":"2025-06-29T22:46:30.660+0800","caller":"utils/logger.go:96","msg":"开始并发截图","roundNumber":0,"mode":"病理形态学分析"}
{"level":"INFO","timestamp":"2025-06-29T22:46:30.660+0800","caller":"utils/logger.go:96","msg":"并发截图完成","roundNumber":0}
{"level":"INFO","timestamp":"2025-06-29T22:46:38.588+0800","caller":"utils/logger.go:96","msg":"开始并发截图","roundNumber":1,"mode":"生化平衡分析"}
{"level":"INFO","timestamp":"2025-06-29T22:46:38.588+0800","caller":"utils/logger.go:96","msg":"并发截图完成","roundNumber":1}
{"level":"INFO","timestamp":"2025-06-29T22:46:38.593+0800","caller":"utils/logger.go:96","msg":"开始并发截图","roundNumber":0,"mode":"生化平衡分析"}
{"level":"INFO","timestamp":"2025-06-29T22:46:38.593+0800","caller":"utils/logger.go:96","msg":"并发截图完成","roundNumber":0}
{"level":"INFO","timestamp":"2025-06-29T22:46:38.625+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T22:46:38.625+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:46:38.836+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T22:46:38.836+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:46:39.037+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:46:39.037+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:46:39.125+0800","caller":"utils/logger.go:96","msg":"开始并发截图","roundNumber":1,"mode":"病理形态学分析"}
{"level":"INFO","timestamp":"2025-06-29T22:46:39.125+0800","caller":"utils/logger.go:96","msg":"并发截图完成","roundNumber":1}
{"level":"INFO","timestamp":"2025-06-29T22:46:39.142+0800","caller":"utils/logger.go:96","msg":"开始并发截图","roundNumber":0,"mode":"病理形态学分析"}
{"level":"INFO","timestamp":"2025-06-29T22:46:39.142+0800","caller":"utils/logger.go:96","msg":"并发截图完成","roundNumber":0}
{"level":"INFO","timestamp":"2025-06-29T22:46:39.497+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:46:39.731+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:50:33.567+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T22:50:33.567+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:50:34.246+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-29T22:50:34.246+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:50:34.810+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:50:34.810+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:50:35.257+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:50:35.500+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:14.951+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:58:14.964+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:58:14.964+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:58:14.964+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:58:14.967+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:15.610+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:15.610+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:15.610+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:16.234+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:16.234+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:58:16.237+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:16.444+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:16.661+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:36.972+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:58:36.975+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:58:36.975+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:58:36.975+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:58:36.981+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:36.987+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:58:37.000+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:58:37.000+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:58:37.000+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:58:37.002+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:37.198+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:37.198+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:37.198+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:37.242+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:37.441+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:37.670+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.199+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.202+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.206+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.206+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.207+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.208+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.208+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.208+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.210+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.212+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.467+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.488+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.489+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.489+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.645+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.737+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.738+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.741+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.831+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:53.959+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:54.015+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:54.015+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T22:58:54.019+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:54.159+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:54.207+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T22:58:54.392+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T23:16:49.008+0800","caller":"utils/logger.go:96","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-06-29T23:16:49.027+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T23:16:49.027+0800","caller":"utils/logger.go:96","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-06-29T23:16:49.027+0800","caller":"utils/logger.go:96","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-06-29T23:16:49.027+0800","caller":"utils/logger.go:96","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-06-29T23:16:49.027+0800","caller":"utils/logger.go:96","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-06-29T23:16:49.062+0800","caller":"utils/logger.go:96","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T23:16:49.062+0800","caller":"utils/logger.go:96","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-06-29T23:16:49.062+0800","caller":"utils/logger.go:96","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T23:59:14.479+0800","caller":"utils/logger.go:96","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-06-29T23:59:14.485+0800","caller":"utils/logger.go:96","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-06-29T23:59:14.485+0800","caller":"utils/logger.go:96","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-06-29T23:59:14.485+0800","caller":"utils/logger.go:96","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-06-29T23:59:14.485+0800","caller":"utils/logger.go:96","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-06-29T23:59:14.485+0800","caller":"utils/logger.go:96","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-06-29T23:59:14.485+0800","caller":"utils/logger.go:96","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-06-29T23:59:14.485+0800","caller":"utils/logger.go:96","msg":"写入当前PID到锁文件","pid":17912}
{"level":"INFO","timestamp":"2025-06-29T23:59:14.524+0800","caller":"utils/logger.go:96","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-06-29T23:59:14.524+0800","caller":"utils/logger.go:96","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-06-29T23:59:15.982+0800","caller":"utils/logger.go:96","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T23:59:15.982+0800","caller":"utils/logger.go:96","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-06-29T23:59:15.994+0800","caller":"utils/logger.go:96","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-06-29T23:59:15.996+0800","caller":"utils/logger.go:96","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-06-29T23:59:15.996+0800","caller":"utils/logger.go:96","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.347+0800","caller":"utils/logger.go:96","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.431+0800","caller":"utils/logger.go:96","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.436+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.436+0800","caller":"utils/logger.go:96","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.436+0800","caller":"utils/logger.go:96","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.442+0800","caller":"utils/logger.go:96","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.444+0800","caller":"utils/logger.go:96","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.445+0800","caller":"utils/logger.go:96","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.469+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.775+0800","caller":"utils/logger.go:96","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.776+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.776+0800","caller":"utils/logger.go:96","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-06-29T23:59:16.776+0800","caller":"utils/logger.go:96","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-06-29T23:59:17.104+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T23:59:17.104+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T23:59:17.104+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T23:59:17.758+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T23:59:17.764+0800","caller":"utils/logger.go:96","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-06-29T23:59:17.766+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T23:59:18.278+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-29T23:59:18.471+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-06-29","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T00:00:49.089+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-06-30T00:00:49.089+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T00:00:49.335+0800","caller":"utils/logger.go:96","msg":"[操作:快捷键截图-模式B] [当前受检者:暂无候检者] [网点:YL-BJ-TZ-001]"}
{"level":"INFO","timestamp":"2025-06-30T00:00:49.335+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T00:00:49.595+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T00:00:49.595+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"处理截图工作流程","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T00:00:50.125+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-06-30T00:00:50.298+0800","caller":"utils/logger.go:74","msg":"操作记录","operation":"图片OCR识别","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
