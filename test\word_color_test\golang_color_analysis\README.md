# 颜色分析工具 - Go语言版本

基于标准颜色范围的优化颜色分析工具，使用Go语言重构实现。

## 功能特性

### 核心功能
- **精确颜色识别**: 基于医学标准的RGB颜色范围识别
- **数字-文字键值对分析**: 智能提取OCR数据中的数值和对应文字
- **多点采样**: 使用加权多点采样提高颜色识别准确性
- **颜色优先级规则**: 应用智能规则确定最终颜色分类
- **异常值检测**: 自动识别红色异常值和橘色警告值

### 支持的颜色标准
- **红色**: RGB(255,0,0) 到 RGB(255,160,128) - 异常值标识
- **橘色**: RGB(235,146,53) 到 RGB(255,231,170) - 警告值标识  
- **蓝色**: RGB(21,21,170) 到 RGB(110,166,255) - 正常值标识
- **白色**: RGB(240,240,240) 到 RGB(255,255,255) - 正常值标识
- **黑色**: RGB(0,0,0) 到 RGB(50,50,50) - 正常值标识

## 项目结构

```
golang_color_analysis/
├── main.go              # 主程序入口和数据结构定义
├── color_analyzer.go    # 颜色分析核心算法
├── analysis.go          # 分析流程和结果处理
├── go.mod              # Go模块文件
├── build_and_run.bat   # 构建运行脚本
├── README.md           # 项目说明文档
└── output/             # 输出目录（自动创建）
    ├── optimized_color_analysis_results.json
    ├── text_with_color.json
    └── optimized_analysis_report.txt
```

## 快速开始

### 环境要求
- Go 1.21 或更高版本
- Windows 操作系统

### 构建和运行

1. **使用批处理脚本（推荐）**:
   ```bash
   build_and_run.bat
   ```

2. **手动构建**:
   ```bash
   go build -o color_analysis.exe .
   color_analysis.exe
   ```

### 输入文件要求

程序需要以下输入文件（位于上级目录）:
- `input_img_0.jpg` - 待分析的图像文件
- `responseBody.json` - OCR识别结果数据

## 输出文件说明

### 1. optimized_color_analysis_results.json
完整的分析结果，包含:
- 图像信息和颜色标准
- 详细的分析结果数组
- 颜色统计信息
- 目标器官信息

### 2. text_with_color.json
网页显示专用格式，包含:
- 分析时间戳和源文件信息
- 颜色统计数据
- 简化的详细结果数组

### 3. optimized_analysis_report.txt
人类可读的详细报告，包含:
- 基本信息和颜色标准
- 颜色分布统计
- 异常值检测结果
- 详细分析数据
- 分析结论和建议

## 算法特性

### 颜色分类算法
1. **标准范围检测**: 优先使用预定义的RGB范围进行精确匹配
2. **模糊分类**: 对于边界情况，计算与标准颜色的欧几里得距离
3. **距离阈值**: 超过阈值的颜色默认归类为蓝色（正常值）

### 采样策略
1. **多点采样**: 在目标像素周围进行多点采样
2. **加权平均**: 中心点权重更高，边缘点权重递减
3. **自适应采样**: 根据文字大小调整采样范围

### 优先级规则
1. **数字颜色优先**: 如果数字颜色明确，优先使用数字颜色
2. **文字颜色备选**: 数字颜色不明确时，使用文字颜色
3. **默认蓝色**: 都无法确定时，默认为蓝色（正常值）

## 性能优势

相比Python版本的优势:
- **更快的执行速度**: Go语言编译型特性
- **更低的内存占用**: 高效的内存管理
- **单文件部署**: 编译后的可执行文件无需依赖
- **并发处理能力**: 为未来扩展预留并发处理能力

## 与Python版本的兼容性

- **输入格式**: 完全兼容现有的OCR数据格式
- **输出格式**: 保持与Python版本相同的JSON结构
- **算法逻辑**: 完全移植Python版本的核心算法
- **颜色标准**: 使用相同的颜色识别标准

## 扩展计划

- [ ] 支持更多图像格式（PNG, BMP等）
- [ ] 并发处理多个图像文件
- [ ] 配置文件支持自定义颜色标准
- [ ] 命令行参数支持
- [ ] 性能监控和日志记录

## 故障排除

### 常见问题

1. **编译失败**
   - 检查Go版本是否为1.21+
   - 确保在项目根目录执行构建命令

2. **文件不存在错误**
   - 确认`input_img_0.jpg`和`responseBody.json`存在于上级目录
   - 检查文件路径和权限

3. **分析结果为空**
   - 检查OCR数据格式是否正确
   - 确认图像文件可以正常打开

### 调试模式

程序运行时会输出详细的处理日志，包括:
- 数据加载状态
- 数字-文字配对过程
- 颜色分类结果
- 文件保存状态

## 技术支持

如有问题或建议，请查看:
1. 控制台输出的详细日志
2. 生成的分析报告
3. 与Python版本的结果对比