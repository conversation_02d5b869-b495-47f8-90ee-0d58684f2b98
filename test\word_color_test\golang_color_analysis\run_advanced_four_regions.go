package main

import (
	json "github.com/goccy/go-json"
	"fmt"
	"image"
	_ "image/jpeg"
	_ "image/png"
	"log"
	"math"
	"os"
	"path/filepath"
	"time"

	"github.com/disintegration/imaging"
)

// RGB 颜色结构体
type RGB struct {
	R, G, B int
}

// OCRData OCR识别数据结构（支持两种格式）
type OCRData struct {
	// 格式1：嵌套结构
	Result struct {
		TableRecResults []struct {
			PrunedResult struct {
				OverallOCRRes struct {
					RecTexts []string     `json:"rec_texts"`
					RecPolys [][][]int    `json:"rec_polys"`
				} `json:"overall_ocr_res"`
			} `json:"prunedResult"`
		} `json:"tableRecResults"`
	} `json:"result"`
	
	// 格式2：直接结构
	PrunedResult struct {
		RecTexts []string     `json:"rec_texts"`
		RecPolys [][][]int    `json:"rec_polys"`
	} `json:"prunedResult"`
}

// SimplifiedAdvancedColorAnalyzer 简化版升级颜色分析器
type SimplifiedAdvancedColorAnalyzer struct {
	imagePath       string
	ocrDataPath     string
	outputDir       string
	image           image.Image
	processedImage  image.Image
	ocrData         *OCRData
	debugMode       bool
	priorityRules   []string
	dilationRadius  int
	regionSampling  bool
	confidenceThreshold float64
	sharpenStrength float64
	contrastFactor  float64
}

// NewSimplifiedAdvancedColorAnalyzer 创建简化版升级颜色分析器
func NewSimplifiedAdvancedColorAnalyzer(imagePath, ocrDataPath, outputDir string) *SimplifiedAdvancedColorAnalyzer {
	return &SimplifiedAdvancedColorAnalyzer{
		imagePath:           imagePath,
		ocrDataPath:         ocrDataPath,
		outputDir:           outputDir,
		dilationRadius:      2,
		regionSampling:      true,
		priorityRules:       []string{"red", "orange", "green", "blue"},
		confidenceThreshold: 0.6,
		sharpenStrength:     1.5,
		contrastFactor:      1.2,
		debugMode:           false,
	}
}

// SetDebugMode 设置调试模式
func (saca *SimplifiedAdvancedColorAnalyzer) SetDebugMode(debug bool) {
	saca.debugMode = debug
}

// LoadOCRData 加载OCR数据
func (saca *SimplifiedAdvancedColorAnalyzer) LoadOCRData() error {
	data, err := os.ReadFile(saca.ocrDataPath)
	if err != nil {
		return fmt.Errorf("读取OCR数据文件失败: %v", err)
	}

	saca.ocrData = &OCRData{}
	err = json.Unmarshal(data, saca.ocrData)
	if err != nil {
		return fmt.Errorf("解析OCR数据失败: %v", err)
	}

	if saca.debugMode {
		// 检查格式1：嵌套结构
		if len(saca.ocrData.Result.TableRecResults) > 0 && 
		   saca.ocrData.Result.TableRecResults[0].PrunedResult.OverallOCRRes.RecTexts != nil {
			log.Printf("OCR数据加载成功（格式1），文本元素数量: %d", len(saca.ocrData.Result.TableRecResults[0].PrunedResult.OverallOCRRes.RecTexts))
		} else if len(saca.ocrData.PrunedResult.RecTexts) > 0 {
			// 检查格式2：直接结构
			log.Printf("OCR数据加载成功（格式2），文本元素数量: %d", len(saca.ocrData.PrunedResult.RecTexts))
		} else {
			log.Printf("OCR数据加载成功，但文本元素为空")
		}
	}

	return nil
}

// LoadImage 加载图像
func (saca *SimplifiedAdvancedColorAnalyzer) LoadImage() error {
	file, err := os.Open(saca.imagePath)
	if err != nil {
		return fmt.Errorf("打开图像文件失败: %v", err)
	}
	defer file.Close()

	img, _, err := image.Decode(file)
	if err != nil {
		return fmt.Errorf("解码图像失败: %v", err)
	}

	saca.image = img

	if saca.debugMode {
		bounds := img.Bounds()
		log.Printf("图像加载成功，尺寸: %dx%d", bounds.Dx(), bounds.Dy())
	}

	return nil
}

// ApplyImageEnhancement 应用图像增强处理
func (saca *SimplifiedAdvancedColorAnalyzer) ApplyImageEnhancement() error {
	if saca.image == nil {
		return fmt.Errorf("源图像未加载")
	}

	if saca.debugMode {
		log.Printf("开始图像增强处理")
	}

	// 1. 锐化处理
	sharpened := imaging.Sharpen(saca.image, saca.sharpenStrength)

	// 2. 调整对比度
	contrasted := imaging.AdjustContrast(sharpened, saca.contrastFactor)

	// 3. 模拟膨胀效果
	if saca.dilationRadius > 0 {
		blurred := imaging.Blur(contrasted, float64(saca.dilationRadius)*0.5)
		saca.processedImage = imaging.Sharpen(blurred, saca.sharpenStrength*1.5)
	} else {
		saca.processedImage = contrasted
	}

	if saca.debugMode {
		log.Printf("图像增强处理完成")
	}

	return nil
}

// FourRegionAnalysisResult 四区域分析结果
type FourRegionAnalysisResult struct {
	RowIndex     int                        `json:"row_index"`
	NumericValue string                     `json:"numeric_value"`
	TextElements []string                   `json:"text_elements"`
	Regions      map[string]RegionColorInfo `json:"regions"`
	FinalColor   string                     `json:"final_color"`
	Confidence   float64                    `json:"confidence"`
}

// RegionColorInfo 区域颜色信息
type RegionColorInfo struct {
	RegionType string  `json:"region_type"`
	MainColor  string  `json:"main_color"`
	RGB        []int   `json:"rgb"`
	Confidence float64 `json:"confidence"`
	Bounds     []int   `json:"bounds"` // [minX, minY, maxX, maxY]
}

// FourRegionReport 四区域分析报告
type FourRegionReport struct {
	Timestamp        string                     `json:"timestamp"`
	ImagePath        string                     `json:"image_path"`
	OCRDataPath      string                     `json:"ocr_data_path"`
	TotalRows        int                        `json:"total_rows"`
	SuccessfulRows   int                        `json:"successful_rows"`
	ProcessingTime   string                     `json:"processing_time"`
	ColorDistribution map[string]int            `json:"color_distribution"`
	AnalysisResults  []FourRegionAnalysisResult `json:"analysis_results"`
}

func main() {
	if len(os.Args) < 3 {
		log.Fatal("用法: go run run_advanced_four_regions.go <image_path> <ocr_data_path> [output_dir]")
	}

	imagePath := os.Args[1]
	ocrDataPath := os.Args[2]
	outputDir := "./advanced_four_regions_output"

	if len(os.Args) >= 4 {
		outputDir = os.Args[3]
	}

	log.Printf("四区域高级颜色分析器")
	log.Printf("图像文件: %s", imagePath)
	log.Printf("OCR数据文件: %s", ocrDataPath)
	log.Printf("输出目录: %s", outputDir)

	startTime := time.Now()

	// 创建简化版高级分析器
	analyzer := NewSimplifiedAdvancedColorAnalyzer(imagePath, ocrDataPath, outputDir)
	analyzer.SetDebugMode(true)

	// 加载数据
	err := analyzer.LoadOCRData()
	if err != nil {
		log.Fatalf("加载OCR数据失败: %v", err)
	}

	err = analyzer.LoadImage()
	if err != nil {
		log.Fatalf("加载图像失败: %v", err)
	}

	// 应用图像增强
	err = analyzer.ApplyImageEnhancement()
	if err != nil {
		log.Fatalf("图像增强失败: %v", err)
	}

	// 执行四区域分析
	results, err := performFourRegionAnalysis(analyzer)
	if err != nil {
		log.Fatalf("四区域分析失败: %v", err)
	}

	processingTime := time.Since(startTime)

	// 生成报告
	report := generateFourRegionReport(analyzer, results, imagePath, ocrDataPath, processingTime)

	// 保存结果
	err = saveFourRegionResults(report, outputDir)
	if err != nil {
		log.Fatalf("保存结果失败: %v", err)
	}

	log.Printf("四区域分析完成，耗时: %v", processingTime)
	log.Printf("成功分析 %d/%d 行", report.SuccessfulRows, report.TotalRows)
}

// performFourRegionAnalysis 执行四区域分析
func performFourRegionAnalysis(analyzer *SimplifiedAdvancedColorAnalyzer) ([]FourRegionAnalysisResult, error) {
	var results []FourRegionAnalysisResult

	// 从OCR数据中提取文本信息
	var recTexts []string
	var recPolys [][][]int

	// 尝试格式1：嵌套结构
	if len(analyzer.ocrData.Result.TableRecResults) > 0 {
		log.Printf("[调试] 使用格式1，TableRecResults数量: %d", len(analyzer.ocrData.Result.TableRecResults))
		overallOCR := analyzer.ocrData.Result.TableRecResults[0].PrunedResult.OverallOCRRes
		recTexts = overallOCR.RecTexts
		recPolys = overallOCR.RecPolys
		log.Printf("[调试] 成功提取RecTexts，数量: %d", len(recTexts))
	} else if len(analyzer.ocrData.PrunedResult.RecTexts) > 0 {
		// 尝试格式2：直接结构
		log.Printf("[调试] 使用格式2，直接从PrunedResult提取")
		recTexts = analyzer.ocrData.PrunedResult.RecTexts
		recPolys = analyzer.ocrData.PrunedResult.RecPolys
		log.Printf("[调试] 成功提取RecTexts，数量: %d", len(recTexts))
	} else {
		log.Printf("[调试] 两种格式都没有找到有效的OCR数据")
	}

	if len(recTexts) > 0 {
		maxShow := 5
		if len(recTexts) < maxShow {
			maxShow = len(recTexts)
		}
		log.Printf("[调试] 前几个文本: %v", recTexts[:maxShow])
	}

	log.Printf("开始四区域分析，文本元素数量: %d", len(recTexts))

	// 假设每两个文本元素构成一行（数字列和文本列）
	for i := 0; i < len(recTexts); i += 2 {
		if i+1 >= len(recTexts) {
			break
		}

		numericText := recTexts[i]
		textDescription := recTexts[i+1]
		numericPoly := recPolys[i]
		textPoly := recPolys[i+1]

		// 分析四个区域
		regions := make(map[string]RegionColorInfo)

		// 数字区域左半部分
		digitLeftRegion := extractAndAnalyzeRegion(analyzer, numericPoly, "digit_left")
		regions["digit_left"] = digitLeftRegion

		// 数字区域右半部分
		digitRightRegion := extractAndAnalyzeRegion(analyzer, numericPoly, "digit_right")
		regions["digit_right"] = digitRightRegion

		// 文本区域左半部分
		textLeftRegion := extractAndAnalyzeRegion(analyzer, textPoly, "text_left")
		regions["text_left"] = textLeftRegion

		// 文本区域右半部分
		textRightRegion := extractAndAnalyzeRegion(analyzer, textPoly, "text_right")
		regions["text_right"] = textRightRegion

		// 应用优先级规则确定最终颜色
		finalColor, confidence := determineFinalColor(regions, analyzer.priorityRules)

		result := FourRegionAnalysisResult{
			RowIndex:     i / 2,
			NumericValue: numericText,
			TextElements: []string{textDescription},
			Regions:      regions,
			FinalColor:   finalColor,
			Confidence:   confidence,
		}

		results = append(results, result)

		log.Printf("第%d行分析完成 - 数字: %s, 文本: %s, 最终颜色: %s",
			result.RowIndex+1, numericText, textDescription, finalColor)
	}

	return results, nil
}

// extractAndAnalyzeRegion 提取并分析指定区域
func extractAndAnalyzeRegion(analyzer *SimplifiedAdvancedColorAnalyzer, polygon [][]int, regionType string) RegionColorInfo {
	if len(polygon) == 0 {
		return RegionColorInfo{RegionType: regionType, MainColor: "unknown"}
	}

	// 计算边界框
	minX, minY := polygon[0][0], polygon[0][1]
	maxX, maxY := minX, minY

	for _, coord := range polygon {
		if coord[0] < minX {
			minX = coord[0]
		}
		if coord[0] > maxX {
			maxX = coord[0]
		}
		if coord[1] < minY {
			minY = coord[1]
		}
		if coord[1] > maxY {
			maxY = coord[1]
		}
	}

	// 根据区域类型确定采样范围
	midX := (minX + maxX) / 2
	var sampleMinX, sampleMaxX int

	if regionType == "digit_left" || regionType == "text_left" {
		sampleMinX = minX
		sampleMaxX = midX
	} else {
		sampleMinX = midX
		sampleMaxX = maxX
	}

	// 采样颜色
	colors := sampleColorsFromBounds(analyzer, sampleMinX, minY, sampleMaxX, maxY)

	// 分析主色
	mainColor, confidence, avgRGB := analyzeMainColor(colors)

	return RegionColorInfo{
		RegionType: regionType,
		MainColor:  mainColor,
		RGB:        []int{avgRGB.R, avgRGB.G, avgRGB.B},
		Confidence: confidence,
		Bounds:     []int{sampleMinX, minY, sampleMaxX, maxY},
	}
}

// sampleColorsFromBounds 从指定边界采样颜色，持续采样直到找到目标颜色
func sampleColorsFromBounds(analyzer *SimplifiedAdvancedColorAnalyzer, minX, minY, maxX, maxY int) []RGB {

	// 选择要采样的图像
	img := analyzer.image
	if analyzer.processedImage != nil {
		img = analyzer.processedImage
	}

	bounds := img.Bounds()

	// 确保采样区域在图像范围内
	if minX < bounds.Min.X {
		minX = bounds.Min.X
	}
	if minY < bounds.Min.Y {
		minY = bounds.Min.Y
	}
	if maxX > bounds.Max.X {
		maxX = bounds.Max.X
	}
	if maxY > bounds.Max.Y {
		maxY = bounds.Max.Y
	}

	// 智能采样策略：优先寻找目标颜色
	targetColors := []RGB{}
	backgroundColors := []RGB{}

	// 第一轮：密集采样寻找目标颜色
	for y := minY; y < maxY; y++ {
		for x := minX; x < maxX; x++ {
			c := img.At(x, y)
			r, g, b, _ := c.RGBA()
			rgb := RGB{
				R: int(r >> 8),
				G: int(g >> 8),
				B: int(b >> 8),
			}

			colorType := classifyColor(rgb)
			// 如果找到目标颜色（红、橘、绿、蓝），优先收集
			if colorType == "红色" || colorType == "橘色" || colorType == "绿色" || colorType == "蓝色" {
				targetColors = append(targetColors, rgb)
			} else if isValidColor(rgb) {
				// 收集其他有效颜色作为备选
				backgroundColors = append(backgroundColors, rgb)
			}
		}
	}

	// 优先返回目标颜色，如果没有目标颜色则返回背景颜色
	if len(targetColors) > 0 {
		return targetColors
	}

	// 如果没有找到目标颜色，尝试扩大采样范围
	if len(backgroundColors) == 0 {
		// 扩大采样范围，向外扩展
		expandedMinX := bounds.Min.X
		if minX-5 > bounds.Min.X {
			expandedMinX = minX - 5
		}
		expandedMinY := bounds.Min.Y
		if minY-5 > bounds.Min.Y {
			expandedMinY = minY - 5
		}
		expandedMaxX := bounds.Max.X
		if maxX+5 < bounds.Max.X {
			expandedMaxX = maxX + 5
		}
		expandedMaxY := bounds.Max.Y
		if maxY+5 < bounds.Max.Y {
			expandedMaxY = maxY + 5
		}

		for y := expandedMinY; y < expandedMaxY; y += 2 {
			for x := expandedMinX; x < expandedMaxX; x += 2 {
				c := img.At(x, y)
				r, g, b, _ := c.RGBA()
				rgb := RGB{
					R: int(r >> 8),
					G: int(g >> 8),
					B: int(b >> 8),
				}

				colorType := classifyColor(rgb)
				if colorType == "红色" || colorType == "橘色" || colorType == "绿色" || colorType == "蓝色" {
					targetColors = append(targetColors, rgb)
				} else if isValidColor(rgb) {
					backgroundColors = append(backgroundColors, rgb)
				}
			}
		}

		if len(targetColors) > 0 {
			return targetColors
		}
	}

	return backgroundColors
}



// analyzeMainColor 分析主色
func analyzeMainColor(colors []RGB) (string, float64, RGB) {
	if len(colors) == 0 {
		return "unknown", 0.0, RGB{}
	}

	// 颜色分类统计
	colorCounts := make(map[string]int)
	colorRGBs := make(map[string][]RGB)

	for _, rgb := range colors {
		colorName := classifyColor(rgb)
		colorCounts[colorName]++
		colorRGBs[colorName] = append(colorRGBs[colorName], rgb)
	}

	// 找出最频繁的颜色
	maxCount := 0
	mainColor := "unknown"
	for colorName, count := range colorCounts {
		if count > maxCount {
			maxCount = count
			mainColor = colorName
		}
	}

	// 计算置信度
	confidence := float64(maxCount) / float64(len(colors))

	// 计算平均RGB
	avgRGB := RGB{}
	if rgbs, exists := colorRGBs[mainColor]; exists && len(rgbs) > 0 {
		var sumR, sumG, sumB int
		for _, rgb := range rgbs {
			sumR += rgb.R
			sumG += rgb.G
			sumB += rgb.B
		}
		avgRGB = RGB{
			R: sumR / len(rgbs),
			G: sumG / len(rgbs),
			B: sumB / len(rgbs),
		}
	}

	return mainColor, confidence, avgRGB
}

// classifyColor 颜色分类
func classifyColor(rgb RGB) string {
	r, g, b := float64(rgb.R), float64(rgb.G), float64(rgb.B)
	
	// 计算饱和度，过滤低饱和度的颜色
	maxVal := math.Max(math.Max(r, g), b)
		minVal := math.Min(math.Min(r, g), b)
	saturation := 0.0
	if maxVal > 0 {
		saturation = (maxVal - minVal) / maxVal
	}
	
	// 背景色检测（更严格的过滤）
	if rgb.R > 230 && rgb.G > 230 && rgb.B > 230 {
		return "背景白色"
	}
	if rgb.R < 30 && rgb.G < 30 && rgb.B < 30 {
		return "背景黑色"
	}
	// 检测灰色背景
	if math.Abs(float64(rgb.R-rgb.G)) < 30 && math.Abs(float64(rgb.G-rgb.B)) < 30 && math.Abs(float64(rgb.R-rgb.B)) < 30 && rgb.R > 180 && rgb.R < 230 {
		return "背景灰色"
	}
	
	// 低饱和度颜色视为背景
	if saturation < 0.3 {
		return "背景灰色"
	}
	
	// 目标颜色检测（更精确的阈值）
	// 橘色检测：优先检测橘色，避免被红色误判
	if r > 150 && g > 80 && g < r*0.9 && b < 120 && r > g && g > b {
		return "橘色"
	}
	if r > 180 && r > g*1.5 && r > b*1.5 {
		return "红色"
	}
	if g > 150 && g > r*1.2 && g > b*1.2 {
		return "绿色"
	}
	if b > 150 && b > r*1.2 && b > g*1.1 {
		return "蓝色"
	}
	
	return "其他"
}



// isValidColor 检查是否为有效颜色
func isValidColor(rgb RGB) bool {
	// 更严格的背景色过滤
	if rgb.R > 230 && rgb.G > 230 && rgb.B > 230 {
		return false // 过白
	}
	if rgb.R < 30 && rgb.G < 30 && rgb.B < 30 {
		return false // 过黑
	}
	// 过滤灰色背景
	if math.Abs(float64(rgb.R-rgb.G)) < 30 && math.Abs(float64(rgb.G-rgb.B)) < 30 && math.Abs(float64(rgb.R-rgb.B)) < 30 && rgb.R > 180 && rgb.R < 230 {
		return false // 灰色背景
	}
	
	// 计算饱和度，过滤低饱和度颜色
	r, g, b := float64(rgb.R), float64(rgb.G), float64(rgb.B)
	maxVal := math.Max(math.Max(r, g), b)
	minVal := math.Min(math.Min(r, g), b)
	saturation := 0.0
	if maxVal > 0 {
		saturation = (maxVal - minVal) / maxVal
	}
	
	// 低饱和度视为无效
	if saturation < 0.2 {
		return false
	}
	
	return true
}

// determineFinalColor 确定最终颜色
func determineFinalColor(regions map[string]RegionColorInfo, priorityRules []string) (string, float64) {
	// 优先级映射（只包含目标颜色）
	priorityMap := map[string]int{
		"红色": 4,
		"橘色": 3,
		"绿色": 2,
		"蓝色": 1,
	}

	highestPriority := 0
	finalColor := "unknown"
	maxConfidence := 0.0

	for _, region := range regions {
		// 跳过置信度过低的区域
		if region.Confidence < 0.4 {
			continue
		}
		
		// 跳过所有背景色和其他非目标颜色
		if region.MainColor == "背景白色" || region.MainColor == "背景黑色" || 
		   region.MainColor == "背景灰色" || region.MainColor == "其他" {
			continue
		}

		priority, exists := priorityMap[region.MainColor]
		if !exists {
			continue // 跳过未定义优先级的颜色
		}
		
		if priority > highestPriority {
			highestPriority = priority
			finalColor = region.MainColor
			maxConfidence = region.Confidence
		} else if priority == highestPriority && region.Confidence > maxConfidence {
			maxConfidence = region.Confidence
		}
	}

	return finalColor, maxConfidence
}

// generateFourRegionReport 生成四区域分析报告
func generateFourRegionReport(analyzer *SimplifiedAdvancedColorAnalyzer, results []FourRegionAnalysisResult,
	imagePath, ocrDataPath string, processingTime time.Duration) *FourRegionReport {

	// 统计颜色分布
	colorDistribution := make(map[string]int)
	successfulRows := 0

	for _, result := range results {
		if result.FinalColor != "unknown" {
			colorDistribution[result.FinalColor]++
			successfulRows++
		}
	}

	return &FourRegionReport{
		Timestamp:         time.Now().Format("2006-01-02 15:04:05"),
		ImagePath:         imagePath,
		OCRDataPath:       ocrDataPath,
		TotalRows:         len(results),
		SuccessfulRows:    successfulRows,
		ProcessingTime:    processingTime.String(),
		ColorDistribution: colorDistribution,
		AnalysisResults:   results,
	}
}

// saveFourRegionResults 保存四区域分析结果
func saveFourRegionResults(report *FourRegionReport, outputDir string) error {
	// 确保输出目录存在
	err := os.MkdirAll(outputDir, 0755)
	if err != nil {
		return fmt.Errorf("创建输出目录失败: %v", err)
	}

	// 保存JSON报告
	jsonPath := filepath.Join(outputDir, "four_region_analysis_results.json")
	jsonData, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化JSON失败: %v", err)
	}

	err = os.WriteFile(jsonPath, jsonData, 0644)
	if err != nil {
		return fmt.Errorf("保存JSON报告失败: %v", err)
	}

	// 保存文本报告
	textPath := filepath.Join(outputDir, "four_region_analysis_summary.txt")
	textReport := generateTextReport(report)
	err = os.WriteFile(textPath, []byte(textReport), 0644)
	if err != nil {
		return fmt.Errorf("保存文本报告失败: %v", err)
	}

	log.Printf("四区域分析结果已保存:")
	log.Printf("  JSON报告: %s", jsonPath)
	log.Printf("  文本报告: %s", textPath)

	return nil
}

// generateTextReport 生成文本格式报告
func generateTextReport(report *FourRegionReport) string {
	reportText := fmt.Sprintf(`四区域高级颜色分析报告
======================

`)
	reportText += fmt.Sprintf("分析时间: %s\n", report.Timestamp)
	reportText += fmt.Sprintf("图像路径: %s\n", report.ImagePath)
	reportText += fmt.Sprintf("OCR数据路径: %s\n", report.OCRDataPath)
	reportText += fmt.Sprintf("处理时间: %s\n", report.ProcessingTime)
	reportText += fmt.Sprintf("总行数: %d\n", report.TotalRows)
	reportText += fmt.Sprintf("成功分析行数: %d\n", report.SuccessfulRows)
	reportText += fmt.Sprintf("\n颜色分布:\n")

	for color, count := range report.ColorDistribution {
		percentage := float64(count) / float64(report.SuccessfulRows) * 100
		reportText += fmt.Sprintf("  %s: %d 行 (%.1f%%)\n", color, count, percentage)
	}

	reportText += fmt.Sprintf("\n详细四区域分析结果:\n")
	for _, result := range report.AnalysisResults {
		reportText += fmt.Sprintf("\n第%d行: %s -> %s\n",
			result.RowIndex+1, result.NumericValue, result.TextElements[0])
		reportText += fmt.Sprintf("  数字左区域: %s (置信度: %.2f)\n",
			result.Regions["digit_left"].MainColor, result.Regions["digit_left"].Confidence)
		reportText += fmt.Sprintf("  数字右区域: %s (置信度: %.2f)\n",
			result.Regions["digit_right"].MainColor, result.Regions["digit_right"].Confidence)
		reportText += fmt.Sprintf("  文本左区域: %s (置信度: %.2f)\n",
			result.Regions["text_left"].MainColor, result.Regions["text_left"].Confidence)
		reportText += fmt.Sprintf("  文本右区域: %s (置信度: %.2f)\n",
			result.Regions["text_right"].MainColor, result.Regions["text_right"].Confidence)
		reportText += fmt.Sprintf("  最终判断: %s (置信度: %.2f)\n",
			result.FinalColor, result.Confidence)
	}

	return reportText
}