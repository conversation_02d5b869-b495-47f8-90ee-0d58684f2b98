package utils

import (
	"fmt"
	"io/ioutil"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v2"
)

// MessageConfig 消息配置结构
type MessageConfig struct {
	OperationStatus struct {
		Initial             string `yaml:"initial"`
		B02Completed        struct {
			Template string `yaml:"template"`
		} `yaml:"b02_completed"`
		C03CompletedContinue struct {
			Template string `yaml:"template"`
		} `yaml:"c03_completed_continue"`
		C03CompletedFinal struct {
			Template string `yaml:"template"`
		} `yaml:"c03_completed_final"`
	} `yaml:"operation_status"`

	ToastMessages struct {
		Success struct {
			ScreenshotB02Success   string `yaml:"screenshot_b02_success"`
			ScreenshotC03Success   string `yaml:"screenshot_c03_success"`
			
			OCRProcessingSuccess   string `yaml:"ocr_processing_success"`
			ColorDetectionSuccess  string `yaml:"color_detection_success"`
		} `yaml:"success"`
		Progress struct {
			OCRProcessing    string `yaml:"ocr_processing"`
			ColorDetection   string `yaml:"color_detection"`
			DataExtraction   string `yaml:"data_extraction"`
			UpdatingRecords  string `yaml:"updating_records"`
		} `yaml:"progress"`
		Error struct {
			ScreenshotFailed      string `yaml:"screenshot_failed"`
			OCRFailed            string `yaml:"ocr_failed"`
			ColorDetectionFailed string `yaml:"color_detection_failed"`
			DataSaveFailed       string `yaml:"data_save_failed"`
		} `yaml:"error"`
		Warning struct {
			DuplicateOrgan   string `yaml:"duplicate_organ"`
	
		} `yaml:"warning"`
	} `yaml:"toast_messages"`

	ButtonTexts struct {
		ScreenshotB02    string `yaml:"screenshot_b02"`
		ScreenshotC03    string `yaml:"screenshot_c03"`
		ContinueDetection string `yaml:"continue_detection"`

		GenerateReport   string `yaml:"generate_report"`
	} `yaml:"button_texts"`

	ProgressBar struct {
		Colors struct {
			B02Mode string `yaml:"b02_mode"`
			C03Mode string `yaml:"c03_mode"`
		} `yaml:"colors"`

		TotalSteps    int `yaml:"total_steps"`
	} `yaml:"progress_bar"`

	ModeNames struct {
		B   string `yaml:"B"`
		C   string `yaml:"C"`
		B02 string `yaml:"B02"`
		C03 string `yaml:"C03"`
	} `yaml:"mode_names"`


}

// 全局消息配置实例
var Messages *MessageConfig

// InitMessages 初始化消息配置
func InitMessages() error {
	configPath := filepath.Join("config", "messages.yaml")
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取消息配置文件失败: %v", err)
	}

	Messages = &MessageConfig{}
	if err := yaml.Unmarshal(data, Messages); err != nil {
		return fmt.Errorf("解析消息配置文件失败: %v", err)
	}

	return nil
}

// GetOperationStatus 获取操作状态消息
func GetOperationStatus(mode string, isCompleted bool) string {
	if Messages == nil {
		return "消息配置未初始化"
	}

	if mode == "B" && isCompleted {
		return Messages.OperationStatus.B02Completed.Template
	}

	if mode == "C" && isCompleted {
		return Messages.OperationStatus.C03CompletedFinal.Template
	}

	return Messages.OperationStatus.Initial
}

// GetToastMessage 获取Toast消息
func GetToastMessage(category, messageType string, params map[string]interface{}) string {
	if Messages == nil {
		return "消息配置未初始化"
	}

	var message string
	switch category {
	case "success":
		switch messageType {
		case "screenshot_b02":
			message = Messages.ToastMessages.Success.ScreenshotB02Success
		case "screenshot_c03":
			message = Messages.ToastMessages.Success.ScreenshotC03Success
		
		case "ocr_processing":
			message = Messages.ToastMessages.Success.OCRProcessingSuccess
		case "color_detection":
			message = Messages.ToastMessages.Success.ColorDetectionSuccess
		}
	case "progress":
		switch messageType {
		case "ocr_processing":
			message = Messages.ToastMessages.Progress.OCRProcessing
		case "color_detection":
			message = Messages.ToastMessages.Progress.ColorDetection
		case "data_extraction":
			message = Messages.ToastMessages.Progress.DataExtraction
		case "updating_records":
			message = Messages.ToastMessages.Progress.UpdatingRecords
		}
	case "error":
		switch messageType {
		case "screenshot_failed":
			message = Messages.ToastMessages.Error.ScreenshotFailed
		case "ocr_failed":
			message = Messages.ToastMessages.Error.OCRFailed
		case "color_detection_failed":
			message = Messages.ToastMessages.Error.ColorDetectionFailed
		case "data_save_failed":
			message = Messages.ToastMessages.Error.DataSaveFailed
		}
	case "warning":
		switch messageType {
		case "duplicate_organ":
			message = Messages.ToastMessages.Warning.DuplicateOrgan
		
		}
	}

	// 替换参数
	if params != nil {
		for key, value := range params {
			placeholder := fmt.Sprintf("{%s}", key)
			message = strings.ReplaceAll(message, placeholder, fmt.Sprintf("%v", value))
		}
	}

	return message
}

// GetModeName 获取模式显示名称
func GetModeName(mode string) string {
	if Messages == nil {
		return mode
	}

	switch mode {
	case "B":
		return Messages.ModeNames.B
	case "C":
		return Messages.ModeNames.C
	case "B02":
		return Messages.ModeNames.B02
	case "C03":
		return Messages.ModeNames.C03
	default:
		return mode
	}
}

// GetProgressBarColor 获取进度条颜色
func GetProgressBarColor(mode string) string {
	if Messages == nil {
		return "#2196f3"
	}

	if mode == "B" || mode == "B02" {
		return Messages.ProgressBar.Colors.B02Mode
	}
	return Messages.ProgressBar.Colors.C03Mode
}
