{"level":"INFO","timestamp":"2025-07-02T00:21:44.434+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.434+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.434+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.434+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.435+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.435+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.435+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.435+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":44060}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.456+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.457+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.457+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.457+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.457+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.464+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.464+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.464+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.464+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T00:21:44.465+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:21:56.837+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
2025-07-02 00:21:56.8375896 +0800 CST m=+0.020026901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:56.870+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
2025-07-02 00:21:56.8702415 +0800 CST m=+0.052678801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:56.914+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
2025-07-02 00:21:56.9147878 +0800 CST m=+0.097225101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:56.937+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
2025-07-02 00:21:56.9374489 +0800 CST m=+0.119886201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:56.969+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-02 00:21:56.969675 +0800 CST m=+0.152112301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:56.993+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
2025-07-02 00:21:56.993045 +0800 CST m=+0.175482301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.025+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
2025-07-02 00:21:57.0259644 +0800 CST m=+0.208401701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.048+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":37024}
2025-07-02 00:21:57.0486578 +0800 CST m=+0.231095101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.114+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
2025-07-02 00:21:57.114969 +0800 CST m=+0.297406301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.137+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
2025-07-02 00:21:57.1373282 +0800 CST m=+0.319765501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.148+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
2025-07-02 00:21:57.1480351 +0800 CST m=+0.330472401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.159+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
2025-07-02 00:21:57.1596249 +0800 CST m=+0.342062201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.170+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
2025-07-02 00:21:57.1707686 +0800 CST m=+0.353205901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.603+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
2025-07-02 00:21:57.6035376 +0800 CST m=+0.785974901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.615+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
2025-07-02 00:21:57.6153555 +0800 CST m=+0.797792801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.626+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
2025-07-02 00:21:57.6269563 +0800 CST m=+0.809393601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.637+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-02 00:21:57.6373542 +0800 CST m=+0.819791501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.648+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
2025-07-02 00:21:57.6486255 +0800 CST m=+0.831062801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.659+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-02 00:21:57.6598562 +0800 CST m=+0.842293501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.676+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
2025-07-02 00:21:57.6763555 +0800 CST m=+0.858792801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.682+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
2025-07-02 00:21:57.6825695 +0800 CST m=+0.865006801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.693+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
2025-07-02 00:21:57.6933005 +0800 CST m=+0.875737801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.704+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
2025-07-02 00:21:57.7045004 +0800 CST m=+0.886937701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.715+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
2025-07-02 00:21:57.7153921 +0800 CST m=+0.897829401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.726+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
2025-07-02 00:21:57.7264369 +0800 CST m=+0.908874201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.737+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
2025-07-02 00:21:57.7376869 +0800 CST m=+0.920124201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.749+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
2025-07-02 00:21:57.749177 +0800 CST m=+0.931614301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.749+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
2025-07-02 00:21:57.7496801 +0800 CST m=+0.932117401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.759+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
2025-07-02 00:21:57.7599736 +0800 CST m=+0.942410901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.782+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
2025-07-02 00:21:57.7822414 +0800 CST m=+0.964678701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.793+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
2025-07-02 00:21:57.7937833 +0800 CST m=+0.976220601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.804+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
2025-07-02 00:21:57.8046263 +0800 CST m=+0.987063601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.805+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T00:21:57.813+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
2025-07-02 00:21:57.8051367 +0800 CST m=+0.987574001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.815+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
2025-07-02 00:21:57.8138964 +0800 CST m=+0.996333701 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:57.8159827 +0800 CST m=+0.998420001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.838+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
2025-07-02 00:21:57.8381869 +0800 CST m=+1.020624201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.838+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T00:21:57.838+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
2025-07-02 00:21:57.8381869 +0800 CST m=+1.020624201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.849+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
2025-07-02 00:21:57.838752 +0800 CST m=+1.021189301 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:57.849603 +0800 CST m=+1.032040301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.860+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
2025-07-02 00:21:57.8604276 +0800 CST m=+1.042864901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.873+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T00:21:57.874+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:57.8734346 +0800 CST m=+1.055871901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.882+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
2025-07-02 00:21:57.8749766 +0800 CST m=+1.057413901 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:57.8829065 +0800 CST m=+1.065343801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.915+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
2025-07-02 00:21:57.9153059 +0800 CST m=+1.097743201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.938+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
2025-07-02 00:21:57.9385905 +0800 CST m=+1.121027801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:57.994+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
2025-07-02 00:21:57.9941785 +0800 CST m=+1.176615801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.026+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
2025-07-02 00:21:58.0262489 +0800 CST m=+1.208686201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.049+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
2025-07-02 00:21:58.0494128 +0800 CST m=+1.231850101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.082+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
2025-07-02 00:21:58.0822193 +0800 CST m=+1.264656601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.105+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T00:21:58.105+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
2025-07-02 00:21:58.1053647 +0800 CST m=+1.287802001 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:58.1053647 +0800 CST m=+1.287802001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.137+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T00:21:58.137+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
2025-07-02 00:21:58.1377703 +0800 CST m=+1.320207601 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:58.1377703 +0800 CST m=+1.320207601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.193+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T00:21:58.193+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
2025-07-02 00:21:58.1933415 +0800 CST m=+1.375778801 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:58.1933415 +0800 CST m=+1.375778801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.248+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
2025-07-02 00:21:58.2489545 +0800 CST m=+1.431391801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.304+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
2025-07-02 00:21:58.3045671 +0800 CST m=+1.487004401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.327+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
2025-07-02 00:21:58.3278146 +0800 CST m=+1.510251901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.360+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
2025-07-02 00:21:58.3601826 +0800 CST m=+1.542619901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.383+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
2025-07-02 00:21:58.3834573 +0800 CST m=+1.565894601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:58.5486619 +0800 CST m=+1.731099201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:58.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T00:21:58.549+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:58.5486619 +0800 CST m=+1.731099201 write error: write /dev/stdout: The handle is invalid.
2025-07-02 00:21:58.5491862 +0800 CST m=+1.731623501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.072+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
2025-07-02 00:21:59.0723332 +0800 CST m=+2.254770501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.105+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
2025-07-02 00:21:59.1050938 +0800 CST m=+2.287531101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.127+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
2025-07-02 00:21:59.1279616 +0800 CST m=+2.310398901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.160+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
2025-07-02 00:21:59.1600927 +0800 CST m=+2.342530001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.183+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
2025-07-02 00:21:59.1835792 +0800 CST m=+2.366016501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.215+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
2025-07-02 00:21:59.2157957 +0800 CST m=+2.398233001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.239+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
2025-07-02 00:21:59.2391406 +0800 CST m=+2.421577901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.271+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
2025-07-02 00:21:59.2719283 +0800 CST m=+2.454365601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.287+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:59.2879161 +0800 CST m=+2.470353401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.294+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
2025-07-02 00:21:59.2944157 +0800 CST m=+2.476853001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.327+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
2025-07-02 00:21:59.3275028 +0800 CST m=+2.509940101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.350+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
2025-07-02 00:21:59.3503385 +0800 CST m=+2.532775801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.384+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:59.3841352 +0800 CST m=+2.566572501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.405+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
2025-07-02 00:21:59.4059852 +0800 CST m=+2.588422501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.461+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
2025-07-02 00:21:59.4615824 +0800 CST m=+2.644019701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.494+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
2025-07-02 00:21:59.4943371 +0800 CST m=+2.676774401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.517+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
2025-07-02 00:21:59.5171102 +0800 CST m=+2.699547501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.652+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:59.6529508 +0800 CST m=+2.835388101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:21:59.916+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025-07-02 00:21:59.9164308 +0800 CST m=+3.098868101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:26:58.249+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
2025-07-02 00:26:58.2496583 +0800 CST m=+301.432095601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:31:58.249+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
2025-07-02 00:31:58.2494008 +0800 CST m=+601.431838101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.480+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
2025-07-02 00:32:09.4801039 +0800 CST m=+612.662541201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
2025-07-02 00:32:09.5486354 +0800 CST m=+612.731072701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.569+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
2025-07-02 00:32:09.5697538 +0800 CST m=+612.752191101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.601+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
2025-07-02 00:32:09.6015138 +0800 CST m=+612.783951101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.625+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
2025-07-02 00:32:09.6253578 +0800 CST m=+612.807795101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.657+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
2025-07-02 00:32:09.657126 +0800 CST m=+612.839563301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.704+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
2025-07-02 00:32:09.7042779 +0800 CST m=+612.886715201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.734+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
2025-07-02 00:32:09.7347434 +0800 CST m=+612.917180701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:09.758+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
2025-07-02 00:32:09.758856 +0800 CST m=+612.941293301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-02T00:32:30.725+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.726+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":58268}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.747+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.748+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.748+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.748+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.748+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.756+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.756+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.756+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.756+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T00:32:30.756+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.550+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.551+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":52328}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.571+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.572+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.572+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.572+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.572+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.580+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.580+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.580+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.580+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T00:32:36.580+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.476+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-02T00:21:57.081+0800"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"37024"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":37024}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":37024}
{"level":"WARN","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":37024,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":37024}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.477+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.478+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.478+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":53472}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.528+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.528+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.528+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.528+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.528+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.985+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.986+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.987+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.987+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.987+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.987+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.994+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.997+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.998+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T00:32:39.999+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.000+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.000+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.000+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.000+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.571+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.634+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.643+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.643+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.643+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.658+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.662+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.662+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T00:32:40.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T00:32:56.635+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T00:32:56.635+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T00:32:56.635+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.125+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.125+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.125+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.125+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.125+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.125+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T00:32:58.126+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.216+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.264+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.264+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.264+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.264+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.264+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.287+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.287+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.287+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.287+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T00:33:04.288+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.826+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.827+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":20792}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.828+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.828+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.829+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.829+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.829+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.836+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.836+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.836+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.836+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T06:58:25.836+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.447+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.447+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.447+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.447+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.447+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.447+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.447+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.447+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":24220}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.452+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.452+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.452+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.452+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.452+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.460+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.460+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.460+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.460+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T10:34:06.460+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.074+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.075+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.075+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.075+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.075+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.075+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.075+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.075+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":23884}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.077+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.077+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.077+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.077+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.077+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.085+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.085+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.085+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.085+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T10:34:19.085+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.964+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.964+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.964+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.964+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.964+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.964+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.964+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.964+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":19900}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.966+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.966+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.966+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.966+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.966+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.994+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.994+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.994+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.994+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T10:34:22.994+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.727+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.728+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.728+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.728+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.728+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.728+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.728+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.728+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":9384}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.780+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.781+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.781+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.781+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:26.781+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.815+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.815+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.816+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.817+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.817+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.817+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.824+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.826+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.826+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.826+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.826+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.827+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.827+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.827+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.827+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.827+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.827+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.827+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.827+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.827+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.827+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.828+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.828+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.828+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.828+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.828+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.828+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.828+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.828+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.828+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.828+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.829+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.829+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.829+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.829+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.829+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.830+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.830+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.830+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:27.830+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:28.362+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T10:34:28.429+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T10:34:28.436+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T10:34:28.436+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T10:34:28.436+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T10:34:28.445+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T10:34:28.449+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T10:34:28.449+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T10:34:28.453+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.166+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.166+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.166+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.166+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.166+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.167+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.167+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.167+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.167+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.167+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.167+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.167+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.167+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.782+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.782+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:34:31.782+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:34:34.350+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:34:34.350+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T10:34:34.358+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:34:36.046+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:34:37.821+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:39:27.829+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T10:44:27.829+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T10:49:27.829+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T10:50:07.495+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T10:50:07.495+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-02T10:50:07.496+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-02T10:50:07.496+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-02T10:50:07.496+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:08.936+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":1,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-02T10:50:08.936+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T10:50:08.936+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_B_1751424608936434900_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":1,"timeout":35}
{"level":"INFO","timestamp":"2025-07-02T10:50:08.936+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_B_1751424608936434900_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-02T10:50:08.936+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751424608936434900_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":1}
{"level":"INFO","timestamp":"2025-07-02T10:50:08.936+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_B_1751424608936434900_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T10:50:08.936+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751424608936434900_test-11200, User: test-11200, Round: 1"}
{"level":"INFO","timestamp":"2025-07-02T10:50:08.936+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_B_1751424608936434900_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-02T10:50:08.937+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T10:50:08.937+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-02T10:50:08.937+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:09.924+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-02T10:50:09.924+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:10.659+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:10.828+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:10.828+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-02T10:50:11.334+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:11.689+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:14.981+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T10:50:14.981+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-02T10:50:14.982+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-02T10:50:14.982+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-02T10:50:14.982+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.232+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":1,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.232+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.232+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_C_1751424615232634900_test-11200","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":1,"timeout":40}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.232+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_C_1751424615232634900_test-11200","taskType":"screenshot_C","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.232+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751424615232634900_test-11200","taskType":"screenshot_C","userName":"test-11200","mode":"C","roundNumber":1}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.233+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751424615232634900_test-11200, User: test-11200, Round: 1"}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.233+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_C_1751424615232634900_test-11200","taskType":"screenshot_C","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.233+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_C_1751424615232634900_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.233+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.233+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-C","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.233+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.442+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式C] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.442+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.674+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.674+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:15.674+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: C, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-02T10:50:16.060+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:16.260+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:50:40.084+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 胃后壁, 键值对数量: 25, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250702_105011.png\n"}
{"level":"INFO","timestamp":"2025-07-02T10:50:40.343+0800","caller":"utils/logger.go:94","msg":"颜色检测成功完成","tempFilePath":"pic\\temp\\temp_screenshot_B_test-11200_20250702_105011.png"}
{"level":"INFO","timestamp":"2025-07-02T10:50:40.343+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"B"}
{"level":"INFO","timestamp":"2025-07-02T10:50:40.346+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-02T10:50:40.346+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751424640346588200"}
{"level":"INFO","timestamp":"2025-07-02T10:50:40.346+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250702_105011.png","organName":"胃后壁","operationID":"test-11200_B_1751424640346588200"}
{"level":"INFO","timestamp":"2025-07-02T10:50:40.346+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751424640346588200","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-02T10:50:40.346+0800","caller":"utils/logger.go:94","msg":"创建轮次数据","轮次数":1,"operationID":"test-11200_B_1751424640346588200"}
{"level":"INFO","timestamp":"2025-07-02T10:50:40.346+0800","caller":"utils/logger.go:94","msg":"设置器官名称","器官":"胃后壁","operationID":"test-11200_B_1751424640346588200"}
{"level":"INFO","timestamp":"2025-07-02T10:50:40.349+0800","caller":"utils/logger.go:94","msg":"开始计算已完成轮次数","operationID":"test-11200_B_1751424640346588200"}
{"level":"INFO","timestamp":"2025-07-02T10:50:40.349+0800","caller":"utils/logger.go:94","msg":"用户数据更新","userName":"test-11200","mode":"B","organName":"胃后壁","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751424640346588200"}
{"level":"INFO","timestamp":"2025-07-02T10:50:40.349+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751424640346588200"}
{"level":"INFO","timestamp":"2025-07-02T10:51:00.076+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 胃后壁, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_test-11200_20250702_105015.png\n"}
{"level":"INFO","timestamp":"2025-07-02T10:51:00.077+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"C"}
{"level":"INFO","timestamp":"2025-07-02T10:51:00.079+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-02T10:51:00.079+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_C_1751424660079060300"}
{"level":"INFO","timestamp":"2025-07-02T10:51:00.079+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"C","imagePath":"pic\\temp\\temp_screenshot_C_test-11200_20250702_105015.png","organName":"胃后壁","operationID":"test-11200_C_1751424660079060300"}
{"level":"INFO","timestamp":"2025-07-02T10:52:13.811+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T10:52:13.811+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-02T10:52:13.811+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-02T10:52:13.812+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-02T10:52:13.812+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:52:14.120+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":1,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-02T10:52:14.120+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-02T10:52:14.120+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 1 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:411\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-02T10:52:14.121+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-02T10:52:16.233+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T10:52:16.233+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-02T10:52:16.233+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-02T10:52:16.233+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-02T10:52:16.234+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:52:16.544+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":1,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-02T10:52:16.544+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-02T10:52:16.544+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 1 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:411\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-02T10:52:16.544+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-02T10:52:39.686+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T10:52:39.686+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-02T10:52:39.686+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-02T10:52:39.687+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-02T10:52:39.687+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:52:39.940+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":1,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-02T10:52:39.940+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-02T10:52:39.940+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 1 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:411\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-02T10:52:39.941+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-02T10:52:42.458+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T10:52:42.458+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-02T10:52:42.458+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-02T10:52:42.458+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-02T10:52:42.458+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:52:42.708+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":1,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-02T10:52:42.708+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-02T10:52:42.709+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 1 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:411\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-02T10:52:42.709+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-02T10:54:27.829+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.583+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.583+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.583+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.583+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.583+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.583+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.583+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.584+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":25516}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.602+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.603+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.603+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.603+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.603+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.624+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.624+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.624+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.624+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.624+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.799+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.801+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.803+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.809+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.809+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.809+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.809+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.809+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.810+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.810+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.810+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.810+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.814+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.814+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:33.817+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:34.802+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:34.803+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:34.803+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:34.804+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:34.804+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:34.804+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:34.804+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:34.804+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:34.804+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.441+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.441+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.441+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.441+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.441+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.442+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":4,"modified":"2025-07-02T10:34:26.729+0800"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.442+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"9384"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.442+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":9384}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.442+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":9384}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.442+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.443+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 9384\" /NH"}
{"level":"WARN","timestamp":"2025-07-02T10:56:35.464+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.464+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":9384}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.464+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.464+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.464+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":23176}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.575+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.585+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.585+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.585+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.585+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.973+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.974+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.975+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.975+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.975+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.975+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.981+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.982+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.982+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.982+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.982+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.982+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.982+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.982+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.982+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.983+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.985+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.986+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.986+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.986+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.986+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.986+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.986+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.986+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.986+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.986+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.986+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.986+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.987+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.987+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.987+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:35.987+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.124+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.187+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.194+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.194+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.194+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.202+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.206+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.206+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.210+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.438+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.438+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.438+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.732+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.732+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.737+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.759+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.759+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.759+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.759+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.759+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.760+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.760+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.760+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.760+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.760+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.760+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.760+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.761+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T10:56:36.990+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:56:37.382+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T10:57:11.139+0800","caller":"utils/logger.go:94","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-07-02T10:57:11.139+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T10:57:11.139+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T10:57:11.139+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T10:57:11.140+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T10:57:11.140+0800","caller":"utils/logger.go:94","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.785+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.785+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.785+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.785+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.785+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.785+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.785+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.790+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":5448}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.802+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.802+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.802+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.802+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.802+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.810+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.810+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.810+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.810+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:00:25.811+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.781+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.781+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.781+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.781+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.782+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.782+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.782+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.782+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":18756}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.784+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.784+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.784+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.784+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.784+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.792+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.792+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.792+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.792+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:00:29.792+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.423+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.424+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.424+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.424+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.424+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.424+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.424+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.424+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":11680}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.470+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.479+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.479+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.479+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.479+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.834+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.834+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.835+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.835+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.835+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.835+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.841+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.843+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.843+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.843+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.843+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.843+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.843+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.843+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.843+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.843+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.843+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.843+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.844+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.844+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.844+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.844+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.844+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.844+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.844+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.845+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.846+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.847+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.848+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.848+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.848+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:31.848+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.323+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.384+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.395+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.395+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.395+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.406+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.411+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.412+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.420+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.969+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.969+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.969+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.969+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.969+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.969+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.970+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.970+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.970+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.970+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.970+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.970+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:32.970+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:00:33.505+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:00:33.505+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:00:33.505+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:00:34.279+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:00:34.279+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:00:34.286+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:00:35.804+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:00:38.018+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:05:31.847+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T11:06:21.175+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:21.179+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T11:06:21.179+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-02T11:06:21.179+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-02T11:06:21.179+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-02T11:06:21.179+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T11:06:21.179+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-02T11:06:21.208+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:06:21.209+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:06:21.209+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:21.209+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:06:21.209+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.802+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.803+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.803+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.803+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.803+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.803+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.803+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.803+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22400}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.814+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.823+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.823+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.823+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.823+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.830+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.830+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.830+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.830+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:06:27.830+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.828+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.828+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.828+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.828+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.828+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.828+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.828+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.829+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":9088}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.844+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.848+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.848+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.848+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.848+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.856+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.856+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.856+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.856+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:06:31.856+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.268+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.269+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.269+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.269+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.269+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.269+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.269+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.270+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":17336}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.393+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.393+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.393+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.393+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.393+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.728+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.729+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.730+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.730+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.730+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.730+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.735+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.737+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.737+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.737+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.737+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.737+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.738+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.738+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.738+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.738+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.738+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.738+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.738+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.738+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.738+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.738+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.738+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.739+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.739+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.739+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.739+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.739+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.739+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.739+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.739+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.739+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.739+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.739+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.739+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.740+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.740+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.740+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.740+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.740+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.740+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.740+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.740+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.740+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.740+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.741+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.741+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.741+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.741+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.741+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.741+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.741+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.741+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.741+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.741+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.741+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.741+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.742+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.742+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.742+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.742+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.742+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.742+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.742+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.742+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.742+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.742+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.742+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.742+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.743+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.743+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.743+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.743+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:33.743+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.220+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.280+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.287+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.287+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.287+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.295+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.300+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.301+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.305+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.388+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.389+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.389+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.389+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.389+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.389+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.389+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.389+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.389+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.389+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.389+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.389+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.390+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.980+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.980+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:06:34.980+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:06:35.643+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:06:35.643+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:06:35.649+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:06:35.944+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:06:36.908+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:11:33.743+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T11:11:36.726+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T11:11:36.749+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T11:11:36.749+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-02T11:11:36.749+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-02T11:11:36.749+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-02T11:11:36.749+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T11:11:36.749+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-02T11:11:36.771+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:11:36.771+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:11:36.771+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:11:36.771+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:11:36.771+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.806+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.806+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.806+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.806+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.806+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.806+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.806+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.806+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":18756}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.808+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.808+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.808+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.808+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.808+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.816+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.816+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.816+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.816+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:14:06.816+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.333+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.333+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.333+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.333+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.333+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.333+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.333+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.334+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":19608}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.356+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.356+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.356+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.356+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.356+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.364+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.364+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.364+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.364+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:14:11.364+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.879+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.880+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.880+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.880+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.880+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.880+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.880+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.881+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":20692}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.979+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.979+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.979+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.979+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:12.979+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.306+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.306+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.307+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.307+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.307+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.307+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.312+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.314+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.314+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.314+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.314+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.314+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.314+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.314+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.314+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.314+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.314+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.315+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.316+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.317+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.317+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.317+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.317+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.317+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.317+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.317+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.317+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.318+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.319+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.319+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.319+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.319+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.784+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.843+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.850+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.851+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.851+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.858+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.862+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.862+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T11:14:13.867+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.036+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.036+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.036+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.036+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.036+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.036+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.036+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.037+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.037+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.037+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.037+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.037+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.037+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:14:14.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:14:15.217+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:14:15.217+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:14:15.223+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:14:15.515+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:14:17.021+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:19:13.318+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T11:21:31.376+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:21:31.378+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:21:31.378+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:21:31.378+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:21:31.380+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:32.108+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:32.750+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:32.952+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:33.167+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:33.168+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:21:33.174+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:33.406+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:33.684+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:39.464+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:39.468+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T11:21:39.468+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-02T11:21:39.468+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-02T11:21:39.468+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-02T11:21:39.468+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T11:21:39.468+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-02T11:21:39.487+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:21:39.487+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:21:39.488+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:39.488+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:21:39.488+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.747+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.747+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.747+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.747+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.747+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.747+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.747+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.747+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":12492}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.756+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.756+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.756+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.756+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.756+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.764+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.764+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.764+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.764+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:21:43.764+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.804+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.804+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.804+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.804+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.804+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.804+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.804+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.805+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":14620}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.839+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.840+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.840+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.840+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.840+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.853+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.853+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.853+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.853+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:21:47.853+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.387+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.387+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.387+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.387+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.388+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.388+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.388+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.388+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":15908}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.457+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.458+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.458+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.458+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.459+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.813+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.813+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.814+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.814+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.814+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.815+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.819+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.821+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.821+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.821+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.821+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.821+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.821+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.821+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.822+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.822+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.822+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.822+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.822+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.822+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.822+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.822+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.822+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.822+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.822+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.823+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.823+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.823+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.823+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.823+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.823+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.823+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.823+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.823+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.823+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.823+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.823+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.824+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.824+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.824+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.824+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.824+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.824+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.824+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.824+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.824+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.824+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.824+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.824+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.825+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.825+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.825+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.825+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.825+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.825+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.825+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.825+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.825+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.825+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.825+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.825+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.826+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.826+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.826+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.826+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.826+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.826+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.826+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.826+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.826+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.827+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.827+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.827+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:49.827+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.302+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.363+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.370+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.370+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.371+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.381+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.385+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.385+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.388+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.584+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.585+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.585+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.585+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.585+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.585+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.585+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.585+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.585+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.585+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.585+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.585+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.586+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.666+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.666+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.666+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.967+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.967+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:21:50.973+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:51.168+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:21:51.505+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:23:08.454+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:23:08.457+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:23:08.457+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:23:08.457+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:23:08.459+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:23:08.842+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:23:08.842+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:23:08.842+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:23:09.150+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:23:09.150+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:23:09.153+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:23:09.430+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:23:09.649+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:26:49.827+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T11:28:32.111+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:32.127+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T11:28:32.127+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-02T11:28:32.127+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-02T11:28:32.127+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-02T11:28:32.127+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T11:28:32.127+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-02T11:28:32.147+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:28:32.147+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:28:32.147+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:32.147+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:28:32.147+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.824+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.827+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.827+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.827+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.827+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.827+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.827+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.827+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":17252}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.831+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.831+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.831+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.832+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.832+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.839+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.839+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.839+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.839+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:28:36.839+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.871+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.872+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.872+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.872+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.872+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.872+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.872+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.872+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":17920}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.877+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.877+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.877+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.877+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.877+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.885+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.885+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.885+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.885+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:28:40.885+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.290+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.291+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.291+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.291+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.291+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.291+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.291+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.291+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":16412}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.416+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.416+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.416+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.416+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.416+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.752+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.752+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.753+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.753+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.753+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.753+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.758+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.760+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.760+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.760+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.760+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.760+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.760+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.760+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.761+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.761+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.761+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.761+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.761+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.761+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.761+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.762+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.763+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.764+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.764+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.764+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.764+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.764+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.764+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.764+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.764+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.764+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.764+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.764+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.764+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.765+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.765+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.765+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.765+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:42.766+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.238+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.302+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.310+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.310+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.310+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.319+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.324+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.325+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.333+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.514+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.514+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.514+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.514+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.515+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.515+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.515+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.515+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.515+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.515+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.515+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.515+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:43.515+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:28:44.163+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:28:44.164+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:28:44.164+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:28:44.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:28:44.894+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:28:44.900+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:28:46.347+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:28:48.331+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:33:42.765+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T11:38:42.765+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T11:40:01.943+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:40:01.947+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:40:01.947+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:40:01.947+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:40:01.949+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:40:02.698+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:40:02.698+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:40:02.698+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:40:03.516+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:40:03.516+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:40:03.519+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:40:04.131+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:40:04.472+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:43:42.765+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T11:48:42.765+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T11:53:42.765+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T11:57:34.790+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:57:34.794+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:57:34.794+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:57:34.794+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:57:34.798+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:57:35.954+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:57:35.954+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:57:35.954+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:57:37.598+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:57:37.599+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:57:37.601+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:57:42.232+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:58:00.784+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:58:42.765+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.701+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.701+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.701+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.701+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.701+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.701+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.701+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.701+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":10156}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.722+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.723+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.723+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.723+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.723+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.749+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.749+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.749+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.749+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.749+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.832+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.834+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.836+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.840+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.840+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.840+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.840+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.840+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.841+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.842+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.843+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.844+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.846+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.850+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:16.852+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:17.366+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:17.366+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:17.366+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.346+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.346+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.346+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.742+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.743+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.743+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.743+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.743+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.744+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-02T11:28:42.292+0800"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.744+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"16412"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.744+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":16412}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.744+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":16412}
{"level":"WARN","timestamp":"2025-07-02T11:59:18.744+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":16412,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.744+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":16412}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.745+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.745+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.745+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":23548}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.806+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.807+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.807+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.807+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:18.807+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.173+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.173+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.174+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.174+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.174+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.174+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.180+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.181+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.181+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.181+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.181+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.182+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.182+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.182+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.182+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.182+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.182+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.182+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.182+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.182+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.182+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.183+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.183+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.183+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.183+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.183+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.183+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.184+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.185+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.185+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.185+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.185+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.185+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.185+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.185+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.185+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.185+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.185+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.185+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.185+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.186+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.187+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.187+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.187+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.187+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.187+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.187+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.187+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.319+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.380+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.397+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.397+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.398+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.410+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.418+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.419+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T11:59:19.428+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.022+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.022+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.022+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.022+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.022+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.022+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.023+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.023+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.023+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.023+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.023+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.023+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:21.023+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T11:59:23.877+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:23.877+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:23.878+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:26.291+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:26.291+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T11:59:26.299+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:27.018+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T11:59:28.157+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T12:04:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T12:09:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T12:14:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T12:19:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T12:24:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T12:29:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T12:34:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T12:39:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T12:44:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T12:49:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T12:54:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T12:59:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T13:04:19.187+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T13:08:17.833+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T13:08:17.838+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T13:08:17.838+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T13:08:17.839+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T13:08:17.845+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:08:19.151+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:08:19.151+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:08:19.151+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:08:20.860+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:08:20.872+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T13:08:20.874+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:08:21.704+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:08:23.606+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:08:25.030+0800","caller":"utils/logger.go:94","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-07-02T13:08:25.030+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T13:08:25.030+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T13:08:25.030+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T13:08:25.030+0800","caller":"utils/logger.go:94","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.032+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.032+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.032+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.032+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.032+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.032+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.032+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.032+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":18240}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.052+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.052+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.052+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.052+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.052+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.060+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.060+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.060+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.060+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T13:57:54.060+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.860+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.860+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.860+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.860+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.860+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.860+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.860+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.860+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":30056}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.882+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.882+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.882+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.882+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.882+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.905+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.905+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.905+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.905+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T13:57:58.906+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.557+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.558+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.558+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.558+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.558+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.558+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.558+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.558+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":2980}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.618+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.618+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.618+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.618+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:01.619+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.639+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.640+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.641+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.641+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.641+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.641+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.649+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.651+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.651+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.651+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.651+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.652+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.652+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.652+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.652+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.652+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.652+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.652+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.652+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.653+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.653+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.653+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.653+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.653+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.653+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.653+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.653+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.653+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.653+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.654+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.654+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.654+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.654+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.654+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.654+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.654+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.654+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.654+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.654+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.654+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.654+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.655+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.655+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.655+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.655+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.655+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.655+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.655+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.655+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.655+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.655+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.655+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.655+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.656+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.656+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.656+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.656+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.656+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.656+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.656+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.656+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.656+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.656+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.657+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.657+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.657+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.657+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.657+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.657+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.657+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.657+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.658+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.658+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:02.658+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:03.258+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T13:58:03.326+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T13:58:03.342+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T13:58:03.342+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T13:58:03.343+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T13:58:03.358+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T13:58:03.358+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:58:03.366+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T13:58:03.366+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.228+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.228+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.229+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.229+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.229+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.229+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.229+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.229+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.229+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.229+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.229+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.229+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.230+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.273+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.273+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:58:08.274+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:58:14.607+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:58:14.608+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T13:58:14.613+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:58:17.395+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T13:58:19.888+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:02:00.460+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:02:00.460+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:03:02.657+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T14:05:39.245+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:05:39.248+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:05:39.248+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:05:39.248+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:05:39.250+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:05:45.456+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:05:45.456+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:05:45.456+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:05:51.726+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:05:51.726+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:05:51.728+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:05:56.723+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:06:01.884+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:08:02.657+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T14:13:02.657+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T14:18:02.657+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T14:23:02.657+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T14:28:02.657+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T14:28:50.585+0800","caller":"utils/logger.go:94","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-07-02T14:28:50.585+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T14:28:50.585+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:28:50.585+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:28:50.585+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:28:50.586+0800","caller":"utils/logger.go:94","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.874+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.874+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.874+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.874+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.874+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.874+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.874+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.874+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":26328}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.893+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.893+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.893+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.893+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.893+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.901+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.901+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.901+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.901+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:28:55.901+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.007+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.007+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.007+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.007+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.007+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.008+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.008+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.008+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":29368}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.012+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.012+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.012+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.012+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.012+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.020+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.020+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.020+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.021+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:29:00.021+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.580+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.581+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.581+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.581+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.581+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.581+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.582+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.582+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":4732}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.615+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.615+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.615+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.615+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.615+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.997+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.998+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.999+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.999+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.999+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:01.999+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.005+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.007+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.007+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.007+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.007+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.007+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.007+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.007+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.007+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.008+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.009+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.010+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.011+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.011+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.011+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.011+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.011+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.011+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.011+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.011+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.011+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.011+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.011+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.011+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.012+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.012+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.012+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.012+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.013+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.496+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.556+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.566+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.566+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.566+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.576+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.580+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.580+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T14:29:02.584+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.271+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.272+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.272+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.272+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.272+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.272+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.272+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.272+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.272+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.272+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.272+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.272+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:06.273+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T14:29:07.544+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:29:07.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:29:07.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:29:15.749+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:29:15.749+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:29:15.756+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:29:18.969+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:29:21.646+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:34:02.012+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T14:37:21.859+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:37:21.863+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:37:21.863+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:37:21.863+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:37:21.866+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:37:25.414+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:37:25.414+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:37:25.414+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:37:28.414+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:37:28.414+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:37:28.418+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:37:31.117+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:37:33.985+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:38:53.360+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:38:53.362+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:38:53.362+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:38:53.362+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:38:53.364+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:38:58.598+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:38:58.598+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:38:58.598+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:39:02.012+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T14:39:04.891+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:39:04.891+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:39:04.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:39:11.666+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:39:15.343+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:44:02.012+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.098+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.098+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.098+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.098+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.098+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.098+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.098+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.099+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":32724}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.106+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.106+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.106+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.106+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.106+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.132+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.132+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.132+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.132+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:46:13.132+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.219+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.220+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.220+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.220+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.220+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.220+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":4,"modified":"2025-07-02T14:29:01.582+0800"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.220+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"4732"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.220+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":4732}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.220+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":4732}
{"level":"WARN","timestamp":"2025-07-02T14:46:15.221+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":4732,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.221+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":4732}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.221+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.221+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.221+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.295+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.296+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.296+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.296+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.296+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.717+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.717+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.718+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.718+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.718+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.718+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.723+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.725+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.725+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.725+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.725+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.725+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.725+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.725+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.726+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.727+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.729+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.730+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.730+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.730+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.730+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.870+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.932+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.940+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.940+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.941+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.951+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.957+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.957+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T14:46:15.966+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:46:24.002+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:46:24.002+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:46:24.002+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.956+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.957+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.957+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.957+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.957+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.957+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.957+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.957+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22056}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.962+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.962+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.962+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.962+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.962+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.970+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.970+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.970+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.970+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:46:30.970+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.628+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.628+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.628+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.628+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.628+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.628+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.628+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.628+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":26148}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.650+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.650+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.650+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.650+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.650+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.658+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.658+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.658+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.658+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:46:35.658+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.461+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.462+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.462+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.462+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.462+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.462+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-02T14:46:15.222+0800"}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.463+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"32528"}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.463+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.463+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.463+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.463+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 32528\" /NH"}
{"level":"WARN","timestamp":"2025-07-02T14:46:37.492+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.493+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":32528}
{"level":"WARN","timestamp":"2025-07-02T14:46:37.493+0800","caller":"utils/logger.go:101","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.494+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"ERROR","timestamp":"2025-07-02T14:46:37.494+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.595+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.595+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-02T14:46:37.595+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 32528\" /NH"}
{"level":"WARN","timestamp":"2025-07-02T14:46:37.618+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"WARN","timestamp":"2025-07-02T14:46:37.618+0800","caller":"utils/logger.go:101","msg":"单实例检查失败，程序退出"}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.384+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.384+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.384+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.384+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.384+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.384+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.384+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.385+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.385+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.385+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.385+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.385+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T14:47:07.385+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T14:47:12.511+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:47:12.511+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:47:12.518+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:47:15.039+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:47:17.641+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.261+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.261+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.261+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.261+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.261+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.261+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.261+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.261+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":19064}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.266+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.266+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.266+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.266+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.266+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.275+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.275+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.275+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.275+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:49:26.275+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.786+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.787+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.787+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.787+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.787+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.787+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-02T14:46:15.222+0800"}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.787+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"32528"}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.787+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.787+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.787+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.787+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 32528\" /NH"}
{"level":"WARN","timestamp":"2025-07-02T14:49:27.813+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.813+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":32528}
{"level":"WARN","timestamp":"2025-07-02T14:49:27.813+0800","caller":"utils/logger.go:101","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.813+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"ERROR","timestamp":"2025-07-02T14:49:27.813+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.914+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.914+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-02T14:49:27.914+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 32528\" /NH"}
{"level":"WARN","timestamp":"2025-07-02T14:49:27.938+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"WARN","timestamp":"2025-07-02T14:49:27.938+0800","caller":"utils/logger.go:101","msg":"单实例检查失败，程序退出"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.838+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.838+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.838+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.838+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.838+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.838+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.839+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.839+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":31376}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.843+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.843+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.843+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.843+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.843+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.851+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.851+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.851+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.851+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.852+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.941+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.944+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.946+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.950+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.950+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.950+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.950+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.950+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.950+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.951+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.951+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.951+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.957+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.957+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:44.957+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.038+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.038+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.038+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.038+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.038+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.038+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.038+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.039+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":31200}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.043+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.043+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.043+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.043+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.043+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.052+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.052+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.052+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.052+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.052+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.125+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.128+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.130+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.136+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.136+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.136+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.136+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.136+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.138+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.138+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.138+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.140+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.144+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.144+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.145+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.304+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.304+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.304+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.499+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.500+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.500+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.500+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.500+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.500+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-02T14:46:15.222+0800"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.500+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"32528"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.500+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.500+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.501+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.501+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 32528\" /NH"}
{"level":"WARN","timestamp":"2025-07-02T14:49:49.524+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.525+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":32528}
{"level":"WARN","timestamp":"2025-07-02T14:49:49.525+0800","caller":"utils/logger.go:101","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.525+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"ERROR","timestamp":"2025-07-02T14:49:49.525+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.626+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.626+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-02T14:49:49.626+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 32528\" /NH"}
{"level":"WARN","timestamp":"2025-07-02T14:49:49.649+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"WARN","timestamp":"2025-07-02T14:49:49.649+0800","caller":"utils/logger.go:101","msg":"单实例检查失败，程序退出"}
{"level":"INFO","timestamp":"2025-07-02T14:49:50.210+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:50.210+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:50.210+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:50.521+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:50.521+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:50.521+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:52.210+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:52.210+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:52.210+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:53.108+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:53.108+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:53.108+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:53.429+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:53.430+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:49:53.431+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:53.949+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:53.949+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:49:53.949+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:00.383+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:04.664+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:04.934+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:04.934+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:50:04.935+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:05.317+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:05.317+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:50:05.319+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:07.581+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:08.159+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:11.341+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:11.341+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:50:11.344+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:11.809+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:13.700+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:14.233+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:14.233+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:50:14.234+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:15.265+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:16.490+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:19.856+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:20.209+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:25.434+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:25.435+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:50:25.436+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:29.955+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:50:37.048+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:51:15.729+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T14:56:15.730+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.328+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.328+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.328+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.328+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.328+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.328+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.328+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.328+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":20500}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.349+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.350+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.350+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.350+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.350+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.361+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.361+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.361+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.361+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.361+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.489+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.493+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.494+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.501+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.501+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.501+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.502+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.502+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.502+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.502+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.507+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.508+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.519+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.520+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:50.521+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.014+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.015+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.015+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.015+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.015+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.015+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-02T14:46:15.222+0800"}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.015+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"32528"}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.016+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.016+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.016+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.016+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 32528\" /NH"}
{"level":"WARN","timestamp":"2025-07-02T14:57:52.037+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.037+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":32528}
{"level":"WARN","timestamp":"2025-07-02T14:57:52.037+0800","caller":"utils/logger.go:101","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.038+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"ERROR","timestamp":"2025-07-02T14:57:52.038+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.138+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.138+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-02T14:57:52.139+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 32528\" /NH"}
{"level":"WARN","timestamp":"2025-07-02T14:57:52.160+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"WARN","timestamp":"2025-07-02T14:57:52.160+0800","caller":"utils/logger.go:101","msg":"单实例检查失败，程序退出"}
{"level":"INFO","timestamp":"2025-07-02T14:57:53.778+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:53.778+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:53.778+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:54.233+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:54.233+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:54.233+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:55.616+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:55.616+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:55.616+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:58.287+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:58.287+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:57:58.289+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:59.107+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:57:59.107+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:57:59.108+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:58:00.126+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:58:00.126+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T14:58:00.127+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:58:02.267+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:58:02.351+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:58:03.321+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:58:05.170+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:58:08.154+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:58:16.850+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.038+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.038+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.038+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.038+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.038+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.038+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.038+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.038+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":34652}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.059+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.060+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.060+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.060+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.060+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.068+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.068+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.068+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.068+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.068+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.153+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.154+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.155+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.158+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.158+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.158+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.158+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.158+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.158+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.158+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.159+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.159+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.162+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.162+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:59:15.162+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.286+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.287+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.287+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.287+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.287+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.287+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.287+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.287+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":34040}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.306+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.306+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.306+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.306+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.306+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.315+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.315+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.315+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.315+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.315+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.800+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.800+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:59:19.800+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.950+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.951+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.951+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.951+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.951+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.951+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-02T14:46:15.222+0800"}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.951+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"32528"}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.951+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.951+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.952+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.952+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 32528\" /NH"}
{"level":"WARN","timestamp":"2025-07-02T14:59:20.973+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.973+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":32528}
{"level":"WARN","timestamp":"2025-07-02T14:59:20.973+0800","caller":"utils/logger.go:101","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
{"level":"INFO","timestamp":"2025-07-02T14:59:20.974+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"ERROR","timestamp":"2025-07-02T14:59:20.974+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-07-02T14:59:21.075+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":32528}
{"level":"INFO","timestamp":"2025-07-02T14:59:21.075+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-02T14:59:21.075+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 32528\" /NH"}
{"level":"WARN","timestamp":"2025-07-02T14:59:21.096+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"WARN","timestamp":"2025-07-02T14:59:21.096+0800","caller":"utils/logger.go:101","msg":"单实例检查失败，程序退出"}
{"level":"INFO","timestamp":"2025-07-02T14:59:30.233+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:59:30.233+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:59:30.233+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T14:59:47.210+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:47.213+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T14:59:47.213+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-02T14:59:47.214+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-02T14:59:47.214+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-02T14:59:47.214+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T14:59:47.214+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-02T14:59:47.250+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T14:59:47.250+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T14:59:47.250+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T14:59:47.250+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T14:59:47.251+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.572+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.573+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.573+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.573+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.573+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.573+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.573+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.573+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":31072}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.594+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.595+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.595+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.595+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.595+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.605+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.605+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.605+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.605+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T15:00:13.605+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.243+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.244+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.244+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.244+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.244+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.245+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.245+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.245+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":19992}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.311+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.312+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.312+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.312+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.312+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.719+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.719+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.720+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.720+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.720+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.721+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.727+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.729+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.729+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.729+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.729+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.729+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.729+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.729+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.730+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.730+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.730+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.730+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.730+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.730+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.730+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.730+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.730+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.730+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.730+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.730+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.731+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.731+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.731+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.731+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.731+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.731+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.731+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.731+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.731+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.731+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.731+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.734+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.734+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.734+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.735+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T15:00:15.735+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T15:00:16.273+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T15:00:16.346+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T15:00:16.380+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T15:00:16.381+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T15:00:16.381+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T15:00:16.412+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T15:00:16.487+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:00:16.490+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T15:00:16.490+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T15:00:58.520+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:00:58.520+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:00:58.521+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.528+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.528+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.528+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.528+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.528+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.528+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.529+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.529+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.529+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.529+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.529+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.529+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:01:00.529+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:01:13.261+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:01:13.262+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T15:01:13.268+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:01:17.839+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:01:40.363+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:05:15.734+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T15:10:15.735+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T15:14:58.787+0800","caller":"utils/logger.go:94","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-07-02T15:14:58.787+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T15:14:58.787+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:14:58.787+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T15:14:58.788+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:14:58.788+0800","caller":"utils/logger.go:94","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.468+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.468+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.469+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.469+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.469+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.469+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.469+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.469+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":19380}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.473+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.473+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.473+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.473+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.473+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.481+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.481+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.481+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.481+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T15:37:56.481+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.575+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.575+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.575+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.575+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.575+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.575+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.575+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.576+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":9328}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.597+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.597+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.597+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.597+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.597+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.605+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.605+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.605+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.605+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T15:38:01.606+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.209+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.209+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.209+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.209+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.210+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.210+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.210+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.210+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":33684}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.284+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.285+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.285+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.285+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:03.286+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.193+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.193+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.194+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.194+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.194+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.194+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.200+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.201+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.201+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.202+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.202+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.202+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.202+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.202+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.202+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.202+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.202+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.202+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.203+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.203+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.203+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.203+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.203+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.203+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.203+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.203+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.203+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.203+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.204+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.205+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.206+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.207+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.208+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.208+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.208+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.208+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.675+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.734+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.745+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.745+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.745+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.754+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.759+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.760+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T15:38:04.763+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.502+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.502+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.502+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.502+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.502+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.502+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.503+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.503+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.503+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.503+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.503+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.503+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.503+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.698+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.698+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:38:07.698+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:38:11.637+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:38:11.637+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T15:38:11.644+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:38:18.488+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:38:25.646+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:41:50.861+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:41:50.861+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:42:18.168+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:42:18.168+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:43:04.207+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T15:48:04.207+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T15:51:31.491+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:51:31.491+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:51:55.960+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:51:55.960+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:52:24.045+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:24.094+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T15:52:24.094+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-02T15:52:24.094+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-02T15:52:24.094+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-02T15:52:24.094+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T15:52:24.094+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-02T15:52:24.127+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T15:52:24.127+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T15:52:24.127+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:24.128+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T15:52:24.128+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.730+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.731+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.731+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.731+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.731+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.731+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.731+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.731+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":29244}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.752+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.753+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.753+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.753+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.753+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.761+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.761+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.761+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.761+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T15:52:28.761+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.719+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.720+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.720+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.720+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.720+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.720+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.720+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.720+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":30624}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.738+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.739+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.739+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.739+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.739+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.747+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.747+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.747+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.747+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T15:52:32.747+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.217+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.218+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.218+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.218+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.218+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.218+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.218+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.218+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":31152}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.346+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.346+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.346+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.346+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.346+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.720+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.721+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.721+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.721+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.722+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.722+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.726+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.728+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.729+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.729+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.729+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.729+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.729+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.729+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.729+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.730+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.730+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.730+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.730+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.730+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.730+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.730+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.730+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.730+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.730+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.730+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.731+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.731+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.731+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.731+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.731+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.732+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.732+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.734+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.735+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.735+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.735+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:34.735+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:35.238+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T15:52:35.300+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T15:52:35.310+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T15:52:35.310+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T15:52:35.310+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T15:52:35.318+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T15:52:35.325+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T15:52:35.325+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T15:52:35.336+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:52:36.759+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:52:36.760+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:52:36.760+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.120+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.120+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.120+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.120+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.120+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.120+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.121+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.121+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.121+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.121+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.121+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.121+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.121+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.922+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.922+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T15:52:37.931+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:52:41.297+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:52:43.499+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:52:46.980+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:52:46.980+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:53:57.970+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T15:53:57.970+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-02T15:53:57.971+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-02T15:53:57.971+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-02T15:53:57.971+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:53:58.415+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":1,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-02T15:53:58.415+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T15:53:58.415+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_B_1751442838415752000_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":1,"timeout":35}
{"level":"INFO","timestamp":"2025-07-02T15:53:58.415+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_B_1751442838415752000_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-02T15:53:58.415+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751442838415752000_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":1}
{"level":"INFO","timestamp":"2025-07-02T15:53:58.415+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751442838415752000_test-11200, User: test-11200, Round: 1"}
{"level":"INFO","timestamp":"2025-07-02T15:53:58.415+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_B_1751442838415752000_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T15:53:58.416+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-02T15:53:58.416+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_B_1751442838415752000_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-02T15:53:58.416+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T15:53:58.416+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:53:59.655+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-02T15:53:59.656+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:54:00.581+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:54:00.581+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:54:00.583+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-02T15:54:01.307+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:54:02.181+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T15:54:34.745+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 40, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250702_155400.png\n"}
{"level":"INFO","timestamp":"2025-07-02T15:54:35.027+0800","caller":"utils/logger.go:94","msg":"颜色检测成功完成","tempFilePath":"pic\\temp\\temp_screenshot_B_test-11200_20250702_155400.png"}
{"level":"INFO","timestamp":"2025-07-02T15:54:35.027+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"B"}
{"level":"INFO","timestamp":"2025-07-02T15:54:35.030+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-02T15:54:35.030+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751442875030312000"}
{"level":"INFO","timestamp":"2025-07-02T15:54:35.030+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250702_155400.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751442875030312000"}
{"level":"INFO","timestamp":"2025-07-02T15:54:35.030+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751442875030312000","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-02T15:54:35.030+0800","caller":"utils/logger.go:94","msg":"创建轮次数据","轮次数":1,"operationID":"test-11200_B_1751442875030312000"}
{"level":"INFO","timestamp":"2025-07-02T15:54:35.030+0800","caller":"utils/logger.go:94","msg":"设置器官名称","器官":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751442875030312000"}
{"level":"INFO","timestamp":"2025-07-02T15:54:35.034+0800","caller":"utils/logger.go:94","msg":"开始计算已完成轮次数","operationID":"test-11200_B_1751442875030312000"}
{"level":"INFO","timestamp":"2025-07-02T15:54:35.034+0800","caller":"utils/logger.go:94","msg":"用户数据更新","userName":"test-11200","mode":"B","organName":"腹部第1腰椎水平截面","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751442875030312000"}
{"level":"INFO","timestamp":"2025-07-02T15:54:35.034+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751442875030312000"}
{"level":"INFO","timestamp":"2025-07-02T15:57:34.734+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T16:02:34.734+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T16:07:34.734+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T16:12:34.734+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T16:17:34.734+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T16:22:34.734+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.632+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.632+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.632+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.633+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.633+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.633+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.633+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.633+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":31440}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.651+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.652+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.652+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.652+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.652+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.663+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.664+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.664+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.664+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T16:23:51.664+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.398+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.398+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.398+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.398+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.398+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.398+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-02T15:52:34.218+0800"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.398+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"31152"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.399+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":31152}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.399+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":31152}
{"level":"WARN","timestamp":"2025-07-02T16:23:53.399+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":31152,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.399+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":31152}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.399+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.399+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.399+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":2800}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.515+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.533+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.533+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.533+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.534+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.938+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.938+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.939+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.939+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.939+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.939+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.945+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.946+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.946+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.947+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.947+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.947+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.947+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.947+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.947+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.947+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.947+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.948+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.948+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.948+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.948+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.948+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.948+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.948+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.948+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.949+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.949+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.955+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.956+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.957+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.957+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.957+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.957+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.957+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.957+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.957+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.957+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.957+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.957+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.957+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.957+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.958+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.958+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.958+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.958+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.958+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.958+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.978+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.978+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.978+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.978+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.978+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.978+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.978+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.978+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.978+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.978+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.978+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.979+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.979+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.980+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:53.980+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.093+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.154+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.163+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.163+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.163+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.173+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.176+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.176+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.189+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.692+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.692+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.692+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.692+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.692+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.692+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.692+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.692+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.693+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.693+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.693+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.693+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.693+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.862+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.862+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:23:54.862+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:23:55.532+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:23:55.532+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T16:23:55.538+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:23:55.787+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:23:56.137+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.430+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.430+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.430+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.430+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.431+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.431+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.431+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.431+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":33528}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.450+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.451+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.451+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.451+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.451+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.459+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.459+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.459+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.459+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.459+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.600+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.605+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.609+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.613+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.613+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.614+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.615+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.616+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.616+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.616+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.616+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.616+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.626+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.626+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:52.626+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.294+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.294+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.295+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.309+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.309+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.309+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.309+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.309+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.309+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.968+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.969+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.969+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.976+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.976+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.977+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.982+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.982+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-02T16:27:53.985+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:54.198+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:54.212+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:54.212+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:54.423+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:54.452+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:27:54.460+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-02","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T16:28:53.979+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T16:32:56.675+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T16:32:56.707+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T16:32:56.708+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-02T16:32:56.708+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-02T16:32:56.708+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-02T16:32:56.708+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T16:32:56.708+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-02T16:32:56.742+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T16:32:56.742+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T16:32:56.742+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T16:32:56.742+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T16:32:56.743+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.706+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.707+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.707+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.707+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.707+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.707+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.707+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.707+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":33432}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.728+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.729+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.729+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.729+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.729+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.737+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.737+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.737+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.737+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T16:38:31.737+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
