{"logId": "8c407c38-3c3d-4e68-98ec-2969d7f90a92", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 8, "label": "table", "score": 0.9869117736816406, "coordinate": [13.163543701171875, 73.89935302734375, 768, 1713.06298828125]}, {"cls_id": 9, "label": "table_title", "score": 0.6508175134658813, "coordinate": [19.698028564453125, 27.225656509399414, 520.6246337890625, 62.94200897216797]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[18, 27], [521, 27], [521, 63], [18, 63]], [[100, 77], [157, 77], [157, 102], [100, 102]], [[190, 75], [423, 77], [423, 102], [190, 100]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 127], [462, 127], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[192, 154], [495, 152], [495, 177], [192, 179]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[194, 181], [423, 181], [423, 204], [194, 204]], [[98, 204], [162, 204], [162, 229], [98, 229]], [[192, 204], [266, 204], [266, 231], [192, 231]], [[98, 229], [161, 229], [161, 256], [98, 256]], [[192, 231], [319, 227], [320, 254], [193, 258]], [[98, 256], [162, 256], [162, 281], [98, 281]], [[194, 259], [554, 259], [554, 277], [194, 277]], [[98, 281], [162, 281], [162, 306], [98, 306]], [[194, 282], [727, 282], [727, 306], [194, 306]], [[98, 306], [161, 306], [161, 332], [98, 332]], [[194, 307], [310, 307], [310, 327], [194, 327]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [426, 336], [426, 354], [196, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[194, 359], [412, 359], [412, 383], [194, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[194, 386], [473, 386], [473, 404], [194, 404]], [[98, 408], [155, 408], [155, 434], [98, 434]], [[194, 409], [443, 409], [443, 433], [194, 433]], [[98, 433], [155, 433], [155, 459], [98, 459]], [[194, 436], [393, 436], [393, 459], [194, 459]], [[98, 458], [155, 458], [155, 484], [98, 484]], [[194, 461], [565, 461], [565, 479], [194, 479]], [[98, 484], [155, 484], [155, 509], [98, 509]], [[192, 486], [399, 486], [399, 509], [192, 509]], [[98, 509], [155, 509], [155, 536], [98, 536]], [[194, 511], [404, 511], [404, 534], [194, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[194, 538], [438, 538], [438, 561], [194, 561]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[191, 559], [298, 563], [297, 588], [190, 584]], [[98, 586], [155, 586], [155, 611], [98, 611]], [[192, 588], [377, 588], [377, 611], [192, 611]], [[98, 611], [153, 611], [153, 638], [98, 638]], [[190, 613], [309, 609], [310, 636], [191, 640]], [[98, 636], [155, 636], [155, 663], [98, 663]], [[190, 636], [310, 634], [310, 661], [190, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[192, 665], [406, 665], [406, 690], [192, 690]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[192, 688], [223, 688], [223, 713], [192, 713]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[192, 715], [386, 715], [386, 740], [192, 740]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[192, 740], [330, 740], [330, 765], [192, 765]], [[98, 765], [155, 765], [155, 790], [98, 790]], [[192, 767], [445, 767], [445, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[190, 790], [279, 790], [279, 817], [190, 817]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [441, 817], [441, 840], [192, 840]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[194, 844], [447, 844], [447, 867], [194, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [687, 869], [687, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[192, 894], [606, 894], [606, 917], [192, 917]], [[98, 917], [157, 917], [157, 944], [98, 944]], [[194, 919], [356, 919], [356, 944], [194, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [458, 946], [458, 969], [194, 969]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[194, 971], [476, 971], [476, 994], [194, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [347, 996], [347, 1019], [192, 1019]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[190, 1019], [244, 1019], [244, 1047], [190, 1047]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[192, 1047], [504, 1047], [504, 1071], [192, 1071]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[190, 1072], [524, 1072], [524, 1096], [190, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[190, 1096], [367, 1096], [367, 1121], [190, 1121]], [[98, 1121], [157, 1121], [157, 1148], [98, 1148]], [[188, 1121], [244, 1121], [244, 1149], [188, 1149]], [[98, 1148], [157, 1148], [157, 1174], [98, 1174]], [[192, 1149], [731, 1149], [731, 1173], [192, 1173]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[194, 1174], [642, 1174], [642, 1198], [194, 1198]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [327, 1199], [327, 1224], [190, 1224]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[186, 1221], [264, 1221], [264, 1253], [186, 1253]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[190, 1249], [279, 1249], [279, 1276], [190, 1276]], [[98, 1274], [155, 1274], [155, 1301], [98, 1301]], [[190, 1276], [362, 1276], [362, 1300], [190, 1300]], [[98, 1301], [155, 1301], [155, 1326], [98, 1326]], [[194, 1305], [476, 1305], [476, 1323], [194, 1323]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[194, 1330], [474, 1330], [474, 1348], [194, 1348]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1351], [360, 1351], [360, 1375], [192, 1375]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[188, 1375], [261, 1370], [263, 1403], [190, 1407]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [412, 1403], [412, 1428], [192, 1428]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[190, 1428], [262, 1428], [262, 1455], [190, 1455]], [[98, 1453], [157, 1453], [157, 1480], [98, 1480]], [[190, 1453], [279, 1453], [279, 1480], [190, 1480]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[192, 1480], [316, 1480], [316, 1505], [192, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[192, 1505], [367, 1505], [367, 1530], [192, 1530]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[192, 1530], [325, 1530], [325, 1555], [192, 1555]], [[98, 1555], [157, 1555], [157, 1582], [98, 1582]], [[192, 1557], [430, 1557], [430, 1580], [192, 1580]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[192, 1582], [353, 1582], [353, 1607], [192, 1607]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[194, 1609], [430, 1609], [430, 1632], [194, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[192, 1634], [402, 1634], [402, 1657], [192, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1659], [330, 1659], [330, 1684], [192, 1684]], [[100, 1682], [157, 1682], [157, 1709], [100, 1709]], [[190, 1682], [297, 1682], [297, 1712], [190, 1712]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["按照标准图谱相似度递减列表：", "0.000", "经厚在第2暖推水平横截面", "3.896", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.049", "C反应蛋白C-REACTIVEPROTEIN", "0.058", "血尿酸SERUMURICACID", "0.064", "脂肪酶*", "0.067", "血管紧张素Ⅱ*", "0.078", "肥固醇COMMONPLASMACHOLESTERIN", "0.088", "血浆丰化脂肪酸NONETHERIZED FATTYACIDSOF PLASMA", "0.089", "血管紧张素I*", "0.137", "血钾PLASMAPOTASSIUM", "0.065", "血清蛋白SERUM ALBUMEN", "0.079", "PERIPHERICBLOODLEUCOCYTES", "0.082", "尿中蛋白质PROTEININURINE", "0.083", "血红蛋白HAEMOGLOBIN", "0.087", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.089", "嗜碱性粒细胞BASOPHILS", "0.092", "血红血球ERYTHROCYTES", "0.099", "尿白血球URINELEUCOCYTES", "0.107", "BETA球蛋白", "0.110", "单核细胞MONOCYTES", "0.111", "免疫球蛋白G*", "0.114", "免疫球蛋白M*", "0.117", "血清蛋白SERUMPROTEIN", "0.174", "锂*", "0.063", "17-血浆氧皮质类固醇类", "0.066", "17-尿中酮类固醇", "0.067", "肿瘤标志物MELANOGENE在尿", "0.072", "醛固酮尿*", "0.074", "血清溶菌酵SERUMLYSOZYME", "0.075", "血清补体SERUMCOMPLEMENT", "0.076", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.076", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.079", "血糖BLOOD SUGAR", "0.080", "尿中肾上腺素URINEADRENALIN", "0.082", "血浆磷脂PLASMAPHOSPHOTIDES", "0.083", "RHEUMOFACTOR*", "0.084", "肾素*", "0.085", "血清淀粉酵素SERUMALPHAAMYLASE", "0.085", "游离胆固醇FREEPLASMACHOLESTERIN", "0.086", "肿瘤标志物胸苷激酶", "0.086", "糖苷*", "0.086", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.086", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.086", "抗链球菌溶血素", "0.088", "铁蛋白*", "0.089", "醛固酮血*", "0.091", "红细胞沉降率（ESR）", "0.091", "酸性磷酸酵素ACIDPHOSPHATASE", "0.093", "嗜中性粒细胞STABNEUTROPHILS", "0.093", "血尿素BLOODUREA", "0.093", "胆汁酸*", "0.095", "尿肌酥URINECREATININE", "0.096", "催乳素*", "0.096", "葡萄糖浆*", "0.097", "甲状腺球蛋白*", "0.097", "甲状腺素结合球蛋白", "0.098", "ALPHA2球蛋白*", "0.098", "嗜酸性粒细胞EOSINOPHILES", "0.098", "血细胞比容，全血*", "0.098", "血组织胺BLOODHISTAMINE", "0.099", "维生素B1（THIAMINE）*", "0.099", "糖基化血红蛋白", "0.099", "胰高血糖素*"], "rec_scores": [0.9965786933898926, 0.9991452097892761, 0.880855143070221, 0.9994939565658569, 0.9983981251716614, 0.9706200361251831, 0.9999065399169922, 0.9697375893592834, 0.9999458193778992, 0.9981278777122498, 0.999856173992157, 0.9802283644676208, 0.9999201893806458, 0.9428358674049377, 0.9999220967292786, 0.9607356786727905, 0.9999352693557739, 0.9589672684669495, 0.9998756647109985, 0.8950443863868713, 0.9998487234115601, 0.9814984798431396, 0.9996844530105591, 0.9809539914131165, 0.9996131658554077, 0.9961169362068176, 0.9996282458305359, 0.9969345927238464, 0.9996234774589539, 0.9983727931976318, 0.9996304512023926, 0.992376983165741, 0.9997150301933289, 0.9985984563827515, 0.9998136758804321, 0.9966340661048889, 0.9996789693832397, 0.9924110770225525, 0.9996486902236938, 0.9965493083000183, 0.9992998242378235, 0.9938358664512634, 0.9991836547851562, 0.9893839955329895, 0.9967408180236816, 0.9943965673446655, 0.9991239309310913, 0.9983377456665039, 0.9995405077934265, 0.7592326998710632, 0.9998430013656616, 0.9896459579467773, 0.9996861219406128, 0.9884322285652161, 0.9995366334915161, 0.9948570132255554, 0.9997445344924927, 0.9746249914169312, 0.9997725486755371, 0.9969785213470459, 0.9997517466545105, 0.997980535030365, 0.9996644854545593, 0.9959428310394287, 0.9997709393501282, 0.9984098672866821, 0.9997868537902832, 0.9789886474609375, 0.9995964169502258, 0.9975314140319824, 0.9997585415840149, 0.9969408512115479, 0.9998342394828796, 0.9896174669265747, 0.9997628927230835, 0.9866700768470764, 0.999701201915741, 0.9988448023796082, 0.999744713306427, 0.9908193349838257, 0.9997943639755249, 0.975483775138855, 0.9998149871826172, 0.9638113379478455, 0.9996939897537231, 0.9962767362594604, 0.9996734857559204, 0.9967041015625, 0.9996250867843628, 0.9955238699913025, 0.999644935131073, 0.9852356910705566, 0.9996911287307739, 0.965745747089386, 0.9997507333755493, 0.9382445812225342, 0.9996320605278015, 0.9954561591148376, 0.9996955990791321, 0.994968593120575, 0.999719500541687, 0.9963285326957703, 0.999856173992157, 0.9457128643989563, 0.9996612668037415, 0.9570692777633667, 0.9995855093002319, 0.9812434315681458, 0.9997925758361816, 0.9783646464347839, 0.9998332858085632, 0.9743338227272034, 0.9996835589408875, 0.9968951344490051, 0.9996887445449829, 0.9461562037467957, 0.9997957348823547, 0.997796356678009, 0.9998306035995483, 0.9862431287765503, 0.9996902346611023, 0.9938141703605652, 0.999626636505127, 0.9597491025924683, 0.9994678497314453, 0.9957807660102844, 0.9994714856147766, 0.9588515758514404], "rec_polys": [[[18, 27], [521, 27], [521, 63], [18, 63]], [[100, 77], [157, 77], [157, 102], [100, 102]], [[190, 75], [423, 77], [423, 102], [190, 100]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 127], [462, 127], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[192, 154], [495, 152], [495, 177], [192, 179]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[194, 181], [423, 181], [423, 204], [194, 204]], [[98, 204], [162, 204], [162, 229], [98, 229]], [[192, 204], [266, 204], [266, 231], [192, 231]], [[98, 229], [161, 229], [161, 256], [98, 256]], [[192, 231], [319, 227], [320, 254], [193, 258]], [[98, 256], [162, 256], [162, 281], [98, 281]], [[194, 259], [554, 259], [554, 277], [194, 277]], [[98, 281], [162, 281], [162, 306], [98, 306]], [[194, 282], [727, 282], [727, 306], [194, 306]], [[98, 306], [161, 306], [161, 332], [98, 332]], [[194, 307], [310, 307], [310, 327], [194, 327]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [426, 336], [426, 354], [196, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[194, 359], [412, 359], [412, 383], [194, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[194, 386], [473, 386], [473, 404], [194, 404]], [[98, 408], [155, 408], [155, 434], [98, 434]], [[194, 409], [443, 409], [443, 433], [194, 433]], [[98, 433], [155, 433], [155, 459], [98, 459]], [[194, 436], [393, 436], [393, 459], [194, 459]], [[98, 458], [155, 458], [155, 484], [98, 484]], [[194, 461], [565, 461], [565, 479], [194, 479]], [[98, 484], [155, 484], [155, 509], [98, 509]], [[192, 486], [399, 486], [399, 509], [192, 509]], [[98, 509], [155, 509], [155, 536], [98, 536]], [[194, 511], [404, 511], [404, 534], [194, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[194, 538], [438, 538], [438, 561], [194, 561]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[191, 559], [298, 563], [297, 588], [190, 584]], [[98, 586], [155, 586], [155, 611], [98, 611]], [[192, 588], [377, 588], [377, 611], [192, 611]], [[98, 611], [153, 611], [153, 638], [98, 638]], [[190, 613], [309, 609], [310, 636], [191, 640]], [[98, 636], [155, 636], [155, 663], [98, 663]], [[190, 636], [310, 634], [310, 661], [190, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[192, 665], [406, 665], [406, 690], [192, 690]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[192, 688], [223, 688], [223, 713], [192, 713]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[192, 715], [386, 715], [386, 740], [192, 740]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[192, 740], [330, 740], [330, 765], [192, 765]], [[98, 765], [155, 765], [155, 790], [98, 790]], [[192, 767], [445, 767], [445, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[190, 790], [279, 790], [279, 817], [190, 817]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [441, 817], [441, 840], [192, 840]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[194, 844], [447, 844], [447, 867], [194, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [687, 869], [687, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[192, 894], [606, 894], [606, 917], [192, 917]], [[98, 917], [157, 917], [157, 944], [98, 944]], [[194, 919], [356, 919], [356, 944], [194, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [458, 946], [458, 969], [194, 969]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[194, 971], [476, 971], [476, 994], [194, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [347, 996], [347, 1019], [192, 1019]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[190, 1019], [244, 1019], [244, 1047], [190, 1047]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[192, 1047], [504, 1047], [504, 1071], [192, 1071]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[190, 1072], [524, 1072], [524, 1096], [190, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[190, 1096], [367, 1096], [367, 1121], [190, 1121]], [[98, 1121], [157, 1121], [157, 1148], [98, 1148]], [[188, 1121], [244, 1121], [244, 1149], [188, 1149]], [[98, 1148], [157, 1148], [157, 1174], [98, 1174]], [[192, 1149], [731, 1149], [731, 1173], [192, 1173]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[194, 1174], [642, 1174], [642, 1198], [194, 1198]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [327, 1199], [327, 1224], [190, 1224]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[186, 1221], [264, 1221], [264, 1253], [186, 1253]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[190, 1249], [279, 1249], [279, 1276], [190, 1276]], [[98, 1274], [155, 1274], [155, 1301], [98, 1301]], [[190, 1276], [362, 1276], [362, 1300], [190, 1300]], [[98, 1301], [155, 1301], [155, 1326], [98, 1326]], [[194, 1305], [476, 1305], [476, 1323], [194, 1323]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[194, 1330], [474, 1330], [474, 1348], [194, 1348]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1351], [360, 1351], [360, 1375], [192, 1375]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[188, 1375], [261, 1370], [263, 1403], [190, 1407]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [412, 1403], [412, 1428], [192, 1428]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[190, 1428], [262, 1428], [262, 1455], [190, 1455]], [[98, 1453], [157, 1453], [157, 1480], [98, 1480]], [[190, 1453], [279, 1453], [279, 1480], [190, 1480]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[192, 1480], [316, 1480], [316, 1505], [192, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[192, 1505], [367, 1505], [367, 1530], [192, 1530]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[192, 1530], [325, 1530], [325, 1555], [192, 1555]], [[98, 1555], [157, 1555], [157, 1582], [98, 1582]], [[192, 1557], [430, 1557], [430, 1580], [192, 1580]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[192, 1582], [353, 1582], [353, 1607], [192, 1607]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[194, 1609], [430, 1609], [430, 1632], [194, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[192, 1634], [402, 1634], [402, 1657], [192, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1659], [330, 1659], [330, 1684], [192, 1684]], [[100, 1682], [157, 1682], [157, 1709], [100, 1709]], [[190, 1682], [297, 1682], [297, 1712], [190, 1712]]], "rec_boxes": [[18, 27, 521, 63], [100, 77, 157, 102], [190, 75, 423, 102], [98, 102, 157, 129], [192, 102, 270, 129], [194, 127, 462, 152], [98, 154, 161, 179], [192, 152, 495, 179], [98, 179, 162, 204], [194, 181, 423, 204], [98, 204, 162, 229], [192, 204, 266, 231], [98, 229, 161, 256], [192, 227, 320, 258], [98, 256, 162, 281], [194, 259, 554, 277], [98, 281, 162, 306], [194, 282, 727, 306], [98, 306, 161, 332], [194, 307, 310, 327], [98, 332, 161, 358], [196, 336, 426, 354], [98, 358, 157, 383], [194, 359, 412, 383], [98, 383, 155, 409], [194, 386, 473, 404], [98, 408, 155, 434], [194, 409, 443, 433], [98, 433, 155, 459], [194, 436, 393, 459], [98, 458, 155, 484], [194, 461, 565, 479], [98, 484, 155, 509], [192, 486, 399, 509], [98, 509, 155, 536], [194, 511, 404, 534], [98, 536, 155, 561], [194, 538, 438, 561], [98, 561, 155, 588], [190, 559, 298, 588], [98, 586, 155, 611], [192, 588, 377, 611], [98, 611, 153, 638], [190, 609, 310, 640], [98, 636, 155, 663], [190, 634, 310, 663], [98, 663, 155, 688], [192, 665, 406, 690], [98, 688, 155, 715], [192, 688, 223, 713], [98, 713, 157, 740], [192, 715, 386, 740], [98, 740, 157, 767], [192, 740, 330, 765], [98, 765, 155, 790], [192, 767, 445, 790], [98, 790, 157, 817], [190, 790, 279, 817], [98, 815, 157, 842], [192, 817, 441, 840], [98, 840, 157, 867], [194, 844, 447, 867], [98, 867, 157, 892], [192, 869, 687, 892], [98, 892, 157, 919], [192, 894, 606, 917], [98, 917, 157, 944], [194, 919, 356, 944], [98, 944, 157, 969], [194, 946, 458, 969], [98, 969, 157, 996], [194, 971, 476, 994], [98, 994, 157, 1021], [192, 996, 347, 1019], [98, 1019, 157, 1046], [190, 1019, 244, 1047], [98, 1046, 157, 1071], [192, 1047, 504, 1071], [98, 1071, 157, 1098], [190, 1072, 524, 1096], [98, 1096, 157, 1123], [190, 1096, 367, 1121], [98, 1121, 157, 1148], [188, 1121, 244, 1149], [98, 1148, 157, 1174], [192, 1149, 731, 1173], [98, 1173, 157, 1199], [194, 1174, 642, 1198], [98, 1198, 157, 1224], [190, 1199, 327, 1224], [98, 1224, 157, 1249], [186, 1221, 264, 1253], [98, 1249, 157, 1274], [190, 1249, 279, 1276], [98, 1274, 155, 1301], [190, 1276, 362, 1300], [98, 1301, 155, 1326], [194, 1305, 476, 1323], [98, 1326, 157, 1351], [194, 1330, 474, 1348], [98, 1351, 157, 1376], [192, 1351, 360, 1375], [98, 1376, 157, 1403], [188, 1370, 263, 1407], [100, 1403, 157, 1428], [192, 1403, 412, 1428], [100, 1428, 157, 1453], [190, 1428, 262, 1455], [98, 1453, 157, 1480], [190, 1453, 279, 1480], [98, 1478, 157, 1505], [192, 1480, 316, 1505], [98, 1505, 157, 1530], [192, 1505, 367, 1530], [98, 1530, 157, 1555], [192, 1530, 325, 1555], [98, 1555, 157, 1582], [192, 1557, 430, 1580], [98, 1580, 157, 1607], [192, 1582, 353, 1607], [98, 1607, 157, 1632], [194, 1609, 430, 1632], [100, 1632, 157, 1657], [192, 1634, 402, 1657], [100, 1657, 157, 1684], [192, 1659, 330, 1684], [100, 1682, 157, 1709], [190, 1682, 297, 1712]]}, "table_res_list": [{"cell_box_list": [[97.88172149658203, 76.84679651260376, 169.6746826171875, 102.96556663513184], [193.66510009765625, 75.98823022842407, 767.7743835449219, 103.88162612915039], [42.24137496948242, 102.6353588104248, 69.41211318969727, 127.86349868774414], [69.37919998168945, 102.6210994720459, 97.95689392089844, 127.90215301513672], [97.85115051269531, 102.5883903503418, 169.8857421875, 127.9073600769043], [192.0, 102.0, 270.0, 129.0], [69.33405303955078, 127.93780517578125, 97.90235137939453, 153.57424926757812], [97.8038558959961, 127.9266586303711, 169.90992736816406, 153.52984619140625], [193.20086669921875, 127.33334350585938, 767.8775329589844, 154.0614471435547], [42.16091728210449, 153.39828491210938, 69.34370422363281, 179.24159240722656], [69.31952285766602, 153.43572998046875, 97.84281921386719, 179.2898712158203], [97.76614379882812, 153.38790130615234, 170.0179443359375, 179.48511505126953], [192.68807983398438, 153.49546813964844, 767.8860778808594, 179.67160034179688], [42.12860107421875, 179.06208038330078, 69.30044174194336, 204.82725524902344], [69.3082504272461, 179.07295989990234, 97.85346221923828, 230.2661590576172], [97.75624084472656, 179.19461059570312, 169.99342346191406, 205.1333465576172], [194.0, 181.0, 423.0, 204.0], [42.147321701049805, 204.6516876220703, 69.28667831420898, 230.17947387695312], [97.73455810546875, 204.97137451171875, 170.29302978515625, 256.0567169189453], [191.85372924804688, 205.18922424316406, 767.7579650878906, 230.34877014160156], [69.29993057250977, 230.3055419921875, 97.87149810791016, 255.98558044433594], [191.65850830078125, 230.58799743652344, 767.7727355957031, 256.61094665527344], [42.08480262756348, 255.89266967773438, 69.3132438659668, 281.72999572753906], [69.27108383178711, 255.84117126464844, 97.8350601196289, 281.69825744628906], [97.7230224609375, 255.90553283691406, 170.5603485107422, 281.7362060546875], [194.0, 259.0, 554.0, 277.0], [42.096187591552734, 281.5968780517578, 69.27558517456055, 307.30580139160156], [69.27623748779297, 281.5194396972656, 97.81602478027344, 307.30323791503906], [97.73983764648438, 281.52711486816406, 170.7516326904297, 307.4837341308594], [191.92257690429688, 281.8204650878906, 767.9609680175781, 308.2101287841797], [42.11864471435547, 307.20619201660156, 69.2633285522461, 332.7666320800781], [69.28433609008789, 307.1519470214844, 97.78330993652344, 332.85302734375], [97.69188690185547, 307.38623046875, 170.8030242919922, 333.0078125], [194.0, 307.0, 310.0, 327.0], [69.27985000610352, 332.8382568359375, 97.85456848144531, 358.482666015625], [97.72885131835938, 332.941162109375, 170.9638671875, 358.5037536621094], [191.88555908203125, 333.46661376953125, 768.0, 384.3574523925781], [42.06722640991211, 358.4189147949219, 69.29945755004883, 384.2966003417969], [69.25765609741211, 358.3724060058594, 97.83369445800781, 384.2814025878906], [97.69406127929688, 358.35186767578125, 170.99703979492188, 384.2681579589844], [42.09866905212402, 384.1863098144531, 69.29472732543945, 409.89306640625], [69.28382110595703, 384.1236877441406, 97.83628845214844, 409.91510009765625], [97.76668548583984, 384.0918884277344, 171.07408142089844, 409.93658447265625], [194.0, 386.0, 473.0, 404.0], [42.13142395019531, 409.8260498046875, 69.29397201538086, 435.3199462890625], [69.31658172607422, 409.7828369140625, 97.82477569580078, 435.3897399902344], [97.69376373291016, 409.8813781738281, 171.09597778320312, 435.3477478027344], [192.08949279785156, 409.81610107421875, 767.9916687011719, 435.06512451171875], [69.31641387939453, 435.3647155761719, 97.89360809326172, 460.7884521484375], [97.75100708007812, 435.3775634765625, 171.12258911132812, 460.80096435546875], [192.387451171875, 435.44873046875, 768.0, 461.2269592285156], [42.09181213378906, 460.69256591796875, 69.33295822143555, 486.2370300292969], [69.30021286010742, 460.7294921875, 97.87858581542969, 511.303955078125], [97.72881317138672, 460.63836669921875, 171.1748046875, 511.4326477050781], [194.0, 461.0, 565.0, 479.0], [192.40965270996094, 486.4798583984375, 768.0, 512.1068115234375], [42.17719078063965, 511.2569885253906, 69.33316421508789, 536.1864929199219], [69.3209342956543, 511.24346923828125, 97.94812774658203, 587.0447998046875], [97.74380493164062, 511.4825744628906, 171.26223754882812, 562.0129699707031], [194.0, 511.0, 404.0, 534.0], [192.4953155517578, 536.6324462890625, 768.0, 562.22607421875], [42.18672752380371, 561.4203796386719, 69.40234375, 587.1224365234375], [97.76358795166016, 561.7368774414062, 171.19114685058594, 612.7938842773438], [190.0, 559.0, 298.0, 588.0], [69.30584335327148, 587.031494140625, 97.88387298583984, 638.0692138671875], [192.0, 588.0, 377.0, 611.0], [42.222328186035156, 612.4354858398438, 69.3514633178711, 637.9793701171875], [97.74263763427734, 612.7374267578125, 171.20278930664062, 638.3506469726562], [192.17283630371094, 613.16796875, 768.0, 637.8433837890625], [42.23497200012207, 638.1555786132812, 69.43832015991211, 689.3380737304688], [69.39370727539062, 638.095458984375, 97.93671417236328, 663.6509399414062], [97.77275848388672, 638.2000732421875, 171.16883850097656, 689.485107421875], [192.33047485351562, 638.3123779296875, 767.9798278808594, 664.2579345703125], [69.32711029052734, 663.3706665039062, 97.84634399414062, 689.31591796875], [192.0, 665.0, 406.0, 690.0], [69.30389022827148, 689.31396484375, 97.87603759765625, 714.887939453125], [97.79033660888672, 689.3617553710938, 171.17434692382812, 715.0431518554688], [192.31881713867188, 689.6424560546875, 768.0, 715.5255126953125], [42.25956916809082, 714.74267578125, 69.3436393737793, 740.4514770507812], [69.30206680297852, 714.7393188476562, 97.83989715576172, 740.4750366210938], [97.69530487060547, 714.9479370117188, 171.1353302001953, 740.5996704101562], [192.0, 715.0, 386.0, 740.0], [42.30327606201172, 740.5996704101562, 69.43830871582031, 766.0615844726562], [69.38276290893555, 740.4481811523438, 97.90402221679688, 765.9819946289062], [97.7861328125, 740.5977172851562, 171.08721923828125, 766.156982421875], [192.42628479003906, 740.68359375, 768.0, 766.6112060546875], [42.29402542114258, 765.8173828125, 69.4317855834961, 791.8056640625], [69.33111572265625, 765.77099609375, 97.83788299560547, 791.69873046875], [97.7558822631836, 765.932861328125, 170.90890502929688, 791.830078125], [192.0, 767.0, 445.0, 790.0], [42.21009826660156, 791.8484497070312, 69.3751220703125, 842.9102172851562], [69.32168960571289, 791.7069091796875, 97.8666763305664, 817.3203735351562], [97.82254028320312, 791.7871704101562, 171.0078582763672, 817.4560546875], [190.0, 790.0, 279.0, 817.0], [69.31449890136719, 817.1802368164062, 97.85995483398438, 842.92333984375], [97.72566986083984, 817.4522705078125, 170.940673828125, 843.1829223632812], [192.11648559570312, 818.0350341796875, 768.0, 868.70068359375], [69.3874740600586, 842.8541870117188, 97.90973663330078, 868.4747924804688], [97.7911376953125, 843.1598510742188, 170.95040893554688, 868.5524291992188], [42.2562255859375, 868.2845458984375, 69.4273796081543, 894.2930908203125], [69.34294128417969, 868.2846069335938, 97.84503936767578, 894.2169189453125], [97.76128387451172, 868.3772583007812, 170.8828125, 894.3052368164062], [192.0, 869.0, 687.0, 892.0], [69.31443405151367, 894.2153930664062, 97.88995361328125, 919.8663940429688], [97.83423614501953, 894.3008422851562, 171.01512145996094, 919.9135131835938], [192.0855255126953, 894.5521240234375, 768.0, 920.2236938476562], [42.22101020812988, 919.7435302734375, 69.36687088012695, 945.4326171875], [69.33898162841797, 919.713134765625, 97.89744567871094, 945.4992065429688], [97.7540054321289, 919.87890625, 170.96926879882812, 945.5177612304688], [194.0, 919.0, 356.0, 944.0], [69.38544464111328, 945.4400634765625, 97.92800903320312, 971.0060424804688], [97.80387115478516, 945.5619506835938, 170.94033813476562, 971.013916015625], [192.1590576171875, 945.8239135742188, 768.0, 971.081787109375], [69.29156494140625, 970.7626953125, 97.87423706054688, 996.5564575195312], [97.77698516845703, 970.93212890625, 170.8690643310547, 996.5952758789062], [192.02554321289062, 971.660888671875, 768.0, 996.9487915039062], [97.84577941894531, 996.57373046875, 171.0325164794922, 1046.0], [192.0, 996.0, 347.0, 1019.0], [192.2546844482422, 1022.36669921875, 768.0, 1046.9028930664062], [69.30529022216797, 1046.6241455078125, 97.84093475341797, 1071.7914428710938], [97.70560455322266, 1047.3867797851562, 171.18399047851562, 1072.1476440429688], [192.0, 1047.0, 504.0, 1071.0], [98.0, 1071.0, 157.0, 1098.0], [192.59413146972656, 1071.8965454101562, 768.0, 1098.2816772460938], [69.24864196777344, 1097.018798828125, 97.83401489257812, 1148.0838012695312], [97.6865463256836, 1097.2425537109375, 171.1061248779297, 1148.4577026367188], [190.0, 1096.0, 367.0, 1121.0], [42.11277389526367, 1122.3798217773438, 69.28721618652344, 1148.0921020507812], [188.0, 1121.0, 244.0, 1149.0], [69.28012466430664, 1147.9194946289062, 97.7728500366211, 1173.4743041992188], [97.66921997070312, 1148.3252563476562, 170.92361450195312, 1199.2892456054688], [192.74929809570312, 1148.7069702148438, 768.0, 1200.2786254882812], [69.26227188110352, 1173.4553833007812, 97.82879638671875, 1199.0065307617188], [42.09878730773926, 1199.0447387695312, 69.30714416503906, 1224.8956909179688], [69.23751449584961, 1198.9786987304688, 97.8187255859375, 1224.8203735351562], [97.69001770019531, 1199.1867065429688, 170.94175720214844, 1224.9730834960938], [190.0, 1199.0, 327.0, 1224.0], [42.09826850891113, 1224.7507934570312, 69.2607307434082, 1250.4439086914062], [69.239990234375, 1224.6616821289062, 97.8269271850586, 1250.4319458007812], [97.7845230102539, 1224.7647094726562, 170.94374084472656, 1250.7351684570312], [192.7024383544922, 1225.7576293945312, 768.0, 1251.3223266601562], [42.100120544433594, 1250.3008422851562, 69.2604751586914, 1275.8899536132812], [69.25751876831055, 1250.2752075195312, 97.75707244873047, 1275.9110717773438], [97.6707992553711, 1250.5682983398438, 170.9346923828125, 1276.2640991210938], [190.0, 1249.0, 279.0, 1276.0], [69.24602890014648, 1275.9299926757812, 97.82356262207031, 1301.4953002929688], [97.7509536743164, 1276.1318969726562, 170.9250946044922, 1301.6625366210938], [192.81927490234375, 1276.4651489257812, 768.0, 1302.3546752929688], [42.099220275878906, 1301.5296020507812, 69.29634094238281, 1327.3915405273438], [69.23623275756836, 1301.4675903320312, 97.81642150878906, 1327.3253784179688], [97.67254638671875, 1301.5894165039062, 171.0207061767578, 1327.4302368164062], [192.65011596679688, 1302.3927612304688, 768.0, 1327.8049926757812], [42.098758697509766, 1327.2839965820312, 69.2622184753418, 1352.9349975585938], [69.24657440185547, 1327.2042846679688, 97.81343078613281, 1352.9243774414062], [97.76995086669922, 1327.2212524414062, 171.0241241455078, 1353.1664428710938], [194.0, 1330.0, 474.0, 1348.0], [42.10086250305176, 1352.8419799804688, 69.27813720703125, 1378.4264526367188], [69.26803207397461, 1352.8152465820312, 97.74819946289062, 1378.4519653320312], [97.6577377319336, 1353.0095825195312, 170.99681091308594, 1378.6436157226562], [192.0, 1351.0, 360.0, 1375.0], [69.25672912597656, 1378.4801635742188, 97.81156158447266, 1404.0110473632812], [97.72772979736328, 1378.5376586914062, 170.98435974121094, 1404.1229858398438], [192.58644104003906, 1378.9367065429688, 767.8703308105469, 1404.5935668945312], [42.067617416381836, 1404.0014038085938, 69.31447219848633, 1429.8720092773438], [69.2406005859375, 1403.9563598632812, 97.81381225585938, 1429.8206176757812], [97.67579650878906, 1404.0410766601562, 171.04685974121094, 1429.8381958007812], [192.0, 1403.0, 412.0, 1428.0], [42.06675148010254, 1429.7372436523438, 69.28641891479492, 1480.7765502929688], [69.25331115722656, 1429.6796264648438, 97.8335952758789, 1455.4035034179688], [97.75015258789062, 1429.6742553710938, 171.1240234375, 1455.5665893554688], [192.39682006835938, 1430.1814575195312, 768.0, 1480.6465454101562], [69.26553726196289, 1455.2909545898438, 97.77055358886719, 1480.8534545898438], [97.67656707763672, 1455.4596557617188, 171.1211395263672, 1506.3775024414062], [192.7218017578125, 1481.1779174804688, 768.0, 1531.7727661132812], [42.02647590637207, 1506.2229614257812, 69.32410049438477, 1531.8112182617188], [69.240478515625, 1506.2582397460938, 97.82577514648438, 1531.7487182617188], [97.7048568725586, 1506.2836303710938, 171.19859313964844, 1557.0221557617188], [69.24197387695312, 1531.6261596679688, 97.83109283447266, 1556.8190307617188], [192.54046630859375, 1532.1253051757812, 768.0, 1581.8916625976562], [42.13104820251465, 1556.8806762695312, 69.29866027832031, 1581.5845336914062], [69.2800064086914, 1556.7736206054688, 97.82339477539062, 1581.5847778320312], [97.72699737548828, 1557.0018920898438, 171.19834899902344, 1607.3667602539062], [69.28082656860352, 1581.5879516601562, 97.8626937866211, 1632.5906372070312], [192.6883544921875, 1582.1211547851562, 767.9764099121094, 1607.8345336914062], [42.14175796508789, 1606.8237915039062, 69.4032096862793, 1632.6561889648438], [97.7314224243164, 1607.1499633789062, 171.0928497314453, 1632.9812622070312], [194.0, 1609.0, 430.0, 1632.0], [69.2865219116211, 1632.5114135742188, 97.88976287841797, 1683.1818237304688], [97.7748031616211, 1632.6596069335938, 171.0718231201172, 1709.0], [192.74295043945312, 1633.1708374023438, 767.7831115722656, 1658.9368286132812], [192.0, 1659.0, 330.0, 1684.0], [190.0, 1682.0, 297.0, 1712.0]], "pred_html": "<html><body><table><tbody><tr><td>0.000</td><td>经厚在第2暖推水平横截面</td><td></td></tr><tr><td></td><td></td><td>3.896</td></tr><tr><td></td><td></td><td>虚拟模式-健康问题发展趋势列表：</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.049</td></tr><tr><td></td><td></td><td>0.058</td></tr><tr><td></td><td>0.064 0.067</td><td>脂肪酶*</td></tr><tr><td></td><td>血管紧张素Ⅱ*</td><td></td></tr><tr><td></td><td></td><td>0.078</td></tr><tr><td></td><td></td><td>0.088</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.089</td></tr><tr><td></td><td>0.137</td><td>血钾PLASMAPOTASSIUM 血清蛋白SERUM ALBUMEN</td></tr><tr><td></td><td></td><td>0.065</td></tr><tr><td></td><td></td><td>0.079</td></tr><tr><td></td><td></td><td>0.082</td></tr><tr><td></td><td>0.083</td><td>血红蛋白HAEMOGLOBIN</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.087 0.089</td></tr><tr><td></td><td></td><td>0.092 0.099</td></tr><tr><td>尿白血球URINELEUCOCYTES</td><td></td><td></td></tr><tr><td></td><td>0.107 0.110</td><td>BETA球蛋白</td></tr><tr><td></td><td>0.111</td><td>免疫球蛋白G*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.114 0.117</td></tr><tr><td></td><td>0.174</td><td>锂*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.063</td></tr><tr><td></td><td></td><td>0.066</td></tr><tr><td></td><td></td><td>0.067</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.072</td></tr><tr><td></td><td>0.074</td><td>血清溶菌酵SERUMLYSOZYME 血清补体SERUMCOMPLEMENT</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.076</td></tr><tr><td></td><td>0.076</td><td>肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE</td></tr><tr><td></td><td></td><td>0.079</td></tr><tr><td></td><td>0.080</td><td>尿中肾上腺素URINEADRENALIN</td></tr><tr><td></td><td>0.082</td><td>血浆磷脂PLASMAPHOSPHOTIDES</td></tr><tr><td>0.083 0.084</td><td>RHEUMOFACTOR*</td><td>肾素*</td></tr><tr><td></td><td>0.085</td><td>血清淀粉酵素SERUMALPHAAMYLASE</td></tr><tr><td>0.085</td><td>游离胆固醇FREEPLASMACHOLESTERIN</td><td></td></tr><tr><td></td><td>0.086 0.086</td><td>肿瘤标志物胸苷激酶</td></tr><tr><td></td><td>0.086 0.086</td><td>AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE 血清中的氨基酸NITROGENOFAMINOACIDSINSERUM</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.086</td></tr><tr><td></td><td></td><td>0.088</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.089</td></tr><tr><td></td><td>0.091</td><td>红细胞沉降率（ESR）</td></tr><tr><td></td><td></td><td>0.091</td></tr><tr><td></td><td></td><td>0.093</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.093</td></tr><tr><td></td><td>0.093</td><td></td></tr><tr><td></td><td></td><td>0.095</td></tr><tr><td></td><td></td><td>0.096</td></tr><tr><td></td><td>0.096 0.097</td><td>甲状腺球蛋白* 甲状腺素结合球蛋白</td></tr><tr><td></td><td></td><td>0.097 0.098</td></tr><tr><td></td><td>ALPHA2球蛋白* 嗜酸性粒细胞EOSINOPHILES</td><td></td></tr><tr><td></td><td></td><td>0.098 0.098</td></tr><tr><td></td><td>血细胞比容，全血*</td><td></td></tr><tr><td></td><td>0.098</td><td>血组织胺BLOODHISTAMINE</td></tr><tr><td></td><td>0.099 0.099 0.099</td><td>维生素B1（THIAMINE）*</td></tr></tbody></table></body></html>", "table_ocr_pred": {"rec_polys": [[[100, 77], [157, 77], [157, 102], [100, 102]], [[190, 75], [423, 77], [423, 102], [190, 100]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[192, 102], [270, 102], [270, 129], [192, 129]], [[194, 127], [462, 127], [462, 152], [194, 152]], [[98, 154], [161, 154], [161, 179], [98, 179]], [[192, 154], [495, 152], [495, 177], [192, 179]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[194, 181], [423, 181], [423, 204], [194, 204]], [[98, 204], [162, 204], [162, 229], [98, 229]], [[192, 204], [266, 204], [266, 231], [192, 231]], [[98, 229], [161, 229], [161, 256], [98, 256]], [[192, 231], [319, 227], [320, 254], [193, 258]], [[98, 256], [162, 256], [162, 281], [98, 281]], [[194, 259], [554, 259], [554, 277], [194, 277]], [[98, 281], [162, 281], [162, 306], [98, 306]], [[194, 282], [727, 282], [727, 306], [194, 306]], [[98, 306], [161, 306], [161, 332], [98, 332]], [[194, 307], [310, 307], [310, 327], [194, 327]], [[98, 332], [161, 332], [161, 358], [98, 358]], [[196, 336], [426, 336], [426, 354], [196, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[194, 359], [412, 359], [412, 383], [194, 383]], [[98, 383], [155, 383], [155, 409], [98, 409]], [[194, 386], [473, 386], [473, 404], [194, 404]], [[98, 408], [155, 408], [155, 434], [98, 434]], [[194, 409], [443, 409], [443, 433], [194, 433]], [[98, 433], [155, 433], [155, 459], [98, 459]], [[194, 436], [393, 436], [393, 459], [194, 459]], [[98, 458], [155, 458], [155, 484], [98, 484]], [[194, 461], [565, 461], [565, 479], [194, 479]], [[98, 484], [155, 484], [155, 509], [98, 509]], [[192, 486], [399, 486], [399, 509], [192, 509]], [[98, 509], [155, 509], [155, 536], [98, 536]], [[194, 511], [404, 511], [404, 534], [194, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[194, 538], [438, 538], [438, 561], [194, 561]], [[98, 561], [155, 561], [155, 588], [98, 588]], [[191, 559], [298, 563], [297, 588], [190, 584]], [[98, 586], [155, 586], [155, 611], [98, 611]], [[192, 588], [377, 588], [377, 611], [192, 611]], [[98, 611], [153, 611], [153, 638], [98, 638]], [[190, 613], [309, 609], [310, 636], [191, 640]], [[98, 636], [155, 636], [155, 663], [98, 663]], [[190, 636], [310, 634], [310, 661], [190, 663]], [[98, 663], [155, 663], [155, 688], [98, 688]], [[192, 665], [406, 665], [406, 690], [192, 690]], [[98, 688], [155, 688], [155, 715], [98, 715]], [[192, 688], [223, 688], [223, 713], [192, 713]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[192, 715], [386, 715], [386, 740], [192, 740]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[192, 740], [330, 740], [330, 765], [192, 765]], [[98, 765], [155, 765], [155, 790], [98, 790]], [[192, 767], [445, 767], [445, 790], [192, 790]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[190, 790], [279, 790], [279, 817], [190, 817]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [441, 817], [441, 840], [192, 840]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[194, 844], [447, 844], [447, 867], [194, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [687, 869], [687, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[192, 894], [606, 894], [606, 917], [192, 917]], [[98, 917], [157, 917], [157, 944], [98, 944]], [[194, 919], [356, 919], [356, 944], [194, 944]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[194, 946], [458, 946], [458, 969], [194, 969]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[194, 971], [476, 971], [476, 994], [194, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [347, 996], [347, 1019], [192, 1019]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[190, 1019], [244, 1019], [244, 1047], [190, 1047]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[192, 1047], [504, 1047], [504, 1071], [192, 1071]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[190, 1072], [524, 1072], [524, 1096], [190, 1096]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[190, 1096], [367, 1096], [367, 1121], [190, 1121]], [[98, 1121], [157, 1121], [157, 1148], [98, 1148]], [[188, 1121], [244, 1121], [244, 1149], [188, 1149]], [[98, 1148], [157, 1148], [157, 1174], [98, 1174]], [[192, 1149], [731, 1149], [731, 1173], [192, 1173]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[194, 1174], [642, 1174], [642, 1198], [194, 1198]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [327, 1199], [327, 1224], [190, 1224]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[186, 1221], [264, 1221], [264, 1253], [186, 1253]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[190, 1249], [279, 1249], [279, 1276], [190, 1276]], [[98, 1274], [155, 1274], [155, 1301], [98, 1301]], [[190, 1276], [362, 1276], [362, 1300], [190, 1300]], [[98, 1301], [155, 1301], [155, 1326], [98, 1326]], [[194, 1305], [476, 1305], [476, 1323], [194, 1323]], [[98, 1326], [157, 1326], [157, 1351], [98, 1351]], [[194, 1330], [474, 1330], [474, 1348], [194, 1348]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1351], [360, 1351], [360, 1375], [192, 1375]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[188, 1375], [261, 1370], [263, 1403], [190, 1407]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [412, 1403], [412, 1428], [192, 1428]], [[100, 1428], [157, 1428], [157, 1453], [100, 1453]], [[190, 1428], [262, 1428], [262, 1455], [190, 1455]], [[98, 1453], [157, 1453], [157, 1480], [98, 1480]], [[190, 1453], [279, 1453], [279, 1480], [190, 1480]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[192, 1480], [316, 1480], [316, 1505], [192, 1505]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[192, 1505], [367, 1505], [367, 1530], [192, 1530]], [[98, 1530], [157, 1530], [157, 1555], [98, 1555]], [[192, 1530], [325, 1530], [325, 1555], [192, 1555]], [[98, 1555], [157, 1555], [157, 1582], [98, 1582]], [[192, 1557], [430, 1557], [430, 1580], [192, 1580]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[192, 1582], [353, 1582], [353, 1607], [192, 1607]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[194, 1609], [430, 1609], [430, 1632], [194, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[192, 1634], [402, 1634], [402, 1657], [192, 1657]], [[100, 1657], [157, 1657], [157, 1684], [100, 1684]], [[192, 1659], [330, 1659], [330, 1684], [192, 1684]], [[100, 1682], [157, 1682], [157, 1709], [100, 1709]], [[190, 1682], [297, 1682], [297, 1712], [190, 1712]]], "rec_texts": ["0.000", "经厚在第2暖推水平横截面", "3.896", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.049", "C反应蛋白C-REACTIVEPROTEIN", "0.058", "血尿酸SERUMURICACID", "0.064", "脂肪酶*", "0.067", "血管紧张素Ⅱ*", "0.078", "肥固醇COMMONPLASMACHOLESTERIN", "0.088", "血浆丰化脂肪酸NONETHERIZED FATTYACIDSOF PLASMA", "0.089", "血管紧张素I*", "0.137", "血钾PLASMAPOTASSIUM", "0.065", "血清蛋白SERUM ALBUMEN", "0.079", "PERIPHERICBLOODLEUCOCYTES", "0.082", "尿中蛋白质PROTEININURINE", "0.083", "血红蛋白HAEMOGLOBIN", "0.087", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.089", "嗜碱性粒细胞BASOPHILS", "0.092", "血红血球ERYTHROCYTES", "0.099", "尿白血球URINELEUCOCYTES", "0.107", "BETA球蛋白", "0.110", "单核细胞MONOCYTES", "0.111", "免疫球蛋白G*", "0.114", "免疫球蛋白M*", "0.117", "血清蛋白SERUMPROTEIN", "0.174", "锂*", "0.063", "17-血浆氧皮质类固醇类", "0.066", "17-尿中酮类固醇", "0.067", "肿瘤标志物MELANOGENE在尿", "0.072", "醛固酮尿*", "0.074", "血清溶菌酵SERUMLYSOZYME", "0.075", "血清补体SERUMCOMPLEMENT", "0.076", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.076", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.079", "血糖BLOOD SUGAR", "0.080", "尿中肾上腺素URINEADRENALIN", "0.082", "血浆磷脂PLASMAPHOSPHOTIDES", "0.083", "RHEUMOFACTOR*", "0.084", "肾素*", "0.085", "血清淀粉酵素SERUMALPHAAMYLASE", "0.085", "游离胆固醇FREEPLASMACHOLESTERIN", "0.086", "肿瘤标志物胸苷激酶", "0.086", "糖苷*", "0.086", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.086", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.086", "抗链球菌溶血素", "0.088", "铁蛋白*", "0.089", "醛固酮血*", "0.091", "红细胞沉降率（ESR）", "0.091", "酸性磷酸酵素ACIDPHOSPHATASE", "0.093", "嗜中性粒细胞STABNEUTROPHILS", "0.093", "血尿素BLOODUREA", "0.093", "胆汁酸*", "0.095", "尿肌酥URINECREATININE", "0.096", "催乳素*", "0.096", "葡萄糖浆*", "0.097", "甲状腺球蛋白*", "0.097", "甲状腺素结合球蛋白", "0.098", "ALPHA2球蛋白*", "0.098", "嗜酸性粒细胞EOSINOPHILES", "0.098", "血细胞比容，全血*", "0.098", "血组织胺BLOODHISTAMINE", "0.099", "维生素B1（THIAMINE）*", "0.099", "糖基化血红蛋白", "0.099", "胰高血糖素*"], "rec_scores": [0.9991452097892761, 0.880855143070221, 0.9994939565658569, 0.9983981251716614, 0.9706200361251831, 0.9999065399169922, 0.9697375893592834, 0.9999458193778992, 0.9981278777122498, 0.999856173992157, 0.9802283644676208, 0.9999201893806458, 0.9428358674049377, 0.9999220967292786, 0.9607356786727905, 0.9999352693557739, 0.9589672684669495, 0.9998756647109985, 0.8950443863868713, 0.9998487234115601, 0.9814984798431396, 0.9996844530105591, 0.9809539914131165, 0.9996131658554077, 0.9961169362068176, 0.9996282458305359, 0.9969345927238464, 0.9996234774589539, 0.9983727931976318, 0.9996304512023926, 0.992376983165741, 0.9997150301933289, 0.9985984563827515, 0.9998136758804321, 0.9966340661048889, 0.9996789693832397, 0.9924110770225525, 0.9996486902236938, 0.9965493083000183, 0.9992998242378235, 0.9938358664512634, 0.9991836547851562, 0.9893839955329895, 0.9967408180236816, 0.9943965673446655, 0.9991239309310913, 0.9983377456665039, 0.9995405077934265, 0.7592326998710632, 0.9998430013656616, 0.9896459579467773, 0.9996861219406128, 0.9884322285652161, 0.9995366334915161, 0.9948570132255554, 0.9997445344924927, 0.9746249914169312, 0.9997725486755371, 0.9969785213470459, 0.9997517466545105, 0.997980535030365, 0.9996644854545593, 0.9959428310394287, 0.9997709393501282, 0.9984098672866821, 0.9997868537902832, 0.9789886474609375, 0.9995964169502258, 0.9975314140319824, 0.9997585415840149, 0.9969408512115479, 0.9998342394828796, 0.9896174669265747, 0.9997628927230835, 0.9866700768470764, 0.999701201915741, 0.9988448023796082, 0.999744713306427, 0.9908193349838257, 0.9997943639755249, 0.975483775138855, 0.9998149871826172, 0.9638113379478455, 0.9996939897537231, 0.9962767362594604, 0.9996734857559204, 0.9967041015625, 0.9996250867843628, 0.9955238699913025, 0.999644935131073, 0.9852356910705566, 0.9996911287307739, 0.965745747089386, 0.9997507333755493, 0.9382445812225342, 0.9996320605278015, 0.9954561591148376, 0.9996955990791321, 0.994968593120575, 0.999719500541687, 0.9963285326957703, 0.999856173992157, 0.9457128643989563, 0.9996612668037415, 0.9570692777633667, 0.9995855093002319, 0.9812434315681458, 0.9997925758361816, 0.9783646464347839, 0.9998332858085632, 0.9743338227272034, 0.9996835589408875, 0.9968951344490051, 0.9996887445449829, 0.9461562037467957, 0.9997957348823547, 0.997796356678009, 0.9998306035995483, 0.9862431287765503, 0.9996902346611023, 0.9938141703605652, 0.999626636505127, 0.9597491025924683, 0.9994678497314453, 0.9957807660102844, 0.9994714856147766, 0.9588515758514404], "rec_boxes": [[100, 77, 157, 102], [190, 75, 423, 102], [98, 102, 157, 129], [192, 102, 270, 129], [194, 127, 462, 152], [98, 154, 161, 179], [192, 152, 495, 179], [98, 179, 162, 204], [194, 181, 423, 204], [98, 204, 162, 229], [192, 204, 266, 231], [98, 229, 161, 256], [192, 227, 320, 258], [98, 256, 162, 281], [194, 259, 554, 277], [98, 281, 162, 306], [194, 282, 727, 306], [98, 306, 161, 332], [194, 307, 310, 327], [98, 332, 161, 358], [196, 336, 426, 354], [98, 358, 157, 383], [194, 359, 412, 383], [98, 383, 155, 409], [194, 386, 473, 404], [98, 408, 155, 434], [194, 409, 443, 433], [98, 433, 155, 459], [194, 436, 393, 459], [98, 458, 155, 484], [194, 461, 565, 479], [98, 484, 155, 509], [192, 486, 399, 509], [98, 509, 155, 536], [194, 511, 404, 534], [98, 536, 155, 561], [194, 538, 438, 561], [98, 561, 155, 588], [190, 559, 298, 588], [98, 586, 155, 611], [192, 588, 377, 611], [98, 611, 153, 638], [190, 609, 310, 640], [98, 636, 155, 663], [190, 634, 310, 663], [98, 663, 155, 688], [192, 665, 406, 690], [98, 688, 155, 715], [192, 688, 223, 713], [98, 713, 157, 740], [192, 715, 386, 740], [98, 740, 157, 767], [192, 740, 330, 765], [98, 765, 155, 790], [192, 767, 445, 790], [98, 790, 157, 817], [190, 790, 279, 817], [98, 815, 157, 842], [192, 817, 441, 840], [98, 840, 157, 867], [194, 844, 447, 867], [98, 867, 157, 892], [192, 869, 687, 892], [98, 892, 157, 919], [192, 894, 606, 917], [98, 917, 157, 944], [194, 919, 356, 944], [98, 944, 157, 969], [194, 946, 458, 969], [98, 969, 157, 996], [194, 971, 476, 994], [98, 994, 157, 1021], [192, 996, 347, 1019], [98, 1019, 157, 1046], [190, 1019, 244, 1047], [98, 1046, 157, 1071], [192, 1047, 504, 1071], [98, 1071, 157, 1098], [190, 1072, 524, 1096], [98, 1096, 157, 1123], [190, 1096, 367, 1121], [98, 1121, 157, 1148], [188, 1121, 244, 1149], [98, 1148, 157, 1174], [192, 1149, 731, 1173], [98, 1173, 157, 1199], [194, 1174, 642, 1198], [98, 1198, 157, 1224], [190, 1199, 327, 1224], [98, 1224, 157, 1249], [186, 1221, 264, 1253], [98, 1249, 157, 1274], [190, 1249, 279, 1276], [98, 1274, 155, 1301], [190, 1276, 362, 1300], [98, 1301, 155, 1326], [194, 1305, 476, 1323], [98, 1326, 157, 1351], [194, 1330, 474, 1348], [98, 1351, 157, 1376], [192, 1351, 360, 1375], [98, 1376, 157, 1403], [188, 1370, 263, 1407], [100, 1403, 157, 1428], [192, 1403, 412, 1428], [100, 1428, 157, 1453], [190, 1428, 262, 1455], [98, 1453, 157, 1480], [190, 1453, 279, 1480], [98, 1478, 157, 1505], [192, 1480, 316, 1505], [98, 1505, 157, 1530], [192, 1505, 367, 1530], [98, 1530, 157, 1555], [192, 1530, 325, 1555], [98, 1555, 157, 1582], [192, 1557, 430, 1580], [98, 1580, 157, 1607], [192, 1582, 353, 1607], [98, 1607, 157, 1632], [194, 1609, 430, 1632], [100, 1632, 157, 1657], [192, 1634, 402, 1657], [100, 1657, 157, 1684], [192, 1659, 330, 1684], [100, 1682, 157, 1709], [190, 1682, 297, 1712]]}}]}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//8c407c38-3c3d-4e68-98ec-2969d7f90a92/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T09%3A40%3A31Z%2F-1%2F%2Fc3b1e6474f80262e16866e741ba5740f8052538823cd2306920fca43d6d1ca95", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//8c407c38-3c3d-4e68-98ec-2969d7f90a92/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T09%3A40%3A31Z%2F-1%2F%2F89e5e3dc992fe6b3e10495faf3e6033a827fc55edac54cc6fcbb2d7e3358cb38", "table_cell_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//8c407c38-3c3d-4e68-98ec-2969d7f90a92/table_cell_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T09%3A40%3A32Z%2F-1%2F%2F0760732fa2495dd722095308b35a07e99eb0cbaadd28be75a58403d2bde3bced"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//8c407c38-3c3d-4e68-98ec-2969d7f90a92/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-07-01T09%3A40%3A31Z%2F-1%2F%2F03b84e1d46743deb745e52ebd1340880e5c8a9a836415909819625d0b239df51"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}