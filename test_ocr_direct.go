package main

import (
	"context"
	"fmt"
	"log"
	"path/filepath"

	"MagneticOperator/app/services"
	"MagneticOperator/app/utils"
)

func main() {
	// 初始化日志
	utils.InitLogger()

	// 创建配置服务
	configService := services.NewConfigService(context.Background())

	// 加载配置
	_, err := configService.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 创建OCR服务
	ocrService := services.NewOCRService(configService, nil)

	// 验证OCR环境
	if err := ocrService.ValidateOCREnvironment(); err != nil {
		log.Fatalf("OCR环境验证失败: %v", err)
	}

	fmt.Println("OCR环境验证通过")

	// 测试图片路径
	imagePath := filepath.Join("pic", "temp", "王明阳+250707004", "temp_screenshot_auto_王明阳_20250707_103246.png")

	fmt.Printf("开始测试OCR，图片路径: %s\n", imagePath)

	// 处理图片
	result, err := ocrService.ProcessImageWithDetails(context.Background(), imagePath)
	if err != nil {
		log.Fatalf("OCR处理失败: %v", err)
	}

	fmt.Printf("OCR处理成功!\n")
	fmt.Printf("器官名称: %s\n", result.OrganName)
	fmt.Printf("置信度: %.2f\n", result.Confidence)
	fmt.Printf("图片路径: %s\n", result.ImagePath)
	fmt.Printf("键值对数量: %d\n", len(result.KeyValuePairs))

	fmt.Println("\n键值对详情:")
	for key, value := range result.KeyValuePairs {
		fmt.Printf("  %s: %s\n", key, value)
	}
}
