{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.363+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":52464}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.211+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":47848}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.221+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.221+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.221+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.221+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.222+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.483+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":41824}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.503+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.504+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.504+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.504+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.504+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.422+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.422+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":23160}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.510+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.511+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.511+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.511+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.511+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.664+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.665+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.665+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.666+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.666+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.666+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.671+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.674+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.674+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.674+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.674+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.677+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.677+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.678+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.678+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.679+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.679+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.679+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.679+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.279+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.342+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.351+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.352+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.352+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.365+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.371+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.371+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.376+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.410+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.410+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.410+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.410+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.411+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.411+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.411+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.412+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.987+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.987+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.987+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:48.641+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:48.641+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T22:56:48.648+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:48.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:49.065+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.447+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.447+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_B_1751381818448177200_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_B_1751381818448177200_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751381818448177200_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751381818448177200_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_B_1751381818448177200_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_B_1751381818448177200_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.631+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.632+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.851+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.851+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.851+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T22:56:59.240+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:59.441+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_C_1751381822547132900_test-11200","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_C_1751381822547132900_test-11200","taskType":"screenshot_C","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751381822547132900_test-11200","taskType":"screenshot_C","userName":"test-11200","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751381822547132900_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_C_1751381822547132900_test-11200","taskType":"screenshot_C","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.548+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.548+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_C_1751381822547132900_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-C","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.768+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式C] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.768+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.954+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.954+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.954+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: C, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T22:57:03.357+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:03.544+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.905+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.905+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-01T22:57:21.905+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.905+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.892+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.892+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.892+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.893+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.251+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 40, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250701_225659.png\n"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.538+0800","caller":"utils/logger.go:94","msg":"颜色检测成功完成","tempFilePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_225659.png"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.545+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"B"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.549+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.549+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_225659.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751381848549339000"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.553+0800","caller":"utils/logger.go:94","msg":"更新操作状态","status":"B02生化平衡分析已加入队列，等待C03病理形态学分析启动...","mode":"B","round":1,"completed":true}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.553+0800","caller":"utils/logger.go:94","msg":"轮次数据更新","userName":"test-11200","currentRound":1,"mode":"B","organName":"腹部第1腰椎水平截面","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751381848549339000"}
{"level":"INFO","timestamp":"2025-07-01T22:57:29.130+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T22:57:29.130+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-01T22:57:29.130+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T22:57:29.131+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T22:57:55.187+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_test-11200_20250701_225703.png\n"}
{"level":"INFO","timestamp":"2025-07-01T22:57:55.187+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"C"}
{"level":"INFO","timestamp":"2025-07-01T22:57:55.190+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T22:57:55.190+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"C","imagePath":"pic\\temp\\temp_screenshot_C_test-11200_20250701_225703.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_C_1751381875190448800"}
{"level":"INFO","timestamp":"2025-07-01T23:01:46.678+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":49024}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.945+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.945+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.946+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.946+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.946+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.087+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.089+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.091+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.092+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.092+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.092+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.097+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.098+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.098+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.708+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.708+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.708+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.322+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.322+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.323+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.326+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.327+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.327+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.327+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.329+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.330+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.507+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.552+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.693+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.796+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.797+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.942+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T22:56:45.422+0800"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"23160"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":23160}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":23160}
{"level":"WARN","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":23160,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.944+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":23160}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.944+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.944+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.944+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":46956}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.017+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.018+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.018+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.018+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.018+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.465+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.465+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.466+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.466+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.466+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.466+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.472+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.478+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.685+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.752+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.762+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.762+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.762+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.773+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.777+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.777+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.789+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.957+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.957+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.957+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.115+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.115+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.117+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.117+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.117+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.242+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.242+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.248+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.450+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.633+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:31.829+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:31.829+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T23:03:31.830+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T23:03:31.830+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:03:31.830+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.135+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.135+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_B_1751382212135734000_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_B_1751382212135734000_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751382212135734000_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_B_1751382212135734000_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751382212135734000_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_B_1751382212135734000_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.365+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.365+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.582+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.582+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.582+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T23:03:33.020+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:33.200+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.067+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.067+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.068+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.068+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.068+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_C_1751382217276990800_test-11200","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_C_1751382217276990800_test-11200","taskType":"screenshot_C","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751382217276990800_test-11200","taskType":"screenshot_C","userName":"test-11200","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751382217276990800_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_C_1751382217276990800_test-11200","taskType":"screenshot_C","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.277+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_C_1751382217276990800_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.277+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.277+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-C","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.278+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.480+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式C] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.480+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.658+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.658+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.659+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: C, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T23:03:38.057+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:38.298+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.596+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.596+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.596+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.597+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.597+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.869+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.869+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-01T23:03:55.870+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.870+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.546+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.546+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.546+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.546+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.803+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.803+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-01T23:03:59.804+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.804+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:04:02.701+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 消化系统--胰腺；十二指肠；正面图, 键值对数量: 28, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250701_230332.png\n"}
{"level":"INFO","timestamp":"2025-07-01T23:04:03.018+0800","caller":"utils/logger.go:94","msg":"颜色检测成功完成","tempFilePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_230332.png"}
{"level":"INFO","timestamp":"2025-07-01T23:04:03.026+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"B"}
{"level":"INFO","timestamp":"2025-07-01T23:04:03.028+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T23:04:03.028+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_230332.png","organName":"消化系统--胰腺；十二指肠；正面图","operationID":"test-11200_B_1751382243028649700"}
{"level":"INFO","timestamp":"2025-07-01T23:04:03.032+0800","caller":"utils/logger.go:94","msg":"更新操作状态","status":"B02生化平衡分析已加入队列，等待C03病理形态学分析启动...","mode":"B","round":1,"completed":true}
{"level":"INFO","timestamp":"2025-07-01T23:04:03.032+0800","caller":"utils/logger.go:94","msg":"轮次数据更新","userName":"test-11200","currentRound":1,"mode":"B","organName":"消化系统--胰腺；十二指肠；正面图","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751382243028649700"}
{"level":"INFO","timestamp":"2025-07-01T23:04:28.689+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 消化系统--胰腺；十二指肠；正面图, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_test-11200_20250701_230337.png\n"}
{"level":"INFO","timestamp":"2025-07-01T23:04:28.690+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"C"}
{"level":"INFO","timestamp":"2025-07-01T23:04:28.692+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T23:04:28.692+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"C","imagePath":"pic\\temp\\temp_screenshot_C_test-11200_20250701_230337.png","organName":"消化系统--胰腺；十二指肠；正面图","operationID":"test-11200_C_1751382268692770100"}
{"level":"INFO","timestamp":"2025-07-01T23:07:59.477+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T23:12:59.477+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T23:17:59.477+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T23:22:05.084+0800","caller":"utils/logger.go:94","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-07-01T23:22:05.084+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-01T23:22:05.084+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:22:05.084+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:22:05.084+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:22:05.084+0800","caller":"utils/logger.go:94","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":25112}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.104+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.104+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.104+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.104+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.104+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.112+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.112+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.112+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.112+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.113+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:30:33.303+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
2025-07-01 23:30:33.303687 +0800 CST m=+0.021747901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.317+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
2025-07-01 23:30:33.3178868 +0800 CST m=+0.035947701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.328+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
2025-07-01 23:30:33.3286616 +0800 CST m=+0.046722501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.340+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
2025-07-01 23:30:33.3401871 +0800 CST m=+0.058248001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.351+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-01 23:30:33.3510266 +0800 CST m=+0.069087501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.375+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
2025-07-01 23:30:33.375292 +0800 CST m=+0.093352901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.397+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
2025-07-01 23:30:33.3973993 +0800 CST m=+0.115460201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.419+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22828}
2025-07-01 23:30:33.4196514 +0800 CST m=+0.137712301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.474+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
2025-07-01 23:30:33.4740198 +0800 CST m=+0.192080701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.497+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
2025-07-01 23:30:33.4971264 +0800 CST m=+0.215187301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.519+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
2025-07-01 23:30:33.5197404 +0800 CST m=+0.237801301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.541+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
2025-07-01 23:30:33.5419716 +0800 CST m=+0.260032501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.564+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
2025-07-01 23:30:33.5641221 +0800 CST m=+0.282183001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.062+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
2025-07-01 23:30:34.0629004 +0800 CST m=+0.780961301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.086+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
2025-07-01 23:30:34.0866257 +0800 CST m=+0.804686601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.108+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
2025-07-01 23:30:34.108734 +0800 CST m=+0.826794901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.130+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-01 23:30:34.1308921 +0800 CST m=+0.848953001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.153+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
2025-07-01 23:30:34.1531726 +0800 CST m=+0.871233501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.174+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-01 23:30:34.174938 +0800 CST m=+0.892998901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.202+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
2025-07-01 23:30:34.2022764 +0800 CST m=+0.920337301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.218+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
2025-07-01 23:30:34.2184208 +0800 CST m=+0.936481701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.242+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
2025-07-01 23:30:34.2422256 +0800 CST m=+0.960286501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.264+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
2025-07-01 23:30:34.2640062 +0800 CST m=+0.982067101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.273+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
2025-07-01 23:30:34.2738133 +0800 CST m=+0.991874201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.276+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
2025-07-01 23:30:34.2767377 +0800 CST m=+0.994798601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.307+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
2025-07-01 23:30:34.3076377 +0800 CST m=+1.025698601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.329+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
2025-07-01 23:30:34.3298203 +0800 CST m=+1.047881201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.334+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
2025-07-01 23:30:34.3347201 +0800 CST m=+1.052781001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.340+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:30:34.344+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
2025-07-01 23:30:34.340672 +0800 CST m=+1.058732901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.360+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
2025-07-01 23:30:34.3448004 +0800 CST m=+1.062861301 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:30:34.3603949 +0800 CST m=+1.078455801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.363+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T23:30:34.374+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
2025-07-01 23:30:34.3630253 +0800 CST m=+1.081086201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.377+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
2025-07-01 23:30:34.374394 +0800 CST m=+1.092454901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.385+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
2025-07-01 23:30:34.37705 +0800 CST m=+1.095110901 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:30:34.3857674 +0800 CST m=+1.103828301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.397+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:30:34.407+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
2025-07-01 23:30:34.3970856 +0800 CST m=+1.115146501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.408+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
2025-07-01 23:30:34.4077258 +0800 CST m=+1.125786701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.422+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:30:34.4082287 +0800 CST m=+1.126289601 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:30:34.4223802 +0800 CST m=+1.140441101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.430+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
2025-07-01 23:30:34.430462 +0800 CST m=+1.148522901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.452+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
2025-07-01 23:30:34.4521872 +0800 CST m=+1.170248101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.463+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
2025-07-01 23:30:34.4638628 +0800 CST m=+1.181923701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.474+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
2025-07-01 23:30:34.4746893 +0800 CST m=+1.192750201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.486+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
2025-07-01 23:30:34.486159 +0800 CST m=+1.204219901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.496+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
2025-07-01 23:30:34.4969569 +0800 CST m=+1.215017801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.508+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
2025-07-01 23:30:34.5085185 +0800 CST m=+1.226579401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.519+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
2025-07-01 23:30:34.519423 +0800 CST m=+1.237483901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.530+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
2025-07-01 23:30:34.5309327 +0800 CST m=+1.248993601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.541+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
2025-07-01 23:30:34.5417539 +0800 CST m=+1.259814801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.553+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:30:34.553+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
2025-07-01 23:30:34.5531303 +0800 CST m=+1.271191201 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:30:34.5531303 +0800 CST m=+1.271191201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.575+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:30:34.575+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
2025-07-01 23:30:34.5755058 +0800 CST m=+1.293566701 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:30:34.5755058 +0800 CST m=+1.293566701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.597+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T23:30:34.597+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
2025-07-01 23:30:34.5978496 +0800 CST m=+1.315910501 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:30:34.5978496 +0800 CST m=+1.315910501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.608+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
2025-07-01 23:30:34.6088064 +0800 CST m=+1.326867301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.631+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
2025-07-01 23:30:34.6312755 +0800 CST m=+1.349336401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.642+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
2025-07-01 23:30:34.6420431 +0800 CST m=+1.360104001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.653+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
2025-07-01 23:30:34.6535343 +0800 CST m=+1.371595201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.664+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
2025-07-01 23:30:34.6647098 +0800 CST m=+1.382770701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:50.807+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
2025-07-01 23:31:50.8074727 +0800 CST m=+77.525533601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.183+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
2025-07-01 23:31:51.1832083 +0800 CST m=+77.901269201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.194+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
2025-07-01 23:31:51.1942824 +0800 CST m=+77.912343301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.205+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
2025-07-01 23:31:51.2051079 +0800 CST m=+77.923168801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.216+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
2025-07-01 23:31:51.2163615 +0800 CST m=+77.934422401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.248+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
2025-07-01 23:31:51.2489439 +0800 CST m=+77.967004801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.261+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
2025-07-01 23:31:51.2611308 +0800 CST m=+77.979191701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.272+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
2025-07-01 23:31:51.2723331 +0800 CST m=+77.990394001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.283+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
2025-07-01 23:31:51.2831797 +0800 CST m=+78.001240601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.294+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-01 23:31:51.2946097 +0800 CST m=+78.012670601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.206+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":6900}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.226+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.227+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.227+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.227+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.227+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.234+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.234+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.234+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.234+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.235+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.065+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":1108}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.089+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.089+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.089+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.089+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.089+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.098+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.098+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.098+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.098+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.098+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.972+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.973+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.973+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.973+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.973+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.973+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.973+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.974+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":47416}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.047+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.047+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.047+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.047+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.047+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.472+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.473+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.474+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.474+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.474+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.474+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.479+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.481+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.481+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.485+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.485+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.485+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.485+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.032+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.098+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.109+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.109+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.109+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.125+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.129+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.129+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.135+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.313+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.313+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.313+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.313+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.313+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.336+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.336+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.336+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.336+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.336+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:34:47.172+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
2025-07-01 23:34:47.1724329 +0800 CST m=+0.019645201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:47.645+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
2025-07-01 23:34:47.6452154 +0800 CST m=+0.492427701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:47.667+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
2025-07-01 23:34:47.6675648 +0800 CST m=+0.514777101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:47.695+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
2025-07-01 23:34:47.6956024 +0800 CST m=+0.542814701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:47.723+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-01 23:34:47.7231438 +0800 CST m=+0.570356101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:47.740+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
2025-07-01 23:34:47.7402406 +0800 CST m=+0.587452901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:47.767+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
2025-07-01 23:34:47.7675925 +0800 CST m=+0.614804801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:47.784+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":48816}
2025-07-01 23:34:47.7845981 +0800 CST m=+0.631810401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:47.856+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
2025-07-01 23:34:47.8563451 +0800 CST m=+0.703557401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:47.884+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
2025-07-01 23:34:47.8847179 +0800 CST m=+0.731930201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:47.912+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
2025-07-01 23:34:47.9121384 +0800 CST m=+0.759350701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:47.940+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
2025-07-01 23:34:47.9403366 +0800 CST m=+0.787548901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:47.967+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
2025-07-01 23:34:47.967785 +0800 CST m=+0.814997301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.395+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
2025-07-01 23:34:48.3959408 +0800 CST m=+1.243153101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.423+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
2025-07-01 23:34:48.4233437 +0800 CST m=+1.270556001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.441+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
2025-07-01 23:34:48.4411638 +0800 CST m=+1.288376101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.467+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-01 23:34:48.467379 +0800 CST m=+1.314591301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.496+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
2025-07-01 23:34:48.496048 +0800 CST m=+1.343260301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.523+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-01 23:34:48.5236529 +0800 CST m=+1.370865201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.545+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
2025-07-01 23:34:48.5453924 +0800 CST m=+1.392604701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.549+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
2025-07-01 23:34:48.5499905 +0800 CST m=+1.397202801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.568+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
2025-07-01 23:34:48.5684499 +0800 CST m=+1.415662201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.608+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
2025-07-01 23:34:48.6084958 +0800 CST m=+1.455708101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.612+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:34:48.615+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
2025-07-01 23:34:48.6120772 +0800 CST m=+1.459289501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.636+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
2025-07-01 23:34:48.6151653 +0800 CST m=+1.462377601 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:34:48.6365539 +0800 CST m=+1.483766201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.657+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
2025-07-01 23:34:48.657009 +0800 CST m=+1.504221301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.674+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:34:48.680+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
2025-07-01 23:34:48.674052 +0800 CST m=+1.521264301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.701+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
2025-07-01 23:34:48.6800626 +0800 CST m=+1.527274901 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:34:48.7014812 +0800 CST m=+1.548693501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.718+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:34:48.745+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
2025-07-01 23:34:48.7185271 +0800 CST m=+1.565739401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.745+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
2025-07-01 23:34:48.7459099 +0800 CST m=+1.593122201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.765+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:34:48.7459099 +0800 CST m=+1.593122201 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:34:48.7656584 +0800 CST m=+1.612870701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.790+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
2025-07-01 23:34:48.7904371 +0800 CST m=+1.637649401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.834+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
2025-07-01 23:34:48.8349078 +0800 CST m=+1.682120101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.851+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
2025-07-01 23:34:48.8519822 +0800 CST m=+1.699194501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.879+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
2025-07-01 23:34:48.8793717 +0800 CST m=+1.726584001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.896+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
2025-07-01 23:34:48.8963894 +0800 CST m=+1.743601701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.923+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
2025-07-01 23:34:48.9238659 +0800 CST m=+1.771078201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.940+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
2025-07-01 23:34:48.9409943 +0800 CST m=+1.788206601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.968+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
2025-07-01 23:34:48.9683752 +0800 CST m=+1.815587501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:48.985+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
2025-07-01 23:34:48.9855245 +0800 CST m=+1.832736801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.012+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
2025-07-01 23:34:49.0128477 +0800 CST m=+1.860060001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.029+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
2025-07-01 23:34:49.0299881 +0800 CST m=+1.877200401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.057+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
2025-07-01 23:34:49.0573503 +0800 CST m=+1.904562601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.074+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
2025-07-01 23:34:49.0745288 +0800 CST m=+1.921741101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.101+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
2025-07-01 23:34:49.1018363 +0800 CST m=+1.949048601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.118+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
2025-07-01 23:34:49.1189996 +0800 CST m=+1.966211901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.146+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:34:49.146+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
2025-07-01 23:34:49.1463103 +0800 CST m=+1.993522601 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:34:49.1463103 +0800 CST m=+1.993522601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.163+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:34:49.163+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
2025-07-01 23:34:49.1634564 +0800 CST m=+2.010668701 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:34:49.1634564 +0800 CST m=+2.010668701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.207+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T23:34:49.207+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
2025-07-01 23:34:49.2079883 +0800 CST m=+2.055200601 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:34:49.2079883 +0800 CST m=+2.055200601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.279+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
2025-07-01 23:34:49.279768 +0800 CST m=+2.126980301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.296+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
2025-07-01 23:34:49.2969593 +0800 CST m=+2.144171601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.324+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
2025-07-01 23:34:49.3242423 +0800 CST m=+2.171454601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.341+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
2025-07-01 23:34:49.3414787 +0800 CST m=+2.188691001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:34:49.368+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
2025-07-01 23:34:49.3687532 +0800 CST m=+2.215965501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:32.447+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:35:32.4471189 +0800 CST m=+45.294331201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:32.447+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:35:32.447+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:35:32.4471189 +0800 CST m=+45.294331201 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:35:32.4471189 +0800 CST m=+45.294331201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.000+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
2025-07-01 23:35:33.0008194 +0800 CST m=+45.848031701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.024+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
2025-07-01 23:35:33.0243793 +0800 CST m=+45.871591601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.052+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
2025-07-01 23:35:33.052066 +0800 CST m=+45.899278301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.080+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
2025-07-01 23:35:33.0800058 +0800 CST m=+45.927218101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.108+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
2025-07-01 23:35:33.1080557 +0800 CST m=+45.955268001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.135+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
2025-07-01 23:35:33.1353999 +0800 CST m=+45.982612201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.163+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
2025-07-01 23:35:33.1636326 +0800 CST m=+46.010844901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.191+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
2025-07-01 23:35:33.1911893 +0800 CST m=+46.038401601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.219+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
2025-07-01 23:35:33.2192146 +0800 CST m=+46.066426901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.246+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
2025-07-01 23:35:33.2464084 +0800 CST m=+46.093620701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.274+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
2025-07-01 23:35:33.2748805 +0800 CST m=+46.122092801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.302+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
2025-07-01 23:35:33.3023747 +0800 CST m=+46.149587001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.330+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
2025-07-01 23:35:33.3304761 +0800 CST m=+46.177688401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.358+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
2025-07-01 23:35:33.3580431 +0800 CST m=+46.205255401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.631+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:35:33.631058 +0800 CST m=+46.478270301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.658+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
2025-07-01 23:35:33.6580711 +0800 CST m=+46.505283401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.687+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:35:33.6871514 +0800 CST m=+46.534363701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:33.896+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:35:33.8964719 +0800 CST m=+46.743684201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:35:34.114+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:35:34.1148731 +0800 CST m=+46.962085401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:22.970+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.970+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.970+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.970+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.970+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.970+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.970+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.970+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":45752}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.989+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.989+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.989+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.989+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.989+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.997+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.997+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.997+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.997+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:36:22.997+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:36:36.393+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
2025-07-01 23:36:36.393224 +0800 CST m=+0.019588501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:36.405+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
2025-07-01 23:36:36.405808 +0800 CST m=+0.032172501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:36.431+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
2025-07-01 23:36:36.431698 +0800 CST m=+0.058062501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:36.449+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
2025-07-01 23:36:36.4490197 +0800 CST m=+0.075384201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:36.476+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-01 23:36:36.4763012 +0800 CST m=+0.102665701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:36.494+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T23:34:47.812+0800"}
2025-07-01 23:36:36.4944683 +0800 CST m=+0.120832801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:36.520+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"48816"}
2025-07-01 23:36:36.5208525 +0800 CST m=+0.147217001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:36.538+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":48816}
2025-07-01 23:36:36.538669 +0800 CST m=+0.165033501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:36.564+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":48816}
2025-07-01 23:36:36.5646282 +0800 CST m=+0.190992701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:36.583+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
2025-07-01 23:36:36.5834444 +0800 CST m=+0.209808901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:36.609+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 48816\" /NH"}
2025-07-01 23:36:36.6098172 +0800 CST m=+0.236181701 write error: write /dev/stdout: The handle is invalid.
{"level":"WARN","timestamp":"2025-07-01T23:36:37.354+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
2025-07-01 23:36:37.3549586 +0800 CST m=+0.981323101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:37.372+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":48816}
2025-07-01 23:36:37.3723675 +0800 CST m=+0.998732001 write error: write /dev/stdout: The handle is invalid.
{"level":"WARN","timestamp":"2025-07-01T23:36:37.398+0800","caller":"utils/logger.go:101","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
2025-07-01 23:36:37.3988214 +0800 CST m=+1.025185901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:37.416+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
2025-07-01 23:36:37.4169293 +0800 CST m=+1.043293801 write error: write /dev/stdout: The handle is invalid.
{"level":"ERROR","timestamp":"2025-07-01T23:36:37.443+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
2025-07-01 23:36:37.443266 +0800 CST m=+1.069630501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:37.561+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":48816}
2025-07-01 23:36:37.5616962 +0800 CST m=+1.188060701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:37.587+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
2025-07-01 23:36:37.5877795 +0800 CST m=+1.214144001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:36:37.605+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 48816\" /NH"}
2025-07-01 23:36:37.6058745 +0800 CST m=+1.232239001 write error: write /dev/stdout: The handle is invalid.
{"level":"WARN","timestamp":"2025-07-01T23:36:38.317+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
2025-07-01 23:36:38.3178019 +0800 CST m=+1.944166401 write error: write /dev/stdout: The handle is invalid.
{"level":"WARN","timestamp":"2025-07-01T23:36:38.328+0800","caller":"utils/logger.go:101","msg":"单实例检查失败，程序退出"}
2025-07-01 23:36:38.3280465 +0800 CST m=+1.954411001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:37:23.101+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
2025-07-01 23:37:23.1011586 +0800 CST m=+155.948370901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:37:23.593+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
2025-07-01 23:37:23.5933787 +0800 CST m=+156.440591001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:37:23.615+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
2025-07-01 23:37:23.6154347 +0800 CST m=+156.462647001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:37:23.643+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
2025-07-01 23:37:23.6434543 +0800 CST m=+156.490666601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:37:23.671+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
2025-07-01 23:37:23.671002 +0800 CST m=+156.518214301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:37:23.698+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
2025-07-01 23:37:23.6986867 +0800 CST m=+156.545899001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:37:23.747+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
2025-07-01 23:37:23.7470384 +0800 CST m=+156.594250701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:37:23.776+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
2025-07-01 23:37:23.7765479 +0800 CST m=+156.623760201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:37:23.804+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
2025-07-01 23:37:23.8044383 +0800 CST m=+156.651650601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:23.963+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.964+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.964+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.964+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.964+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.964+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.964+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.964+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":51304}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.966+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.966+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.966+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.966+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.966+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.974+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.974+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.974+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.974+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:44:23.974+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:44:37.336+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
2025-07-01 23:44:37.3368928 +0800 CST m=+0.020052401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.357+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
2025-07-01 23:44:37.3571393 +0800 CST m=+0.040298901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.374+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
2025-07-01 23:44:37.3743293 +0800 CST m=+0.057488901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.401+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
2025-07-01 23:44:37.4015837 +0800 CST m=+0.084743301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.418+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-01 23:44:37.4187922 +0800 CST m=+0.101951801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.446+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T23:34:47.812+0800"}
2025-07-01 23:44:37.4460696 +0800 CST m=+0.129229201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.463+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"48816"}
2025-07-01 23:44:37.4633228 +0800 CST m=+0.146482401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.490+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":48816}
2025-07-01 23:44:37.4905853 +0800 CST m=+0.173744901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.507+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":48816}
2025-07-01 23:44:37.5073707 +0800 CST m=+0.190530301 write error: write /dev/stdout: The handle is invalid.
{"level":"WARN","timestamp":"2025-07-01T23:44:37.535+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":48816,"error":"OpenProcess: The parameter is incorrect."}
2025-07-01 23:44:37.535082 +0800 CST m=+0.218241601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.552+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":48816}
2025-07-01 23:44:37.5522932 +0800 CST m=+0.235452801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.579+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
2025-07-01 23:44:37.5795568 +0800 CST m=+0.262716401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.596+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
2025-07-01 23:44:37.5967933 +0800 CST m=+0.279952901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.624+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":40024}
2025-07-01 23:44:37.6240613 +0800 CST m=+0.307220901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.679+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
2025-07-01 23:44:37.6790954 +0800 CST m=+0.362255001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.696+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
2025-07-01 23:44:37.6968846 +0800 CST m=+0.380044201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.724+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
2025-07-01 23:44:37.7241291 +0800 CST m=+0.407288701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.741+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
2025-07-01 23:44:37.7413736 +0800 CST m=+0.424533201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:37.768+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
2025-07-01 23:44:37.7686181 +0800 CST m=+0.451777701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.224+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
2025-07-01 23:44:38.2244341 +0800 CST m=+0.907593701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.246+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
2025-07-01 23:44:38.2465167 +0800 CST m=+0.929676301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.263+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
2025-07-01 23:44:38.2636784 +0800 CST m=+0.946838001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.290+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-01 23:44:38.2909815 +0800 CST m=+0.974141101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.308+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
2025-07-01 23:44:38.3081891 +0800 CST m=+0.991348701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.334+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-01 23:44:38.3348821 +0800 CST m=+1.018041701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.357+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
2025-07-01 23:44:38.3573103 +0800 CST m=+1.040469901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.380+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
2025-07-01 23:44:38.3804194 +0800 CST m=+1.063579001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.397+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
2025-07-01 23:44:38.3971113 +0800 CST m=+1.080270901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.406+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
2025-07-01 23:44:38.4063519 +0800 CST m=+1.089511501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.424+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
2025-07-01 23:44:38.4243779 +0800 CST m=+1.107537501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.459+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
2025-07-01 23:44:38.4590153 +0800 CST m=+1.142174901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.464+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T23:44:38.473+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
2025-07-01 23:44:38.4641327 +0800 CST m=+1.147292301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.485+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
2025-07-01 23:44:38.473017 +0800 CST m=+1.156176601 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:44:38.4858911 +0800 CST m=+1.169050701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.510+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T23:44:38.530+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
2025-07-01 23:44:38.5104401 +0800 CST m=+1.193599701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.558+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
2025-07-01 23:44:38.5308437 +0800 CST m=+1.214003301 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:44:38.5588161 +0800 CST m=+1.241975701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.588+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T23:44:38.614+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
2025-07-01 23:44:38.5883185 +0800 CST m=+1.271478101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.641+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
2025-07-01 23:44:38.6144565 +0800 CST m=+1.297616101 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:44:38.6419366 +0800 CST m=+1.325096201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.669+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T23:44:38.697+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
2025-07-01 23:44:38.6698052 +0800 CST m=+1.352964801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.699+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:44:38.6974777 +0800 CST m=+1.380637301 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:44:38.6990177 +0800 CST m=+1.382177301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.742+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
2025-07-01 23:44:38.7421044 +0800 CST m=+1.425264001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.797+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
2025-07-01 23:44:38.7975792 +0800 CST m=+1.480738801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.825+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
2025-07-01 23:44:38.825747 +0800 CST m=+1.508906601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.853+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
2025-07-01 23:44:38.8531821 +0800 CST m=+1.536341701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.881+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
2025-07-01 23:44:38.8812982 +0800 CST m=+1.564457801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.908+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
2025-07-01 23:44:38.9087791 +0800 CST m=+1.591938701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.936+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
2025-07-01 23:44:38.9364726 +0800 CST m=+1.619632201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.964+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
2025-07-01 23:44:38.9643984 +0800 CST m=+1.647558001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:38.992+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
2025-07-01 23:44:38.9924812 +0800 CST m=+1.675640801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.019+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
2025-07-01 23:44:39.0199698 +0800 CST m=+1.703129401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.037+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
2025-07-01 23:44:39.037263 +0800 CST m=+1.720422601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.064+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
2025-07-01 23:44:39.0645047 +0800 CST m=+1.747664301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.081+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:44:39.081+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
2025-07-01 23:44:39.0817629 +0800 CST m=+1.764922501 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:44:39.0817629 +0800 CST m=+1.764922501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.108+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:44:39.108+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
2025-07-01 23:44:39.1089909 +0800 CST m=+1.792150501 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:44:39.1089909 +0800 CST m=+1.792150501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.153+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T23:44:39.153+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
2025-07-01 23:44:39.1534977 +0800 CST m=+1.836657301 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:44:39.1534977 +0800 CST m=+1.836657301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.215+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
2025-07-01 23:44:39.2152324 +0800 CST m=+1.898392001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.242+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
2025-07-01 23:44:39.2424751 +0800 CST m=+1.925634701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.259+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
2025-07-01 23:44:39.2597407 +0800 CST m=+1.942900301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.286+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
2025-07-01 23:44:39.2869516 +0800 CST m=+1.970111201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.304+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
2025-07-01 23:44:39.3042249 +0800 CST m=+1.987384501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.433+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:44:39.4336096 +0800 CST m=+2.116769201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.434+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:44:39.434+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:44:39.434138 +0800 CST m=+2.117297601 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:44:39.434138 +0800 CST m=+2.117297601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:39.983+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
2025-07-01 23:44:39.983151 +0800 CST m=+2.666310601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.009+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
2025-07-01 23:44:40.0093458 +0800 CST m=+2.692505401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.026+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
2025-07-01 23:44:40.0265518 +0800 CST m=+2.709711401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.052+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
2025-07-01 23:44:40.0528117 +0800 CST m=+2.735971301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.071+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
2025-07-01 23:44:40.0711746 +0800 CST m=+2.754334201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.097+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
2025-07-01 23:44:40.0974452 +0800 CST m=+2.780604801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.115+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
2025-07-01 23:44:40.1155531 +0800 CST m=+2.798712701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.142+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
2025-07-01 23:44:40.1428052 +0800 CST m=+2.825964801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.159+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:44:40.1590212 +0800 CST m=+2.842180801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.159+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
2025-07-01 23:44:40.159536 +0800 CST m=+2.842695601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.187+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
2025-07-01 23:44:40.1872736 +0800 CST m=+2.870433201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.215+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
2025-07-01 23:44:40.2153544 +0800 CST m=+2.898514001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.244+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:44:40.2445348 +0800 CST m=+2.927694401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.271+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
2025-07-01 23:44:40.2716539 +0800 CST m=+2.954813501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.327+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
2025-07-01 23:44:40.3272138 +0800 CST m=+3.010373401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.354+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
2025-07-01 23:44:40.3549541 +0800 CST m=+3.038113701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.382+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
2025-07-01 23:44:40.3828416 +0800 CST m=+3.066001201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.479+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:44:40.4790855 +0800 CST m=+3.162245101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:44:40.754+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:44:40.754893 +0800 CST m=+3.438052601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:45:06.208+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.209+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.209+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.209+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.209+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.209+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.209+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.209+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":5800}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.211+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.211+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.211+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.211+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.211+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.218+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.218+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.218+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.218+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:45:06.218+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.198+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.198+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.198+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.198+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.198+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.198+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.198+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.199+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":16032}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.217+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.218+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.218+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.218+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.218+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.226+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.226+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.226+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.226+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:45:12.226+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.781+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.781+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.782+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.782+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.782+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.782+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T23:44:37.641+0800"}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.782+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"40024"}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.782+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":40024}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.782+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":40024}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.782+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.782+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 40024\" /NH"}
{"level":"WARN","timestamp":"2025-07-01T23:45:14.806+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.806+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":40024}
{"level":"WARN","timestamp":"2025-07-01T23:45:14.806+0800","caller":"utils/logger.go:101","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.806+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"ERROR","timestamp":"2025-07-01T23:45:14.806+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.907+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":40024}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.907+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-01T23:45:14.907+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 40024\" /NH"}
{"level":"WARN","timestamp":"2025-07-01T23:45:14.928+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"WARN","timestamp":"2025-07-01T23:45:14.928+0800","caller":"utils/logger.go:101","msg":"单实例检查失败，程序退出"}
{"level":"INFO","timestamp":"2025-07-01T23:46:24.616+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
2025-07-01 23:46:24.6166859 +0800 CST m=+107.299845501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:24.670+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
2025-07-01 23:46:24.670997 +0800 CST m=+107.354156601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:24.692+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
2025-07-01 23:46:24.692181 +0800 CST m=+107.375340601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:24.710+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
2025-07-01 23:46:24.710028 +0800 CST m=+107.393187601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:24.736+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
2025-07-01 23:46:24.7366418 +0800 CST m=+107.419801401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:24.754+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
2025-07-01 23:46:24.7545404 +0800 CST m=+107.437700001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:24.807+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
2025-07-01 23:46:24.8076578 +0800 CST m=+107.490817401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:24.821+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
2025-07-01 23:46:24.8212438 +0800 CST m=+107.504403401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:24.847+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
2025-07-01 23:46:24.8478508 +0800 CST m=+107.531010401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:24.865+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
2025-07-01 23:46:24.8657099 +0800 CST m=+107.548869501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.574+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
2025-07-01 23:46:52.5742113 +0800 CST m=+0.021035801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.603+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
2025-07-01 23:46:52.6036583 +0800 CST m=+0.050482801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.621+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
2025-07-01 23:46:52.6216138 +0800 CST m=+0.068438301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.648+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
2025-07-01 23:46:52.6481685 +0800 CST m=+0.094993001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.666+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-01 23:46:52.6661615 +0800 CST m=+0.112986001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.692+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
2025-07-01 23:46:52.6926547 +0800 CST m=+0.139479201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.710+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
2025-07-01 23:46:52.7106199 +0800 CST m=+0.157444401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.737+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":54716}
2025-07-01 23:46:52.7371631 +0800 CST m=+0.183987601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.791+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
2025-07-01 23:46:52.7916973 +0800 CST m=+0.238521801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.810+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
2025-07-01 23:46:52.8107578 +0800 CST m=+0.257582301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.837+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
2025-07-01 23:46:52.8372487 +0800 CST m=+0.284073201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.855+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
2025-07-01 23:46:52.8552411 +0800 CST m=+0.302065601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:52.881+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
2025-07-01 23:46:52.881751 +0800 CST m=+0.328575501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.331+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
2025-07-01 23:46:53.3312821 +0800 CST m=+0.778106601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.359+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
2025-07-01 23:46:53.3596165 +0800 CST m=+0.806441001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.377+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
2025-07-01 23:46:53.3775605 +0800 CST m=+0.824385001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.404+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-01 23:46:53.4040298 +0800 CST m=+0.850854301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.422+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
2025-07-01 23:46:53.4220317 +0800 CST m=+0.868856201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.448+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-01 23:46:53.4483648 +0800 CST m=+0.895189301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.472+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
2025-07-01 23:46:53.4724484 +0800 CST m=+0.919272901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.482+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
2025-07-01 23:46:53.4821691 +0800 CST m=+0.928993601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.493+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
2025-07-01 23:46:53.493491 +0800 CST m=+0.940315501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.537+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
2025-07-01 23:46:53.5373241 +0800 CST m=+0.984148601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.540+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T23:46:53.550+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
2025-07-01 23:46:53.5409149 +0800 CST m=+0.987739401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.555+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
2025-07-01 23:46:53.5507529 +0800 CST m=+0.997577401 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:46:53.5553745 +0800 CST m=+1.002199001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.599+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
2025-07-01 23:46:53.5997216 +0800 CST m=+1.046546101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.599+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:46:53.603+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
2025-07-01 23:46:53.5997216 +0800 CST m=+1.046546101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.625+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
2025-07-01 23:46:53.6033356 +0800 CST m=+1.050160101 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:46:53.6258924 +0800 CST m=+1.072716901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.644+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:46:53.670+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
2025-07-01 23:46:53.6445938 +0800 CST m=+1.091418301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.672+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
2025-07-01 23:46:53.6709986 +0800 CST m=+1.117823101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.691+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:46:53.6725249 +0800 CST m=+1.119349401 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:46:53.6916117 +0800 CST m=+1.138436201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.715+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
2025-07-01 23:46:53.7155487 +0800 CST m=+1.162373201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.733+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
2025-07-01 23:46:53.7336174 +0800 CST m=+1.180441901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.759+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
2025-07-01 23:46:53.7599743 +0800 CST m=+1.206798801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.804+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
2025-07-01 23:46:53.8044599 +0800 CST m=+1.251284401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.822+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
2025-07-01 23:46:53.8225424 +0800 CST m=+1.269366901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.848+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
2025-07-01 23:46:53.8489765 +0800 CST m=+1.295801001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.867+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
2025-07-01 23:46:53.8670384 +0800 CST m=+1.313862901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.893+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
2025-07-01 23:46:53.8933254 +0800 CST m=+1.340149901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.911+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
2025-07-01 23:46:53.9115233 +0800 CST m=+1.358347801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.937+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
2025-07-01 23:46:53.9379264 +0800 CST m=+1.384750901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.956+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
2025-07-01 23:46:53.9560096 +0800 CST m=+1.402834101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:53.982+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
2025-07-01 23:46:53.9825592 +0800 CST m=+1.429383701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.000+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
2025-07-01 23:46:54.0005649 +0800 CST m=+1.447389401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.026+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
2025-07-01 23:46:54.0269135 +0800 CST m=+1.473738001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.045+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
2025-07-01 23:46:54.0450136 +0800 CST m=+1.491838101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.071+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:46:54.071+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
2025-07-01 23:46:54.071392 +0800 CST m=+1.518216501 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:46:54.071392 +0800 CST m=+1.518216501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.089+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:46:54.089+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
2025-07-01 23:46:54.0895308 +0800 CST m=+1.536355301 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:46:54.0895308 +0800 CST m=+1.536355301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.134+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T23:46:54.134+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
2025-07-01 23:46:54.1340293 +0800 CST m=+1.580853801 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:46:54.1340293 +0800 CST m=+1.580853801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.205+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
2025-07-01 23:46:54.2052472 +0800 CST m=+1.652071701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.249+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
2025-07-01 23:46:54.2497468 +0800 CST m=+1.696571301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.267+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
2025-07-01 23:46:54.2674938 +0800 CST m=+1.714318301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.294+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
2025-07-01 23:46:54.2942215 +0800 CST m=+1.741046001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.312+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
2025-07-01 23:46:54.3120803 +0800 CST m=+1.758904801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.386+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:46:54.386316 +0800 CST m=+1.833140501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.386+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:46:54.386+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:46:54.3868309 +0800 CST m=+1.833655401 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:46:54.3868309 +0800 CST m=+1.833655401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:54.994+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
2025-07-01 23:46:54.9948454 +0800 CST m=+2.441669901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.016+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
2025-07-01 23:46:55.0166858 +0800 CST m=+2.463510301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.034+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
2025-07-01 23:46:55.0343811 +0800 CST m=+2.481205601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.061+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
2025-07-01 23:46:55.0610732 +0800 CST m=+2.507897701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.078+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
2025-07-01 23:46:55.0784548 +0800 CST m=+2.525279301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.087+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:46:55.0871945 +0800 CST m=+2.534019001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.105+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
2025-07-01 23:46:55.1055726 +0800 CST m=+2.552397101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.123+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
2025-07-01 23:46:55.1233873 +0800 CST m=+2.570211801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.150+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
2025-07-01 23:46:55.1500775 +0800 CST m=+2.596902001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.168+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:46:55.1688865 +0800 CST m=+2.615711001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.194+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
2025-07-01 23:46:55.1945361 +0800 CST m=+2.641360601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.239+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
2025-07-01 23:46:55.2390247 +0800 CST m=+2.685849201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.256+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
2025-07-01 23:46:55.2568543 +0800 CST m=+2.703678801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.283+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
2025-07-01 23:46:55.2835242 +0800 CST m=+2.730348701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.301+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
2025-07-01 23:46:55.3013096 +0800 CST m=+2.748134101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.327+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
2025-07-01 23:46:55.3271096 +0800 CST m=+2.773934101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.345+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
2025-07-01 23:46:55.3458571 +0800 CST m=+2.792681601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.476+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:46:55.4768591 +0800 CST m=+2.923683601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:46:55.725+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:46:55.7253975 +0800 CST m=+3.172222001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:23.355+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
2025-07-01 23:47:23.3556877 +0800 CST m=+0.018591201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:23.758+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
2025-07-01 23:47:23.7588272 +0800 CST m=+0.421730701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:23.777+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
2025-07-01 23:47:23.7772054 +0800 CST m=+0.440108901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:23.803+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
2025-07-01 23:47:23.8034785 +0800 CST m=+0.466382001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:23.821+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-01 23:47:23.8217498 +0800 CST m=+0.484653301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:23.848+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T23:46:52.755+0800"}
2025-07-01 23:47:23.8480171 +0800 CST m=+0.510920601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:23.866+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"54716"}
2025-07-01 23:47:23.866182 +0800 CST m=+0.529085501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:23.892+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":54716}
2025-07-01 23:47:23.8924539 +0800 CST m=+0.555357401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:23.910+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":54716}
2025-07-01 23:47:23.910723 +0800 CST m=+0.573626501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:23.936+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
2025-07-01 23:47:23.93698 +0800 CST m=+0.599883501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:23.955+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 54716\" /NH"}
2025-07-01 23:47:23.9552151 +0800 CST m=+0.618118601 write error: write /dev/stdout: The handle is invalid.
{"level":"WARN","timestamp":"2025-07-01T23:47:24.680+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
2025-07-01 23:47:24.6803021 +0800 CST m=+1.343205601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:24.703+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":54716}
2025-07-01 23:47:24.7036729 +0800 CST m=+1.366576401 write error: write /dev/stdout: The handle is invalid.
{"level":"WARN","timestamp":"2025-07-01T23:47:24.721+0800","caller":"utils/logger.go:101","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
2025-07-01 23:47:24.7219507 +0800 CST m=+1.384854201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:24.748+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
2025-07-01 23:47:24.7481795 +0800 CST m=+1.411083001 write error: write /dev/stdout: The handle is invalid.
{"level":"ERROR","timestamp":"2025-07-01T23:47:24.766+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
2025-07-01 23:47:24.7664608 +0800 CST m=+1.429364301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:24.893+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":54716}
2025-07-01 23:47:24.8935192 +0800 CST m=+1.556422701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:24.914+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
2025-07-01 23:47:24.9142116 +0800 CST m=+1.577115101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:24.933+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 54716\" /NH"}
2025-07-01 23:47:24.9332134 +0800 CST m=+1.596116901 write error: write /dev/stdout: The handle is invalid.
{"level":"WARN","timestamp":"2025-07-01T23:47:25.640+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
2025-07-01 23:47:25.6403747 +0800 CST m=+2.303278201 write error: write /dev/stdout: The handle is invalid.
{"level":"WARN","timestamp":"2025-07-01T23:47:25.655+0800","caller":"utils/logger.go:101","msg":"单实例检查失败，程序退出"}
2025-07-01 23:47:25.6554612 +0800 CST m=+2.318364701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:46.424+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
2025-07-01 23:47:46.4248293 +0800 CST m=+53.871653801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:46.859+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
2025-07-01 23:47:46.8593555 +0800 CST m=+54.306180001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:46.882+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
2025-07-01 23:47:46.8827993 +0800 CST m=+54.329623801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:46.901+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
2025-07-01 23:47:46.9013095 +0800 CST m=+54.348134001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:46.912+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
2025-07-01 23:47:46.9120768 +0800 CST m=+54.358901301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:46.923+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
2025-07-01 23:47:46.9234427 +0800 CST m=+54.370267201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:46.955+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
2025-07-01 23:47:46.9558228 +0800 CST m=+54.402647301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:46.967+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
2025-07-01 23:47:46.9679948 +0800 CST m=+54.414819301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:46.979+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
2025-07-01 23:47:46.9791048 +0800 CST m=+54.425929301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:46.990+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
2025-07-01 23:47:46.9900957 +0800 CST m=+54.436920201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:47:47.001+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-01 23:47:47.0015702 +0800 CST m=+54.448394701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:27.418+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
2025-07-01 23:48:27.4182944 +0800 CST m=+0.020675101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:27.813+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
2025-07-01 23:48:27.8136041 +0800 CST m=+0.415984801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:27.824+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
2025-07-01 23:48:27.8244916 +0800 CST m=+0.426872301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:27.835+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
2025-07-01 23:48:27.8357711 +0800 CST m=+0.438151801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:27.846+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-01 23:48:27.8469635 +0800 CST m=+0.449344201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:27.857+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
2025-07-01 23:48:27.8577751 +0800 CST m=+0.460155801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:27.869+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
2025-07-01 23:48:27.8692409 +0800 CST m=+0.471621601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:27.880+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":54060}
2025-07-01 23:48:27.8803204 +0800 CST m=+0.482701101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:27.938+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
2025-07-01 23:48:27.9388155 +0800 CST m=+0.541196201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:27.958+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
2025-07-01 23:48:27.9582887 +0800 CST m=+0.560669401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:27.984+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
2025-07-01 23:48:27.9841516 +0800 CST m=+0.586532301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.090+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
2025-07-01 23:48:28.0904835 +0800 CST m=+0.692864201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.117+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
2025-07-01 23:48:28.1175382 +0800 CST m=+0.719918901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.521+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
2025-07-01 23:48:28.5211327 +0800 CST m=+1.123513401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.539+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
2025-07-01 23:48:28.5393313 +0800 CST m=+1.141712001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.558+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
2025-07-01 23:48:28.5584753 +0800 CST m=+1.160856001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.584+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-01 23:48:28.5843883 +0800 CST m=+1.186769001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.602+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
2025-07-01 23:48:28.6029356 +0800 CST m=+1.205316301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.628+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-01 23:48:28.6286211 +0800 CST m=+1.231001801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.652+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
2025-07-01 23:48:28.6526749 +0800 CST m=+1.255055601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.655+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
2025-07-01 23:48:28.6552451 +0800 CST m=+1.257625801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.673+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
2025-07-01 23:48:28.6737283 +0800 CST m=+1.276109001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.712+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
2025-07-01 23:48:28.712672 +0800 CST m=+1.315052701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.717+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:48:28.722+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
2025-07-01 23:48:28.7173388 +0800 CST m=+1.319719501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.745+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
2025-07-01 23:48:28.7225442 +0800 CST m=+1.324924901 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:48:28.7457048 +0800 CST m=+1.348085501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.762+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
2025-07-01 23:48:28.7622519 +0800 CST m=+1.364632601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.780+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:48:28.785+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
2025-07-01 23:48:28.7809906 +0800 CST m=+1.383371301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.806+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
2025-07-01 23:48:28.7851149 +0800 CST m=+1.387495601 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:48:28.8068292 +0800 CST m=+1.409209901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.825+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
2025-07-01 23:48:28.8254266 +0800 CST m=+1.427807301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.851+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
2025-07-01 23:48:28.8512433 +0800 CST m=+1.453624001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.869+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T23:48:28.871+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:48:28.869893 +0800 CST m=+1.472273701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.895+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
2025-07-01 23:48:28.8714383 +0800 CST m=+1.473819001 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:48:28.8957061 +0800 CST m=+1.498086801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.940+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
2025-07-01 23:48:28.9401913 +0800 CST m=+1.542572001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.958+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
2025-07-01 23:48:28.9589216 +0800 CST m=+1.561302301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:28.984+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
2025-07-01 23:48:28.98467 +0800 CST m=+1.587050701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.003+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
2025-07-01 23:48:29.0031005 +0800 CST m=+1.605481201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.029+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
2025-07-01 23:48:29.0291271 +0800 CST m=+1.631507801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.047+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
2025-07-01 23:48:29.047885 +0800 CST m=+1.650265701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.073+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
2025-07-01 23:48:29.0734832 +0800 CST m=+1.675863901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.081+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
2025-07-01 23:48:29.0813685 +0800 CST m=+1.683749201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.092+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
2025-07-01 23:48:29.0921753 +0800 CST m=+1.694556001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.103+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
2025-07-01 23:48:29.103695 +0800 CST m=+1.706075701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.114+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
2025-07-01 23:48:29.1144583 +0800 CST m=+1.716839001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.126+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
2025-07-01 23:48:29.1260019 +0800 CST m=+1.728382601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.137+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
2025-07-01 23:48:29.1371869 +0800 CST m=+1.739567601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.147+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
2025-07-01 23:48:29.1479133 +0800 CST m=+1.750294001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.159+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:48:29.159+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
2025-07-01 23:48:29.1594709 +0800 CST m=+1.761851601 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:48:29.1594709 +0800 CST m=+1.761851601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.170+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:48:29.170+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
2025-07-01 23:48:29.1702369 +0800 CST m=+1.772617601 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:48:29.1702369 +0800 CST m=+1.772617601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.192+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T23:48:29.192+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
2025-07-01 23:48:29.192911 +0800 CST m=+1.795291701 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:48:29.192911 +0800 CST m=+1.795291701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.224+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:48:29.224+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:48:29.224+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:48:29.2242677 +0800 CST m=+1.826648401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.226+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
2025-07-01 23:48:29.2247842 +0800 CST m=+1.827164901 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:48:29.2247842 +0800 CST m=+1.827164901 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:48:29.2263756 +0800 CST m=+1.828756301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.248+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
2025-07-01 23:48:29.2489126 +0800 CST m=+1.851293301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.259+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
2025-07-01 23:48:29.2598166 +0800 CST m=+1.862197301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.271+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
2025-07-01 23:48:29.2710913 +0800 CST m=+1.873472001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.281+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
2025-07-01 23:48:29.2819176 +0800 CST m=+1.884298301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.466+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:48:29.4664056 +0800 CST m=+2.068786301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.485+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
2025-07-01 23:48:29.4851523 +0800 CST m=+2.087533001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.494+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:48:29.4944573 +0800 CST m=+2.096838001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.526+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
2025-07-01 23:48:29.5265514 +0800 CST m=+2.128932101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.537+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
2025-07-01 23:48:29.5379259 +0800 CST m=+2.140306601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.548+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
2025-07-01 23:48:29.5486985 +0800 CST m=+2.151079201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.560+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
2025-07-01 23:48:29.5603321 +0800 CST m=+2.162712801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.571+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
2025-07-01 23:48:29.5711433 +0800 CST m=+2.173524001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.582+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
2025-07-01 23:48:29.582171 +0800 CST m=+2.184551701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.593+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
2025-07-01 23:48:29.5933588 +0800 CST m=+2.195739501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.604+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
2025-07-01 23:48:29.6049959 +0800 CST m=+2.207376601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.615+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
2025-07-01 23:48:29.6157723 +0800 CST m=+2.218153001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.627+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
2025-07-01 23:48:29.6273168 +0800 CST m=+2.229697501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.638+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
2025-07-01 23:48:29.6380719 +0800 CST m=+2.240452601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.649+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
2025-07-01 23:48:29.6496453 +0800 CST m=+2.252026001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.660+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
2025-07-01 23:48:29.6604812 +0800 CST m=+2.262861901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.671+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
2025-07-01 23:48:29.6719166 +0800 CST m=+2.274297301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.713+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:48:29.7134284 +0800 CST m=+2.315809101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:48:29.936+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:48:29.9362928 +0800 CST m=+2.538673501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:49:44.994+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:49:44.995+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:49:44.995+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:49:44.995+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:49:44.995+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:49:44.995+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T23:48:27.906+0800"}
{"level":"INFO","timestamp":"2025-07-01T23:49:44.995+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"54060"}
{"level":"INFO","timestamp":"2025-07-01T23:49:44.995+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":54060}
{"level":"INFO","timestamp":"2025-07-01T23:49:44.995+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":54060}
{"level":"INFO","timestamp":"2025-07-01T23:49:44.995+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-01T23:49:44.995+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 54060\" /NH"}
{"level":"WARN","timestamp":"2025-07-01T23:49:45.016+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-07-01T23:49:45.016+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":54060}
{"level":"WARN","timestamp":"2025-07-01T23:49:45.016+0800","caller":"utils/logger.go:101","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
{"level":"INFO","timestamp":"2025-07-01T23:49:45.016+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"ERROR","timestamp":"2025-07-01T23:49:45.017+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-07-01T23:49:45.117+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":54060}
{"level":"INFO","timestamp":"2025-07-01T23:49:45.117+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-01T23:49:45.117+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 54060\" /NH"}
{"level":"WARN","timestamp":"2025-07-01T23:49:45.138+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"WARN","timestamp":"2025-07-01T23:49:45.138+0800","caller":"utils/logger.go:101","msg":"单实例检查失败，程序退出"}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.837+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.837+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.837+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.837+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.837+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.838+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.838+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.838+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":55608}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.876+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.877+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.877+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.877+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:50:45.877+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:50:49.633+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:50:49.633+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:50:49.633+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:50:49.633+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:50:49.633+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.352+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.352+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.352+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.352+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.352+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.352+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.352+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.353+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":12932}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.355+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.355+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.355+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.355+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.355+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.362+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.362+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.362+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.362+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:51:12.363+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.725+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.726+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.726+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.726+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.726+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.726+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.726+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.726+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":27620}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.749+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.750+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.750+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.750+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.750+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.759+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.759+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.759+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.759+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:51:18.759+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.463+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.463+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.464+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.464+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.464+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.464+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.464+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.464+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":54640}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.495+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.495+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.495+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.495+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:21.496+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.064+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.065+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.066+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.066+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.066+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.066+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.073+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.074+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.074+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.075+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.075+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.075+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.075+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.075+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.075+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.075+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.075+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.076+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.076+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.076+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.076+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.076+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.076+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.076+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.076+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.077+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.077+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.077+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.078+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.078+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.079+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.079+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.630+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.709+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.721+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.721+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.721+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.722+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.722+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.722+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.722+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.722+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.722+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.722+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.722+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.722+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.722+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.723+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.754+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.754+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.754+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.771+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.841+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.841+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T23:51:22.852+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:51:23.496+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:51:23.496+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:51:23.496+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:51:24.137+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:51:24.137+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:51:24.143+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:51:24.452+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:51:24.613+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:15.765+0800","caller":"utils/logger.go:94","msg":"开始并发截图","roundNumber":1,"mode":"生化平衡分析"}
{"level":"ERROR","timestamp":"2025-07-01T23:54:15.766+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"发送任务错误事件","patient":"concurrent-screenshot","error":"[2000] 参数错误: 模式必须是B或C","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nMagneticOperator/app/services.(*ConcurrentScreenshotService).sendTaskErrorEvent\n\tF:/myHbuilderAPP/MagneticOperator/app/services/concurrent_screenshot_methods.go:106\nMagneticOperator/app/services.(*ConcurrentScreenshotService).TakeConcurrentScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/concurrent_screenshot_methods.go:238\nmain.(*App).TakeConcurrentScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app.go:2787\nreflect.Value.call\n\tD:/Program Files/Go/src/reflect/value.go:584\nreflect.Value.Call\n\tD:/Program Files/Go/src/reflect/value.go:368\ngithub.com/wailsapp/wails/v2/internal/binding.(*BoundMethod).Call\n\tC:/Users/<USER>/go/pkg/mod/github.com/wailsapp/wails/v2@v2.10.1/internal/binding/boundMethod.go:72\ngithub.com/wailsapp/wails/v2/internal/frontend/dispatcher.(*Dispatcher).processCallMessage\n\tC:/Users/<USER>/go/pkg/mod/github.com/wailsapp/wails/v2@v2.10.1/internal/frontend/dispatcher/calls.go:45\ngithub.com/wailsapp/wails/v2/internal/frontend/dispatcher.(*Dispatcher).ProcessMessage\n\tC:/Users/<USER>/go/pkg/mod/github.com/wailsapp/wails/v2@v2.10.1/internal/frontend/dispatcher/dispatcher.go:56\ngithub.com/wailsapp/wails/v2/internal/frontend/desktop/windows.(*Frontend).dispatchMessage\n\tC:/Users/<USER>/go/pkg/mod/github.com/wailsapp/wails/v2@v2.10.1/internal/frontend/desktop/windows/frontend.go:787"}
{"level":"INFO","timestamp":"2025-07-01T23:54:15.771+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:15.802+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:54:15.802+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T23:54:15.802+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T23:54:15.803+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:54:15.803+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.408+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式生化平衡分析] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.408+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.421+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.421+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.421+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_B_1751385256421961800_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.421+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_B_1751385256421961800_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.421+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751385256421961800_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.422+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751385256421961800_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.422+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_B_1751385256421961800_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.423+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.423+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_B_1751385256421961800_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.423+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.423+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.673+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.673+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.673+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: 生化平衡分析, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.918+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T23:54:16.918+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:17.133+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:17.134+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:17.134+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T23:54:17.412+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:17.518+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:17.640+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:17.726+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.290+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.290+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.290+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.290+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.290+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.674+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.674+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.674+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_C_1751385267674354300_test-11200","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.674+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_C_1751385267674354300_test-11200","taskType":"screenshot_C","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.674+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751385267674354300_test-11200","taskType":"screenshot_C","userName":"test-11200","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.674+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751385267674354300_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.674+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_C_1751385267674354300_test-11200","taskType":"screenshot_C","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.675+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_C_1751385267674354300_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.675+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.675+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-C","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.675+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.879+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式C] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T23:54:27.880+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:28.102+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:28.102+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:28.103+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: C, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T23:54:28.498+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:29.681+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:44.483+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 男性小骨盆器官，右侧, 键值对数量: 2, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_生化平衡分析_test-11200_20250701_235416.png\n"}
{"level":"INFO","timestamp":"2025-07-01T23:54:44.483+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"生化平衡分析"}
{"level":"INFO","timestamp":"2025-07-01T23:54:44.485+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T23:54:44.486+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_生化平衡分析_1751385284486347300"}
{"level":"INFO","timestamp":"2025-07-01T23:54:44.486+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"生化平衡分析","imagePath":"pic\\temp\\temp_screenshot_生化平衡分析_test-11200_20250701_235416.png","organName":"男性小骨盆器官，右侧","operationID":"test-11200_生化平衡分析_1751385284486347300"}
{"level":"INFO","timestamp":"2025-07-01T23:54:44.486+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_生化平衡分析_1751385284486347300","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-01T23:54:44.486+0800","caller":"utils/logger.go:94","msg":"当前轮次","轮次":1,"operationID":"test-11200_生化平衡分析_1751385284486347300"}
{"level":"INFO","timestamp":"2025-07-01T23:54:44.486+0800","caller":"utils/logger.go:94","msg":"扩展轮次数组","轮次数":1,"operationID":"test-11200_生化平衡分析_1751385284486347300"}
{"level":"INFO","timestamp":"2025-07-01T23:54:44.486+0800","caller":"utils/logger.go:94","msg":"设置轮次器官名称","轮次":1,"器官":"男性小骨盆器官，右侧","operationID":"test-11200_生化平衡分析_1751385284486347300"}
{"level":"INFO","timestamp":"2025-07-01T23:54:44.486+0800","caller":"utils/logger.go:94","msg":"开始计算已完成轮次数","operationID":"test-11200_生化平衡分析_1751385284486347300"}
{"level":"INFO","timestamp":"2025-07-01T23:54:44.486+0800","caller":"utils/logger.go:94","msg":"轮次数据更新","userName":"test-11200","currentRound":1,"mode":"生化平衡分析","organName":"男性小骨盆器官，右侧","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_生化平衡分析_1751385284486347300"}
{"level":"INFO","timestamp":"2025-07-01T23:54:44.487+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_生化平衡分析_1751385284486347300"}
{"level":"INFO","timestamp":"2025-07-01T23:54:47.947+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:54:47.947+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T23:54:47.948+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T23:54:47.948+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:54:47.948+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:48.196+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:54:48.196+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-01T23:54:48.196+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T23:54:48.197+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:54:52.386+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:54:52.386+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T23:54:52.387+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T23:54:52.387+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:54:52.387+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:54:52.623+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:54:52.623+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-01T23:54:52.623+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T23:54:52.623+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:55:11.211+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 男性小骨盆器官，右侧, 键值对数量: 2, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250701_235417.png\n"}
{"level":"INFO","timestamp":"2025-07-01T23:55:11.489+0800","caller":"utils/logger.go:94","msg":"颜色检测成功完成","tempFilePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_235417.png"}
{"level":"INFO","timestamp":"2025-07-01T23:55:11.497+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"B"}
{"level":"INFO","timestamp":"2025-07-01T23:55:11.500+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T23:55:11.500+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751385311500878100"}
{"level":"INFO","timestamp":"2025-07-01T23:55:11.500+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_235417.png","organName":"男性小骨盆器官，右侧","operationID":"test-11200_B_1751385311500878100"}
{"level":"INFO","timestamp":"2025-07-01T23:56:04.573+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 男性小骨盆器官，右侧, 键值对数量: 2, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_test-11200_20250701_235428.png\n"}
{"level":"INFO","timestamp":"2025-07-01T23:56:04.573+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"C"}
{"level":"INFO","timestamp":"2025-07-01T23:56:04.575+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T23:56:04.575+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_C_1751385364575565900"}
{"level":"INFO","timestamp":"2025-07-01T23:56:04.576+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"C","imagePath":"pic\\temp\\temp_screenshot_C_test-11200_20250701_235428.png","organName":"男性小骨盆器官，右侧","operationID":"test-11200_C_1751385364575565900"}
{"level":"INFO","timestamp":"2025-07-01T23:56:22.078+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.721+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.721+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.721+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.721+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.721+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.721+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.721+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.722+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":55612}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.744+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.744+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.744+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.744+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.744+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.767+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.767+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.767+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.767+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.768+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.890+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.892+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.893+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.895+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.895+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.895+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.898+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.898+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.898+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.898+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.898+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.899+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.902+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.902+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:29.903+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.126+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.126+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.126+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.128+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.128+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.128+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.141+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.141+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.141+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.388+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.388+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.390+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.390+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.390+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.392+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.392+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.393+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.394+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.603+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.607+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.639+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.778+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.848+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:30.874+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.532+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.532+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.532+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.532+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.532+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.533+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T23:51:21.464+0800"}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.533+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"54640"}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.533+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":54640}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.533+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":54640}
{"level":"WARN","timestamp":"2025-07-01T23:56:32.533+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":54640,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.533+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":54640}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.533+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.534+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.534+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":23432}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.593+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.593+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.593+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.593+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:32.594+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.047+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.048+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.049+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.049+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.049+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.049+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.056+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.059+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.059+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.059+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.059+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.059+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.060+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.060+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.060+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.060+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.060+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.060+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.060+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.071+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.071+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.072+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.072+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.072+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.072+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.072+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.072+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.073+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.073+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.073+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.073+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.073+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.073+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.073+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.074+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.074+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.074+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.074+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.261+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.328+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.337+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.338+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.338+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.349+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.354+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.354+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.366+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.700+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.700+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.700+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.700+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.700+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.701+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.701+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.701+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.701+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.701+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.701+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.701+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.701+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.701+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.802+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.802+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:56:33.808+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:34.044+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:34.222+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:56:38.410+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:38.431+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:56:38.431+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-01T23:56:38.431+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T23:56:38.431+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T23:56:38.431+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-01T23:56:38.479+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:56:38.480+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:56:38.480+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:56:38.480+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:56:38.480+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.667+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.667+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.667+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.667+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.667+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.667+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.667+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.667+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":28276}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.693+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.693+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.693+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.693+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.693+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.701+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.701+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.701+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.701+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:57:45.701+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.978+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.978+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.978+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.978+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.978+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.978+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.978+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.979+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":56000}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.986+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.986+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.986+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.986+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.986+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.994+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.994+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.994+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.994+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:57:51.994+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.277+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.278+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":49496}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.314+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.314+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.314+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.314+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.314+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.750+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.751+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.751+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.751+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.751+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.752+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.757+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.760+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.760+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.760+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.760+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.760+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.761+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.762+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.763+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.763+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:53.763+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.053+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.054+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.055+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.055+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.055+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.055+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.312+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.373+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.385+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.385+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.385+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.394+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.399+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.399+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.403+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.645+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.645+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.645+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.878+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.878+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:57:54.885+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:57:55.102+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:57:55.302+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:05.879+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:58:05.879+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T23:58:05.880+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T23:58:05.880+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:58:05.880+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_B_1751385486126981900_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_B_1751385486126981900_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751385486126981900_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751385486126981900_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.126+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_B_1751385486126981900_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.127+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_B_1751385486126981900_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.127+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.127+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.128+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.345+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.345+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.530+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.530+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.530+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T23:58:06.917+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:07.088+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.444+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.444+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.444+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.444+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.444+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.667+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.667+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_C_1751385488668060000_test-11200","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_C_1751385488668060000_test-11200","taskType":"screenshot_C","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751385488668060000_test-11200","taskType":"screenshot_C","userName":"test-11200","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751385488668060000_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_C_1751385488668060000_test-11200","taskType":"screenshot_C","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_C_1751385488668060000_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-C","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.845+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式C] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T23:58:08.846+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:09.077+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:09.077+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:09.079+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: C, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T23:58:09.464+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:09.676+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.562+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 消化系统--胰腺；十二指肠；正面图, 键值对数量: 28, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250701_235806.png\n"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.866+0800","caller":"utils/logger.go:94","msg":"颜色检测成功完成","tempFilePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_235806.png"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.867+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"B"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.869+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.869+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.869+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_235806.png","organName":"消化系统--胰腺；十二指肠；正面图","operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.870+0800","caller":"utils/logger.go:94","msg":"创建新用户检测信息","operationID":"test-11200_B_1751385516869754500","用户":"test-11200"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.870+0800","caller":"utils/logger.go:94","msg":"当前轮次","轮次":1,"operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.870+0800","caller":"utils/logger.go:94","msg":"扩展轮次数组","轮次数":1,"operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.870+0800","caller":"utils/logger.go:94","msg":"设置轮次器官名称","轮次":1,"器官":"消化系统--胰腺；十二指肠；正面图","operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.873+0800","caller":"utils/logger.go:94","msg":"更新操作状态","status":"B02生化平衡分析已加入队列，等待C03病理形态学分析启动...","mode":"B","round":1,"completed":true}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.873+0800","caller":"utils/logger.go:94","msg":"开始计算已完成轮次数","operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.873+0800","caller":"utils/logger.go:94","msg":"轮次数据更新","userName":"test-11200","currentRound":1,"mode":"B","organName":"消化系统--胰腺；十二指肠；正面图","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:58:36.873+0800","caller":"utils/logger.go:94","msg":"用户检测信息更新完全完成","operationID":"test-11200_B_1751385516869754500"}
{"level":"INFO","timestamp":"2025-07-01T23:59:02.926+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 消化系统--胰腺；十二指肠；正面图, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_test-11200_20250701_235809.png\n"}
{"level":"INFO","timestamp":"2025-07-01T23:59:02.926+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"C"}
{"level":"INFO","timestamp":"2025-07-01T23:59:02.928+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T23:59:02.928+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","operationID":"test-11200_C_1751385542928617400"}
{"level":"INFO","timestamp":"2025-07-01T23:59:02.929+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"C","imagePath":"pic\\temp\\temp_screenshot_C_test-11200_20250701_235809.png","organName":"消化系统--胰腺；十二指肠；正面图","operationID":"test-11200_C_1751385542928617400"}
{"level":"INFO","timestamp":"2025-07-02T00:01:15.762+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T00:01:15.762+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-02T00:01:15.762+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-02T00:01:15.762+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-02T00:01:15.762+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T00:01:16.394+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-02T00:01:16.394+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-02T00:01:16.394+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-02T00:01:16.394+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-02T00:01:23.187+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T00:01:23.187+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-02T00:01:23.188+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-02T00:01:23.188+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-02T00:01:23.188+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-02T00:01:23.809+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-02T00:01:23.809+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-02T00:01:23.809+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-02T00:01:23.810+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-02T00:02:53.763+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-02T00:05:54.027+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-02T00:05:54.044+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-02T00:05:54.044+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-02T00:05:54.044+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T00:05:54.044+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-02T00:05:54.044+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-02T00:05:54.074+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-02T00:05:54.074+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-02T00:05:54.074+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-02T00:05:54.074+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-02T00:05:54.074+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
