<template>
  <div class="qrcode-panel">
    <div class="panel-header">
      <h3>微信小程序二维码</h3>
      <div class="header-actions">
        <button @click="generateQRCode" :disabled="generating" class="generate-btn">
          <span v-if="generating">生成中...</span>
          <span v-else>🔄 重新生成</span>
        </button>
      </div>
    </div>
    
    <div class="panel-content">
      <!-- 二维码显示区域 -->
      <div class="qrcode-display">
        <div v-if="qrCodeImage" class="qrcode-container">
          <img :src="qrCodeImage" alt="微信小程序二维码" class="qrcode-image" />
          <div class="qrcode-overlay">
            <div class="qrcode-actions">
              <button @click="saveQRCode" class="action-btn save">
                💾 保存
              </button>
              <button @click="printQRCode" class="action-btn print">
                🖨️ 打印
              </button>
              <button @click="copyQRCode" class="action-btn copy">
                📋 复制
              </button>
            </div>
          </div>
        </div>
        
        <div v-else class="qrcode-placeholder">
          <div class="placeholder-content">
            <div class="placeholder-icon">📱</div>
            <p>点击生成按钮创建微信小程序二维码</p>
            <button @click="generateQRCode" class="generate-placeholder-btn">
              生成二维码
            </button>
          </div>
        </div>
      </div>
      
      <!-- 二维码信息 -->
      <div class="qrcode-info">
        <h4>二维码信息</h4>
        <div class="info-list">
          <div class="info-item">
            <label>小程序AppID:</label>
            <span class="app-id">{{ qrCodeInfo.appId || '未配置' }}</span>
          </div>
          
          <div class="info-item">
            <label>目标页面:</label>
            <span>{{ qrCodeInfo.targetPage || '未配置' }}</span>
          </div>
          
          <div class="info-item">
            <label>网点ID:</label>
            <span>{{ qrCodeInfo.siteId || '未配置' }}</span>
          </div>
          
          <div class="info-item">
            <label>设备MAC:</label>
            <span class="mac-address">{{ qrCodeInfo.macAddress || '未获取' }}</span>
          </div>
          
          <div class="info-item">
            <label>生成时间:</label>
            <span class="generate-time">{{ formatTime(qrCodeInfo.generateTime) }}</span>
          </div>
          
          <div class="info-item">
            <label>文件大小:</label>
            <span>{{ formatFileSize(qrCodeInfo.fileSize) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 使用说明 -->
      <div class="usage-instructions">
        <h4>使用说明</h4>
        <div class="instruction-list">
          <div class="instruction-item">
            <div class="step-number">1</div>
            <div class="step-content">
              <div class="step-title">打开微信扫一扫</div>
              <div class="step-desc">使用微信扫描上方二维码</div>
            </div>
          </div>
          
          <div class="instruction-item">
            <div class="step-number">2</div>
            <div class="step-content">
              <div class="step-title">进入小程序</div>
              <div class="step-desc">自动跳转到磁感分析报告小程序</div>
            </div>
          </div>
          
          <div class="instruction-item">
            <div class="step-number">3</div>
            <div class="step-content">
              <div class="step-title">查看报告</div>
              <div class="step-desc">在小程序中查看检查报告</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 快捷操作 -->
      <div class="quick-actions">
        <h4>快捷操作</h4>
        <div class="action-buttons">
          <button @click="openQRCodeFolder" class="quick-btn">
            📂 打开文件夹
          </button>
          
          <button @click="setAsWallpaper" class="quick-btn">
            🖼️ 设为壁纸
          </button>
          
          <button @click="shareQRCode" class="quick-btn">
            📤 分享二维码
          </button>
          
          <button @click="previewInBrowser" class="quick-btn">
            🌐 浏览器预览
          </button>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="statistics" v-if="statistics">
        <h4>使用统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.todayScans }}</div>
            <div class="stat-label">今日扫描</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalScans }}</div>
            <div class="stat-label">总扫描数</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-value">{{ statistics.lastScanTime }}</div>
            <div class="stat-label">最后扫描</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QRCodePanel',
  data() {
    return {
      qrCodeImage: null,
      generating: false,
      qrCodeInfo: {
        appId: '',
        targetPage: '',
        siteId: '',
        macAddress: '',
        generateTime: null,
        fileSize: 0
      },
      statistics: {
        todayScans: 0,
        totalScans: 0,
        lastScanTime: '暂无'
      }
    }
  },
  mounted() {
    this.loadQRCodeInfo()
    this.loadStatistics()
  },
  methods: {
    async generateQRCode() {
      this.generating = true
      try {
        const qrCodePath = await this.$emit('generate-qrcode')
        
        // 模拟加载二维码图片
        this.qrCodeImage = `/pic/qrcode_${Date.now()}.png`
        
        // 更新二维码信息
        this.qrCodeInfo = {
          appId: 'wx1234567890abcdef',
          targetPage: 'pages/report/index',
          siteId: 'SITE001',
          macAddress: '00:11:22:33:44:55',
          generateTime: new Date(),
          fileSize: 2048
        }
        
        this.saveQRCodeInfo()
        
      } catch (error) {
        console.error('生成二维码失败:', error)
      } finally {
        this.generating = false
      }
    },
    
    async saveQRCode() {
      if (!this.qrCodeImage) {
        this.showNotification('操作错误', '请先生成二维码')
        return
      }
      
      try {
        // 实现保存逻辑
        const link = document.createElement('a')
        link.href = this.qrCodeImage
        link.download = `qrcode_${Date.now()}.png`
        link.click()
        
        this.showMessage('二维码已保存', 'success')
      } catch (error) {
        this.showMessage('保存失败', 'error')
      }
    },
    
    async printQRCode() {
      if (!this.qrCodeImage) {
        this.showNotification('操作错误', '请先生成二维码')
        return
      }
      
      try {
        const printWindow = window.open('', '_blank')
        printWindow.document.write(`
          <html>
            <head>
              <title>打印二维码</title>
              <style>
                body { margin: 0; padding: 20px; text-align: center; }
                img { max-width: 300px; }
                .info { margin-top: 20px; font-family: Arial, sans-serif; }
              </style>
            </head>
            <body>
              <img src="${this.qrCodeImage}" alt="二维码" />
              <div class="info">
                <h3>微信小程序二维码</h3>
                <p>网点: ${this.qrCodeInfo.siteId}</p>
                <p>生成时间: ${this.formatTime(this.qrCodeInfo.generateTime)}</p>
              </div>
            </body>
          </html>
        `)
        printWindow.document.close()
        printWindow.print()
        
      } catch (error) {
        this.showMessage('打印失败', 'error')
      }
    },
    
    async copyQRCode() {
      if (!this.qrCodeImage) {
        this.showNotification('操作错误', '请先生成二维码')
        return
      }
      
      try {
        // 复制图片到剪贴板
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        const img = new Image()
        
        img.onload = () => {
          canvas.width = img.width
          canvas.height = img.height
          ctx.drawImage(img, 0, 0)
          
          canvas.toBlob(async (blob) => {
            try {
              await navigator.clipboard.write([
                new ClipboardItem({ 'image/png': blob })
              ])
              this.showMessage('二维码已复制到剪贴板', 'success')
            } catch (error) {
              this.showMessage('复制失败', 'error')
            }
          })
        }
        
        img.src = this.qrCodeImage
        
      } catch (error) {
        this.showMessage('复制失败', 'error')
      }
    },
    
    openQRCodeFolder() {
      // 实现打开文件夹逻辑
      this.showMessage('正在打开文件夹...', 'info')
    },
    
    setAsWallpaper() {
      if (!this.qrCodeImage) {
        this.showNotification('操作错误', '请先生成二维码')
        return
      }
      
      this.showMessage('设置壁纸功能开发中...', 'info')
    },
    
    shareQRCode() {
      if (!this.qrCodeImage) {
        this.showNotification('操作错误', '请先生成二维码')
        return
      }
      
      if (navigator.share) {
        navigator.share({
          title: '微信小程序二维码',
          text: '扫描查看磁感分析检查报告',
          url: this.qrCodeImage
        })
      } else {
        this.showMessage('分享功能不支持', 'warning')
      }
    },
    
    previewInBrowser() {
      if (!this.qrCodeImage) {
        this.showNotification('操作错误', '请先生成二维码')
        return
      }
      
      window.open(this.qrCodeImage, '_blank')
    },
    
    loadQRCodeInfo() {
      try {
        const saved = localStorage.getItem('qrCodeInfo')
        if (saved) {
          this.qrCodeInfo = { ...this.qrCodeInfo, ...JSON.parse(saved) }
        }
      } catch (error) {
        console.error('加载二维码信息失败:', error)
      }
    },
    
    saveQRCodeInfo() {
      try {
        localStorage.setItem('qrCodeInfo', JSON.stringify(this.qrCodeInfo))
      } catch (error) {
        console.error('保存二维码信息失败:', error)
      }
    },
    
    loadStatistics() {
      try {
        const saved = localStorage.getItem('qrCodeStatistics')
        if (saved) {
          this.statistics = { ...this.statistics, ...JSON.parse(saved) }
        }
      } catch (error) {
        console.error('加载统计信息失败:', error)
      }
    },
    
    formatTime(time) {
      if (!time) return '未生成'
      return new Date(time).toLocaleString()
    },
    
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    },
    
    showMessage(message, type = 'info') {
      // 实现消息提示
      console.log(`[${type}] ${message}`)
    },
    
    showNotification(title, message) {
      // 调用后端的置顶信息窗口通知
      if (window.go && window.go.main && window.go.main.App) {
        window.go.main.App.ShowWailsNotification('error', title, message, 5000)
      } else {
        // 备用方案：控制台输出
        console.error(`${title}: ${message}`)
      }
    }
  }
}
</script>

<style scoped>
.qrcode-panel {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.panel-header {
  background: #f8f9fa;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.generate-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.generate-btn:hover:not(:disabled) {
  background: #218838;
}

.generate-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.panel-content {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.qrcode-display {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.qrcode-container {
  position: relative;
  display: inline-block;
}

.qrcode-image {
  width: 180px;
  height: 180px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: white;
}

.qrcode-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
  border-radius: 8px;
}

.qrcode-container:hover .qrcode-overlay {
  opacity: 1;
}

.qrcode-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: white;
}

.qrcode-placeholder {
  width: 180px;
  height: 180px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.placeholder-content {
  text-align: center;
  color: #666;
}

.placeholder-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.placeholder-content p {
  margin: 8px 0;
  font-size: 12px;
}

.generate-placeholder-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.qrcode-info h4,
.usage-instructions h4,
.quick-actions h4,
.statistics h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.info-item label {
  color: #666;
  font-weight: 500;
}

.info-item span {
  color: #333;
  text-align: right;
  word-break: break-all;
}

.app-id,
.mac-address {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 11px;
}

.generate-time {
  font-size: 11px;
  color: #666;
}

.instruction-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.step-number {
  width: 20px;
  height: 20px;
  background: #3498db;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.step-desc {
  font-size: 11px;
  color: #666;
  line-height: 1.4;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.quick-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s;
  text-align: center;
}

.quick-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 12px 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 10px;
  color: #666;
}
</style>