package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/panjf2000/ants/v2"
	"golang.org/x/time/rate"
)

// ScreenshotTask 截图处理任务
type ScreenshotTask struct {
	ID           string    `json:"id"`            // 任务唯一标识
	ImagePath    string    `json:"image_path"`    // 图片路径
	UserName     string    `json:"user_name"`     // 用户名
	RoundNumber  int       `json:"round_number"`  // 轮次编号
	ScreenNumber int       `json:"screen_number"` // 截图编号（每轮2次）
	Timestamp    time.Time `json:"timestamp"`     // 创建时间
	Mode         string    `json:"mode"`          // 截图模式（B或C）
}

// OCRTaskResult OCR处理结果
type OCRTaskResult struct {
	Task      *ScreenshotTask `json:"task"`
	Result    *OCRResult      `json:"result"`
	Error     error           `json:"error"`
	Processed time.Time       `json:"processed"`
	Duration  time.Duration   `json:"duration"`
}

// ResultCallback 结果回调函数类型
type ResultCallback func(*OCRTaskResult)

// ConcurrentOCRProcessor 并发OCR处理器
type ConcurrentOCRProcessor struct {
	pool         *ants.Pool          // ants协程池
	rateLimiter  *rate.Limiter       // API限流器
	ocrService   OCRInterface        // OCR服务
	resultChan   chan *OCRTaskResult // 结果通道
	callbacks    []ResultCallback    // 结果回调函数列表
	maxWorkers   int                 // 最大工作协程数
	maxQueueSize int                 // 最大队列大小
	ctx          context.Context     // 上下文
	cancel       context.CancelFunc  // 取消函数
	wg           sync.WaitGroup      // 等待组
	mu           sync.RWMutex        // 读写锁
	running      bool                // 运行状态
	stats        *ProcessorStats     // 统计信息
}

// ProcessorStats 处理器统计信息
type ProcessorStats struct {
	TotalSubmitted int64         `json:"total_submitted"` // 总提交任务数
	TotalCompleted int64         `json:"total_completed"` // 已完成任务数
	TotalFailed    int64         `json:"total_failed"`    // 失败任务数
	AverageTime    time.Duration `json:"average_time"`    // 平均处理时间
	StartTime      time.Time     `json:"start_time"`      // 启动时间
	mu             sync.RWMutex  // 统计锁
}

// NewConcurrentOCRProcessor 创建并发OCR处理器
func NewConcurrentOCRProcessor(ocrService OCRInterface, maxWorkers int) (*ConcurrentOCRProcessor, error) {
	if maxWorkers <= 0 {
		maxWorkers = 5 // 默认5个工作协程
	}

	// 创建ants协程池
	pool, err := ants.NewPool(maxWorkers, ants.WithOptions(ants.Options{
		Nonblocking: true, // 非阻塞模式
		PanicHandler: func(i interface{}) {
			fmt.Printf("[并发OCR] 协程异常: %v\n", i)
		},
	}))
	if err != nil {
		return nil, fmt.Errorf("创建协程池失败: %v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	processor := &ConcurrentOCRProcessor{
		pool:         pool,
		rateLimiter:  rate.NewLimiter(rate.Every(50*time.Millisecond), 20), // 每50ms最多20个请求，提高并发度
		ocrService:   ocrService,
		resultChan:   make(chan *OCRTaskResult, maxWorkers*2), // 结果缓冲区
		callbacks:    make([]ResultCallback, 0),
		maxWorkers:   maxWorkers,
		maxQueueSize: maxWorkers * 10, // 队列大小为工作协程数的10倍
		ctx:          ctx,
		cancel:       cancel,
		running:      false,
		stats: &ProcessorStats{
			StartTime: time.Now(),
		},
	}

	return processor, nil
}

// Start 启动处理器
func (cop *ConcurrentOCRProcessor) Start() error {
	cop.mu.Lock()
	defer cop.mu.Unlock()

	if cop.running {
		return fmt.Errorf("处理器已在运行中")
	}

	cop.running = true
	cop.stats.StartTime = time.Now()

	// 启动结果处理协程
	cop.wg.Add(1)
	go cop.resultProcessor()

	fmt.Printf("[并发OCR] 处理器已启动，最大工作协程数: %d\n", cop.maxWorkers)
	return nil
}

// Stop 停止处理器
func (cop *ConcurrentOCRProcessor) Stop() error {
	cop.mu.Lock()
	defer cop.mu.Unlock()

	if !cop.running {
		return fmt.Errorf("处理器未运行")
	}

	cop.running = false
	cop.cancel() // 取消上下文

	// 等待所有任务完成
	cop.pool.Release()
	close(cop.resultChan)
	cop.wg.Wait()

	fmt.Printf("[并发OCR] 处理器已停止\n")
	return nil
}

// SubmitTask 提交OCR处理任务
func (cop *ConcurrentOCRProcessor) SubmitTask(task *ScreenshotTask) error {
	cop.mu.RLock()
	running := cop.running
	cop.mu.RUnlock()

	if !running {
		return fmt.Errorf("处理器未运行")
	}

	// 检查队列是否已满
	if cop.pool.Running() >= cop.maxQueueSize {
		return fmt.Errorf("任务队列已满，请稍后重试")
	}

	// 等待限流器许可
	if err := cop.rateLimiter.Wait(cop.ctx); err != nil {
		return fmt.Errorf("限流等待失败: %v", err)
	}

	// 提交任务到协程池
	err := cop.pool.Submit(func() {
		cop.processTask(task)
	})

	if err != nil {
		return fmt.Errorf("提交任务失败: %v", err)
	}

	// 更新统计
	cop.stats.mu.Lock()
	cop.stats.TotalSubmitted++
	cop.stats.mu.Unlock()

	fmt.Printf("[并发OCR] 任务已提交: %s (轮次%d-截图%d)\n", task.ID, task.RoundNumber, task.ScreenNumber)
	return nil
}

// processTask 处理单个任务
func (cop *ConcurrentOCRProcessor) processTask(task *ScreenshotTask) {
	startTime := time.Now()
	result := &OCRTaskResult{
		Task:      task,
		Processed: startTime,
	}

	fmt.Printf("[并发OCR] 开始处理任务: %s\n", task.ID)

	// 调用OCR服务处理图片
	ocrResult, err := cop.ocrService.ProcessImageWithDetails(task.ImagePath)
	if err != nil {
		result.Error = err
		cop.stats.mu.Lock()
		cop.stats.TotalFailed++
		cop.stats.mu.Unlock()
		fmt.Printf("[并发OCR] 任务处理失败: %s, 错误: %v\n", task.ID, err)
	} else {
		result.Result = ocrResult
		cop.stats.mu.Lock()
		cop.stats.TotalCompleted++
		cop.stats.mu.Unlock()
		fmt.Printf("[并发OCR] 任务处理成功: %s, 器官: %s\n", task.ID, ocrResult.OrganName)
	}

	result.Duration = time.Since(startTime)

	// 更新平均处理时间
	cop.updateAverageTime(result.Duration)

	// 发送结果到通道
	select {
	case cop.resultChan <- result:
	case <-cop.ctx.Done():
		fmt.Printf("[并发OCR] 处理器已停止，丢弃任务结果: %s\n", task.ID)
	}
}

// resultProcessor 结果处理协程
func (cop *ConcurrentOCRProcessor) resultProcessor() {
	defer cop.wg.Done()

	for {
		select {
		case result, ok := <-cop.resultChan:
			if !ok {
				fmt.Printf("[并发OCR] 结果处理协程退出\n")
				return
			}

			// 调用所有注册的回调函数
			cop.mu.RLock()
			callbacks := make([]ResultCallback, len(cop.callbacks))
			copy(callbacks, cop.callbacks)
			cop.mu.RUnlock()

			for _, callback := range callbacks {
				go func(cb ResultCallback, res *OCRTaskResult) {
					defer func() {
						if r := recover(); r != nil {
							fmt.Printf("[并发OCR] 回调函数异常: %v\n", r)
						}
					}()
					cb(res)
				}(callback, result)
			}

		case <-cop.ctx.Done():
			fmt.Printf("[并发OCR] 结果处理协程收到停止信号\n")
			return
		}
	}
}

// AddResultCallback 添加结果回调函数
func (cop *ConcurrentOCRProcessor) AddResultCallback(callback ResultCallback) {
	cop.mu.Lock()
	defer cop.mu.Unlock()
	cop.callbacks = append(cop.callbacks, callback)
}

// GetStats 获取统计信息
func (cop *ConcurrentOCRProcessor) GetStats() *ProcessorStats {
	cop.stats.mu.RLock()
	defer cop.stats.mu.RUnlock()

	return &ProcessorStats{
		TotalSubmitted: cop.stats.TotalSubmitted,
		TotalCompleted: cop.stats.TotalCompleted,
		TotalFailed:    cop.stats.TotalFailed,
		AverageTime:    cop.stats.AverageTime,
		StartTime:      cop.stats.StartTime,
	}
}

// updateAverageTime 更新平均处理时间
func (cop *ConcurrentOCRProcessor) updateAverageTime(duration time.Duration) {
	cop.stats.mu.Lock()
	defer cop.stats.mu.Unlock()

	if cop.stats.TotalCompleted == 1 {
		cop.stats.AverageTime = duration
	} else {
		// 计算移动平均值
		total := cop.stats.TotalCompleted + cop.stats.TotalFailed
		cop.stats.AverageTime = time.Duration(
			(int64(cop.stats.AverageTime)*(total-1) + int64(duration)) / total,
		)
	}
}

// GetQueueStatus 获取队列状态
func (cop *ConcurrentOCRProcessor) GetQueueStatus() map[string]interface{} {
	return map[string]interface{}{
		"running_tasks":   cop.pool.Running(),
		"free_workers":    cop.pool.Free(),
		"capacity":        cop.pool.Cap(),
		"max_queue_size":  cop.maxQueueSize,
		"result_chan_len": len(cop.resultChan),
		"result_chan_cap": cap(cop.resultChan),
	}
}

// IsRunning 检查处理器是否运行中
func (cop *ConcurrentOCRProcessor) IsRunning() bool {
	cop.mu.RLock()
	defer cop.mu.RUnlock()
	return cop.running
}
