package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/panjf2000/ants/v2"
	"golang.org/x/time/rate"
)

// ScreenshotTask 截图处理任务
type ScreenshotTask struct {
	ID        string    `json:"id"`         // 任务唯一标识
	ImagePath string    `json:"image_path"` // 图片路径
	UserName  string    `json:"user_name"`  // 用户名
	Timestamp time.Time `json:"timestamp"`  // 创建时间
	Mode      string    `json:"mode"`       // 截图模式（B或C）
}

// OCRTaskResult OCR处理结果
type OCRTaskResult struct {
	Task      *ScreenshotTask `json:"task"`
	Result    *OCRResult      `json:"result"`
	Error     error           `json:"error"`
	Processed time.Time       `json:"processed"`
	Duration  time.Duration   `json:"duration"`
}

// ResultCallback 结果回调函数类型
type ResultCallback func(*OCRTaskResult)

// ConcurrentOCRProcessor 并发OCR处理器
type ConcurrentOCRProcessor struct {
	pool         *ants.Pool          // ants协程池
	rateLimiter  *rate.Limiter       // API限流器
	ocrService   OCRInterface        // OCR服务
	resultChan   chan *OCRTaskResult // 结果通道
	callbacks    []ResultCallback    // 结果回调函数列表
	maxWorkers   int                 // 最大工作协程数
	maxQueueSize int                 // 最大队列大小
	ctx          context.Context     // 上下文
	cancel       context.CancelFunc  // 取消函数
	wg           sync.WaitGroup      // 等待组
	mu           sync.RWMutex        // 读写锁
	running      bool                // 运行状态
	stats        *ProcessorStats     // 统计信息
}

// ProcessorStats 处理器统计信息
type ProcessorStats struct {
	TotalSubmitted int64         `json:"total_submitted"` // 总提交任务数
	TotalCompleted int64         `json:"total_completed"` // 已完成任务数
	TotalFailed    int64         `json:"total_failed"`    // 失败任务数
	AverageTime    time.Duration `json:"average_time"`    // 平均处理时间
	StartTime      time.Time     `json:"start_time"`      // 启动时间
	mu             sync.RWMutex  // 统计锁
}

// NewConcurrentOCRProcessor 创建新的并发OCR处理器
func NewConcurrentOCRProcessor(ocrService OCRInterface, configService ConfigServiceInterface, defaultMaxWorkers int) (*ConcurrentOCRProcessor, error) {
	// 从配置中读取参数，如果配置不存在则使用默认值
	config := configService.GetConfig()
	maxWorkers := defaultMaxWorkers
	rateLimit := 20 // 默认值
	rateBurst := 5  // 默认值

	// 如果配置中有并发设置，则使用配置值
	if config.Concurrency.MaxWorkers > 0 {
		maxWorkers = config.Concurrency.MaxWorkers
	}
	if config.Concurrency.RateLimit > 0 {
		rateLimit = config.Concurrency.RateLimit
	}
	if config.Concurrency.RateBurst > 0 {
		rateBurst = config.Concurrency.RateBurst
	}

	fmt.Printf("[OCR处理器] 初始化参数: maxWorkers=%d, rateLimit=%d, rateBurst=%d\n", maxWorkers, rateLimit, rateBurst)

	if maxWorkers <= 0 {
		maxWorkers = 5 // 最小保护值
	}

	// 创建限流器
	rlimit := rate.Limit(float64(rateLimit)) // 每秒请求数

	// 创建ants协程池
	pool, err := ants.NewPool(maxWorkers, ants.WithOptions(ants.Options{
		Nonblocking: true, // 非阻塞模式
		PanicHandler: func(i interface{}) {
			fmt.Printf("[并发OCR] 协程异常: %v\n", i)
		},
	}))
	if err != nil {
		return nil, fmt.Errorf("创建协程池失败: %v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	processor := &ConcurrentOCRProcessor{
		pool:         pool,
		rateLimiter:  rate.NewLimiter(rlimit, rateBurst),
		ocrService:   ocrService,
		resultChan:   make(chan *OCRTaskResult, maxWorkers*2), // 结果缓冲区
		callbacks:    make([]ResultCallback, 0),
		maxWorkers:   maxWorkers,
		maxQueueSize: maxWorkers * 20, // 队列大小为工作协程数的20倍
		ctx:          ctx,
		cancel:       cancel,
		running:      false,
		stats: &ProcessorStats{
			StartTime: time.Now(),
		},
	}

	return processor, nil
}

// Start 启动处理器
func (cop *ConcurrentOCRProcessor) Start() error {
	cop.mu.Lock()
	defer cop.mu.Unlock()

	if cop.running {
		return fmt.Errorf("处理器已在运行中")
	}

	cop.running = true
	cop.stats.StartTime = time.Now()

	// 启动结果处理协程
	cop.wg.Add(1)
	go cop.resultProcessor()

	fmt.Printf("[并发OCR] 处理器已启动，最大工作协程数: %d\n", cop.maxWorkers)
	return nil
}

// Stop 停止处理器
func (cop *ConcurrentOCRProcessor) Stop() error {
	cop.mu.Lock()
	defer cop.mu.Unlock()

	if !cop.running {
		return fmt.Errorf("处理器未运行")
	}

	cop.running = false
	cop.cancel() // 取消上下文

	// 等待所有任务完成
	cop.pool.Release()
	close(cop.resultChan)
	cop.wg.Wait()

	fmt.Printf("[并发OCR] 处理器已停止\n")
	return nil
}

// SubmitTask 提交OCR处理任务
func (cop *ConcurrentOCRProcessor) SubmitTask(ctx context.Context, task *ScreenshotTask) error {
	cop.mu.RLock()
	running := cop.running
	cop.mu.RUnlock()

	if !running {
		return fmt.Errorf("处理器未运行")
	}

	// 队列监控与告警
	currentQueueSize := cop.pool.Running()
	if currentQueueSize >= cop.maxQueueSize {
		return fmt.Errorf("任务队列已满 (大小: %d)，请稍后重试", currentQueueSize)
	}
	if float64(currentQueueSize) >= float64(cop.maxQueueSize)*0.8 {
		fmt.Printf("[并发OCR][警告] 任务队列使用率已达%.0f%% (大小: %d/%d)\n", float64(currentQueueSize)/float64(cop.maxQueueSize)*100, currentQueueSize, cop.maxQueueSize)
	}

	// 结合外部上下文和内部限流器等待
	if err := cop.rateLimiter.Wait(ctx); err != nil {
		return fmt.Errorf("限流等待失败或上下文取消: %v", err)
	}

	// 提交任务到协程池
	err := cop.pool.Submit(func() {
		cop.processTask(ctx, task)
	})

	if err != nil {
		return fmt.Errorf("提交任务失败: %v", err)
	}

	// 更新统计
	cop.stats.mu.Lock()
	cop.stats.TotalSubmitted++
	cop.stats.mu.Unlock()

	fmt.Printf("[并发OCR] 任务已提交: %s\n", task.ID)
	return nil
}

// processTask 处理单个任务
func (cop *ConcurrentOCRProcessor) processTask(ctx context.Context, task *ScreenshotTask) {
	startTime := time.Now()
	result := &OCRTaskResult{
		Task:      task,
		Processed: startTime,
	}

	fmt.Printf("[并发OCR] 开始处理任务: %s\n", task.ID)

	// 实现带指数退避的重试机制，使用更长的重试间隔
	var ocrResult *OCRResult
	var err error

	maxRetries := 5              // 增加最大重试次数到5次
	baseDelay := 3 * time.Second // 基础延迟从500ms增加到3秒

	for attempt := 0; attempt < maxRetries; attempt++ {
		select {
		case <-ctx.Done(): // 检查外部上下文是否已取消
			result.Error = ctx.Err()
			cop.stats.mu.Lock()
			cop.stats.TotalFailed++
			cop.stats.mu.Unlock()
			fmt.Printf("[并发OCR] 任务被取消: %s, 错误: %v\n", task.ID, result.Error)
			cop.resultChan <- result
			return
		default:
		}

		// 调用OCR服务处理图片，并传递上下文
		ocrResult, err = cop.ocrService.ProcessImageWithDetails(ctx, task.ImagePath)
		if err == nil {
			break // 成功则跳出循环
		}

		fmt.Printf("[并发OCR] 任务处理失败 (尝试 %d/%d): %s, 错误: %v\n", attempt+1, maxRetries, task.ID, err)

		if attempt < maxRetries-1 {
			// 计算指数退避延迟，设置最大延迟限制
			delay := baseDelay
			for i := 0; i < attempt; i++ {
				delay = time.Duration(float64(delay) * 2.0) // 使用2.0倍数退避
			}
			if delay > 60*time.Second { // 最大延迟60秒
				delay = 60 * time.Second
			}
			fmt.Printf("[并发OCR] 等待 %v 后重试...\n", delay)
			time.Sleep(delay)
		}
	}

	if err != nil {
		result.Error = err
		cop.stats.mu.Lock()
		cop.stats.TotalFailed++
		cop.stats.mu.Unlock()
		fmt.Printf("[并发OCR] 任务最终失败: %s, 错误: %v\n", task.ID, err)
	} else {
		result.Result = ocrResult
		cop.stats.mu.Lock()
		cop.stats.TotalCompleted++
		cop.stats.mu.Unlock()
		fmt.Printf("[并发OCR] 任务处理成功: %s, 器官: %s\n", task.ID, ocrResult.OrganName)
	}

	result.Duration = time.Since(startTime)

	// 更新平均处理时间
	cop.updateAverageTime(result.Duration)

	// 发送结果到通道
	select {
	case cop.resultChan <- result:
	case <-cop.ctx.Done():
		fmt.Printf("[并发OCR] 处理器已停止，丢弃任务结果: %s\n", task.ID)
	}
}

// resultProcessor 结果处理协程
func (cop *ConcurrentOCRProcessor) resultProcessor() {
	defer cop.wg.Done()

	for {
		select {
		case result, ok := <-cop.resultChan:
			if !ok {
				fmt.Printf("[并发OCR] 结果处理协程退出\n")
				return
			}

			// 调用所有注册的回调函数
			cop.mu.RLock()
			callbacks := make([]ResultCallback, len(cop.callbacks))
			copy(callbacks, cop.callbacks)
			cop.mu.RUnlock()

			for _, callback := range callbacks {
				go func(cb ResultCallback, res *OCRTaskResult) {
					defer func() {
						if r := recover(); r != nil {
							fmt.Printf("[并发OCR] 回调函数异常: %v\n", r)
						}
					}()
					cb(res)
				}(callback, result)
			}

		case <-cop.ctx.Done():
			fmt.Printf("[并发OCR] 结果处理协程收到停止信号\n")
			return
		}
	}
}

// AddResultCallback 添加结果回调函数
func (cop *ConcurrentOCRProcessor) AddResultCallback(callback ResultCallback) {
	cop.mu.Lock()
	defer cop.mu.Unlock()
	cop.callbacks = append(cop.callbacks, callback)
}

// GetStats 获取统计信息
func (cop *ConcurrentOCRProcessor) GetStats() *ProcessorStats {
	cop.stats.mu.RLock()
	defer cop.stats.mu.RUnlock()

	return &ProcessorStats{
		TotalSubmitted: cop.stats.TotalSubmitted,
		TotalCompleted: cop.stats.TotalCompleted,
		TotalFailed:    cop.stats.TotalFailed,
		AverageTime:    cop.stats.AverageTime,
		StartTime:      cop.stats.StartTime,
	}
}

// updateAverageTime 更新平均处理时间
func (cop *ConcurrentOCRProcessor) updateAverageTime(duration time.Duration) {
	cop.stats.mu.Lock()
	defer cop.stats.mu.Unlock()

	if cop.stats.TotalCompleted == 1 {
		cop.stats.AverageTime = duration
	} else {
		// 计算移动平均值
		total := cop.stats.TotalCompleted + cop.stats.TotalFailed
		cop.stats.AverageTime = time.Duration(
			(int64(cop.stats.AverageTime)*(total-1) + int64(duration)) / total,
		)
	}
}

// GetQueueStatus 获取队列状态
func (cop *ConcurrentOCRProcessor) GetQueueStatus() map[string]interface{} {
	return map[string]interface{}{
		"running_tasks":   cop.pool.Running(),
		"free_workers":    cop.pool.Free(),
		"capacity":        cop.pool.Cap(),
		"max_queue_size":  cop.maxQueueSize,
		"result_chan_len": len(cop.resultChan),
		"result_chan_cap": cap(cop.resultChan),
	}
}

// IsRunning 检查处理器是否运行中
func (cop *ConcurrentOCRProcessor) IsRunning() bool {
	cop.mu.RLock()
	defer cop.mu.RUnlock()
	return cop.running
}

// HasPendingTasks 检查是否有待处理任务（并发模式）
func (cop *ConcurrentOCRProcessor) HasPendingTasks() bool {
	cop.mu.RLock()
	defer cop.mu.RUnlock()

	// 并发模式下检查协程池是否有运行中的任务
	return cop.running && cop.pool.Running() > 0
}

// GetRemainingTasksInfo 获取剩余任务信息（并发模式）
func (cop *ConcurrentOCRProcessor) GetRemainingTasksInfo() map[string]interface{} {
	cop.mu.RLock()
	defer cop.mu.RUnlock()

	runningTasks := 0
	if cop.pool != nil {
		runningTasks = cop.pool.Running()
	}

	// 并发模式预计时间较短
	averageTaskTime := 10 * time.Second
	estimatedTime := time.Duration(runningTasks) * averageTaskTime

	return map[string]interface{}{
		"mode":               "concurrent",
		"running_tasks":      runningTasks,
		"estimated_time_sec": int(estimatedTime.Seconds()),
		"estimated_time_str": formatDurationConcurrent(estimatedTime),
		"is_processing":      runningTasks > 0,
	}
}

// formatDurationConcurrent 格式化时间显示（并发模式）
func formatDurationConcurrent(d time.Duration) string {
	minutes := int(d.Minutes())
	seconds := int(d.Seconds()) % 60

	if minutes > 0 {
		return fmt.Sprintf("%d分%d秒", minutes, seconds)
	}
	return fmt.Sprintf("%d秒", seconds)
}

// GracefulStop 优雅停止处理器（并发模式）
func (cop *ConcurrentOCRProcessor) GracefulStop(timeout time.Duration) error {
	cop.mu.Lock()
	defer cop.mu.Unlock()

	if !cop.running {
		return fmt.Errorf("并发OCR处理器未运行")
	}

	fmt.Printf("[并发OCR] 开始优雅停止，等待当前任务完成...\n")

	// 设置停止标志
	cop.running = false

	// 创建超时上下文
	timeoutCtx, timeoutCancel := context.WithTimeout(context.Background(), timeout)
	defer timeoutCancel()

	// 等待所有任务完成或超时
	done := make(chan struct{})
	go func() {
		cop.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		fmt.Printf("[并发OCR] 所有任务已完成，处理器已停止\n")
		cop.cancel()
		cop.pool.Release()
		close(cop.resultChan)
		return nil
	case <-timeoutCtx.Done():
		fmt.Printf("[并发OCR] 等待超时，强制停止处理器\n")
		cop.cancel()
		cop.pool.Release()
		close(cop.resultChan)
		return fmt.Errorf("优雅停止超时")
	}
}
