{"environment": "development", "debug": true, "api_keys": {"VolcEngine_ocr": {"api_url": "https://visual.volcengineapi.com", "AccessKeyID": "AKLTZThlYmFhMWM5Y2MyNGE1ZDllNGMxN2JiNGU1ODg1NGI", "SecretAccessKey": "TWpFME0ySmhOVFV3WW1JMU5ETTVNbUUxT0dGaFpXSTFPVE0zWkdNd05EYw=="}, "coze": {"token": "pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0", "workflow_id_post_pic": "7496900622433812531", "workflow_id_post_registration": "7501566019660939279", "workflow_id_user_info": "7501680491335614516", "space_id": "7331689003143544832", "app_id": "7496871719090077733"}, "cloud_function": {"registrations_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice", "screenshot_records_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/screenshot-records/createOrUpdateScreenshotRecord", "siteInfoByDeviceMAC_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC", "mark_patient_completed_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/markPatientCompleted", "user_detect_raw_result_data_url": "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/saveUserDetectRawResultData"}}, "color_detection": {"debug_mode": true, "save_debug_files": true}}