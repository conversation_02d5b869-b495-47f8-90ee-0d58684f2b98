# OCR并发安全实现文档

## 问题背景

在10轮次B、C模式（共20次）截图工作流程中，每次截图都是独立线程实现，但执行代码和保存返回值的变量都是共享的。这导致了以下并发安全问题：

1. **OCR API调用返回顺序不确定**：20次截图的OCR API调用返回顺序不一定按截图顺序返回
2. **数据覆盖问题**：后返回的API结果可能覆盖先返回的正确结果
3. **竞态条件**：多个线程同时修改同一个变量导致数据不一致
4. **重复处理**：同一个截图可能被多次处理，导致数据重复或错误

## 解决方案

### 1. OCR任务上下文追踪

#### OCRTaskContext结构体
```go
type OCRTaskContext struct {
    TaskID    string              `json:"task_id"`          // 唯一任务ID
    UserName  string              `json:"user_name"`        // 用户名
    Mode      string              `json:"mode"`             // 模式 (B02/C03)
    ImagePath string              `json:"image_path"`       // 图片路径
    StartTime time.Time           `json:"start_time"`       // 开始时间
    Completed bool                `json:"completed"`        // 是否完成
    Result    *services.OCRResult `json:"result,omitempty"` // OCR结果
    Error     error               `json:"error,omitempty"`  // 错误信息
}
```

#### 任务管理字段
```go
type App struct {
    // OCR任务追踪 - 用于防止并发冲突
    ocrTaskContexts map[string]*OCRTaskContext // key: taskID, value: 任务上下文
    ocrTaskMutex    sync.RWMutex               // 保护OCR任务上下文的并发访问
}
```

### 2. 线程安全的任务管理函数

#### createOCRTaskContext - 创建任务上下文
- 生成唯一的任务ID（包含用户名、模式、轮次、纳秒时间戳）
- 线程安全地将任务上下文存储到映射中
- 记录任务创建日志

#### completeOCRTaskContext - 完成任务上下文
- 线程安全地更新任务完成状态
- 保存OCR结果和错误信息
- 记录任务完成日志

#### isOCRTaskDuplicate - 检查重复任务
- 检查是否已存在相同的已完成任务
- 基于用户名、模式、轮次、图片路径进行匹配
- 防止重复处理同一个截图

#### cleanupOldOCRTasks - 清理旧任务
- 定期清理1小时前的任务上下文
- 防止内存泄漏
- 每30分钟自动执行一次

### 3. 防重复更新机制

#### updateCurrentUserCheckingInfo函数改进
```go
// 创建唯一的操作ID用于日志追踪
operationID := fmt.Sprintf("%s_%s_%d", userName, mode, time.Now().UnixNano())

// 检查是否已经处理过这个模式（防止重复更新）
if roundData.B02InputImage != "" && roundData.B02InputImage != imagePath {
    fmt.Printf("[THREAD-SAFE] 警告: 轮次%d的B02数据已存在，跳过重复更新\n", currentRound)
    return nil // 跳过重复更新
}
```

#### 关键改进点
1. **操作ID追踪**：每个操作都有唯一ID，便于日志追踪和调试
2. **重复检测**：检查数据是否已存在，避免覆盖
3. **条件更新**：只有在数据为空时才更新，防止覆盖已有数据
4. **详细日志**：记录每个操作的详细信息，便于问题排查

### 4. ProcessScreenshotWorkflow集成

#### OCR调用前的安全检查
```go
// 获取当前轮次用于任务追踪
currentRound := a.getCurrentRound(patientName)

// 检查是否为重复的OCR任务
if a.isOCRTaskDuplicate(patientName, mode, currentRound, tempFilePath) {
    fmt.Printf("[THREAD-SAFE] 检测到重复的OCR任务，跳过处理\n")
    return "", fmt.Errorf("重复的OCR任务，已跳过处理")
}

// 创建OCR任务上下文
ocrTaskContext := a.createOCRTaskContext(patientName, mode, tempFilePath, currentRound)

// 执行OCR识别
ocrResult, err := a.ProcessImageWithOCR(tempFilePath)

// 完成OCR任务上下文
a.completeOCRTaskContext(ocrTaskContext.TaskID, ocrResult, err)
```

### 5. 自动清理机制

#### 启动时初始化定时器
```go
// 启动OCR任务清理定时器
go func() {
    ticker := time.NewTicker(30 * time.Minute) // 每30分钟清理一次
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            a.cleanupOldOCRTasks()
        case <-ctx.Done():
            return
        }
    }
}()
```

## 安全保障

### 1. 互斥锁保护
- `ocrTaskMutex`：保护OCR任务上下文的并发访问
- `checkingInfoMutex`：保护用户检测信息的并发访问
- 使用读写锁提高并发性能

### 2. 唯一性保证
- 任务ID包含纳秒时间戳，确保全局唯一
- 操作ID用于日志追踪，便于问题定位
- 基于多个维度检查重复任务

### 3. 错误处理
- OCR失败时记录错误但不中断流程
- 重复任务直接跳过，避免数据覆盖
- 详细的错误日志便于问题排查

### 4. 内存管理
- 定期清理旧的任务上下文
- 防止长时间运行导致的内存泄漏
- 可配置的清理间隔和保留时间

## 使用效果

### 1. 并发安全
- 20个并发OCR任务不会相互干扰
- 数据更新操作原子化，避免竞态条件
- 重复任务自动检测和跳过

### 2. 数据一致性
- 每个轮次的数据只会被更新一次
- 后到达的重复结果不会覆盖已有数据
- 完整的操作日志便于审计

### 3. 性能优化
- 重复任务直接跳过，节省计算资源
- 读写锁提高并发读取性能
- 定期清理避免内存泄漏

### 4. 调试友好
- 每个操作都有唯一ID追踪
- 详细的日志记录操作过程
- 清晰的错误信息和警告提示

## 监控和调试

### 关键日志标识
- `[THREAD-SAFE]`：线程安全相关操作
- `[OCR-TASK]`：OCR任务管理相关
- `操作ID`：每个操作的唯一标识符

### 调试建议
1. 查看日志中的操作ID，追踪特定操作的完整流程
2. 监控重复任务警告，了解并发冲突情况
3. 检查任务清理日志，确认内存管理正常
4. 使用GetUserCheckingInfoStatus查看实时数据状态

## 注意事项

1. **任务ID唯一性**：依赖系统时间，确保系统时间准确
2. **清理间隔**：可根据实际使用情况调整清理间隔
3. **日志量**：详细日志可能产生大量输出，生产环境可适当调整
4. **错误恢复**：重复任务跳过后，如需重新处理需手动清理任务上下文

这个实现确保了在高并发的OCR处理场景下，数据的一致性和系统的稳定性。
