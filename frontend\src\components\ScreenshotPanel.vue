<template>
  <div class="screenshot-panel">
    <div class="panel-header">
      <h3>截图控制</h3>
      <div class="header-actions">
        <span class="mode-indicator">当前模式: {{ currentMode || '未选择' }}</span>
      </div>
    </div>
    
    <div class="panel-content">
      <!-- 模式选择 -->
      <div class="mode-selection">
        <h4>扫描模式</h4>
        <div class="mode-grid">
          <button 
            v-for="(mode, key) in modes" 
            :key="key"
            :class="['mode-btn', { active: currentMode === key }]"
            @click="selectMode(key)"
          >
            <div class="mode-icon">{{ mode.Icon || '📷' }}</div>
            <div class="mode-info">
              <div class="mode-name">{{ mode.Name || key }}</div>
              <div class="mode-code">{{ mode.Code }}</div>
            </div>
            <div class="mode-hotkey">{{ getModeHotkey(key) }}</div>
          </button>
        </div>
      </div>
      
      <!-- 当前候检人信息 -->
      <div class="user-input">
        <h4>当前候检人</h4>
        <div class="patient-info">
          <div class="patient-display" v-if="currentPatient">
            <span class="patient-name">{{ currentPatient.name }}</span>
            <span class="patient-number" v-if="currentPatient.registrationNumber">{{ currentPatient.registrationNumber }}</span>
          </div>
          <div class="no-patient" v-else>
            <span class="placeholder-text">未选择候检人（将使用默认名称：无）</span>
          </div>
        </div>
      </div>
      
      <!-- 控制按钮 -->
      <div class="control-buttons">
        <button 
          @click="takeScreenshot" 
          :disabled="!currentMode || !userName.trim() || processing"
          class="screenshot-btn primary"
        >
          <span v-if="processing">处理中...</span>
          <span v-else>📷 开始截图</span>
        </button>
        
        <button 
          @click="quickScreenshot" 
          :disabled="processing"
          class="screenshot-btn secondary"
        >
          ⚡ 快速截图
        </button>
        
        <button 
          @click="previewMode" 
          class="screenshot-btn tertiary"
        >
          👁️ 预览模式
        </button>
        
        <button 
          @click="testConcurrentScreenshots" 
          :disabled="processing"
          class="screenshot-btn test"
          title="测试B模式和C模式并发截图"
        >
          🔄 并发测试
        </button>
      </div>
      
      <!-- 快捷键提示 -->
      <div class="hotkey-tips">
        <h4>快捷键</h4>
        <div class="hotkey-list">
          <div class="hotkey-item">
            <kbd>F1</kbd>
            <span>T1 模式截图</span>
          </div>
          <div class="hotkey-item">
            <kbd>F2</kbd>
            <span>T2 模式截图</span>
          </div>
          <div class="hotkey-item">
            <kbd>F3</kbd>
            <span>FLAIR 模式截图</span>
          </div>
          <div class="hotkey-item">
            <kbd>F4</kbd>
            <span>DWI 模式截图</span>
          </div>
          <div class="hotkey-item">
            <kbd>F5</kbd>
            <span>生成二维码</span>
          </div>
          <div class="hotkey-item">
            <kbd>Ctrl+S</kbd>
            <span>保存当前截图</span>
          </div>
        </div>
      </div>
      
      <!-- 最近截图 -->
      <div class="recent-screenshots" v-if="recentScreenshots.length > 0">
        <h4>最近截图</h4>
        <div class="screenshot-list">
          <div 
            v-for="(screenshot, index) in recentScreenshots" 
            :key="index"
            class="screenshot-item"
            @click="viewScreenshot(screenshot)"
          >
            <div class="screenshot-thumb">
              <img :src="screenshot.thumbnail" :alt="screenshot.name" />
            </div>
            <div class="screenshot-info">
              <div class="screenshot-name">{{ screenshot.name }}</div>
              <div class="screenshot-time">{{ formatTime(screenshot.time) }}</div>
              <div class="screenshot-mode">{{ screenshot.mode }}</div>
            </div>
            <div class="screenshot-actions">
              <button @click.stop="openScreenshot(screenshot)" class="action-btn">
                📂
              </button>
              <button @click.stop="deleteScreenshot(index)" class="action-btn delete">
                🗑️
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScreenshotPanel',
  props: {
    modes: {
      type: Object,
      default: () => ({})
    },
    currentPatient: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      currentMode: '',
      processing: false,
      recentScreenshots: [],
      hotkeyMap: {
        'T1': 'F1',
        'T2': 'F2',
        'FLAIR': 'F3',
        'DWI': 'F4'
      }
    }
  },
  mounted() {
    this.setupKeyboardShortcuts()
    this.loadRecentScreenshots()
  },
  beforeUnmount() {
    this.removeKeyboardShortcuts()
  },
  methods: {
    selectMode(mode) {
      this.currentMode = mode
    },
    
    getModeHotkey(mode) {
      return this.hotkeyMap[mode] || ''
    },
    
    async takeScreenshot() {
      if (!this.currentMode) {
        this.showNotification('操作错误', '请选择扫描模式')
        return
      }
      
      // 获取当前候检人姓名，如果没有则使用默认值
      const patientName = this.currentPatient && this.currentPatient.name ? this.currentPatient.name : '无'
      
      this.processing = true
      try {
        this.$emit('take-screenshot', this.currentMode, patientName)
        
        // 添加到最近截图列表
        this.addToRecentScreenshots({
          name: `${patientName}_${this.currentMode}_${Date.now()}`,
          mode: this.currentMode,
          user: patientName,
          time: new Date(),
          thumbnail: '/api/placeholder/60/40' // 占位符缩略图
        })
        
      } catch (error) {
        console.error('截图失败:', error)
      } finally {
        this.processing = false
      }
    },
    
    async quickScreenshot() {
      if (!this.currentMode) {
        // 使用默认模式
        this.currentMode = Object.keys(this.modes)[0] || 'T1'
      }
      
      const defaultUser = this.userName || '快速截图用户'
      this.userName = defaultUser
      
      await this.takeScreenshot()
    },
    
    previewMode() {
      // 实现预览模式逻辑
      this.showNotification('功能提示', '预览模式功能开发中...')
    },
    
    async testConcurrentScreenshots() {
      this.processing = true
      
      try {
        this.showNotification('并发测试', '正在启动B模式和C模式并发截图测试...')
        
        // 调用后端的TakeConcurrentScreenshot方法（已优化的版本）
        const result = await window.go.main.App.TakeConcurrentScreenshot()
        
        if (result.success) {
          const message = `并发截图完成！\n处理时间: ${result.processing_time}ms\n任务ID: ${result.task_id}`
          this.showNotification('并发截图成功', message)
          
          // 记录到最近截图列表
          this.addToRecentScreenshots({
            name: `并发截图_${result.task_id}_${Date.now()}`,
            mode: 'B+C并发',
            user: this.currentPatient?.name || '测试用户',
            time: new Date(),
            thumbnail: '/api/placeholder/60/40'
          })
          
          console.log('并发截图结果:', result)
        } else {
          this.showNotification('并发截图失败', result.error || '未知错误')
        }
        
      } catch (error) {
        console.error('并发截图失败:', error)
        this.showNotification('并发截图错误', error.message || '调用失败')
      } finally {
        this.processing = false
      }
    },
    
    addToRecentScreenshots(screenshot) {
      this.recentScreenshots.unshift(screenshot)
      if (this.recentScreenshots.length > 10) {
        this.recentScreenshots = this.recentScreenshots.slice(0, 10)
      }
      this.saveRecentScreenshots()
    },
    
    viewScreenshot(screenshot) {
      // 实现查看截图逻辑
      console.log('查看截图:', screenshot)
    },
    
    openScreenshot(screenshot) {
      // 实现打开截图文件夹逻辑
      console.log('打开截图:', screenshot)
    },
    
    async deleteScreenshot(index) {
      if (await this.showConfirm('确认删除', '确定要删除这个截图记录吗？')) {
        this.recentScreenshots.splice(index, 1)
        this.saveRecentScreenshots()
      }
    },
    
    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleString()
    },
    
    loadRecentScreenshots() {
      try {
        const saved = localStorage.getItem('recentScreenshots')
        if (saved) {
          this.recentScreenshots = JSON.parse(saved)
        }
      } catch (error) {
        console.error('加载最近截图失败:', error)
      }
    },
    
    saveRecentScreenshots() {
      try {
        localStorage.setItem('recentScreenshots', JSON.stringify(this.recentScreenshots))
      } catch (error) {
        console.error('保存最近截图失败:', error)
      }
    },
    
    setupKeyboardShortcuts() {
      this.keydownHandler = (event) => {
        if (event.ctrlKey && event.key === 's') {
          event.preventDefault()
          this.takeScreenshot()
        }
      }
      
      document.addEventListener('keydown', this.keydownHandler)
    },
    
    removeKeyboardShortcuts() {
      if (this.keydownHandler) {
        document.removeEventListener('keydown', this.keydownHandler)
      }
    },
    
    showNotification(title, message) {
      // 调用后端的置顶信息窗口通知
      if (window.go && window.go.main && window.go.main.App) {
        window.go.main.App.ShowWailsNotification('info', title, message, 5000)
      } else {
        // 备用方案：控制台输出
        console.log(`${title}: ${message}`)
      }
    },
    
    showConfirm(title, message) {
      // 使用置顶信息窗口显示确认消息，并返回Promise
      return new Promise((resolve) => {
        // 调用后端的置顶信息窗口通知
        if (window.go && window.go.main && window.go.main.App) {
          window.go.main.App.ShowWailsNotification('warning', title, message + ' (请在控制台确认)', 5000)
        }
        
        // 简化处理：直接返回true（实际应用中可以实现更复杂的确认机制）
        // 这里暂时使用原生confirm作为备用方案
        resolve(confirm(`${title}: ${message}`))
      })
    }
  }
}
</script>

<style scoped>
.screenshot-panel {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.panel-header {
  background: #f8f9fa;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.mode-indicator {
  font-size: 12px;
  color: #666;
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
}

.panel-content {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mode-selection h4,
.user-input h4,
.hotkey-tips h4,
.recent-screenshots h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.mode-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.mode-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.mode-btn:hover {
  border-color: #3498db;
  background: #f8f9fa;
}

.mode-btn.active {
  border-color: #3498db;
  background: #e3f2fd;
}

.mode-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.mode-info {
  text-align: center;
}

.mode-name {
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.mode-code {
  font-size: 10px;
  color: #666;
}

.mode-hotkey {
  position: absolute;
  top: 4px;
  right: 4px;
  background: #666;
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 9px;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  box-sizing: border-box;
  max-width: 100%;
}

.input-group input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.clear-btn {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.clear-btn:hover {
  background: #f0f0f0;
}

.patient-info {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px;
}

.patient-display {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.patient-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.patient-number {
  font-size: 12px;
  color: #666;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  align-self: flex-start;
}

.no-patient {
  text-align: center;
}

.placeholder-text {
  font-size: 12px;
  color: #999;
  font-style: italic;
}

.control-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.screenshot-btn {
  padding: 12px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.screenshot-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.screenshot-btn.primary {
  background: #3498db;
  color: white;
}

.screenshot-btn.primary:hover:not(:disabled) {
  background: #2980b9;
}

.screenshot-btn.secondary {
  background: #28a745;
  color: white;
}

.screenshot-btn.secondary:hover:not(:disabled) {
  background: #218838;
}

.screenshot-btn.tertiary {
  background: #6c757d;
  color: white;
}

.screenshot-btn.tertiary:hover:not(:disabled) {
  background: #5a6268;
}

.screenshot-btn.test {
  background: #ff6b35;
  color: white;
}

.screenshot-btn.test:hover:not(:disabled) {
  background: #e55a2b;
}

.hotkey-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.hotkey-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

kbd {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 11px;
  font-family: monospace;
  min-width: 24px;
  text-align: center;
}

.screenshot-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.screenshot-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.screenshot-item:hover {
  background: #f8f9fa;
}

.screenshot-thumb {
  width: 40px;
  height: 30px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.screenshot-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.screenshot-info {
  flex: 1;
  min-width: 0;
}

.screenshot-name {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.screenshot-time {
  font-size: 10px;
  color: #666;
}

.screenshot-mode {
  font-size: 10px;
  color: #3498db;
  font-weight: 500;
}

.screenshot-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: #e9ecef;
}

.action-btn.delete:hover {
  background: #f8d7da;
}
</style>