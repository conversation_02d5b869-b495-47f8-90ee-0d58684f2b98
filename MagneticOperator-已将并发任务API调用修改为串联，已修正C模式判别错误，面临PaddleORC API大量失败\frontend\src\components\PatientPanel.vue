<template>
  <div class="patient-panel">
    <div class="panel-header">
      <h3>患者管理</h3>
      <div class="header-actions">
        <button @click="showAddPatient = true" class="add-btn">
          ➕ 添加患者
        </button>
        <button @click="clearAllPatients" class="clear-btn">
          🗑️ 清空列表
        </button>
      </div>
    </div>
    
    <div class="panel-content">
      <!-- 搜索和筛选 -->
      <div class="search-section">
        <div class="search-bar">
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="搜索患者姓名、身份证号或挂号码..."
            class="search-input"
          />
          <button @click="searchPatients" class="search-btn">
            🔍
          </button>
        </div>
        
        <div class="filter-bar">
          <select v-model="statusFilter" class="filter-select">
            <option value="">全部状态</option>
            <option value="waiting">等待中</option>
            <option value="processing">检查中</option>
            <option value="completed">已完成</option>
          </select>
          
          <select v-model="sortBy" class="filter-select">
            <option value="registrationTime">按登记时间</option>
            <option value="name">按姓名</option>
            <option value="registrationNumber">按挂号码</option>
          </select>
          
          <button @click="exportPatientList" class="export-btn">
            📤 导出
          </button>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="statistics-bar">
        <div class="stat-item">
          <span class="stat-label">今日患者:</span>
          <span class="stat-value">{{ todayPatients }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">等待中:</span>
          <span class="stat-value waiting">{{ waitingPatients }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">检查中:</span>
          <span class="stat-value processing">{{ processingPatients }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">已完成:</span>
          <span class="stat-value completed">{{ completedPatients }}</span>
        </div>
      </div>
      
      <!-- 患者列表 -->
      <div class="patient-list">
        <div v-if="filteredPatients.length === 0" class="empty-state">
          <div class="empty-icon">👥</div>
          <p>暂无患者信息</p>
          <button @click="showAddPatient = true" class="add-first-btn">
            添加第一个患者
          </button>
        </div>
        
        <div v-else class="patient-items">
          <div 
            v-for="patient in filteredPatients" 
            :key="patient.id"
            class="patient-item"
            :class="{ 'selected': selectedPatient?.id === patient.id }"
            @click="selectPatient(patient)"
          >
            <div class="patient-avatar">
              <div class="avatar-circle" :class="patient.gender">
                {{ patient.name.charAt(0) }}
              </div>
              <div class="status-indicator" :class="patient.status"></div>
            </div>
            
            <div class="patient-info">
              <div class="patient-header">
                <h4 class="patient-name">{{ patient.name }}</h4>
                <span class="registration-number">#{{ patient.registrationNumber }}</span>
              </div>
              
              <div class="patient-details">
                <div class="detail-row">
                  <span class="label">性别:</span>
                  <span class="value">{{ patient.gender === 'male' ? '男' : '女' }}</span>
                  <span class="label">年龄:</span>
                  <span class="value">{{ patient.age }}岁</span>
                </div>
                
                <div class="detail-row">
                  <span class="label">身份证:</span>
                  <span class="value id-number">{{ maskIdNumber(patient.idNumber) }}</span>
                </div>
                
                <div class="detail-row">
                  <span class="label">联系电话:</span>
                  <span class="value">{{ patient.phone }}</span>
                </div>
                
                <div class="detail-row">
                  <span class="label">登记时间:</span>
                  <span class="value time">{{ formatTime(patient.registrationTime) }}</span>
                </div>
              </div>
            </div>
            
            <div class="patient-actions">
              <button @click.stop="editPatient(patient)" class="action-btn edit">
                ✏️
              </button>
              <button @click.stop="removePatient(patient.id)" class="action-btn remove">
                🗑️
              </button>
              <button @click.stop="generateReport(patient)" class="action-btn report">
                📋
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination" v-if="totalPages > 1">
        <button 
          @click="currentPage = Math.max(1, currentPage - 1)"
          :disabled="currentPage === 1"
          class="page-btn"
        >
          ‹
        </button>
        
        <span class="page-info">
          {{ currentPage }} / {{ totalPages }}
        </span>
        
        <button 
          @click="currentPage = Math.min(totalPages, currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="page-btn"
        >
          ›
        </button>
      </div>
    </div>
    
    <!-- 添加/编辑患者弹窗 -->
    <div v-if="showAddPatient || editingPatient" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ editingPatient ? '编辑患者信息' : '添加新患者' }}</h3>
          <button @click="closeModal" class="close-btn">✕</button>
        </div>
        
        <div class="modal-body">
          <form @submit.prevent="savePatient" class="patient-form">
            <div class="form-row">
              <div class="form-group">
                <label>姓名 *</label>
                <input 
                  v-model="patientForm.name" 
                  type="text" 
                  required 
                  class="form-input"
                  placeholder="请输入患者姓名"
                />
              </div>
              
              <div class="form-group">
                <label>性别 *</label>
                <select v-model="patientForm.gender" required class="form-select">
                  <option value="">请选择</option>
                  <option value="male">男</option>
                  <option value="female">女</option>
                </select>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label>年龄 *</label>
                <input 
                  v-model.number="patientForm.age" 
                  type="number" 
                  required 
                  min="1" 
                  max="120"
                  class="form-input"
                  placeholder="年龄"
                />
              </div>
              
              <div class="form-group">
                <label>联系电话 *</label>
                <input 
                  v-model="patientForm.phone" 
                  type="tel" 
                  required 
                  class="form-input"
                  placeholder="手机号码"
                />
              </div>
            </div>
            
            <div class="form-group">
              <label>身份证号 *</label>
              <input 
                v-model="patientForm.idNumber" 
                type="text" 
                required 
                class="form-input"
                placeholder="18位身份证号码"
                maxlength="18"
              />
            </div>
            
            <div class="form-group">
              <label>检查部位</label>
              <input 
                v-model="patientForm.examPart" 
                type="text" 
                class="form-input"
                placeholder="如：头部、腰椎、膝关节等"
              />
            </div>
            
            <div class="form-group">
              <label>备注</label>
              <textarea 
                v-model="patientForm.notes" 
                class="form-textarea"
                placeholder="其他备注信息"
                rows="3"
              ></textarea>
            </div>
            
            <div class="form-actions">
              <button type="button" @click="closeModal" class="cancel-btn">
                取消
              </button>
              <button type="submit" class="save-btn">
                {{ editingPatient ? '更新' : '添加' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { SetCurrentPatientName } from '../../wailsjs/go/main/App'

export default {
  name: 'PatientPanel',
  data() {
    return {
      patients: [],
      searchQuery: '',
      statusFilter: '',
      sortBy: 'registrationTime',
      currentPage: 1,
      pageSize: 10,
      selectedPatient: null,
      showAddPatient: false,
      editingPatient: null,
      patientForm: {
        name: '',
        gender: '',
        age: '',
        phone: '',
        idNumber: '',
        examPart: '',
        notes: ''
      }
    }
  },
  computed: {
    filteredPatients() {
      let filtered = [...this.patients]
      
      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(patient => 
          patient.name.toLowerCase().includes(query) ||
          patient.idNumber.includes(query) ||
          patient.registrationNumber.toString().includes(query)
        )
      }
      
      // 状态过滤
      if (this.statusFilter) {
        filtered = filtered.filter(patient => patient.status === this.statusFilter)
      }
      
      // 排序
      filtered.sort((a, b) => {
        switch (this.sortBy) {
          case 'name':
            return a.name.localeCompare(b.name)
          case 'registrationNumber':
            return a.registrationNumber - b.registrationNumber
          case 'registrationTime':
          default:
            return new Date(b.registrationTime) - new Date(a.registrationTime)
        }
      })
      
      // 分页
      const start = (this.currentPage - 1) * this.pageSize
      return filtered.slice(start, start + this.pageSize)
    },
    
    totalPages() {
      return Math.ceil(this.patients.length / this.pageSize)
    },
    
    todayPatients() {
      const today = new Date().toDateString()
      return this.patients.filter(p => 
        new Date(p.registrationTime).toDateString() === today
      ).length
    },
    
    waitingPatients() {
      return this.patients.filter(p => p.status === 'waiting').length
    },
    
    processingPatients() {
      return this.patients.filter(p => p.status === 'processing').length
    },
    
    completedPatients() {
      return this.patients.filter(p => p.status === 'completed').length
    }
  },
  mounted() {
    this.loadPatients()
  },
  methods: {
    async loadPatients() {
      try {
        // 模拟加载患者数据
        this.patients = [
          {
            id: 1,
            name: '张三',
            gender: 'male',
            age: 45,
            phone: '13800138001',
            idNumber: '110101197801011234',
            examPart: '腰椎',
            notes: '腰痛症状',
            registrationNumber: 1001,
            registrationTime: new Date(),
            status: 'waiting'
          },
          {
            id: 2,
            name: '李四',
            gender: 'female',
            age: 32,
            phone: '13800138002',
            idNumber: '110101199001015678',
            examPart: '头部',
            notes: '头痛检查',
            registrationNumber: 1002,
            registrationTime: new Date(Date.now() - 3600000),
            status: 'processing'
          }
        ]
      } catch (error) {
        console.error('加载患者列表失败:', error)
      }
    },
    
    searchPatients() {
      // 搜索逻辑已在computed中实现
      this.currentPage = 1
    },
    
    async selectPatient(patient) {
      this.selectedPatient = patient
      
      // 设置当前患者为历史患者（用于查看过往检测结果）
      try {
        await SetCurrentPatientName(patient.name)
        this.$emit('patient-selected', patient)
      } catch (error) {
        console.error('设置当前患者（历史查看）失败:', error)
        // 即使后端设置失败，前端仍然可以选择患者
        this.$emit('patient-selected', patient)
      }
    },
    
    editPatient(patient) {
      this.editingPatient = patient
      this.patientForm = { ...patient }
      this.showAddPatient = false
    },
    
    async removePatient(patientId) {
      if (await this.showConfirm('确认删除', '确定要删除这个患者吗？')) {
        try {
          this.patients = this.patients.filter(p => p.id !== patientId)
          if (this.selectedPatient?.id === patientId) {
            this.selectedPatient = null
          }
          this.showMessage('患者已删除', 'success')
        } catch (error) {
          this.showMessage('删除失败', 'error')
        }
      }
    },
    
    async clearAllPatients() {
      if (await this.showConfirm('确认清空', '确定要清空所有患者信息吗？此操作不可恢复！')) {
        try {
          this.patients = []
          this.selectedPatient = null
          this.showMessage('患者列表已清空', 'success')
        } catch (error) {
          this.showMessage('清空失败', 'error')
        }
      }
    },
    
    async savePatient() {
      try {
        // 验证表单
        if (!this.validateForm()) {
          return
        }
        
        if (this.editingPatient) {
          // 更新患者
          const index = this.patients.findIndex(p => p.id === this.editingPatient.id)
          if (index !== -1) {
            this.patients[index] = { ...this.patients[index], ...this.patientForm }
          }
          this.showMessage('患者信息已更新', 'success')
        } else {
          // 添加新患者
          const newPatient = {
            id: Date.now(),
            ...this.patientForm,
            registrationNumber: this.generateRegistrationNumber(),
            registrationTime: new Date(),
            status: 'waiting'
          }
          this.patients.unshift(newPatient)
          this.showMessage('患者已添加', 'success')
        }
        
        this.closeModal()
        
      } catch (error) {
        this.showMessage('保存失败', 'error')
      }
    },
    
    validateForm() {
      if (!this.patientForm.name.trim()) {
        this.showNotification('输入错误', '请输入患者姓名')
        return false
      }
      
      if (!this.patientForm.gender) {
        this.showNotification('输入错误', '请选择性别')
        return false
      }
      
      if (!this.patientForm.age || this.patientForm.age < 1 || this.patientForm.age > 120) {
        this.showNotification('输入错误', '请输入有效年龄')
        return false
      }
      
      if (!this.patientForm.phone.trim()) {
        this.showNotification('输入错误', '请输入联系电话')
        return false
      }
      
      if (!this.patientForm.idNumber.trim() || this.patientForm.idNumber.length !== 18) {
        this.showNotification('输入错误', '请输入有效的18位身份证号码')
        return false
      }
      
      return true
    },
    
    generateRegistrationNumber() {
      const today = new Date()
      const dateStr = today.getFullYear().toString().slice(-2) + 
                     (today.getMonth() + 1).toString().padStart(2, '0') + 
                     today.getDate().toString().padStart(2, '0')
      const sequence = (this.patients.length + 1).toString().padStart(3, '0')
      return parseInt(dateStr + sequence)
    },
    
    async generateReport(patient) {
      try {
        this.showMessage('正在生成报告...', 'info')
        // 实现报告生成逻辑
        this.$emit('generate-report', patient)
      } catch (error) {
        this.showMessage('生成报告失败', 'error')
      }
    },
    
    async exportPatientList() {
      try {
        const csvContent = this.generateCSV()
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = `患者列表_${new Date().toISOString().slice(0, 10)}.csv`
        link.click()
        
        this.showMessage('患者列表已导出', 'success')
      } catch (error) {
        this.showMessage('导出失败', 'error')
      }
    },
    
    generateCSV() {
      const headers = ['挂号码', '姓名', '性别', '年龄', '联系电话', '身份证号', '检查部位', '状态', '登记时间', '备注']
      const rows = this.patients.map(patient => [
        patient.registrationNumber,
        patient.name,
        patient.gender === 'male' ? '男' : '女',
        patient.age,
        patient.phone,
        patient.idNumber,
        patient.examPart || '',
        this.getStatusText(patient.status),
        this.formatTime(patient.registrationTime),
        patient.notes || ''
      ])
      
      return [headers, ...rows].map(row => 
        row.map(field => `"${field}"`).join(',')
      ).join('\n')
    },
    
    getStatusText(status) {
      const statusMap = {
        waiting: '等待中',
        processing: '检查中',
        completed: '已完成'
      }
      return statusMap[status] || status
    },
    
    closeModal() {
      this.showAddPatient = false
      this.editingPatient = null
      this.patientForm = {
        name: '',
        gender: '',
        age: '',
        phone: '',
        idNumber: '',
        examPart: '',
        notes: ''
      }
    },
    
    maskIdNumber(idNumber) {
      if (!idNumber || idNumber.length < 8) return idNumber
      return idNumber.slice(0, 6) + '****' + idNumber.slice(-4)
    },
    
    formatTime(time) {
      return new Date(time).toLocaleString()
    },
    
    showMessage(message, type = 'info') {
      console.log(`[${type}] ${message}`);
    },
    
    showNotification(title, message) {
      // 调用后端的置顶信息窗口通知
      if (window.go && window.go.main && window.go.main.App) {
        window.go.main.App.ShowWailsNotification('error', title, message, 5000)
      } else {
        // 备用方案：控制台输出
        console.error(`${title}: ${message}`)
      }
    },
    
    showConfirm(title, message) {
      // 使用置顶信息窗口显示确认消息，并返回Promise
      return new Promise((resolve) => {
        // 调用后端的置顶信息窗口通知
        if (window.go && window.go.main && window.go.main.App) {
          window.go.main.App.ShowWailsNotification('warning', title, message + ' (请在控制台确认)', 5000)
        }
        
        // 简化处理：直接返回true（实际应用中可以实现更复杂的确认机制）
        // 这里暂时使用原生confirm作为备用方案
        resolve(confirm(`${title}: ${message}`))
      })
    }
  }
}
</script>

<style scoped>
.patient-panel {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.panel-header {
  background: #f8f9fa;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.add-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.panel-content {
  padding: 16px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-section {
  flex-shrink: 0;
}

.search-bar {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
}

.search-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
}

.filter-bar {
  display: flex;
  gap: 8px;
  align-items: center;
}

.filter-select {
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
}

.export-btn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-left: auto;
}

.statistics-bar {
  display: flex;
  gap: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  flex-shrink: 0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: bold;
  color: #333;
}

.stat-value.waiting {
  color: #ffc107;
}

.stat-value.processing {
  color: #17a2b8;
}

.stat-value.completed {
  color: #28a745;
}

.patient-list {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.add-first-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.patient-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.patient-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.patient-item:hover {
  background: #f8f9fa;
  border-color: #3498db;
}

.patient-item.selected {
  background: #e3f2fd;
  border-color: #2196f3;
}

.patient-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 16px;
}

.avatar-circle.male {
  background: #3498db;
}

.avatar-circle.female {
  background: #e91e63;
}

.status-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-indicator.waiting {
  background: #ffc107;
}

.status-indicator.processing {
  background: #17a2b8;
}

.status-indicator.completed {
  background: #28a745;
}

.patient-info {
  flex: 1;
  min-width: 0;
}

.patient-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.patient-name {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.registration-number {
  font-size: 11px;
  color: #666;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

.patient-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-row {
  display: flex;
  gap: 8px;
  font-size: 11px;
  align-items: center;
}

.detail-row .label {
  color: #666;
  min-width: 50px;
}

.detail-row .value {
  color: #333;
}

.id-number {
  font-family: monospace;
  background: #f8f9fa;
  padding: 1px 3px;
  border-radius: 2px;
}

.time {
  color: #666;
  font-size: 10px;
}

.patient-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.action-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #e9ecef;
}

.action-btn.edit:hover {
  background: #fff3cd;
  border-color: #ffc107;
}

.action-btn.remove:hover {
  background: #f8d7da;
  border-color: #dc3545;
}

.action-btn.report:hover {
  background: #d1ecf1;
  border-color: #17a2b8;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-top: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.page-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 12px;
  color: #666;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-sizing: border-box;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
}

.patient-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-group label {
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.form-input,
.form-select,
.form-textarea {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
}

.form-textarea {
  resize: vertical;
  min-height: 60px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.save-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
</style>