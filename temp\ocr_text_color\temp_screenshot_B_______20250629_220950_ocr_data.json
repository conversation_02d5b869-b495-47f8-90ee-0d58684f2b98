{"logId": "e8664c51-9b88-4474-a727-323e2df1baa7", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 1, "label": "image", "score": 0.6694586277008057, "coordinate": [212.07510375976562, 0.6927019357681274, 768, 1422.5106201171875]}, {"cls_id": 22, "label": "aside_text", "score": 0.568352997303009, "coordinate": [11.26849365234375, 11.653910636901855, 50.018943786621094, 1409.922607421875]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[17, 23], [42, 23], [42, 48], [17, 48]], [[17, 86], [42, 86], [42, 111], [17, 111]], [[227, 79], [382, 79], [382, 102], [227, 102]], [[13, 136], [46, 136], [46, 168], [13, 168]], [[226, 130], [311, 134], [309, 161], [225, 157]], [[482, 132], [766, 134], [766, 157], [482, 155]], [[227, 168], [310, 168], [310, 193], [227, 193]], [[384, 170], [498, 170], [498, 189], [384, 189]], [[530, 168], [613, 168], [613, 193], [530, 193]], [[678, 170], [766, 170], [766, 189], [678, 189]], [[18, 197], [37, 197], [37, 214], [18, 214]], [[227, 204], [308, 204], [308, 229], [227, 229]], [[465, 202], [504, 202], [504, 231], [465, 231]], [[532, 204], [611, 204], [611, 229], [532, 229]], [[18, 250], [37, 250], [37, 268], [18, 268]], [[10, 309], [34, 297], [44, 318], [21, 330]], [[17, 358], [42, 358], [42, 381], [17, 381]], [[11, 415], [36, 405], [46, 428], [21, 439]], [[13, 459], [44, 459], [44, 492], [13, 492]], [[15, 517], [42, 517], [42, 543], [15, 543]], [[295, 540], [421, 540], [421, 559], [295, 559]], [[449, 538], [582, 538], [582, 563], [449, 563]], [[615, 538], [742, 538], [742, 563], [615, 563]], [[15, 570], [42, 570], [42, 597], [15, 597]], [[20, 629], [37, 629], [37, 647], [20, 647]], [[238, 626], [334, 626], [334, 651], [238, 651]], [[17, 679], [42, 679], [42, 704], [17, 704]], [[456, 685], [583, 681], [584, 711], [457, 715]], [[15, 733], [42, 733], [42, 758], [15, 758]], [[17, 786], [42, 786], [42, 813], [17, 813]], [[310, 812], [378, 812], [378, 838], [310, 838]], [[486, 812], [554, 812], [554, 838], [486, 838]], [[655, 810], [729, 810], [729, 842], [655, 842]], [[271, 824], [294, 824], [294, 853], [271, 853]], [[443, 826], [469, 826], [469, 851], [443, 851]], [[617, 826], [641, 826], [641, 851], [617, 851]], [[13, 838], [44, 838], [44, 871], [13, 871]], [[329, 840], [360, 840], [360, 865], [329, 865]], [[500, 838], [537, 838], [537, 867], [500, 867]], [[674, 837], [711, 837], [711, 869], [674, 869]], [[13, 890], [44, 890], [44, 922], [13, 922]], [[13, 942], [46, 942], [46, 976], [13, 976]], [[15, 1003], [44, 1003], [44, 1028], [15, 1028]], [[489, 994], [554, 994], [554, 1069], [489, 1069]], [[414, 1103], [628, 1103], [628, 1128], [414, 1128]], [[449, 1237], [593, 1237], [593, 1262], [449, 1262]], [[395, 1276], [644, 1276], [644, 1300], [395, 1300]], [[426, 1305], [617, 1305], [617, 1328], [426, 1328]], [[450, 1335], [593, 1335], [593, 1360], [450, 1360]], [[447, 1366], [592, 1364], [593, 1389], [447, 1391]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["51", "O", "磁感分析操作台", "Q", "检测站点：", "北京市通州区潞城镇潞城社区卫生服务", "站点编号：", "YL-BJ-TZ-001", "设备编号：", "00155ded", "Q", "今日报到：", "0人", "今日检测：", "Q", "🌸", "中", "🌸", "51", "51", "应用程序已启动", "测试前端Toast", "测试后端通知", "51", "Q", "当前受检者", ",", "暂无受检者", "3", "3", "待检测", "已完成", "待分析", "7", "☑", "W", "ü", "(0)", "(0)", "(0)", "凶", "ă", "v", "自", "暂无待检测的候检者", "Toast调试信息", "Wails Runtime状态: available", "事件监听器状态：active", "当前Toast数量：0", "最大Toast数量：3"], "rec_scores": [0.9995644092559814, 0.43476563692092896, 0.9967768788337708, 0.29507511854171753, 0.9704018831253052, 0.937240719795227, 0.9901466369628906, 0.995674192905426, 0.9918606877326965, 0.9840559363365173, 0.9787058234214783, 0.9783617258071899, 0.9847341775894165, 0.9118016958236694, 0.9736744165420532, 0.24194329977035522, 0.982731282711029, 0.3056175708770752, 0.9914751052856445, 0.9982342720031738, 0.9999151825904846, 0.9969832301139832, 0.9990296363830566, 0.9977153539657593, 0.9927694797515869, 0.9998197555541992, 0.16812235116958618, 0.9998449087142944, 0.3774080276489258, 0.39717891812324524, 0.9989087581634521, 0.9996815323829651, 0.999650239944458, 0.4033682644367218, 0.46425384283065796, 0.1959507316350937, 0.31015563011169434, 0.9276222586631775, 0.9659974575042725, 0.9851385951042175, 0.12770292162895203, 0.23574072122573853, 0.30293044447898865, 0.9797576069831848, 0.9983715415000916, 0.9995816349983215, 0.9694061279296875, 0.9899329543113708, 0.9713556170463562, 0.9732393622398376], "rec_polys": [[[17, 23], [42, 23], [42, 48], [17, 48]], [[17, 86], [42, 86], [42, 111], [17, 111]], [[227, 79], [382, 79], [382, 102], [227, 102]], [[13, 136], [46, 136], [46, 168], [13, 168]], [[226, 130], [311, 134], [309, 161], [225, 157]], [[482, 132], [766, 134], [766, 157], [482, 155]], [[227, 168], [310, 168], [310, 193], [227, 193]], [[384, 170], [498, 170], [498, 189], [384, 189]], [[530, 168], [613, 168], [613, 193], [530, 193]], [[678, 170], [766, 170], [766, 189], [678, 189]], [[18, 197], [37, 197], [37, 214], [18, 214]], [[227, 204], [308, 204], [308, 229], [227, 229]], [[465, 202], [504, 202], [504, 231], [465, 231]], [[532, 204], [611, 204], [611, 229], [532, 229]], [[18, 250], [37, 250], [37, 268], [18, 268]], [[10, 309], [34, 297], [44, 318], [21, 330]], [[17, 358], [42, 358], [42, 381], [17, 381]], [[11, 415], [36, 405], [46, 428], [21, 439]], [[13, 459], [44, 459], [44, 492], [13, 492]], [[15, 517], [42, 517], [42, 543], [15, 543]], [[295, 540], [421, 540], [421, 559], [295, 559]], [[449, 538], [582, 538], [582, 563], [449, 563]], [[615, 538], [742, 538], [742, 563], [615, 563]], [[15, 570], [42, 570], [42, 597], [15, 597]], [[20, 629], [37, 629], [37, 647], [20, 647]], [[238, 626], [334, 626], [334, 651], [238, 651]], [[17, 679], [42, 679], [42, 704], [17, 704]], [[456, 685], [583, 681], [584, 711], [457, 715]], [[15, 733], [42, 733], [42, 758], [15, 758]], [[17, 786], [42, 786], [42, 813], [17, 813]], [[310, 812], [378, 812], [378, 838], [310, 838]], [[486, 812], [554, 812], [554, 838], [486, 838]], [[655, 810], [729, 810], [729, 842], [655, 842]], [[271, 824], [294, 824], [294, 853], [271, 853]], [[443, 826], [469, 826], [469, 851], [443, 851]], [[617, 826], [641, 826], [641, 851], [617, 851]], [[13, 838], [44, 838], [44, 871], [13, 871]], [[329, 840], [360, 840], [360, 865], [329, 865]], [[500, 838], [537, 838], [537, 867], [500, 867]], [[674, 837], [711, 837], [711, 869], [674, 869]], [[13, 890], [44, 890], [44, 922], [13, 922]], [[13, 942], [46, 942], [46, 976], [13, 976]], [[15, 1003], [44, 1003], [44, 1028], [15, 1028]], [[489, 994], [554, 994], [554, 1069], [489, 1069]], [[414, 1103], [628, 1103], [628, 1128], [414, 1128]], [[449, 1237], [593, 1237], [593, 1262], [449, 1262]], [[395, 1276], [644, 1276], [644, 1300], [395, 1300]], [[426, 1305], [617, 1305], [617, 1328], [426, 1328]], [[450, 1335], [593, 1335], [593, 1360], [450, 1360]], [[447, 1366], [592, 1364], [593, 1389], [447, 1391]]], "rec_boxes": [[17, 23, 42, 48], [17, 86, 42, 111], [227, 79, 382, 102], [13, 136, 46, 168], [225, 130, 311, 161], [482, 132, 766, 157], [227, 168, 310, 193], [384, 170, 498, 189], [530, 168, 613, 193], [678, 170, 766, 189], [18, 197, 37, 214], [227, 204, 308, 229], [465, 202, 504, 231], [532, 204, 611, 229], [18, 250, 37, 268], [10, 297, 44, 330], [17, 358, 42, 381], [11, 405, 46, 439], [13, 459, 44, 492], [15, 517, 42, 543], [295, 540, 421, 559], [449, 538, 582, 563], [615, 538, 742, 563], [15, 570, 42, 597], [20, 629, 37, 647], [238, 626, 334, 651], [17, 679, 42, 704], [456, 681, 584, 715], [15, 733, 42, 758], [17, 786, 42, 813], [310, 812, 378, 838], [486, 812, 554, 838], [655, 810, 729, 842], [271, 824, 294, 853], [443, 826, 469, 851], [617, 826, 641, 851], [13, 838, 44, 871], [329, 840, 360, 865], [500, 838, 537, 867], [674, 837, 711, 869], [13, 890, 44, 922], [13, 942, 46, 976], [15, 1003, 44, 1028], [489, 994, 554, 1069], [414, 1103, 628, 1128], [449, 1237, 593, 1262], [395, 1276, 644, 1300], [426, 1305, 617, 1328], [450, 1335, 593, 1360], [447, 1364, 593, 1391]]}, "table_res_list": []}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//e8664c51-9b88-4474-a727-323e2df1baa7/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T14%3A10%3A04Z%2F-1%2F%2F6b09f5b28ddc433c97c2b8f41942487c395f7823efbae1e1d09fb6528a2bff1b", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//e8664c51-9b88-4474-a727-323e2df1baa7/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T14%3A10%3A04Z%2F-1%2F%2F2665fec6cf846283a23ba67d2105b5463b6bca8ee0897bc2f209ea9967021a65"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//e8664c51-9b88-4474-a727-323e2df1baa7/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-29T14%3A10%3A04Z%2F-1%2F%2F681e130c4c927404df5fdafe2ee92a767dd101b15f644ab71f55f286d251689f"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}