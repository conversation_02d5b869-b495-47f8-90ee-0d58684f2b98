# 用户交互消息配置文件
# 用于配置Toast通知和页面状态显示的所有消息内容

# 操作状态消息 (currentOperationStatus)
operation_status:
  # 初始状态
  initial: "等待您选择目标器官/部位，进行“B02生化平衡分析”采样..."
  
  # B02模式完成后的状态
  b02_completed:
    template: "B02生化平衡分析已加入队列，等待“C03病理形态学分析”启动..."
  

  
  # C03模式完成后的状态
  c03_completed_final:
    template: "C03病理形态学分析已加入队列，所有检测任务完成，请等待最终分析结果..."

# Toast通知消息
toast_messages:
  # 成功消息
  success:
    screenshot_b02_success: "B02生化平衡分析截图成功"
    screenshot_c03_success: "C03病理形态学分析截图成功"

    ocr_processing_success: "OCR识别处理成功"
    color_detection_success: "颜色检测分析完成"
    
  # 进度消息
  progress:
    ocr_processing: "正在进行OCR文字识别..."
    color_detection: "正在进行颜色检测分析..."
    data_extraction: "正在提取检测数据..."
    updating_records: "正在更新检测记录..."
    
  # 错误消息
  error:
    screenshot_failed: "截图操作失败，请重试"
    ocr_failed: "OCR识别失败，请重试"
    color_detection_failed: "颜色检测失败，请重试"
    data_save_failed: "数据保存失败，请重试"
    
  # 警告消息
  warning:
    duplicate_organ: "检测到重复器官：{organ_name}，是否继续？"


# 按钮文本配置
button_texts:
  screenshot_b02: "B02生化平衡分析截图"
  screenshot_c03: "C03病理形态学分析截图"
  continue_detection: "继续检测"
  
  generate_report: "生成健康报告"

# 进度条配置
progress_bar:
  colors:
    b02_mode: "#2196f3"  # 蓝色
    c03_mode: "#4caf50"  # 绿色
  
  # 进度计算：每轮包含B02和C03两个步骤，共20个步骤

  total_steps: 20

# 模式显示名称
mode_names:
  B: "B02生化平衡分析"
  C: "C03病理形态学分析"
  B02: "B02生化平衡分析"
  C03: "C03病理形态学分析"
