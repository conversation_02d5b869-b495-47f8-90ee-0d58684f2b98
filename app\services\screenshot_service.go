package services

import (
	"context"
	"fmt"
	"image"
	"image/color"
	"image/png"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"

	json "github.com/goccy/go-json"

	"MagneticOperator/app/models"

	"github.com/kbinani/screenshot"
)

// ScreenshotService 截图服务
type ScreenshotService struct {
	configService  *ConfigService
	ocrService     OCRInterface
	getPatientInfo func() (string, string)                          // 获取患者姓名和报到号的回调函数
	addFailedTask  func(userName, mode, imagePath, errorMsg string) // 添加失败任务的回调函数
}

// NewScreenshotService 创建新的截图服务实例
func NewScreenshotService(configService *ConfigService) *ScreenshotService {
	return &ScreenshotService{
		configService: configService,
		ocrService:    NewOCRService(configService, nil), // 注意：这里传递nil，因为screenshot_service中没有app实例
	}
}

// SetPatientInfoCallback 设置获取患者信息的回调函数
func (ss *ScreenshotService) SetPatientInfoCallback(callback func() (string, string)) {
	ss.getPatientInfo = callback
}

// SetFailedTaskCallback 设置添加失败任务的回调函数
func (ss *ScreenshotService) SetFailedTaskCallback(callback func(userName, mode, imagePath, errorMsg string)) {
	ss.addFailedTask = callback
}

// TakeScreenshot 截取屏幕截图
func (ss *ScreenshotService) TakeScreenshot(mode string, userName string) (string, error) {
	// 使用kbinani/screenshot库进行截图
	bounds := screenshot.GetDisplayBounds(0)
	img, err := screenshot.CaptureRect(bounds)
	if err != nil {
		return "", fmt.Errorf("截图失败: %v", err)
	}
	// 保存截图为PNG文件
	fileName := fmt.Sprintf("%s_%dx%d.png", userName, bounds.Dx(), bounds.Dy())
	file, err := os.Create(fileName)
	if err != nil {
		return "", fmt.Errorf("无法创建文件: %v", err)
	}
	defer file.Close()
	err = png.Encode(file, img)
	if err != nil {
		return "", fmt.Errorf("无法编码PNG: %v", err)
	}
	return fileName, nil
}

// TakeScreenshotWithOptimizedNaming 使用优化命名规则的截图方法
// 文件命名规则：用户名_报到号_器官部位_列表类型.png
// 示例：张三_RPT20240616001_心脏_B02.png
func (ss *ScreenshotService) TakeScreenshotWithOptimizedNaming(mode string, userName string, organName string, currentUser interface{}) (string, error) {
	// 获取屏幕截图
	bounds := screenshot.GetDisplayBounds(0)
	img, err := screenshot.CaptureRect(bounds)
	if err != nil {
		return "", fmt.Errorf("截图失败: %v", err)
	}

	// 获取配置
	config := ss.configService.GetConfig()
	if config == nil {
		return "", fmt.Errorf("配置未加载")
	}

	// 裁剪图像
	croppedImg := ss.cropImage(img, config.CropSettings)

	// 图像预处理
	// processedImg := ss.preprocessImage(croppedImg)

	// 生成优化的文件名
	// 文件命名规则：用户名_器官部位_检测报告号_列表类型.png
	fileName := ss.generateOptimizedFilename(mode, userName, organName, currentUser)
	filePath := filepath.Join("pic", fileName)

	// 确保目录存在
	if err := os.MkdirAll("pic", 0755); err != nil {
		return "", fmt.Errorf("创建目录失败: %v", err)
	}

	// 保存图像
	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	if err := png.Encode(file, croppedImg); err != nil {
		return "", fmt.Errorf("保存图像失败: %v", err)
	}

	return filePath, nil
}

// TakeScreenshotWithOCRAsync 截取屏幕截图并异步进行OCR识别
// 这个方法保持原有截图功能不变，同时启动异步OCR识别
func (ss *ScreenshotService) TakeScreenshotWithOCRAsync(mode string, userName string) (string, error) {
	// 1. 首先执行原有的截图功能（保持不变）
	filePath, err := ss.TakeScreenshot(mode, userName)
	if err != nil {
		return "", err
	}

	// 2. 异步启动OCR识别（不影响主流程）
	go ss.performAsyncOCRAnalysis(filePath, mode, userName)

	return filePath, nil
}

// performAsyncOCRAnalysis 异步执行OCR分析
func (ss *ScreenshotService) performAsyncOCRAnalysis(originalFilePath, mode, userName string) {
	// 使用专门的OCR区域截图方法
	ocrFilePath, organName, err := ss.TakeOCRRegionScreenshot(mode, userName)
	if err != nil {
		fmt.Printf("OCR区域截图失败: %v\n", err)

		// 将失败的OCR任务添加到失败任务管理器
		if ss.addFailedTask != nil {
			ss.addFailedTask(userName, mode, originalFilePath, err.Error())
			fmt.Printf("[INFO] 已将失败的异步OCR任务添加到重试队列: %s\n", originalFilePath)
		}
		return
	}

	// 记录OCR识别结果
	fmt.Printf("OCR识别完成 - 原始文件: %s, OCR文件: %s, 器官: %s\n", originalFilePath, ocrFilePath, organName)

	// 这里可以将OCR结果保存到数据库或发送到前端
	// TODO: 将OCR结果保存到数据库或通知前端
}

// TakeOCRRegionScreenshot 直接截取屏幕左上角区域用于OCR识别
// 这个方法专门用于OCR，只截取左上角1/4区域
func (ss *ScreenshotService) TakeOCRRegionScreenshot(mode string, userName string) (string, string, error) {
	fmt.Printf("[OCR截图] 开始OCR区域截图 - 模式: %s, 用户: %s\n", mode, userName)

	// 截取屏幕截图
	bounds := screenshot.GetDisplayBounds(0)
	rawImg, err := screenshot.CaptureRect(bounds)
	if err != nil {
		return "", "", fmt.Errorf("截图失败: %v", err)
	}

	// 将原始图像转换为image.Image接口类型
	var img image.Image = rawImg

	// 裁剪到左上角1/4区域用于OCR
	ocrImg := ss.cropImageForOCR(img)

	// 生成OCR文件名
	timestamp := time.Now().Format("20060102_150405")
	ocrFileName := fmt.Sprintf("ocr_%s_%s_%s.png", mode, userName, timestamp)
	ocrFilePath := filepath.Join("pic", "temp", ocrFileName)

	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(ocrFilePath), 0755); err != nil {
		return "", "", fmt.Errorf("创建OCR目录失败: %v", err)
	}

	// 保存OCR图像
	file, err := os.Create(ocrFilePath)
	if err != nil {
		return "", "", fmt.Errorf("创建OCR文件失败: %v", err)
	}
	defer file.Close()

	if err := png.Encode(file, ocrImg); err != nil {
		return "", "", fmt.Errorf("保存OCR图像失败: %v", err)
	}

	// 模拟OCR识别（这里应该调用真实的OCR API）
	organName := ss.simulateOCRRecognition(mode)

	fmt.Printf("[OCR截图] OCR区域截图完成 - 文件: %s, 器官: %s\n", ocrFilePath, organName)
	return ocrFilePath, organName, nil
}

// cropImageForOCR 裁剪图像到左上角1/4区域用于OCR
func (ss *ScreenshotService) cropImageForOCR(img image.Image) image.Image {
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 裁剪到左上角1/4区域
	cropWidth := width / 4
	cropHeight := height / 4

	cropRect := image.Rect(0, 0, cropWidth, cropHeight)

	// 创建新的图像
	croppedImg := image.NewRGBA(cropRect)

	// 复制像素
	for y := 0; y < cropHeight; y++ {
		for x := 0; x < cropWidth; x++ {
			croppedImg.Set(x, y, img.At(x, y))
		}
	}

	return croppedImg
}

// simulateOCRRecognition 模拟OCR识别（临时方案）
func (ss *ScreenshotService) simulateOCRRecognition(mode string) string {
	// 根据模式返回不同的器官名称（模拟OCR识别结果）
	organs := []string{"心脏", "肝脏", "肺部", "肾脏", "胃部", "脾脏", "胰腺", "胆囊", "甲状腺", "前列腺"}

	// 使用当前时间的秒数作为随机种子
	index := int(time.Now().Unix()) % len(organs)
	organName := organs[index]

	fmt.Printf("[OCR模拟] 模式: %s, 识别器官: %s\n", mode, organName)
	return organName
}

// CropImage 裁剪图像（公开方法）
func (ss *ScreenshotService) CropImage(img image.Image, cropSettings models.CropSettings) image.Image {
	return ss.cropImage(img, cropSettings)
}

// cropImage 裁剪图像
func (ss *ScreenshotService) cropImage(img image.Image, cropSettings models.CropSettings) image.Image {
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 计算裁剪区域
	left := int(float64(width) * cropSettings.LeftPercent)
	right := width - int(float64(width)*cropSettings.RightPercent)
	top := int(float64(height) * cropSettings.TopPercent)
	bottom := height - int(float64(height)*cropSettings.BottomPercent)

	// 创建裁剪后的图像
	croppedBounds := image.Rect(0, 0, right-left, bottom-top)
	croppedImg := image.NewRGBA(croppedBounds)

	for y := top; y < bottom; y++ {
		for x := left; x < right; x++ {
			croppedImg.Set(x-left, y-top, img.At(x, y))
		}
	}

	return croppedImg
}

// 辅助函数
func (ss *ScreenshotService) maxf(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

func (ss *ScreenshotService) minf(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

// cleanOrganName 清理器官名称
func (ss *ScreenshotService) cleanOrganName(organName string) string {
	// 移除常见的无关字符和词汇
	cleanName := strings.TrimSpace(organName)
	cleanName = strings.Trim(cleanName, "()[]{}")
	cleanName = strings.ReplaceAll(cleanName, "检测", "")
	cleanName = strings.ReplaceAll(cleanName, "分析", "")
	cleanName = strings.ReplaceAll(cleanName, "报告", "")
	cleanName = strings.TrimSpace(cleanName)

	// 移除数字和特殊符号
	re := regexp.MustCompile(`[0-9\-\+\=\(\)\[\]\{\}]`)
	cleanName = re.ReplaceAllString(cleanName, "")

	// 只保留中文字符和常见的英文字母
	var result strings.Builder
	for _, r := range cleanName {
		if unicode.Is(unicode.Han, r) || (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') {
			result.WriteRune(r)
		}
	}

	return result.String()
}

// generateOptimizedFilename 生成优化的文件名
// 文件命名规则：当前受检用户名_报到号_截图模式_受检器官名称_截图时间.png
// 示例：张三_REG20240616001_器官问题来源分析_心脏_20240616_143022.png
func (ss *ScreenshotService) generateOptimizedFilename(mode string, userName string, organName string, currentUser interface{}) string {
	// 清理用户名（移除特殊字符）
	cleanUserName := ss.cleanFilenameString(userName)

	// 清理器官名称
	cleanOrganName := ss.cleanFilenameString(organName)

	// 清理截图模式名称
	cleanMode := ss.cleanFilenameString(mode)

	// 生成报到号：REG + 日期 + 序号 (假设报到号格式，如果 currentUser 包含报到号则使用它)
	registrationNo := ""
	if cu, ok := currentUser.(map[string]interface{}); ok {
		if regNo, ok := cu["registration_no"].(string); ok && regNo != "" {
			registrationNo = ss.cleanFilenameString(regNo)
		}
	}
	if registrationNo == "" {
		// 检查是否为开发模式下的测试用户
		if userName == "test-11200" {
			registrationNo = "REG20250701test"
			fmt.Printf("[开发模式] 使用测试报到号: %s\n", registrationNo)
		} else {
			timestamp := time.Now()
			registrationNo = fmt.Sprintf("REG%s%03d", timestamp.Format("20060102"), timestamp.Nanosecond()/1000000) // 使用纳秒保证唯一性
		}
	}

	// 截图时间
	screenshotTime := time.Now().Format("20060102_150405")

	// 生成最终文件名
	// 格式：当前受检用户名_报到号_截图模式_受检器官名称_截图时间.png
	filename := fmt.Sprintf("%s_%s_%s_%s_%s.png",
		cleanUserName, registrationNo, cleanMode, cleanOrganName, screenshotTime)

	return filename
}

func (ss *ScreenshotService) minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func (ss *ScreenshotService) maxInt(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// --- End of precise cropping helper functions ---

// cleanFilenameString 清理文件名字符串，移除不适合文件名的字符
func (ss *ScreenshotService) cleanFilenameString(input string) string {
	if input == "" {
		return "未知"
	}

	// 移除或替换不适合文件名的字符
	cleaned := strings.ReplaceAll(input, " ", "")
	cleaned = strings.ReplaceAll(cleaned, "/", "_")
	cleaned = strings.ReplaceAll(cleaned, "\\", "_")
	cleaned = strings.ReplaceAll(cleaned, ":", "_")
	cleaned = strings.ReplaceAll(cleaned, "*", "_")
	cleaned = strings.ReplaceAll(cleaned, "?", "_")
	cleaned = strings.ReplaceAll(cleaned, "\"", "_")
	cleaned = strings.ReplaceAll(cleaned, "<", "_")
	cleaned = strings.ReplaceAll(cleaned, ">", "_")
	cleaned = strings.ReplaceAll(cleaned, "|", "_")

	// 如果清理后为空，返回默认值
	if cleaned == "" {
		return "未知"
	}

	return cleaned
}

// TakeScreenshotWithPreprocessing 截取完整屏幕截图并进行预处理（步骤1）
func (ss *ScreenshotService) TakeScreenshotWithPreprocessing(mode string, userName string) (string, error) {
	// 截取屏幕截图
	bounds := screenshot.GetDisplayBounds(0)
	rawImg, err := screenshot.CaptureRect(bounds)
	if err != nil {
		return "", fmt.Errorf("截图失败: %v", err)
	}

	// 将原始图像转换为image.Image接口类型
	var img image.Image = rawImg

	// 获取配置并进行裁剪
	config := ss.configService.GetConfig()
	if config != nil {
		img = ss.cropImage(img, config.CropSettings)
	}

	// 预处理图像
	// processedImg := ss.preprocessImage(img)

	// 生成临时文件名
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("temp_screenshot_%s_%s_%s.png", mode, userName, timestamp)

	// 获取当前患者报到号
	registrationNumber := ss.getCurrentPatientRegistrationNumber()

	// 生成患者目录名：受检者名称+报到号
	patientDir := fmt.Sprintf("%s+%s", userName, registrationNumber)
	filePath := filepath.Join("pic", "temp", patientDir, fileName)

	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
		return "", fmt.Errorf("创建目录失败: %v", err)
	}

	// 保存图像
	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	if err := png.Encode(file, img); err != nil {
		return "", fmt.Errorf("保存图像失败: %v", err)
	}

	return filePath, nil
}

// ExtractOrganNameFromOCRResponse 从OCR响应JSON数据中提取器官名称（第三步）
// 从rec_texts数组中查找以"0.000"为标志的数值对，其后的文本内容就是当前检测结果分析的当前器官/部位名称
func (ss *ScreenshotService) ExtractOrganNameFromOCRResponse(ocrResult *OCRResult) (string, error) {
	if ocrResult == nil {
		return "", fmt.Errorf("OCR结果为空")
	}

	// 首先尝试使用已经解析的器官名称
	if ocrResult.OrganName != "" && ocrResult.OrganName != "未识别" {
		fmt.Printf("[DEBUG] 从OCR结果中获取器官名称: %s\n", ocrResult.OrganName)
		return ocrResult.OrganName, nil
	}

	// 如果没有解析的器官名称，尝试从原始JSON数据中解析
	// 这里需要解析rec_texts数组，查找"0.000"标志
	if ocrResult.RawResponse != nil {
		// 解析原始响应JSON
		var responseData map[string]interface{}
		err := json.Unmarshal(ocrResult.RawResponse, &responseData)
		if err != nil {
			fmt.Printf("[DEBUG] 解析原始OCR响应失败: %v\n", err)
			return "未知器官", fmt.Errorf("解析OCR响应失败: %v", err)
		}

		// 查找rec_texts数组（字符串数组格式）
		if recTexts, ok := responseData["rec_texts"].([]interface{}); ok {
			fmt.Printf("[DEBUG] 找到rec_texts数组，长度: %d\n", len(recTexts))
			for i, item := range recTexts {
				if text, ok := item.(string); ok {
					text = strings.TrimSpace(text)
					fmt.Printf("[DEBUG] rec_texts[%d]: %s\n", i, text)
					// 检查是否为"0.000"
					if text == "0.000" {
						// 查找下一个非空文本作为器官名称
						for j := i + 1; j < len(recTexts); j++ {
							if nextItem, ok := recTexts[j].(string); ok {
								nextText := strings.TrimSpace(nextItem)
								if nextText != "" && nextText != "0.000" {
									// 清理器官名称
									organName := ss.cleanOrganName(nextText)
									if organName != "" {
										fmt.Printf("[DEBUG] 从rec_texts中提取器官名称: %s (原始: %s)\n", organName, nextText)
										return organName, nil
									}
								}
							}
						}
						break
					}
				}
			}
		}
	}

	fmt.Printf("[DEBUG] 未能从OCR响应中提取器官名称，使用默认值\n")
	return "未知器官", fmt.Errorf("未能从OCR响应中提取器官名称")
}

// ExtractHealthTrendDataFromOCR 从OCR结果中提取D值列表数据（第四步）
func (ss *ScreenshotService) ExtractHealthTrendDataFromOCR(ocrResult *OCRResult) (map[string]string, error) {
	if ocrResult == nil {
		return nil, fmt.Errorf("OCR结果为空")
	}

	// 解析原始响应JSON以提取D值数据
	if ocrResult.RawResponse != nil {
		var responseData map[string]interface{}
		err := json.Unmarshal(ocrResult.RawResponse, &responseData)
		if err != nil {
			fmt.Printf("[DEBUG] 解析原始OCR响应失败: %v\n", err)
			return nil, fmt.Errorf("解析OCR响应失败: %v", err)
		}

		// 提取D值相关数据
		dHealthTrend := make(map[string]string)

		// 查找rec_texts数组中的D值数据
		if recTexts, ok := responseData["rec_texts"].([]interface{}); ok {
			for _, item := range recTexts {
				if itemArray, ok := item.([]interface{}); ok && len(itemArray) >= 2 {
					if text, ok := itemArray[0].(string); ok {
						// 查找包含D值的文本模式
						if strings.Contains(text, "D") && (strings.Contains(text, ":") || strings.Contains(text, "=")) {
							// 解析D值数据
							parts := strings.Split(text, ":")
							if len(parts) < 2 {
								parts = strings.Split(text, "=")
							}
							if len(parts) >= 2 {
								key := strings.TrimSpace(parts[0])
								value := strings.TrimSpace(parts[1])
								dHealthTrend[key] = value
								fmt.Printf("[DEBUG] 提取D值数据: %s = %s\n", key, value)
							}
						}
					}
				}
			}
		}

		fmt.Printf("[DEBUG] 从OCR结果中提取到 %d 个D值数据\n", len(dHealthTrend))
		return dHealthTrend, nil
	}

	return nil, fmt.Errorf("OCR结果中没有原始响应数据")
}

// ExtractHealthTrendData 对完整图像进行OCR识别并提取D值列表（原有功能保留）
func (ss *ScreenshotService) ExtractHealthTrendData(imagePath string) (map[string]string, error) {
	if ss.ocrService == nil {
		return nil, fmt.Errorf("OCR服务未初始化")
	}

	// 使用ProcessImageWithDetails获取完整OCR结果
	ocrResult, err := ss.ocrService.ProcessImageWithDetails(context.Background(), imagePath)
	if err != nil {
		return nil, fmt.Errorf("OCR处理失败: %v", err)
	}

	// 基于键值对数据解析D值数据
	dHealthTrend := ss.parseHealthTrendDataFromKeyValuePairs(ocrResult.KeyValuePairs)
	return dHealthTrend, nil
}

// parseHealthTrendDataFromKeyValuePairs 从OCR键值对数据中解析D值和器官名称的配对数据
func (ss *ScreenshotService) parseHealthTrendDataFromKeyValuePairs(keyValuePairs map[string]string) map[string]string {
	dHealthTrend := make(map[string]string)

	// 从键值对中查找数值型的键，这些通常代表D值
	for key, value := range keyValuePairs {
		// 检查键是否为数值格式（可能包含小数点）
		if isNumeric(key) {
			// 清理器官名称（移除特殊字符）
			organName := ss.cleanOrganName(value)
			if organName != "" {
				// 格式化为"数值 (D值): 器官名称"
				dValueKey := fmt.Sprintf("%s (D值)", key)
				dHealthTrend[dValueKey] = organName
			}
		}
	}

	return dHealthTrend
}

// isNumeric 检查字符串是否为数值
func isNumeric(s string) bool {
	_, err := strconv.ParseFloat(s, 64)
	return err == nil
}

// SaveFinalScreenshotWithData 保存最终截图文件和JSON数据（步骤5）
func (ss *ScreenshotService) SaveFinalScreenshotWithData(tempFilePath string, mode string, userName string, organName string, currentUser interface{}, dHealthTrend map[string]string) (string, string, error) {
	// 获取当前患者报到号
	registrationNumber := ss.getCurrentPatientRegistrationNumber()

	// 生成患者目录名：受检者名称+报到号
	patientDir := fmt.Sprintf("%s+%s", userName, registrationNumber)

	// 生成最终文件名
	finalFileName := ss.generateOptimizedFilename(mode, userName, organName, currentUser)
	finalFilePath := filepath.Join("pic", patientDir, finalFileName)

	// 复制临时文件到最终位置
	err := ss.copyFile(tempFilePath, finalFilePath)
	if err != nil {
		return "", "", fmt.Errorf("复制文件失败: %v", err)
	}

	// 生成JSON文件名（与图片文件同名，但扩展名为.json）
	jsonFileName := strings.TrimSuffix(finalFileName, ".png") + "_D_health_trend.json"
	jsonFilePath := filepath.Join("pic", patientDir, jsonFileName)

	// 保存JSON数据
	err = ss.saveHealthTrendJSON(jsonFilePath, dHealthTrend)
	if err != nil {
		return finalFilePath, "", fmt.Errorf("保存JSON数据失败: %v", err)
	}

	// 删除临时文件
	os.Remove(tempFilePath)

	return finalFilePath, jsonFilePath, nil
}

// getCurrentPatientRegistrationNumber 获取当前患者报到号
func (ss *ScreenshotService) getCurrentPatientRegistrationNumber() string {
	if ss.getPatientInfo != nil {
		_, registrationNumber := ss.getPatientInfo()
		return registrationNumber
	}
	return "00000"
}

// copyFile 复制文件
func (ss *ScreenshotService) copyFile(src, dst string) error {
	// 确保目标目录存在
	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return err
	}

	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = dstFile.ReadFrom(srcFile)
	return err
}

// saveHealthTrendJSON 保存健康趋势JSON数据
func (ss *ScreenshotService) saveHealthTrendJSON(filePath string, dHealthTrend map[string]string) error {
	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
		return err
	}

	// 创建JSON文件
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 格式化JSON数据
	jsonData := map[string]interface{}{
		"timestamp":      time.Now().Format("2006-01-02 15:04:05"),
		"d_health_trend": dHealthTrend,
		"total_count":    len(dHealthTrend),
	}

	// 写入JSON数据（格式化输出）
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	return encoder.Encode(jsonData)
}

// parseOrganNameFromOCRText 从OCR JSON响应中解析当前检测的器官名称
// 根据OCR API响应格式，查找rec_texts中数值为"0.000"后的文本作为器官名称
func (ss *ScreenshotService) parseOrganNameFromOCRText(responseBody []byte) string {
	// 解析JSON响应
	var apiResp map[string]interface{}
	if err := json.Unmarshal(responseBody, &apiResp); err != nil {
		fmt.Printf("[WARNING] 解析OCR JSON响应失败: %v\n", err)
		return ""
	}

	// 从prunedResult.rec_texts中提取文本数组
	var recTexts []string
	if prunedResult, ok := apiResp["prunedResult"]; ok {
		if prunedMap, ok := prunedResult.(map[string]interface{}); ok {
			if recTextsRaw, ok := prunedMap["rec_texts"]; ok {
				if recTextsArray, ok := recTextsRaw.([]interface{}); ok {
					for _, item := range recTextsArray {
						if text, ok := item.(string); ok {
							recTexts = append(recTexts, text)
						}
					}
				}
			}
		}
	}

	// 如果没有找到prunedResult.rec_texts，尝试从results.text_data.rec_texts中提取
	if len(recTexts) == 0 {
		if results, ok := apiResp["results"]; ok {
			if resultsList, ok := results.([]interface{}); ok {
				for _, item := range resultsList {
					if itemMap, ok := item.(map[string]interface{}); ok {
						if textData, ok := itemMap["text_data"].([]interface{}); ok {
							for _, textItem := range textData {
								if textMap, ok := textItem.(map[string]interface{}); ok {
									if recTextsRaw, ok := textMap["rec_texts"].([]interface{}); ok {
										for _, recText := range recTextsRaw {
											if text, ok := recText.(string); ok {
												recTexts = append(recTexts, text)
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// 在rec_texts数组中查找"0.000"，并返回其后的文本作为器官名称
	for i, text := range recTexts {
		text = strings.TrimSpace(text)
		if text == "0.000" {
			// 查找下一个非空文本作为器官名称
			for j := i + 1; j < len(recTexts); j++ {
				nextText := strings.TrimSpace(recTexts[j])
				if nextText != "" {
					// 清理器官名称（移除特殊字符）
					organName := ss.cleanOrganName(nextText)
					if organName != "" {
						return organName
					}
				}
			}
			break
		}
	}

	// 如果没有找到"0.000"对应的器官名称，返回空字符串
	return ""
}

// fuzzyMatchOrganName 模糊匹配器官名称
func (ss *ScreenshotService) fuzzyMatchOrganName(text string, organParts []OrganPart) string {
	// 器官关键词列表
	organKeywords := []string{
		"心脏", "肝脏", "肺", "肾脏", "脾脏", "胃", "肠", "胆囊", "胰腺",
		"甲状腺", "前列腺", "子宫", "卵巢", "乳腺", "膀胱", "食管",
		"十二指肠", "小肠", "大肠", "结肠", "直肠", "阑尾",
		"心", "肝", "脾", "肺", "肾", "胆", "胰", "胃肠", "肠胃",
		"头部", "颈部", "胸部", "腹部", "盆腔", "四肢",
		"脑", "脑部", "头颅", "颅脑", "脊柱", "脊椎",
		"骨骼", "关节", "肌肉", "血管", "神经",
	}

	// 在文本中查找器官关键词
	for _, keyword := range organKeywords {
		if strings.Contains(text, keyword) {
			// 找到关键词后，在器官配置中查找包含该关键词的器官名称
			for _, organPart := range organParts {
				if strings.Contains(organPart.Name, keyword) {
					return organPart.Name
				}
			}
			// 如果没有找到配置中的器官，返回关键词本身
			return keyword
		}
	}

	return ""
}

// loadOrganPartsConfig 加载器官部位配置
func (ss *ScreenshotService) loadOrganPartsConfig() ([]OrganPart, error) {
	var organParts []OrganPart

	// 读取配置文件
	configPath := filepath.Join("config", "detection_Indicator_organ_part.json")
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取器官部位配置文件失败: %v", err)
	}

	// 解析JSON
	var configItems []struct {
		ID     string `json:"id"`
		Name   string `json:"名称"`
		Gender string `json:"性别"`
	}

	if err := json.Unmarshal(data, &configItems); err != nil {
		return nil, fmt.Errorf("解析器官部位配置失败: %v", err)
	}

	// 转换为OrganPart结构
	for _, item := range configItems {
		organParts = append(organParts, OrganPart{
			ID:     item.ID,
			Name:   item.Name,
			Gender: item.Gender,
		})
	}

	return organParts, nil
}

// parseHealthTrendData 解析健康趋势数据，从OCR JSON响应的rec_texts字段中提取"数值 (D值): 器官名称"格式的数据（已废弃，保留用于兼容性）
func (ss *ScreenshotService) parseHealthTrendData(responseBody []byte) map[string]string {
	dHealthTrend := make(map[string]string)

	// 解析JSON响应
	var apiResp map[string]interface{}
	if err := json.Unmarshal(responseBody, &apiResp); err != nil {
		fmt.Printf("[WARNING] 解析OCR JSON响应失败: %v\n", err)
		return dHealthTrend
	}

	// 从prunedResult.rec_texts中提取文本数组
	var recTexts []string
	if prunedResult, ok := apiResp["prunedResult"]; ok {
		if prunedMap, ok := prunedResult.(map[string]interface{}); ok {
			if recTextsRaw, ok := prunedMap["rec_texts"]; ok {
				if recTextsArray, ok := recTextsRaw.([]interface{}); ok {
					for _, item := range recTextsArray {
						if text, ok := item.(string); ok {
							recTexts = append(recTexts, text)
						}
					}
				}
			}
		}
	}

	// 如果没有找到prunedResult.rec_texts，尝试从results.text_data.rec_texts中提取
	if len(recTexts) == 0 {
		if results, ok := apiResp["results"]; ok {
			if resultsList, ok := results.([]interface{}); ok {
				for _, item := range resultsList {
					if itemMap, ok := item.(map[string]interface{}); ok {
						if textData, ok := itemMap["text_data"].([]interface{}); ok {
							for _, textItem := range textData {
								if textMap, ok := textItem.(map[string]interface{}); ok {
									if recTextsRaw, ok := textMap["rec_texts"].([]interface{}); ok {
										for _, recText := range recTextsRaw {
											if text, ok := recText.(string); ok {
												recTexts = append(recTexts, text)
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// 分析rec_texts数组，查找数值和器官名称的配对
	for i, text := range recTexts {
		text = strings.TrimSpace(text)
		if text == "" {
			continue
		}

		// 检查是否为数值（可能包含小数点）
		if isNumeric(text) {
			// 查找下一个文本作为器官名称
			if i+1 < len(recTexts) {
				nextText := strings.TrimSpace(recTexts[i+1])
				if nextText != "" && !isNumeric(nextText) {
					// 清理器官名称（移除特殊字符）
					organName := ss.cleanOrganName(nextText)
					if organName != "" {
						// 格式化为"数值 (D值): 器官名称"
						dValueKey := fmt.Sprintf("%s (D值)", text)
						dHealthTrend[dValueKey] = organName
					}
				}
			}
		}
	}

	return dHealthTrend
}

func (ss *ScreenshotService) performCrop(img image.Image, cropBounds image.Rectangle) image.Image {
	if cropBounds.Dx() <= 0 || cropBounds.Dy() <= 0 {
		fmt.Println("[ScreenshotService] 警告: 计算出的精确裁剪边界无效，返回原始图片副本。")
		bounds := img.Bounds()
		newImg := image.NewRGBA(bounds)
		for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
			for x := bounds.Min.X; x < bounds.Max.X; x++ {
				newImg.Set(x, y, img.At(x, y))
			}
		}
		return newImg
	}

	croppedImg := image.NewRGBA(image.Rect(0, 0, cropBounds.Dx(), cropBounds.Dy()))
	for y := 0; y < cropBounds.Dy(); y++ {
		for x := 0; x < cropBounds.Dx(); x++ {
			originalX := cropBounds.Min.X + x
			originalY := cropBounds.Min.Y + y
			c := img.At(originalX, originalY)
			croppedImg.Set(x, y, c)
		}
	}
	return croppedImg
}

// preprocessImage 图像预处理
func (ss *ScreenshotService) preprocessImage(img image.Image) image.Image {
	bounds := img.Bounds()
	enhancedImg := image.NewRGBA(bounds)

	// 1. 提高对比度和亮度
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			r, g, b, a := img.At(x, y).RGBA()

			// 将颜色值从uint32转换为float64进行处理
			rf := float64(r >> 8)
			gf := float64(g >> 8)
			bf := float64(b >> 8)

			// 提高对比度（增加20%）
			contrast := 1.2
			rf = ((rf - 128.0) * contrast) + 128.0
			gf = ((gf - 128.0) * contrast) + 128.0
			bf = ((bf - 128.0) * contrast) + 128.0

			// 确保值在0-255范围内
			rf = ss.maxf(0.0, ss.minf(255.0, rf))
			gf = ss.maxf(0.0, ss.minf(255.0, gf))
			bf = ss.maxf(0.0, ss.minf(255.0, bf))

			enhancedImg.Set(x, y, color.RGBA{
				R: uint8(rf),
				G: uint8(gf),
				B: uint8(bf),
				A: uint8(a >> 8),
			})
		}
	}

	// 2. 轻微锐化处理
	sharpenKernel := []float64{
		0, -0.5, 0,
		-0.5, 3, -0.5,
		0, -0.5, 0,
	}

	sharpenedImg := image.NewRGBA(bounds)
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			var sumR, sumG, sumB float64

			// 应用锐化核
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					r, g, b, _ := enhancedImg.At(x+kx, y+ky).RGBA()
					weight := sharpenKernel[(ky+1)*3+(kx+1)]
					sumR += float64(r>>8) * weight
					sumG += float64(g>>8) * weight
					sumB += float64(b>>8) * weight
				}
			}

			// 确保值在0-255范围内
			r := uint8(ss.maxf(0.0, ss.minf(255.0, sumR)))
			g := uint8(ss.maxf(0.0, ss.minf(255.0, sumG)))
			b := uint8(ss.maxf(0.0, ss.minf(255.0, sumB)))

			sharpenedImg.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	// 3. 轻微平滑处理（使用简单的3x3均值滤波）
	finalImg := image.NewRGBA(bounds)
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			var sumR, sumG, sumB float64
			count := 0.0

			// 3x3邻域平均
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					r, g, b, _ := sharpenedImg.At(x+kx, y+ky).RGBA()
					sumR += float64(r >> 8)
					sumG += float64(g >> 8)
					sumB += float64(b >> 8)
					count++
				}
			}

			// 计算平均值
			r := uint8(ss.maxf(0.0, ss.minf(255.0, sumR/count)))
			g := uint8(ss.maxf(0.0, ss.minf(255.0, sumG/count)))
			b := uint8(ss.maxf(0.0, ss.minf(255.0, sumB/count)))

			finalImg.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	return finalImg
}
