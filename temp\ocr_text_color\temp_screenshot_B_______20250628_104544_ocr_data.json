{"logId": "73971fc2-7919-4092-aa22-9d9410c37bfc", "result": {"tableRecResults": [{"prunedResult": {"model_settings": {"use_doc_preprocessor": false, "use_layout_detection": true, "use_ocr_model": true}, "layout_det_res": {"boxes": [{"cls_id": 8, "label": "table", "score": 0.9863713383674622, "coordinate": [12.741783142089844, 75.53785705566406, 768, 1711.0150146484375]}, {"cls_id": 9, "label": "table_title", "score": 0.6605097055435181, "coordinate": [19.372283935546875, 27.178064346313477, 520.54150390625, 63.015533447265625]}]}, "overall_ocr_res": {"model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 77], [295, 77], [295, 102], [192, 102]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[190, 98], [271, 98], [271, 130], [190, 130]], [[190, 123], [462, 127], [461, 157], [190, 154]], [[98, 154], [162, 154], [162, 179], [98, 179]], [[192, 154], [319, 154], [319, 179], [192, 179]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[194, 179], [314, 179], [314, 204], [194, 204]], [[98, 204], [162, 204], [162, 229], [98, 229]], [[194, 206], [556, 206], [556, 229], [194, 229]], [[98, 231], [162, 231], [162, 256], [98, 256]], [[196, 234], [727, 234], [727, 252], [196, 252]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [415, 257], [415, 281], [192, 281]], [[98, 281], [155, 281], [155, 306], [98, 306]], [[192, 279], [305, 279], [305, 304], [192, 304]], [[98, 306], [155, 306], [155, 332], [98, 332]], [[194, 309], [306, 309], [306, 329], [194, 329]], [[98, 332], [155, 332], [155, 358], [98, 358]], [[192, 331], [406, 331], [406, 354], [192, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[190, 358], [260, 358], [260, 384], [190, 384]], [[98, 383], [157, 383], [157, 409], [98, 409]], [[192, 384], [689, 384], [689, 408], [192, 408]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[192, 409], [445, 409], [445, 433], [192, 433]], [[98, 433], [155, 433], [155, 459], [98, 459]], [[188, 431], [262, 431], [262, 463], [188, 463]], [[100, 458], [157, 458], [157, 484], [100, 484]], [[192, 459], [417, 459], [417, 484], [192, 484]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[194, 484], [454, 484], [454, 508], [194, 508]], [[98, 509], [155, 509], [155, 536], [98, 536]], [[194, 511], [441, 511], [441, 534], [194, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[190, 534], [246, 534], [246, 563], [190, 563]], [[98, 561], [155, 561], [155, 586], [98, 586]], [[185, 560], [243, 555], [247, 587], [189, 592]], [[98, 586], [157, 586], [157, 611], [98, 611]], [[194, 590], [474, 590], [474, 608], [194, 608]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[196, 617], [639, 617], [639, 635], [196, 635]], [[98, 636], [157, 636], [157, 663], [98, 663]], [[192, 640], [447, 640], [447, 663], [192, 663]], [[98, 663], [157, 663], [157, 688], [98, 688]], [[188, 663], [400, 661], [401, 686], [188, 688]], [[98, 688], [157, 688], [157, 715], [98, 715]], [[192, 690], [362, 690], [362, 713], [192, 713]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[194, 715], [731, 715], [731, 738], [194, 738]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[194, 740], [504, 740], [504, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[190, 765], [279, 765], [279, 792], [190, 792]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [522, 792], [522, 815], [192, 815]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [314, 817], [314, 842], [192, 842]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[192, 844], [606, 844], [606, 867], [192, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [430, 869], [430, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[190, 892], [386, 894], [386, 919], [190, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[190, 919], [474, 919], [474, 942], [190, 942]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[184, 943], [263, 938], [265, 970], [186, 975]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[192, 971], [412, 971], [412, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [330, 996], [330, 1021], [192, 1021]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[190, 1019], [284, 1019], [284, 1047], [190, 1047]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[188, 1042], [264, 1042], [264, 1074], [188, 1074]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[190, 1069], [364, 1069], [364, 1094], [190, 1094]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [569, 1098], [569, 1121], [192, 1121]], [[98, 1121], [157, 1121], [157, 1148], [98, 1148]], [[192, 1124], [412, 1124], [412, 1148], [192, 1148]], [[98, 1148], [157, 1148], [157, 1173], [98, 1173]], [[190, 1148], [347, 1146], [347, 1171], [190, 1173]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[192, 1174], [393, 1174], [393, 1198], [192, 1198]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [297, 1199], [297, 1224], [190, 1224]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[192, 1224], [351, 1224], [351, 1249], [192, 1249]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1251], [438, 1251], [438, 1274], [192, 1274]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[192, 1276], [508, 1276], [508, 1300], [192, 1300]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[190, 1301], [260, 1301], [260, 1328], [190, 1328]], [[98, 1326], [155, 1326], [155, 1351], [98, 1351]], [[192, 1326], [332, 1326], [332, 1351], [192, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [500, 1353], [500, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1380], [473, 1380], [473, 1403], [192, 1403]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [297, 1403], [297, 1428], [192, 1428]], [[98, 1428], [157, 1428], [157, 1453], [98, 1453]], [[192, 1428], [362, 1426], [362, 1451], [192, 1453]], [[98, 1453], [155, 1453], [155, 1480], [98, 1480]], [[194, 1455], [371, 1455], [371, 1478], [194, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[194, 1484], [412, 1484], [412, 1502], [194, 1502]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[194, 1509], [476, 1509], [476, 1527], [194, 1527]], [[98, 1530], [155, 1530], [155, 1555], [98, 1555]], [[194, 1532], [401, 1532], [401, 1555], [194, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[192, 1557], [367, 1557], [367, 1582], [192, 1582]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[190, 1582], [282, 1578], [283, 1605], [191, 1609]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[194, 1607], [478, 1607], [478, 1632], [194, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[194, 1636], [330, 1636], [330, 1655], [194, 1655]], [[98, 1657], [157, 1657], [157, 1684], [98, 1684]], [[188, 1655], [266, 1655], [266, 1687], [188, 1687]], [[100, 1682], [157, 1682], [157, 1709], [100, 1709]], [[192, 1684], [327, 1684], [327, 1709], [192, 1709]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.4, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0, "rec_texts": ["按照标准图谱相似度递减列表：", "0.000", "厦膜后脏器", "4.520", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.065", "血管紧张素Ⅱ*", "0.086", "血管紧张素I*", "0.096", "胆固醇COMMONPLASMA CHOLESTERIN", "0.115", "血浆丰酸化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA", "0.147", "血红血球ERYTHROCYTES", "0.154", "BETA球蛋白*", "0.163", "免疫球蛋白G*", "0.183", "血钾PLASMAPOTASSIUM", "0.112", "脂肪酶*", "0.115", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.116", "血清补体SERUMCOMPLEMENT", "0.117", "催乳素*", "0.129", "血肌酥SERUM CREATININE", "0.130", "伽马球蛋白GAMMA-GLOBULINS", "0.130", "血清溶菌酵SERUMLYSOZYME", "0.130", "肾素*", "0.131", "糖苷*", "0.133", "PERIPHERICBLOODLEUCOCYTES", "0.134", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.134", "肿瘤标志物MELANOGENE在尿*", "0.135", "嗜碱性粒细胞BASOPHILS", "0.135", "甲状腺素结合球蛋白", "0.136", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.138", "血清淀粉酵素SERUMALPHAAMYLASE", "0.139", "醛固酮尿*", "0.139", "游离胆固醇FREEPLASMACHOLESTERIN", "0.139", "甲状腺球蛋白*", "0.140", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.140", "嗜酸性粒细胞EOSINOPHILES", "0.140", "17-血浆氧皮质类固醇类", "0.141", "嗜中性粒细胞STABNEUTROPHILS", "0.142", "唾液酸*", "0.142", "尿肌配URINECREATININE", "0.143", "17-尿中酮类固醇", "0.143", "维生素B6*", "0.143", "多巴胺*", "0.143", "红细胞沉降率(ESR)", "0.143", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.144", "血清蛋白SERUMALBUMEN", "0.144", "RHEUMOFACTOR*", "0.144", "血红蛋白HAEMOGLOBIN", "0.144", "抗利尿激素*", "0.145", "血细胞比容，全血*", "0.147", "尿白血球URINE LEUCOCYTES", "0.147", "11 - PLASMA OXYCORTICOSTEROIDS", "0.147", "胰岛素*", "0.147", "抗链球菌溶血素*", "0.147", "生长激素SOMATOTROPICHORMONE", "0.149", "皮质醇SERUMHYDROCORTISONE", "0.149", "胰高血糖素*", "0.149", "血尿素BLOODUREA", "0.150", "总铁结合力（TIBC）", "0.150", "尿中尿酸URINEURICACID", "0.150", "酸性磷酸酵素ACIDPHOSPHATASE", "0.151", "血尿酸SERUMURIC ACID", "0.151", "肿瘤标志物胸苷激酶*", "0.152", "维生素B2*", "0.152", "血浆磷脂PLASMA PHOSPHOTIDES", "0.152", "糖基化血红蛋白", "0.152", "备解素*", "0.152", "ALPHA2球蛋白*"], "rec_scores": [0.9954331517219543, 0.9992889165878296, 0.8574286699295044, 0.9972284436225891, 0.9888066649436951, 0.9720867872238159, 0.9999223947525024, 0.9494190812110901, 0.9999534487724304, 0.9845221638679504, 0.9998990297317505, 0.9788220524787903, 0.9998382329940796, 0.9436192512512207, 0.9998611211776733, 0.996026873588562, 0.999524712562561, 0.9833086729049683, 0.9996541142463684, 0.9215916991233826, 0.9996191263198853, 0.9784450531005859, 0.9992367625236511, 0.9442469477653503, 0.9992122650146484, 0.9957689046859741, 0.9990226030349731, 0.9979596138000488, 0.9992015957832336, 0.9704620838165283, 0.9995498657226562, 0.9436194896697998, 0.9994012713432312, 0.9922927021980286, 0.9993805885314941, 0.9943169355392456, 0.999383807182312, 0.9775409698486328, 0.9992146492004395, 0.9703496098518372, 0.999433159828186, 0.9960910677909851, 0.999461829662323, 0.9974311590194702, 0.9994473457336426, 0.9686256051063538, 0.9992998838424683, 0.9987472295761108, 0.9995485544204712, 0.9957135319709778, 0.9995496869087219, 0.9970381259918213, 0.9996085166931152, 0.99711674451828, 0.99932861328125, 0.9802201986312866, 0.9994992017745972, 0.9961502552032471, 0.9994916915893555, 0.9408621191978455, 0.9993370175361633, 0.9979647397994995, 0.999134361743927, 0.997809648513794, 0.9993459582328796, 0.991276204586029, 0.9993607401847839, 0.9968063831329346, 0.9993988871574402, 0.9574174880981445, 0.9994600415229797, 0.9590333104133606, 0.9995555877685547, 0.9839123487472534, 0.9995719790458679, 0.9941267371177673, 0.9994319081306458, 0.9447834491729736, 0.9995003938674927, 0.9542663097381592, 0.9995555877685547, 0.9941748380661011, 0.9993694424629211, 0.9986621141433716, 0.9992073774337769, 0.9967509508132935, 0.9991703033447266, 0.995011568069458, 0.9991536140441895, 0.9810506701469421, 0.9992132186889648, 0.9761788845062256, 0.9994081258773804, 0.9751254916191101, 0.9995087385177612, 0.9629639387130737, 0.9993146061897278, 0.9597681760787964, 0.999161422252655, 0.9738208055496216, 0.9994081258773804, 0.9958900213241577, 0.999505341053009, 0.9969716668128967, 0.9989840388298035, 0.9706903100013733, 0.9993130564689636, 0.9935243129730225, 0.9995059967041016, 0.8953628540039062, 0.9995495080947876, 0.997216522693634, 0.9995476007461548, 0.9954561591148376, 0.9992704391479492, 0.971625030040741, 0.9995473623275757, 0.9439200162887573, 0.999647319316864, 0.9927509427070618, 0.9996153712272644, 0.9795926809310913, 0.9993577003479004, 0.9972587823867798, 0.9996970295906067, 0.9413313269615173, 0.9993345141410828, 0.9787399172782898], "rec_polys": [[[20, 30], [519, 30], [519, 61], [20, 61]], [[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 77], [295, 77], [295, 102], [192, 102]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[190, 98], [271, 98], [271, 130], [190, 130]], [[190, 123], [462, 127], [461, 157], [190, 154]], [[98, 154], [162, 154], [162, 179], [98, 179]], [[192, 154], [319, 154], [319, 179], [192, 179]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[194, 179], [314, 179], [314, 204], [194, 204]], [[98, 204], [162, 204], [162, 229], [98, 229]], [[194, 206], [556, 206], [556, 229], [194, 229]], [[98, 231], [162, 231], [162, 256], [98, 256]], [[196, 234], [727, 234], [727, 252], [196, 252]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [415, 257], [415, 281], [192, 281]], [[98, 281], [155, 281], [155, 306], [98, 306]], [[192, 279], [305, 279], [305, 304], [192, 304]], [[98, 306], [155, 306], [155, 332], [98, 332]], [[194, 309], [306, 309], [306, 329], [194, 329]], [[98, 332], [155, 332], [155, 358], [98, 358]], [[192, 331], [406, 331], [406, 354], [192, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[190, 358], [260, 358], [260, 384], [190, 384]], [[98, 383], [157, 383], [157, 409], [98, 409]], [[192, 384], [689, 384], [689, 408], [192, 408]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[192, 409], [445, 409], [445, 433], [192, 433]], [[98, 433], [155, 433], [155, 459], [98, 459]], [[188, 431], [262, 431], [262, 463], [188, 463]], [[100, 458], [157, 458], [157, 484], [100, 484]], [[192, 459], [417, 459], [417, 484], [192, 484]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[194, 484], [454, 484], [454, 508], [194, 508]], [[98, 509], [155, 509], [155, 536], [98, 536]], [[194, 511], [441, 511], [441, 534], [194, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[190, 534], [246, 534], [246, 563], [190, 563]], [[98, 561], [155, 561], [155, 586], [98, 586]], [[185, 560], [243, 555], [247, 587], [189, 592]], [[98, 586], [157, 586], [157, 611], [98, 611]], [[194, 590], [474, 590], [474, 608], [194, 608]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[196, 617], [639, 617], [639, 635], [196, 635]], [[98, 636], [157, 636], [157, 663], [98, 663]], [[192, 640], [447, 640], [447, 663], [192, 663]], [[98, 663], [157, 663], [157, 688], [98, 688]], [[188, 663], [400, 661], [401, 686], [188, 688]], [[98, 688], [157, 688], [157, 715], [98, 715]], [[192, 690], [362, 690], [362, 713], [192, 713]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[194, 715], [731, 715], [731, 738], [194, 738]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[194, 740], [504, 740], [504, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[190, 765], [279, 765], [279, 792], [190, 792]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [522, 792], [522, 815], [192, 815]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [314, 817], [314, 842], [192, 842]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[192, 844], [606, 844], [606, 867], [192, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [430, 869], [430, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[190, 892], [386, 894], [386, 919], [190, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[190, 919], [474, 919], [474, 942], [190, 942]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[184, 943], [263, 938], [265, 970], [186, 975]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[192, 971], [412, 971], [412, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [330, 996], [330, 1021], [192, 1021]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[190, 1019], [284, 1019], [284, 1047], [190, 1047]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[188, 1042], [264, 1042], [264, 1074], [188, 1074]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[190, 1069], [364, 1069], [364, 1094], [190, 1094]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [569, 1098], [569, 1121], [192, 1121]], [[98, 1121], [157, 1121], [157, 1148], [98, 1148]], [[192, 1124], [412, 1124], [412, 1148], [192, 1148]], [[98, 1148], [157, 1148], [157, 1173], [98, 1173]], [[190, 1148], [347, 1146], [347, 1171], [190, 1173]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[192, 1174], [393, 1174], [393, 1198], [192, 1198]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [297, 1199], [297, 1224], [190, 1224]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[192, 1224], [351, 1224], [351, 1249], [192, 1249]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1251], [438, 1251], [438, 1274], [192, 1274]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[192, 1276], [508, 1276], [508, 1300], [192, 1300]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[190, 1301], [260, 1301], [260, 1328], [190, 1328]], [[98, 1326], [155, 1326], [155, 1351], [98, 1351]], [[192, 1326], [332, 1326], [332, 1351], [192, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [500, 1353], [500, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1380], [473, 1380], [473, 1403], [192, 1403]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [297, 1403], [297, 1428], [192, 1428]], [[98, 1428], [157, 1428], [157, 1453], [98, 1453]], [[192, 1428], [362, 1426], [362, 1451], [192, 1453]], [[98, 1453], [155, 1453], [155, 1480], [98, 1480]], [[194, 1455], [371, 1455], [371, 1478], [194, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[194, 1484], [412, 1484], [412, 1502], [194, 1502]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[194, 1509], [476, 1509], [476, 1527], [194, 1527]], [[98, 1530], [155, 1530], [155, 1555], [98, 1555]], [[194, 1532], [401, 1532], [401, 1555], [194, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[192, 1557], [367, 1557], [367, 1582], [192, 1582]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[190, 1582], [282, 1578], [283, 1605], [191, 1609]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[194, 1607], [478, 1607], [478, 1632], [194, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[194, 1636], [330, 1636], [330, 1655], [194, 1655]], [[98, 1657], [157, 1657], [157, 1684], [98, 1684]], [[188, 1655], [266, 1655], [266, 1687], [188, 1687]], [[100, 1682], [157, 1682], [157, 1709], [100, 1709]], [[192, 1684], [327, 1684], [327, 1709], [192, 1709]]], "rec_boxes": [[20, 30, 519, 61], [98, 77, 157, 102], [192, 77, 295, 102], [98, 102, 157, 129], [190, 98, 271, 130], [190, 123, 462, 157], [98, 154, 162, 179], [192, 154, 319, 179], [98, 179, 162, 204], [194, 179, 314, 204], [98, 204, 162, 229], [194, 206, 556, 229], [98, 231, 162, 256], [196, 234, 727, 252], [98, 256, 161, 281], [192, 257, 415, 281], [98, 281, 155, 306], [192, 279, 305, 304], [98, 306, 155, 332], [194, 309, 306, 329], [98, 332, 155, 358], [192, 331, 406, 354], [98, 358, 157, 383], [190, 358, 260, 384], [98, 383, 157, 409], [192, 384, 689, 408], [98, 408, 157, 434], [192, 409, 445, 433], [98, 433, 155, 459], [188, 431, 262, 463], [100, 458, 157, 484], [192, 459, 417, 484], [98, 484, 157, 509], [194, 484, 454, 508], [98, 509, 155, 536], [194, 511, 441, 534], [98, 536, 155, 561], [190, 534, 246, 563], [98, 561, 155, 586], [185, 555, 247, 592], [98, 586, 157, 611], [194, 590, 474, 608], [98, 611, 157, 638], [196, 617, 639, 635], [98, 636, 157, 663], [192, 640, 447, 663], [98, 663, 157, 688], [188, 661, 401, 688], [98, 688, 157, 715], [192, 690, 362, 713], [98, 713, 157, 740], [194, 715, 731, 738], [98, 740, 157, 767], [194, 740, 504, 765], [98, 765, 157, 790], [190, 765, 279, 792], [98, 790, 157, 817], [192, 792, 522, 815], [98, 815, 157, 842], [192, 817, 314, 842], [98, 840, 157, 867], [192, 844, 606, 867], [98, 867, 157, 892], [192, 869, 430, 892], [98, 892, 157, 919], [190, 892, 386, 919], [98, 917, 155, 944], [190, 919, 474, 942], [98, 944, 157, 969], [184, 938, 265, 975], [98, 969, 157, 996], [192, 971, 412, 994], [98, 994, 157, 1021], [192, 996, 330, 1021], [98, 1019, 157, 1046], [190, 1019, 284, 1047], [98, 1046, 157, 1071], [188, 1042, 264, 1074], [98, 1071, 157, 1098], [190, 1069, 364, 1094], [98, 1096, 157, 1123], [192, 1098, 569, 1121], [98, 1121, 157, 1148], [192, 1124, 412, 1148], [98, 1148, 157, 1173], [190, 1146, 347, 1173], [98, 1173, 157, 1199], [192, 1174, 393, 1198], [98, 1198, 157, 1224], [190, 1199, 297, 1224], [98, 1224, 157, 1249], [192, 1224, 351, 1249], [98, 1249, 157, 1274], [192, 1251, 438, 1274], [98, 1274, 157, 1301], [192, 1276, 508, 1300], [98, 1300, 157, 1326], [190, 1301, 260, 1328], [98, 1326, 155, 1351], [192, 1326, 332, 1351], [98, 1351, 157, 1376], [192, 1353, 500, 1376], [98, 1376, 157, 1403], [192, 1380, 473, 1403], [100, 1403, 157, 1428], [192, 1403, 297, 1428], [98, 1428, 157, 1453], [192, 1426, 362, 1453], [98, 1453, 155, 1480], [194, 1455, 371, 1478], [98, 1478, 157, 1505], [194, 1484, 412, 1502], [98, 1505, 157, 1530], [194, 1509, 476, 1527], [98, 1530, 155, 1555], [194, 1532, 401, 1555], [98, 1555, 155, 1582], [192, 1557, 367, 1582], [98, 1580, 157, 1607], [190, 1578, 283, 1609], [98, 1607, 157, 1632], [194, 1607, 478, 1632], [100, 1632, 157, 1657], [194, 1636, 330, 1655], [98, 1657, 157, 1684], [188, 1655, 266, 1687], [100, 1682, 157, 1709], [192, 1684, 327, 1709]]}, "table_res_list": [{"cell_box_list": [[98.52265167236328, 76.32578200101852, 170.67485809326172, 103.23447036743164], [194.55261993408203, 76.53302800655365, 768.0, 103.41762161254883], [43.07999229431152, 102.65264511108398, 70.15354919433594, 128.25178146362305], [70.10797882080078, 102.76364326477051, 98.65120697021484, 128.4303092956543], [98.47559356689453, 102.72452735900879, 170.85779571533203, 128.48823165893555], [190.0, 98.0, 271.0, 130.0], [70.0689697265625, 128.4665756225586, 98.64767456054688, 153.75994110107422], [98.41526794433594, 128.36566925048828, 170.90254974365234, 179.3408660888672], [194.2236099243164, 127.75345611572266, 768.0, 154.14706420898438], [70.06364059448242, 153.49815368652344, 98.5659408569336, 179.17181396484375], [193.66301727294922, 154.0184326171875, 768.0, 179.74564361572266], [98.467041015625, 179.0544662475586, 171.3217544555664, 229.9541473388672], [194.0, 179.0, 314.0, 204.0], [70.0221176147461, 204.02711486816406, 98.61611938476562, 229.46542358398438], [193.3534164428711, 204.91244506835938, 768.0, 229.8555145263672], [70.05792236328125, 229.59152221679688, 98.6531982421875, 280.6991882324219], [98.55902099609375, 229.867919921875, 171.57062530517578, 255.62826538085938], [193.29792022705078, 230.22125244140625, 768.0, 256.45335388183594], [98.47509002685547, 255.3564910888672, 171.83802032470703, 306.4972839355469], [192.0, 257.0, 415.0, 281.0], [70.0086898803711, 280.6006774902344, 98.62354278564453, 306.07005310058594], [193.22924041748047, 281.3136444091797, 768.0, 307.31349182128906], [42.99572944641113, 305.95008850097656, 70.0683708190918, 331.4729461669922], [70.012451171875, 305.9455261230469, 98.660888671875, 331.59486389160156], [98.47797393798828, 306.3704376220703, 171.93392181396484, 331.9759063720703], [194.0, 309.0, 306.0, 329.0], [42.99984550476074, 331.65806579589844, 70.16788482666016, 382.7645721435547], [70.0638542175293, 331.59727478027344, 98.69918823242188, 357.0884552001953], [98.58089447021484, 331.8144989013672, 172.00244903564453, 357.4743194580078], [193.22428131103516, 332.07020568847656, 768.0, 357.87400817871094], [70.069091796875, 356.80873107910156, 98.61760711669922, 382.73423767089844], [98.49726867675781, 357.23814392089844, 171.79964447021484, 383.02687072753906], [193.2521743774414, 358.17283630371094, 768.0, 383.5277862548828], [42.97343444824219, 382.7544403076172, 70.08276748657227, 433.69227600097656], [70.0180892944336, 382.6828155517578, 98.61936950683594, 408.22804260253906], [98.50991821289062, 382.7368927001953, 171.88397979736328, 408.5159149169922], [192.0, 384.0, 689.0, 408.0], [70.00494766235352, 408.1170196533203, 98.6456069946289, 433.74302673339844], [98.47036743164062, 408.4749298095703, 171.89539337158203, 434.03749084472656], [193.05950164794922, 408.9904327392578, 768.0, 433.66468811035156], [43.02364158630371, 433.8878936767578, 70.14620208740234, 459.35093688964844], [70.05866241455078, 433.7573699951172, 98.66387939453125, 459.2700958251953], [98.52715301513672, 433.93968200683594, 171.90050506591797, 459.5627899169922], [193.29332733154297, 434.10655212402344, 768.0, 460.1526641845703], [43.06868934631348, 459.07139587402344, 70.1623306274414, 484.9513702392578], [70.06490325927734, 459.00291442871094, 98.59900665283203, 484.8820343017578], [98.47270202636719, 459.31541442871094, 171.77254486083984, 485.09620666503906], [192.0, 459.0, 417.0, 484.0], [42.99717330932617, 484.9497528076172, 70.0505142211914, 535.9249725341797], [70.00271224975586, 484.90440368652344, 98.61756896972656, 510.42359924316406], [98.52799224853516, 484.87770080566406, 171.84874725341797, 510.6256561279297], [193.05391693115234, 485.41941833496094, 768.0, 511.4062042236328], [69.98308944702148, 510.3046112060547, 98.6355209350586, 535.9548492431641], [98.44989013671875, 510.5865020751953, 171.80469512939453, 536.1940460205078], [194.0, 511.0, 441.0, 534.0], [43.04005241394043, 536.1294097900391, 70.13808059692383, 587.1914520263672], [70.0280876159668, 535.9554901123047, 98.65399932861328, 561.4839630126953], [98.51651763916016, 536.1941070556641, 171.78951263427734, 561.7238311767578], [193.06055450439453, 536.3658905029297, 768.0, 562.0339813232422], [70.04946899414062, 561.2524261474609, 98.59468841552734, 587.1292877197266], [98.47148895263672, 561.4978485107422, 171.6861343383789, 587.2937164306641], [192.81111907958984, 562.5383758544922, 768.0, 587.7564849853516], [43.02714157104492, 587.1860504150391, 70.05997467041016, 638.0838775634766], [69.97181701660156, 587.1865386962891, 98.63484191894531, 638.1300201416016], [98.54785919189453, 587.1220245361328, 171.75725555419922, 612.8462066650391], [194.0, 590.0, 474.0, 608.0], [98.47209930419922, 612.7814483642578, 171.72901153564453, 638.4929351806641], [192.88642120361328, 613.0199127197266, 768.0, 638.1385040283203], [43.056161880493164, 638.3276519775391, 70.13486862182617, 689.4481964111328], [70.01681137084961, 638.1231231689453, 98.6490249633789, 663.6595611572266], [98.45698547363281, 638.4190216064453, 171.74762725830078, 689.4837799072266], [192.87006378173828, 638.5735626220703, 768.0, 664.7387847900391], [70.03801727294922, 663.4950714111328, 98.60608673095703, 689.4436798095703], [188.0, 661.0, 401.0, 688.0], [43.02227020263672, 689.4018096923828, 70.05400085449219, 740.3679351806641], [69.98540878295898, 689.3855133056641, 98.6310043334961, 714.9356842041016], [98.53524780273438, 689.3821563720703, 171.7386703491211, 715.0747833251953], [192.0, 690.0, 362.0, 713.0], [69.97579956054688, 714.7491607666016, 98.64176177978516, 740.4123077392578], [98.47549438476562, 715.0119781494141, 171.68578338623047, 740.6877593994141], [194.0, 715.0, 731.0, 738.0], [43.057573318481445, 740.5883331298828, 70.14387130737305, 791.7100372314453], [70.01810455322266, 740.3766021728516, 98.64884185791016, 765.9184112548828], [98.47942352294922, 740.6309967041016, 171.74947357177734, 791.6637115478516], [192.88274383544922, 740.7301177978516, 768.0, 766.2696685791016], [70.04677963256836, 765.7650909423828, 98.61128997802734, 791.7126617431641], [192.84967803955078, 767.0024566650391, 768.0, 792.1634674072266], [43.021846771240234, 791.7364044189453, 70.07056427001953, 842.5839996337891], [69.99256896972656, 791.6530303955078, 98.64238739013672, 842.5808258056641], [98.5389404296875, 791.6138458251953, 171.79309844970703, 817.3087921142578], [192.0, 792.0, 522.0, 815.0], [98.47174072265625, 817.1902618408203, 171.77745819091797, 842.8211822509766], [192.0, 817.0, 314.0, 842.0], [43.04909324645996, 842.7777252197266, 70.15366744995117, 868.2234039306641], [70.04581451416016, 842.5023345947266, 98.63748168945312, 868.0614166259766], [98.46044158935547, 842.8965606689453, 171.77063751220703, 893.8574981689453], [192.9604721069336, 842.9571075439453, 768.0, 868.8811798095703], [43.07318305969238, 867.9488067626953, 70.17306518554688, 893.9511871337891], [70.08372497558594, 867.9027862548828, 98.59378051757812, 893.8941802978516], [192.0, 869.0, 430.0, 892.0], [98.51548767089844, 893.7873687744141, 171.7729721069336, 919.6147613525391], [192.7695083618164, 894.9082183837891, 768.0, 919.4397735595703], [43.02618980407715, 919.2816314697266, 70.07537460327148, 944.8047637939453], [70.04841613769531, 919.3865509033203, 98.61500549316406, 970.2858428955078], [98.43672943115234, 919.6860504150391, 171.72126007080078, 970.3322296142578], [190.0, 919.0, 474.0, 942.0], [43.01555824279785, 945.0415191650391, 70.19073486328125, 996.1740875244141], [193.01815032958984, 945.3271026611328, 768.0, 971.0883331298828], [70.0934944152832, 970.1573028564453, 98.59436798095703, 996.1392974853516], [98.44019317626953, 970.2437896728516, 171.55757904052734, 996.1314239501953], [192.0, 971.0, 412.0, 994.0], [98.5436782836914, 996.0213165283203, 171.68030548095703, 1021.7620391845703], [192.0, 996.0, 330.0, 1021.0], [43.01478958129883, 1021.5174102783203, 70.08948135375977, 1047.0407257080078], [70.06121063232422, 1021.6828765869141, 98.64491271972656, 1072.5026397705078], [98.47511291503906, 1021.8679351806641, 171.72846221923828, 1072.4663848876953], [192.78327178955078, 1022.3074493408203, 768.0, 1046.8417510986328], [193.09932708740234, 1047.6165313720703, 768.0, 1072.9517974853516], [70.08943939208984, 1072.3392486572266, 98.61798858642578, 1098.2246856689453], [98.48603820800781, 1072.4301300048828, 171.6512680053711, 1098.2149200439453], [190.0, 1069.0, 364.0, 1094.0], [98.0, 1098.0665435791016, 171.69673919677734, 1148.0], [193.1891860961914, 1098.3192901611328, 768.0, 1124.3717193603516], [192.0, 1124.0, 412.0, 1148.0], [70.0640640258789, 1148.4037017822266, 98.663818359375, 1173.7935943603516], [98.52323913574219, 1149.1952056884766, 171.67534637451172, 1174.1915435791016], [193.22066497802734, 1149.357681274414, 768.0, 1199.841079711914], [98.0, 1173.0, 157.0, 1199.0], [70.03569793701172, 1199.365982055664, 98.65473175048828, 1249.958755493164], [98.49026489257812, 1199.6312408447266, 171.47858428955078, 1225.082290649414], [190.0, 1199.0, 297.0, 1224.0], [43.00979995727539, 1224.282974243164, 70.06381607055664, 1249.9171295166016], [98.50774383544922, 1224.9393463134766, 171.5114974975586, 1301.0606842041016], [193.23798370361328, 1225.4908599853516, 768.0, 1251.0562896728516], [70.01532363891602, 1249.8382720947266, 98.59544372558594, 1275.167495727539], [192.0, 1251.0, 438.0, 1274.0], [193.12201690673828, 1275.5853424072266, 768.0, 1301.5965728759766], [70.02009963989258, 1300.461196899414, 98.62357330322266, 1326.1959381103516], [98.44200897216797, 1301.047866821289, 171.49396514892578, 1326.6566314697266], [193.09691619873047, 1302.000991821289, 768.0, 1326.5914459228516], [42.996036529541016, 1326.0560455322266, 70.0483627319336, 1351.686538696289], [69.99879837036133, 1326.029800415039, 98.64169311523438, 1351.676284790039], [98.51558685302734, 1326.3172760009766, 171.49764251708984, 1352.220230102539], [192.0, 1326.0, 332.0, 1351.0], [70.01573944091797, 1351.5860748291016, 98.59898376464844, 1377.0457916259766], [98.48055267333984, 1352.1114654541016, 171.52254486083984, 1402.8768463134766], [193.46935272216797, 1352.178970336914, 768.0, 1377.7867584228516], [193.3528060913086, 1377.599380493164, 768.0, 1403.774917602539], [43.03226852416992, 1402.431900024414, 70.1165885925293, 1428.257583618164], [70.03615951538086, 1402.390884399414, 98.63074493408203, 1428.239517211914], [98.4617691040039, 1402.782974243164, 171.48126983642578, 1428.4884185791016], [192.0, 1403.0, 297.0, 1428.0], [42.9810676574707, 1428.114517211914, 70.0644416809082, 1453.770523071289], [70.0057487487793, 1428.0987701416016, 98.63446044921875, 1453.743423461914], [98.55747985839844, 1428.231704711914, 171.54149627685547, 1454.1058502197266], [193.13880157470703, 1429.0538482666016, 768.0, 1454.6915435791016], [42.96469306945801, 1453.7013092041016, 70.04054641723633, 1479.1038970947266], [70.00358581542969, 1453.6742095947266, 98.58543395996094, 1479.145767211914], [98.50386047363281, 1454.012466430664, 171.58182525634766, 1479.601577758789], [194.0, 1455.0, 371.0, 1478.0], [69.99990844726562, 1479.2379302978516, 98.61150360107422, 1504.652359008789], [98.4944839477539, 1479.483413696289, 171.66437530517578, 1504.9403228759766], [192.9803695678711, 1479.5460357666016, 768.0, 1505.592056274414], [43.01398468017578, 1504.642593383789, 70.11130905151367, 1530.4840240478516], [70.03007888793945, 1504.614028930664, 98.60228729248047, 1530.4249420166016], [98.4796142578125, 1504.8065338134766, 171.64800262451172, 1530.643325805664], [192.7867202758789, 1505.494644165039, 768.0, 1530.905532836914], [42.9889030456543, 1530.3512115478516, 70.07625198364258, 1555.9342193603516], [70.00249099731445, 1530.3243560791016, 98.61856079101562, 1555.9032135009766], [98.54503631591797, 1530.3416900634766, 171.6679916381836, 1556.204116821289], [194.0, 1532.0, 401.0, 1555.0], [42.975494384765625, 1555.878921508789, 70.05398559570312, 1581.3331451416016], [69.99940490722656, 1555.867446899414, 98.57246398925781, 1581.3436431884766], [98.48329162597656, 1556.1253814697266, 171.70572662353516, 1581.7144927978516], [193.21610260009766, 1556.5914459228516, 768.0, 1581.6419830322266], [70.00746154785156, 1581.4381256103516, 98.61446380615234, 1606.859634399414], [98.4989013671875, 1581.566909790039, 171.71334075927734, 1607.0980377197266], [193.13495635986328, 1581.8497467041016, 768.0, 1607.5785064697266], [43.0421199798584, 1606.864517211914, 70.09701156616211, 1632.725845336914], [70.0337028503418, 1606.827896118164, 98.59797668457031, 1632.706558227539], [98.48246765136719, 1606.965835571289, 171.6902847290039, 1632.9049224853516], [194.0, 1607.0, 478.0, 1632.0], [43.04058074951172, 1632.482192993164, 70.07134628295898, 1658.1046295166016], [70.02757263183594, 1632.4642486572266, 98.6444320678711, 1658.0680084228516], [98.55811309814453, 1632.5648345947266, 171.68058013916016, 1658.5374908447266], [193.13665008544922, 1632.7979888916016, 768.0, 1658.8521881103516], [98.47128295898438, 1658.2923736572266, 171.64527130126953, 1709.0], [188.0, 1655.0, 266.0, 1687.0], [193.82881927490234, 1683.815933227539, 768.0, 1710.210952758789]], "pred_html": "<html><body><table><tbody><tr><td>0.000</td><td>厦膜后脏器</td><td></td></tr><tr><td></td><td></td><td>4.520</td></tr><tr><td></td><td>0.065</td><td>虚拟模式-健康问题发展趋势列表：</td></tr><tr><td></td><td>血管紧张素Ⅱ*</td><td></td></tr><tr><td>0.086 0.096</td><td>血管紧张素I*</td><td></td></tr><tr><td></td><td>0.115</td><td>血浆丰酸化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA</td></tr><tr><td>0.147 0.154</td><td>血红血球ERYTHROCYTES</td><td></td></tr><tr><td></td><td>BETA球蛋白*</td><td></td></tr><tr><td></td><td></td><td>0.163</td></tr><tr><td></td><td></td><td>0.183</td></tr><tr><td></td><td>0.112</td><td>脂肪酶*</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.115</td></tr><tr><td></td><td>0.116</td><td>血清补体SERUMCOMPLEMENT</td></tr><tr><td></td><td></td><td>0.117</td></tr><tr><td></td><td></td><td>0.129</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.130</td></tr><tr><td></td><td>0.130</td><td>血清溶菌酵SERUMLYSOZYME</td></tr><tr><td></td><td></td><td>0.130</td></tr><tr><td></td><td>0.131</td><td></td></tr><tr><td></td><td></td><td>0.133</td></tr><tr><td>0.134</td><td>血清中的氨基酸NITROGENOFAMINOACIDSINSERUM</td><td></td></tr><tr><td></td><td></td><td>0.134 0.135</td></tr><tr><td></td><td>嗜碱性粒细胞BASOPHILS</td><td></td></tr><tr><td></td><td></td><td>0.135</td></tr><tr><td></td><td>0.136</td><td>AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.138 0.139</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.139</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.140 0.140</td></tr><tr><td></td><td></td><td>嗜酸性粒细胞EOSINOPHILES</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.141 0.142</td></tr><tr><td></td><td>0.142</td><td>尿肌配URINECREATININE</td></tr><tr><td>0.143</td><td>17-尿中酮类固醇</td><td></td></tr><tr><td></td><td></td><td>0.143 0.143</td></tr><tr><td>多巴胺*</td><td></td><td></td></tr><tr><td></td><td>0.143</td><td>红细胞沉降率(ESR)</td></tr><tr><td>血清蛋白SERUMALBUMEN</td><td></td><td></td></tr><tr><td></td><td>0.144</td><td>RHEUMOFACTOR* 血红蛋白HAEMOGLOBIN</td></tr><tr><td></td><td>0.144</td><td>抗利尿激素*</td></tr><tr><td></td><td>0.145 0.147 0.147</td><td>血细胞比容，全血*</td></tr><tr><td></td><td>尿白血球URINE LEUCOCYTES</td><td>11 - PLASMA OXYCORTICOSTEROIDS</td></tr><tr><td></td><td>0.147</td><td>胰岛素*</td></tr><tr><td></td><td></td><td>0.147</td></tr><tr><td></td><td>0.147 0.149</td><td>生长激素SOMATOTROPICHORMONE</td></tr><tr><td>皮质醇SERUMHYDROCORTISONE</td><td></td><td></td></tr><tr><td></td><td></td><td>0.149</td></tr><tr><td></td><td></td><td>0.149</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.150</td></tr><tr><td></td><td>0.150</td><td>尿中尿酸URINEURICACID</td></tr><tr><td></td><td></td><td>0.150</td></tr><tr><td></td><td></td><td>0.151</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>0.151</td></tr><tr><td></td><td>0.152</td><td>维生素B2*</td></tr><tr><td></td><td></td><td>0.152</td></tr><tr><td></td><td></td><td>0.152</td></tr><tr><td>0.152 0.152</td><td>备解素*</td><td>ALPHA2球蛋白*</td></tr></tbody></table></body></html>", "table_ocr_pred": {"rec_polys": [[[98, 77], [157, 77], [157, 102], [98, 102]], [[192, 77], [295, 77], [295, 102], [192, 102]], [[98, 102], [157, 102], [157, 129], [98, 129]], [[190, 98], [271, 98], [271, 130], [190, 130]], [[190, 123], [462, 127], [461, 157], [190, 154]], [[98, 154], [162, 154], [162, 179], [98, 179]], [[192, 154], [319, 154], [319, 179], [192, 179]], [[98, 179], [162, 179], [162, 204], [98, 204]], [[194, 179], [314, 179], [314, 204], [194, 204]], [[98, 204], [162, 204], [162, 229], [98, 229]], [[194, 206], [556, 206], [556, 229], [194, 229]], [[98, 231], [162, 231], [162, 256], [98, 256]], [[196, 234], [727, 234], [727, 252], [196, 252]], [[98, 256], [161, 256], [161, 281], [98, 281]], [[192, 257], [415, 257], [415, 281], [192, 281]], [[98, 281], [155, 281], [155, 306], [98, 306]], [[192, 279], [305, 279], [305, 304], [192, 304]], [[98, 306], [155, 306], [155, 332], [98, 332]], [[194, 309], [306, 309], [306, 329], [194, 329]], [[98, 332], [155, 332], [155, 358], [98, 358]], [[192, 331], [406, 331], [406, 354], [192, 354]], [[98, 358], [157, 358], [157, 383], [98, 383]], [[190, 358], [260, 358], [260, 384], [190, 384]], [[98, 383], [157, 383], [157, 409], [98, 409]], [[192, 384], [689, 384], [689, 408], [192, 408]], [[98, 408], [157, 408], [157, 434], [98, 434]], [[192, 409], [445, 409], [445, 433], [192, 433]], [[98, 433], [155, 433], [155, 459], [98, 459]], [[188, 431], [262, 431], [262, 463], [188, 463]], [[100, 458], [157, 458], [157, 484], [100, 484]], [[192, 459], [417, 459], [417, 484], [192, 484]], [[98, 484], [157, 484], [157, 509], [98, 509]], [[194, 484], [454, 484], [454, 508], [194, 508]], [[98, 509], [155, 509], [155, 536], [98, 536]], [[194, 511], [441, 511], [441, 534], [194, 534]], [[98, 536], [155, 536], [155, 561], [98, 561]], [[190, 534], [246, 534], [246, 563], [190, 563]], [[98, 561], [155, 561], [155, 586], [98, 586]], [[185, 560], [243, 555], [247, 587], [189, 592]], [[98, 586], [157, 586], [157, 611], [98, 611]], [[194, 590], [474, 590], [474, 608], [194, 608]], [[98, 611], [157, 611], [157, 638], [98, 638]], [[196, 617], [639, 617], [639, 635], [196, 635]], [[98, 636], [157, 636], [157, 663], [98, 663]], [[192, 640], [447, 640], [447, 663], [192, 663]], [[98, 663], [157, 663], [157, 688], [98, 688]], [[188, 663], [400, 661], [401, 686], [188, 688]], [[98, 688], [157, 688], [157, 715], [98, 715]], [[192, 690], [362, 690], [362, 713], [192, 713]], [[98, 713], [157, 713], [157, 740], [98, 740]], [[194, 715], [731, 715], [731, 738], [194, 738]], [[98, 740], [157, 740], [157, 767], [98, 767]], [[194, 740], [504, 740], [504, 765], [194, 765]], [[98, 765], [157, 765], [157, 790], [98, 790]], [[190, 765], [279, 765], [279, 792], [190, 792]], [[98, 790], [157, 790], [157, 817], [98, 817]], [[192, 792], [522, 792], [522, 815], [192, 815]], [[98, 815], [157, 815], [157, 842], [98, 842]], [[192, 817], [314, 817], [314, 842], [192, 842]], [[98, 840], [157, 840], [157, 867], [98, 867]], [[192, 844], [606, 844], [606, 867], [192, 867]], [[98, 867], [157, 867], [157, 892], [98, 892]], [[192, 869], [430, 869], [430, 892], [192, 892]], [[98, 892], [157, 892], [157, 919], [98, 919]], [[190, 892], [386, 894], [386, 919], [190, 917]], [[98, 917], [155, 917], [155, 944], [98, 944]], [[190, 919], [474, 919], [474, 942], [190, 942]], [[98, 944], [157, 944], [157, 969], [98, 969]], [[184, 943], [263, 938], [265, 970], [186, 975]], [[98, 969], [157, 969], [157, 996], [98, 996]], [[192, 971], [412, 971], [412, 994], [192, 994]], [[98, 994], [157, 994], [157, 1021], [98, 1021]], [[192, 996], [330, 996], [330, 1021], [192, 1021]], [[98, 1019], [157, 1019], [157, 1046], [98, 1046]], [[190, 1019], [284, 1019], [284, 1047], [190, 1047]], [[98, 1046], [157, 1046], [157, 1071], [98, 1071]], [[188, 1042], [264, 1042], [264, 1074], [188, 1074]], [[98, 1071], [157, 1071], [157, 1098], [98, 1098]], [[190, 1069], [364, 1069], [364, 1094], [190, 1094]], [[98, 1096], [157, 1096], [157, 1123], [98, 1123]], [[192, 1098], [569, 1098], [569, 1121], [192, 1121]], [[98, 1121], [157, 1121], [157, 1148], [98, 1148]], [[192, 1124], [412, 1124], [412, 1148], [192, 1148]], [[98, 1148], [157, 1148], [157, 1173], [98, 1173]], [[190, 1148], [347, 1146], [347, 1171], [190, 1173]], [[98, 1173], [157, 1173], [157, 1199], [98, 1199]], [[192, 1174], [393, 1174], [393, 1198], [192, 1198]], [[98, 1198], [157, 1198], [157, 1224], [98, 1224]], [[190, 1199], [297, 1199], [297, 1224], [190, 1224]], [[98, 1224], [157, 1224], [157, 1249], [98, 1249]], [[192, 1224], [351, 1224], [351, 1249], [192, 1249]], [[98, 1249], [157, 1249], [157, 1274], [98, 1274]], [[192, 1251], [438, 1251], [438, 1274], [192, 1274]], [[98, 1274], [157, 1274], [157, 1301], [98, 1301]], [[192, 1276], [508, 1276], [508, 1300], [192, 1300]], [[98, 1300], [157, 1300], [157, 1326], [98, 1326]], [[190, 1301], [260, 1301], [260, 1328], [190, 1328]], [[98, 1326], [155, 1326], [155, 1351], [98, 1351]], [[192, 1326], [332, 1326], [332, 1351], [192, 1351]], [[98, 1351], [157, 1351], [157, 1376], [98, 1376]], [[192, 1353], [500, 1353], [500, 1376], [192, 1376]], [[98, 1376], [157, 1376], [157, 1403], [98, 1403]], [[192, 1380], [473, 1380], [473, 1403], [192, 1403]], [[100, 1403], [157, 1403], [157, 1428], [100, 1428]], [[192, 1403], [297, 1403], [297, 1428], [192, 1428]], [[98, 1428], [157, 1428], [157, 1453], [98, 1453]], [[192, 1428], [362, 1426], [362, 1451], [192, 1453]], [[98, 1453], [155, 1453], [155, 1480], [98, 1480]], [[194, 1455], [371, 1455], [371, 1478], [194, 1478]], [[98, 1478], [157, 1478], [157, 1505], [98, 1505]], [[194, 1484], [412, 1484], [412, 1502], [194, 1502]], [[98, 1505], [157, 1505], [157, 1530], [98, 1530]], [[194, 1509], [476, 1509], [476, 1527], [194, 1527]], [[98, 1530], [155, 1530], [155, 1555], [98, 1555]], [[194, 1532], [401, 1532], [401, 1555], [194, 1555]], [[98, 1555], [155, 1555], [155, 1582], [98, 1582]], [[192, 1557], [367, 1557], [367, 1582], [192, 1582]], [[98, 1580], [157, 1580], [157, 1607], [98, 1607]], [[190, 1582], [282, 1578], [283, 1605], [191, 1609]], [[98, 1607], [157, 1607], [157, 1632], [98, 1632]], [[194, 1607], [478, 1607], [478, 1632], [194, 1632]], [[100, 1632], [157, 1632], [157, 1657], [100, 1657]], [[194, 1636], [330, 1636], [330, 1655], [194, 1655]], [[98, 1657], [157, 1657], [157, 1684], [98, 1684]], [[188, 1655], [266, 1655], [266, 1687], [188, 1687]], [[100, 1682], [157, 1682], [157, 1709], [100, 1709]], [[192, 1684], [327, 1684], [327, 1709], [192, 1709]]], "rec_texts": ["0.000", "厦膜后脏器", "4.520", "优化配置", "虚拟模式-健康问题发展趋势列表：", "0.065", "血管紧张素Ⅱ*", "0.086", "血管紧张素I*", "0.096", "胆固醇COMMONPLASMA CHOLESTERIN", "0.115", "血浆丰酸化脂肪酸NONETHERIZEDFATTYACIDSOFPLASMA", "0.147", "血红血球ERYTHROCYTES", "0.154", "BETA球蛋白*", "0.163", "免疫球蛋白G*", "0.183", "血钾PLASMAPOTASSIUM", "0.112", "脂肪酶*", "0.115", "ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM", "0.116", "血清补体SERUMCOMPLEMENT", "0.117", "催乳素*", "0.129", "血肌酥SERUM CREATININE", "0.130", "伽马球蛋白GAMMA-GLOBULINS", "0.130", "血清溶菌酵SERUMLYSOZYME", "0.130", "肾素*", "0.131", "糖苷*", "0.133", "PERIPHERICBLOODLEUCOCYTES", "0.134", "血清中的氨基酸NITROGENOFAMINOACIDSINSERUM", "0.134", "肿瘤标志物MELANOGENE在尿*", "0.135", "嗜碱性粒细胞BASOPHILS", "0.135", "甲状腺素结合球蛋白", "0.136", "AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE", "0.138", "血清淀粉酵素SERUMALPHAAMYLASE", "0.139", "醛固酮尿*", "0.139", "游离胆固醇FREEPLASMACHOLESTERIN", "0.139", "甲状腺球蛋白*", "0.140", "肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE", "0.140", "嗜酸性粒细胞EOSINOPHILES", "0.140", "17-血浆氧皮质类固醇类", "0.141", "嗜中性粒细胞STABNEUTROPHILS", "0.142", "唾液酸*", "0.142", "尿肌配URINECREATININE", "0.143", "17-尿中酮类固醇", "0.143", "维生素B6*", "0.143", "多巴胺*", "0.143", "红细胞沉降率(ESR)", "0.143", "分段的中性粒细胞SEGMENTEDNEUTROPHILS", "0.144", "血清蛋白SERUMALBUMEN", "0.144", "RHEUMOFACTOR*", "0.144", "血红蛋白HAEMOGLOBIN", "0.144", "抗利尿激素*", "0.145", "血细胞比容，全血*", "0.147", "尿白血球URINE LEUCOCYTES", "0.147", "11 - PLASMA OXYCORTICOSTEROIDS", "0.147", "胰岛素*", "0.147", "抗链球菌溶血素*", "0.147", "生长激素SOMATOTROPICHORMONE", "0.149", "皮质醇SERUMHYDROCORTISONE", "0.149", "胰高血糖素*", "0.149", "血尿素BLOODUREA", "0.150", "总铁结合力（TIBC）", "0.150", "尿中尿酸URINEURICACID", "0.150", "酸性磷酸酵素ACIDPHOSPHATASE", "0.151", "血尿酸SERUMURIC ACID", "0.151", "肿瘤标志物胸苷激酶*", "0.152", "维生素B2*", "0.152", "血浆磷脂PLASMA PHOSPHOTIDES", "0.152", "糖基化血红蛋白", "0.152", "备解素*", "0.152", "ALPHA2球蛋白*"], "rec_scores": [0.9992889165878296, 0.8574286699295044, 0.9972284436225891, 0.9888066649436951, 0.9720867872238159, 0.9999223947525024, 0.9494190812110901, 0.9999534487724304, 0.9845221638679504, 0.9998990297317505, 0.9788220524787903, 0.9998382329940796, 0.9436192512512207, 0.9998611211776733, 0.996026873588562, 0.999524712562561, 0.9833086729049683, 0.9996541142463684, 0.9215916991233826, 0.9996191263198853, 0.9784450531005859, 0.9992367625236511, 0.9442469477653503, 0.9992122650146484, 0.9957689046859741, 0.9990226030349731, 0.9979596138000488, 0.9992015957832336, 0.9704620838165283, 0.9995498657226562, 0.9436194896697998, 0.9994012713432312, 0.9922927021980286, 0.9993805885314941, 0.9943169355392456, 0.999383807182312, 0.9775409698486328, 0.9992146492004395, 0.9703496098518372, 0.999433159828186, 0.9960910677909851, 0.999461829662323, 0.9974311590194702, 0.9994473457336426, 0.9686256051063538, 0.9992998838424683, 0.9987472295761108, 0.9995485544204712, 0.9957135319709778, 0.9995496869087219, 0.9970381259918213, 0.9996085166931152, 0.99711674451828, 0.99932861328125, 0.9802201986312866, 0.9994992017745972, 0.9961502552032471, 0.9994916915893555, 0.9408621191978455, 0.9993370175361633, 0.9979647397994995, 0.999134361743927, 0.997809648513794, 0.9993459582328796, 0.991276204586029, 0.9993607401847839, 0.9968063831329346, 0.9993988871574402, 0.9574174880981445, 0.9994600415229797, 0.9590333104133606, 0.9995555877685547, 0.9839123487472534, 0.9995719790458679, 0.9941267371177673, 0.9994319081306458, 0.9447834491729736, 0.9995003938674927, 0.9542663097381592, 0.9995555877685547, 0.9941748380661011, 0.9993694424629211, 0.9986621141433716, 0.9992073774337769, 0.9967509508132935, 0.9991703033447266, 0.995011568069458, 0.9991536140441895, 0.9810506701469421, 0.9992132186889648, 0.9761788845062256, 0.9994081258773804, 0.9751254916191101, 0.9995087385177612, 0.9629639387130737, 0.9993146061897278, 0.9597681760787964, 0.999161422252655, 0.9738208055496216, 0.9994081258773804, 0.9958900213241577, 0.999505341053009, 0.9969716668128967, 0.9989840388298035, 0.9706903100013733, 0.9993130564689636, 0.9935243129730225, 0.9995059967041016, 0.8953628540039062, 0.9995495080947876, 0.997216522693634, 0.9995476007461548, 0.9954561591148376, 0.9992704391479492, 0.971625030040741, 0.9995473623275757, 0.9439200162887573, 0.999647319316864, 0.9927509427070618, 0.9996153712272644, 0.9795926809310913, 0.9993577003479004, 0.9972587823867798, 0.9996970295906067, 0.9413313269615173, 0.9993345141410828, 0.9787399172782898], "rec_boxes": [[98, 77, 157, 102], [192, 77, 295, 102], [98, 102, 157, 129], [190, 98, 271, 130], [190, 123, 462, 157], [98, 154, 162, 179], [192, 154, 319, 179], [98, 179, 162, 204], [194, 179, 314, 204], [98, 204, 162, 229], [194, 206, 556, 229], [98, 231, 162, 256], [196, 234, 727, 252], [98, 256, 161, 281], [192, 257, 415, 281], [98, 281, 155, 306], [192, 279, 305, 304], [98, 306, 155, 332], [194, 309, 306, 329], [98, 332, 155, 358], [192, 331, 406, 354], [98, 358, 157, 383], [190, 358, 260, 384], [98, 383, 157, 409], [192, 384, 689, 408], [98, 408, 157, 434], [192, 409, 445, 433], [98, 433, 155, 459], [188, 431, 262, 463], [100, 458, 157, 484], [192, 459, 417, 484], [98, 484, 157, 509], [194, 484, 454, 508], [98, 509, 155, 536], [194, 511, 441, 534], [98, 536, 155, 561], [190, 534, 246, 563], [98, 561, 155, 586], [185, 555, 247, 592], [98, 586, 157, 611], [194, 590, 474, 608], [98, 611, 157, 638], [196, 617, 639, 635], [98, 636, 157, 663], [192, 640, 447, 663], [98, 663, 157, 688], [188, 661, 401, 688], [98, 688, 157, 715], [192, 690, 362, 713], [98, 713, 157, 740], [194, 715, 731, 738], [98, 740, 157, 767], [194, 740, 504, 765], [98, 765, 157, 790], [190, 765, 279, 792], [98, 790, 157, 817], [192, 792, 522, 815], [98, 815, 157, 842], [192, 817, 314, 842], [98, 840, 157, 867], [192, 844, 606, 867], [98, 867, 157, 892], [192, 869, 430, 892], [98, 892, 157, 919], [190, 892, 386, 919], [98, 917, 155, 944], [190, 919, 474, 942], [98, 944, 157, 969], [184, 938, 265, 975], [98, 969, 157, 996], [192, 971, 412, 994], [98, 994, 157, 1021], [192, 996, 330, 1021], [98, 1019, 157, 1046], [190, 1019, 284, 1047], [98, 1046, 157, 1071], [188, 1042, 264, 1074], [98, 1071, 157, 1098], [190, 1069, 364, 1094], [98, 1096, 157, 1123], [192, 1098, 569, 1121], [98, 1121, 157, 1148], [192, 1124, 412, 1148], [98, 1148, 157, 1173], [190, 1146, 347, 1173], [98, 1173, 157, 1199], [192, 1174, 393, 1198], [98, 1198, 157, 1224], [190, 1199, 297, 1224], [98, 1224, 157, 1249], [192, 1224, 351, 1249], [98, 1249, 157, 1274], [192, 1251, 438, 1274], [98, 1274, 157, 1301], [192, 1276, 508, 1300], [98, 1300, 157, 1326], [190, 1301, 260, 1328], [98, 1326, 155, 1351], [192, 1326, 332, 1351], [98, 1351, 157, 1376], [192, 1353, 500, 1376], [98, 1376, 157, 1403], [192, 1380, 473, 1403], [100, 1403, 157, 1428], [192, 1403, 297, 1428], [98, 1428, 157, 1453], [192, 1426, 362, 1453], [98, 1453, 155, 1480], [194, 1455, 371, 1478], [98, 1478, 157, 1505], [194, 1484, 412, 1502], [98, 1505, 157, 1530], [194, 1509, 476, 1527], [98, 1530, 155, 1555], [194, 1532, 401, 1555], [98, 1555, 155, 1582], [192, 1557, 367, 1582], [98, 1580, 157, 1607], [190, 1578, 283, 1609], [98, 1607, 157, 1632], [194, 1607, 478, 1632], [100, 1632, 157, 1657], [194, 1636, 330, 1655], [98, 1657, 157, 1684], [188, 1655, 266, 1687], [100, 1682, 157, 1709], [192, 1684, 327, 1709]]}}]}, "outputImages": {"layout_det_res": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//73971fc2-7919-4092-aa22-9d9410c37bfc/layout_det_res_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-28T02%3A46%3A24Z%2F-1%2F%2Ffa5d3118e9de9041751c4cf3b0474c30313f1b9f3dcfb5ac5a2d6e9ad1a75b3d", "ocr_res_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//73971fc2-7919-4092-aa22-9d9410c37bfc/ocr_res_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-28T02%3A46%3A24Z%2F-1%2F%2F60f2262d4df4cdcd2de19cee026beb0a98865f5579d3f54e5bb4e5fabd9a5f4c", "table_cell_img": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//73971fc2-7919-4092-aa22-9d9410c37bfc/table_cell_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-28T02%3A46%3A24Z%2F-1%2F%2F11511447da750ca17a26a4f12178a270f6fc61a1fa5af68a1b2dca233d8925b2"}, "inputImage": "https://pplines-online.bj.bcebos.com/deploy/2664359/87351//73971fc2-7919-4092-aa22-9d9410c37bfc/input_img_0.jpg?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-06-28T02%3A46%3A24Z%2F-1%2F%2F69ea90a0474a16f1c1ea23d2240b150ae612441414bcf5c9f8fe9213e3316fe2"}], "dataInfo": {"width": 768, "height": 1716, "type": "image"}}, "errorCode": 0, "errorMsg": "Success"}