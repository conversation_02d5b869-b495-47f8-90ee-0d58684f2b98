package api

import (
	"fmt"
	"time"

	"MagneticOperator/app/services"
	"MagneticOperator/app/utils"
)

// ConcurrentScreenshotAPI 并发截图API
type ConcurrentScreenshotAPI struct {
	integratedService *services.IntegratedScreenshotService
}

// NewConcurrentScreenshotAPI 创建并发截图API
func NewConcurrentScreenshotAPI(integratedService *services.IntegratedScreenshotService) *ConcurrentScreenshotAPI {
	return &ConcurrentScreenshotAPI{
		integratedService: integratedService,
	}
}

// StartNewRound 开始新的截图轮次
func (api *ConcurrentScreenshotAPI) StartNewRound(userName string) (map[string]interface{}, error) {
	utils.LogOperation("开始新轮次", userName, "")

	if api.integratedService == nil {
		return nil, fmt.Errorf("并发截图服务未初始化")
	}

	round, err := api.integratedService.StartNewRound(userName)
	if err != nil {
		utils.LogError("开始新轮次失败", userName, err)
		return nil, err
	}

	result := map[string]interface{}{
		"success":      true,
		"round_number": round.RoundNumber,
		"user_name":    round.UserName,
		"start_time":   round.StartTime,
		"status":       round.Status,
	}

	utils.LogInfo(fmt.Sprintf("新轮次%d已开始，用户: %s", round.RoundNumber, userName))
	return result, nil
}

// TakeScreenshot 执行截图并提交OCR处理（非阻塞）
func (api *ConcurrentScreenshotAPI) TakeScreenshot(roundNumber int, mode string) (map[string]interface{}, error) {
	utils.LogOperation("并发截图", fmt.Sprintf("轮次%d_%s", roundNumber, mode), "")

	if api.integratedService == nil {
		return nil, fmt.Errorf("并发截图服务未初始化")
	}

	err := api.integratedService.TakeScreenshot(roundNumber, mode)
	if err != nil {
		utils.LogError("并发截图失败", fmt.Sprintf("轮次%d_%s", roundNumber, mode), err)
		return map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		}, err
	}

	result := map[string]interface{}{
		"success":      true,
		"round_number": roundNumber,
		"mode":         mode,
		"message":      "截图已提交，OCR处理中...",
		"timestamp":    time.Now(),
	}

	utils.LogInfo(fmt.Sprintf("轮次%d截图已提交，模式: %s", roundNumber, mode))
	return result, nil
}

// GetRoundStatus 获取指定轮次状态
func (api *ConcurrentScreenshotAPI) GetRoundStatus(roundNumber int) (map[string]interface{}, error) {
	if api.integratedService == nil {
		return nil, fmt.Errorf("并发截图服务未初始化")
	}

	round, exists := api.integratedService.GetRound(roundNumber)
	if !exists {
		return map[string]interface{}{
			"success": false,
			"error":   fmt.Sprintf("轮次 %d 不存在", roundNumber),
		}, fmt.Errorf("轮次 %d 不存在", roundNumber)
	}

	round.RLock()
	defer round.RUnlock()

	result := map[string]interface{}{
		"success":             true,
		"round_number":        round.RoundNumber,
		"user_name":           round.UserName,
		"status":              round.Status,
		"screenshots_count":   len(round.Screenshots),
		"results_count":       len(round.Results),
		"errors_count":        len(round.Errors),
		"start_time":          round.StartTime,
		"completed_time":      round.CompletedTime,
		"progress":            float64(len(round.Results)) / float64(len(round.Screenshots)),
	}

	// 添加详细结果信息
	if len(round.Results) > 0 {
		results := make([]map[string]interface{}, len(round.Results))
		for i, result := range round.Results {
			resultInfo := map[string]interface{}{
					"task_id":   result.Task.ID,
					"mode":      result.Task.Mode,
					"duration":  result.Duration,
					"timestamp": result.Processed,
				}

			if result.Error != nil {
				resultInfo["success"] = false
				resultInfo["error"] = result.Error.Error()
			} else {
				resultInfo["success"] = true
				resultInfo["organ_name"] = result.Result.OrganName
				resultInfo["confidence"] = result.Result.Confidence
			}

			results[i] = resultInfo
		}
		result["results"] = results
	}

	// 添加错误信息
	if len(round.Errors) > 0 {
		result["errors"] = round.Errors
	}

	return result, nil
}

// GetOverallProgress 获取整体进度
func (api *ConcurrentScreenshotAPI) GetOverallProgress() (map[string]interface{}, error) {
	if api.integratedService == nil {
		return nil, fmt.Errorf("并发截图服务未初始化")
	}

	progress := api.integratedService.GetOverallProgress()
	return progress, nil
}

// GetAllRounds 获取所有轮次信息
func (api *ConcurrentScreenshotAPI) GetAllRounds() ([]RoundStatusDTO, error) {
	if api.integratedService == nil {
		return nil, fmt.Errorf("并发截图服务未初始化")
	}

	allRounds := api.integratedService.GetAllRounds()
	roundsList := make([]RoundStatusDTO, 0, len(allRounds))
	for _, round := range allRounds {
		round.RLock()
		progress := 0.0
		if len(round.Screenshots) > 0 {
			progress = float64(len(round.Results)) / float64(len(round.Screenshots))
		}
		
		// Handle CompletedTime pointer
		var completedTime time.Time
		if round.CompletedTime != nil {
			completedTime = *round.CompletedTime
		}
		
		roundInfo := RoundStatusDTO{
			RoundNumber:      round.RoundNumber,
			Status:            string(round.Status),
			TotalTasks:       len(round.Screenshots),
			CompletedTasks:   len(round.Results),
			Progress:          progress,
			StartTime:        round.StartTime,
			CompletedTime:    completedTime,
		}
		round.RUnlock()
		roundsList = append(roundsList, roundInfo)
	}

	return roundsList, nil
}

// WaitForRoundCompletion 等待指定轮次完成（可选的阻塞方法）
func (api *ConcurrentScreenshotAPI) WaitForRoundCompletion(roundNumber int, timeoutSeconds int) (map[string]interface{}, error) {
	if api.integratedService == nil {
		return nil, fmt.Errorf("并发截图服务未初始化")
	}

	timeout := time.Duration(timeoutSeconds) * time.Second
	round, err := api.integratedService.WaitForRoundCompletion(roundNumber, timeout)
	if err != nil {
		return map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		}, err
	}

	round.RLock()
	defer round.RUnlock()

	result := map[string]interface{}{
		"success":        true,
		"round_number":   round.RoundNumber,
		"status":         round.Status,
		"completed_time": round.CompletedTime,
		"duration":       round.CompletedTime.Sub(round.StartTime),
	}

	return result, nil
}

// GetServiceStatus 获取服务状态
func (api *ConcurrentScreenshotAPI) GetServiceStatus() (*ServiceStatusDTO, error) {
	if api.integratedService == nil {
		return nil, fmt.Errorf("服务未初始化")
	}

	stats := api.integratedService.GetOCRProcessorStats()
	allRounds, err := api.GetAllRounds()
	if err != nil {
		return nil, err
	}

	statusDTO := &ServiceStatusDTO{
		IsRunning:      api.integratedService.IsRunning(),
		CurrentRound:   api.integratedService.GetCurrentRoundNumber(),
		TotalRounds:    api.integratedService.GetTotalRounds(),
		OcrStats:       *stats,
		CompletedRounds: api.integratedService.GetCompletedRoundsCount(),
		AllRoundsStatus: allRounds,
	}

	return statusDTO, nil
}