# 受检者列表加载问题排查指南

## 问题描述
用户报告：2025年7月6日已有受检者报到，但应用启动后受检者列表未读取到，前端界面的刷新按钮也失效。

## 诊断结果

通过运行诊断工具，我们发现：

### ✅ 正常的部分
1. **配置加载正常**
   - 站点ID: `YL-BJ-TZ-001`
   - 站点名称: `北京市通州区潞城镇潞城社区卫生服务中心`
   - 设备MAC: `00:15:5d:ed:4a:58`
   - 处理后设备号: `00155ded4a58`

2. **API通信正常**
   - API URL: `https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice`
   - 请求参数正确: `{"date":"2025-07-06","device_no":"00155ded4a58","page":1,"pageSize":20,"site_id":"YL-BJ-TZ-001"}`
   - 响应状态码: `200`
   - 响应格式正确: `{"errCode":"0","errMsg":"查询成功","data":[],"total":0}`

3. **前端刷新功能正常**
   - 刷新按钮绑定正确
   - `refreshAllLists()` 函数正常调用
   - 各个加载函数 (`loadPendingRegistrations`, `loadCompletedPatients`, `loadUnanalyzedPatients`) 正常执行

### ❌ 问题根因
**云数据库中没有匹配的报到记录**

经过多种参数组合测试（不同日期、不同站点ID、不同设备号格式），均未找到任何记录，说明：
- 2025年7月6日该站点该设备确实没有报到记录
- 或者报到记录存储在不同的站点ID/设备号下

## 可能的原因分析

### 1. 数据录入问题
- **受检者可能在其他设备报到**：使用了不同的设备MAC地址
- **受检者可能在其他站点报到**：使用了不同的站点ID
- **日期格式问题**：报到记录可能使用了不同的日期格式

### 2. 配置不匹配
- **设备MAC地址配置错误**：当前配置的MAC地址可能与实际报到设备不符
- **站点ID配置错误**：当前配置的站点ID可能与实际报到站点不符

### 3. 数据同步问题
- **数据延迟**：报到记录可能还未同步到云数据库
- **数据库连接问题**：可能存在数据库连接或权限问题

## 解决方案

### 立即解决方案

#### 1. 验证报到记录是否存在
请联系系统管理员或数据库管理员，直接查询云数据库中的报到记录：
```sql
-- 查询2025年7月6日的所有报到记录
SELECT * FROM registrations 
WHERE registration_date = '2025-07-06'
ORDER BY registration_time DESC;

-- 查询该站点的所有报到记录
SELECT * FROM registrations 
WHERE site_id = 'YL-BJ-TZ-001'
ORDER BY registration_time DESC
LIMIT 10;

-- 查询该设备的所有报到记录
SELECT * FROM registrations 
WHERE device_no LIKE '%00155ded4a58%'
ORDER BY registration_time DESC
LIMIT 10;
```

#### 2. 检查设备MAC地址
1. 打开命令提示符
2. 运行 `ipconfig /all`
3. 找到当前使用的网络适配器的物理地址（MAC地址）
4. 与配置文件中的MAC地址进行对比

#### 3. 验证站点信息
确认当前设备是否确实属于站点 `YL-BJ-TZ-001`，如果不是，需要更新配置文件中的站点信息。

### 长期解决方案

#### 1. 增强诊断功能
在应用中添加诊断面板，显示：
- 当前配置的站点ID和设备MAC
- 最近的API请求和响应
- 数据库连接状态
- 最后成功获取数据的时间

#### 2. 改进错误提示
当没有找到报到记录时，显示更详细的信息：
- 当前查询的参数（站点ID、设备号、日期）
- 建议的排查步骤
- 联系管理员的方式

#### 3. 添加配置验证
在应用启动时验证：
- 站点ID是否有效
- 设备MAC地址是否正确
- API连接是否正常

#### 4. 实现数据缓存
- 缓存最近的查询结果
- 在网络问题时显示缓存的数据
- 提供离线模式支持

## 操作步骤

### 步骤1：验证配置
1. 检查 `config/app_config.json` 中的配置：
   ```json
   {
     "site_info": {
       "site_id": "YL-BJ-TZ-001",
       "site_name": "北京市通州区潞城镇潞城社区卫生服务中心"
     },
     "device_info": {
       "mac_address": "00:15:5d:ed:4a:58"
     }
   }
   ```

2. 确认这些信息与实际环境匹配

### 步骤2：联系数据管理员
提供以下信息给数据管理员：
- 站点ID: `YL-BJ-TZ-001`
- 设备号: `00155ded4a58`
- 查询日期: `2025-07-06`
- 请求他们直接查询数据库中是否存在匹配的记录

### 步骤3：测试其他日期
尝试查询其他日期的数据，确认API和配置是否正常工作：
- 查询昨天的数据
- 查询上周的数据
- 如果能找到其他日期的数据，说明配置正确，问题在于特定日期的数据

### 步骤4：更新配置（如需要）
如果发现配置错误，更新 `config/app_config.json` 文件中的相应配置，然后重启应用。

## 预防措施

1. **定期备份配置**：确保配置文件有备份
2. **监控数据同步**：建立数据同步监控机制
3. **测试环境验证**：在测试环境中验证配置的正确性
4. **文档记录**：记录所有配置变更和原因

## 联系支持

如果按照以上步骤仍无法解决问题，请联系技术支持，并提供：
- 诊断工具的完整输出
- 配置文件内容
- 具体的错误信息
- 预期的行为描述

---

**注意**：本指南基于2025年7月6日的诊断结果，如果问题持续存在，建议重新运行诊断工具获取最新的状态信息。