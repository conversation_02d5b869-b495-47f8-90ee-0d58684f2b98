//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
)

func main() {
	// 获取当前工作目录
	cwd, err := os.Getwd()
	if err != nil {
		log.Fatal("Failed to get current directory:", err)
	}

	// 设置静态文件服务器
	fs := http.FileServer(http.Dir(cwd))
	http.Handle("/", fs)

	// 设置端口
	port := "8001"
	if len(os.Args) > 1 {
		port = os.Args[1]
	}

	fmt.Printf("Starting HTTP server on port %s...\n", port)
	fmt.Printf("Serving files from: %s\n", cwd)
	fmt.Printf("Open in browser: http://localhost:%s/text_color_display.html\n", port)

	// 启动服务器
	err = http.ListenAndServe(":"+port, nil)
	if err != nil {
		log.Fatal("Server failed to start:", err)
	}
}
